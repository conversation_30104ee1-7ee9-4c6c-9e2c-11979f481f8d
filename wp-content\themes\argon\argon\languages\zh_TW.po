msgid ""
msgstr ""
"Project-Id-Version: argon\n"
"POT-Creation-Date: 2022-02-14 19:15+0800\n"
"PO-Revision-Date: 2022-02-14 19:19+0800\n"
"Last-Translator: \n"
"Language-Team: solstice23\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Poedit 3.0.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n;_nx\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: languages\n"
"X-Poedit-SearchPathExcluded-2: theme-update-checker\n"
"X-Poedit-SearchPathExcluded-3: argontheme.js\n"
"X-Poedit-SearchPathExcluded-4: gutenberg\n"

#: 404.php:11
msgid "404 - 找不到页面"
msgstr "404 - 找不到頁面"

#: 404.php:101
msgid "这个页面不见了"
msgstr "這個頁面不見了"

#: 404.php:105 template-parts/content-none-search.php:13
#: template-parts/content-none-tag.php:9
msgid "返回上一页"
msgstr "返回上一頁"

#: 404.php:109
msgid "回到首页"
msgstr "回到首頁"

#: archive.php:14
msgid "篇文章"
msgstr "篇文章"

#: comments.php:12 header.php:524 settings.php:1250
#: template-parts/shuoshuo-operations.php:10
msgid "评论"
msgstr "評論"

#: comments.php:51
msgid "暂无评论"
msgstr "暫無評論"

#: comments.php:59
msgid "本文评论已关闭"
msgstr "本文評論已關閉"

#: comments.php:76 settings.php:1290
msgid "发送评论"
msgstr "發怖評論"

#: comments.php:77
msgid "编辑评论"
msgstr "編輯評論"

#: comments.php:80
msgid "正在回复"
msgstr "正在回覆"

#: comments.php:80
msgid " 的评论"
msgstr " 的評論"

#: comments.php:82
msgid "取消回复"
msgstr "取消回覆"

#: comments.php:87
msgid "评论内容"
msgstr "評論內容"

#: comments.php:126
msgid "昵称"
msgstr "暱稱"

#: comments.php:136
msgid "邮箱"
msgstr "電子信箱"

#: comments.php:136
msgid " / QQ 号"
msgstr " / QQ 號"

#: comments.php:146
msgid "验证码"
msgstr "驗證碼"

#: comments.php:166
msgid "获取验证码失败"
msgstr "獲取驗證碼失敗"

#: comments.php:182
msgid "网站"
msgstr "網站"

#: comments.php:189
msgid "展开附加字段"
msgstr "展開附加欄位"

#: comments.php:189
msgid "折叠附加字段"
msgstr "摺疊附加欄位"

#: comments.php:202
msgid "评论仅发送者和博主可见"
msgstr "評論僅發怖者和博主可見"

#: comments.php:204 functions.php:1065
msgid "悄悄话"
msgstr "悄悄話"

#: comments.php:208
msgid "有回复时邮件通知我"
msgstr "有回覆時郵件通知我"

#: comments.php:210
msgid "邮件提醒"
msgstr "郵件提醒"

#: comments.php:216
msgid "发送"
msgstr "傳送"

#: comments.php:217 functions.php:1100
msgid "编辑"
msgstr "編輯"

#: comments.php:221
msgid "取消"
msgstr "取消"

#: comments.php:224
msgid "表情"
msgstr "表情"

#: emotions.php:4
msgid "颜文字"
msgstr "顏文字"

#: functions.php:3
msgid "Argon 主题不支持 Wordpress 4.4 以下版本，请更新 Wordpress"
msgstr "Argon 主題不支援 WordPress 4.4 以下版本，請更新 WordPress"

#: functions.php:157
msgid "左侧栏小工具"
msgstr "左側欄小工具"

#: functions.php:159
msgid "左侧栏小工具 (如果设置会在侧栏增加一个 Tab)"
msgstr "左側欄小工具 (如果設置會在側欄新增一個 Tab)"

#: functions.php:168
msgid "右侧栏小工具"
msgstr "右側欄小工具"

#: functions.php:170
msgid "右侧栏小工具 (在 \"Argon 主题选项\" 中选择 \"三栏布局\" 才会显示)"
msgstr "右側欄小工具 (在「Argon 主題選項」 中選擇「三欄布局」 才會顯示)"

#: functions.php:179 functions.php:181
msgid "站点概览额外内容"
msgstr "站點概覽額外內容"

#: functions.php:415
msgid "这是一个加密页面，需要密码来查看"
msgstr "這篇內容受到密碼保護，請輸入密碼以查看"

#: functions.php:574
msgid "几秒读完"
msgstr "幾秒讀完"

#: functions.php:577
msgid "1 分钟内"
msgstr "1 分鐘內"

#: functions.php:580
msgid "分钟"
msgstr "分鐘"

#: functions.php:582
msgid "小时"
msgstr "小時"

#: functions.php:607 functions.php:1062
#: template-parts/content-shuoshuo-details.php:16
#: template-parts/content-shuoshuo-preview.php:24
#: template-parts/content-shuoshuo.php:16
msgctxt "pinned"
msgid "置顶"
msgstr "置頂"

#: functions.php:613
msgid "需要密码"
msgstr "需要密碼"

#: functions.php:619 functions.php:627
msgid "发布于"
msgstr "發布於"

#: functions.php:619 functions.php:627
msgid "编辑于"
msgstr "編輯於"

#: functions.php:673
#, php-format
msgid "包含 %d 行代码"
msgstr "包含 %d 行代碼"

#: functions.php:677
msgid "字"
msgstr "字"

#: functions.php:826
msgid "该评论为悄悄话"
msgstr "此評論為悄悄話"

#: functions.php:891
msgid "最初版本"
msgstr "最初版本"

#: functions.php:935
msgid "查看图片"
msgstr "檢視圖片"

#: functions.php:1004 unsubscribe-comment-mailnotice.php:12
msgid "评论不存在"
msgstr "評論不存在"

#: functions.php:1012
msgid "该评论已被赞过"
msgstr "已按讚此評論"

#: functions.php:1021 functions.php:1948
msgid "点赞成功"
msgstr "按讚成功"

#: functions.php:1059 functions.php:1117
msgid "博主"
msgstr "博主"

#: functions.php:1068 functions.php:1120
msgid "待审核"
msgstr "待審核"

#: functions.php:1077
msgid "已编辑"
msgstr "已編輯"

#: functions.php:1081
msgid "前"
msgstr "前"

#: functions.php:1094
msgid "取消置顶"
msgstr "取消置頂"

#: functions.php:1096
msgctxt "to pin"
msgid "置顶"
msgstr "置頂"

#: functions.php:1102
msgid "回复"
msgstr "回覆"

#: functions.php:1226
msgid "验证码错误"
msgstr "驗證碼錯誤"

#: functions.php:1273
msgid "不能回复其他人的悄悄话评论"
msgstr "無法回覆其他人的悄悄話評論"

#: functions.php:1403 functions.php:1404
msgid "您在"
msgstr "您在"

#: functions.php:1403 functions.php:1404
msgid "的评论有了新的回复"
msgstr "的評論有了新的回覆"

#: functions.php:1419
msgid "回复了你"
msgstr "回覆了您"

#: functions.php:1430
msgid "前往查看"
msgstr "前往閱讀"

#: functions.php:1447
msgid "退订该评论的邮件提醒"
msgstr "取消訂閱該評論的郵件提醒"

#: functions.php:1530
msgid "博主关闭了编辑评论功能"
msgstr "博主關閉了編輯評論功能"

#: functions.php:1539
msgid "您不是这条评论的作者或 Token 已过期"
msgstr "您不是這條評論的作者或 Token 已過期"

#: functions.php:1545
msgid "新的评论为空"
msgstr "新的評論為空"

#: functions.php:1571
msgid "编辑评论成功"
msgstr "編輯評論成功"

#: functions.php:1579
msgid "编辑评论失败，可能原因: 与原评论相同"
msgstr "編輯評論失敗，可能原因: 與原評論相同"

#: functions.php:1591
msgid "博主关闭了评论置顶功能"
msgstr "博主關閉了評論置頂功能"

#: functions.php:1597
msgid "您没有权限进行此操作"
msgstr "您沒有權限進行此操作"

#: functions.php:1606
msgid "评论已经是置顶状态"
msgstr "評論已經是置頂狀態"

#: functions.php:1606
msgid "评论已经是取消置顶状态"
msgstr "評論已經是取消置頂狀態"

#: functions.php:1612
msgid "不能置顶子评论"
msgstr "不能置頂子評論"

#: functions.php:1618
msgid "不能置顶悄悄话"
msgstr "不能置頂悄悄話"

#: functions.php:1624
msgid "置顶评论成功"
msgstr "置頂評論成功"

#: functions.php:1624
msgid "取消置顶成功"
msgstr "取消置頂成功"

#: functions.php:1939
msgid "该说说已被赞过"
msgstr "該說說已被讚過"

#: functions.php:1957
msgid ""
"警告：你可能修改了 Argon 主题页脚的版权声明，Argon 主题要求你至少保留主题的 "
"Github 链接或主题的发布文章链接。"
msgstr ""
"警告: 您似乎修改了 Argon 主題頁尾的版權聲明，如要使用 Argon 主題必須保留主題"
"的 GitHub 連結或主題發布文章連結。"

#: functions.php:2106 settings.php:668
msgid "显示字数和预计阅读时间"
msgstr "顯示字數和預計閱讀時間"

#: functions.php:2109 functions.php:2123 functions.php:2131
msgid "跟随全局设置"
msgstr "使用全域設定"

#: functions.php:2110 settings.php:543 settings.php:553 settings.php:564
#: settings.php:629 settings.php:673 settings.php:740 settings.php:751
#: settings.php:905 settings.php:966 settings.php:1447 settings.php:1463
#: settings.php:1573
msgid "不显示"
msgstr "不顯示"

#: functions.php:2112
msgid "是否显示字数和预计阅读时间 Meta 信息"
msgstr "是否顯示字數和預計閱讀時間 Meta 資訊"

#: functions.php:2113
msgid "Meta 中隐藏发布时间和分类"
msgstr "Meta 中隱藏發布時間和分類"

#: functions.php:2116 settings.php:1307
msgid "不隐藏"
msgstr "不隱藏"

#: functions.php:2117 settings.php:386 settings.php:1308
msgid "隐藏"
msgstr "隱藏"

#: functions.php:2119
msgid ""
"适合特定的页面，例如友链页面。开启后文章 Meta 的第一行只显示阅读数和评论数。"
msgstr ""
"開啟後文章 Meta 資訊第一行僅顯示閱讀數和評論數。適用於特定的頁面 (例如友連頁"
"面)。"

#: functions.php:2120
msgid "使用文章中第一张图作为头图"
msgstr "使用文章中第一張圖作為精選圖片"

#: functions.php:2124
msgid "使用"
msgstr "使用"

#: functions.php:2125 settings.php:283 settings.php:1102
msgid "不使用"
msgstr "不使用"

#: functions.php:2127
msgid "显示文章过时信息"
msgstr "顯示文章過時資訊"

#: functions.php:2132
msgid "一直显示"
msgstr "永遠顯示"

#: functions.php:2133
msgid "永不显示"
msgstr "永不顯示"

#: functions.php:2135
msgid "应用"
msgstr "應用"

#: functions.php:2137
msgid "单独控制该文章的过时信息显示。"
msgstr "為文章獨立配置過時資訊顯示。"

#: functions.php:2138 settings.php:765 settings.php:767
msgid "文末附加内容"
msgstr "文末附加內容"

#: functions.php:2141
msgid ""
"给该文章设置单独的文末附加内容，留空则跟随全局，设为 <code>--none--</code> "
"则不显示。"
msgstr ""
"為文章配置獨立的文末附加內容，留空則使用全域設定，設為 <code>--none--</"
"code> 則不顯示。"

#: functions.php:2142
msgid "自定义 CSS"
msgstr "自訂 CSS"

#: functions.php:2145
msgid "给该文章添加单独的 CSS"
msgstr "為文章新增獨立的 CSS"

#: functions.php:2181 functions.php:2190
msgid "应用失败"
msgstr "應用失敗"

#: functions.php:2185
msgid "应用成功"
msgstr "應用成功"

#: functions.php:2198
msgid "文章设置"
msgstr "文章設定"

#: functions.php:2961 settings.php:727
msgid "参考"
msgstr "參考"

#: functions.php:3024 settings.php:40
msgid "Argon 主题设置"
msgstr "Argon 主題設定"

#: functions.php:3024
msgid "Argon 主题选项"
msgstr "Argon 主題選項"

#: functions.php:3032
msgid "顶部导航"
msgstr "頂部導覽列"

#: functions.php:3033
msgid "左侧栏菜单"
msgstr "左側欄選單"

#: functions.php:3034
msgid "左侧栏作者个人链接"
msgstr "左側欄作者個人連結"

#: functions.php:3035
msgid "左侧栏友情链接"
msgstr "左側欄友情連結"

#: functions.php:3046 functions.php:3047 functions.php:3057 shuoshuo.php:13
msgid "说说"
msgstr "說說"

#: functions.php:3048 functions.php:3049
msgid "发表说说"
msgstr "發表說說"

#: functions.php:3050
msgid "编辑说说"
msgstr "編輯說說"

#: functions.php:3051
msgid "新说说"
msgstr "新說說"

#: functions.php:3052
msgid "查看说说"
msgstr "檢視說說"

#: functions.php:3053
msgid "搜索说说"
msgstr "搜尋說說"

#: functions.php:3054
msgid "暂无说说"
msgstr "暫無說說"

#: functions.php:3055
msgid "没有已遗弃的说说"
msgstr "沒有已遺棄的說說"

#: header.php:374 searchform.php:7 sidebar.php:67
msgid "搜索什么..."
msgstr "搜尋什麼..."

#: header.php:392 searchform.php:11 sidebar.php:66
msgid "搜索"
msgstr "搜尋"

#: header.php:517
msgid "移至左侧"
msgstr "移至左側"

#: header.php:517
msgid "移至右侧"
msgstr "移至右側"

#: header.php:521
msgid "回到顶部"
msgstr "回到頂部"

#: header.php:527 header.php:536 settings.php:122
msgid "夜间模式"
msgstr "夜間模式"

#: header.php:527 header.php:536
msgid "暗黑模式"
msgstr "暗黑模式"

#: header.php:527
msgid "日间模式"
msgstr "日間模式"

#: header.php:530
msgid "设置"
msgstr "設定"

#: header.php:536
msgid "切换到夜间模式"
msgstr "切換到夜間模式"

#: header.php:536
msgid "切换到暗黑模式"
msgstr "切換到暗黑模式"

#: header.php:543 settings.php:259
msgid "字体"
msgstr "字型"

#: header.php:549
msgid "阴影"
msgstr "陰影"

#: header.php:551 settings.php:162
msgid "浅阴影"
msgstr "淺陰影"

#: header.php:551 settings.php:166
msgid "深阴影"
msgstr "深陰影"

#: header.php:555
msgid "滤镜"
msgstr "濾鏡"

#: header.php:557 settings.php:111 settings.php:355 settings.php:396
#: settings.php:779 settings.php:1425 settings.php:1608
msgid "关闭"
msgstr "關閉"

#: header.php:558
msgid "日落"
msgstr "日落"

#: header.php:559
msgid "暗化"
msgstr "暗化"

#: header.php:560
msgid "灰度"
msgstr "灰度"

#: header.php:564
msgid "恢复默认"
msgstr "回復預設"

#: header.php:564
msgid "圆角"
msgstr "圓角"

#: header.php:571 settings.php:48
msgid "主题色"
msgstr "主題色彩"

#: header.php:576
msgid "阅读进度"
msgstr "閱讀進度"

#: msgboard.php:42
msgid "留言板"
msgstr "留言板"

#: msgboard.php:53
msgid "发送留言"
msgstr "傳送留言"

#: search.php:8
msgid "的搜索结果"
msgstr "的搜尋結果"

#: search.php:36
msgid "个结果"
msgstr "個結果"

#: settings.php:41
msgid "按下"
msgstr "按下"

#: settings.php:41
msgid "或在右侧目录中来查找设置"
msgstr "或在右側選單中來快速選取設定類別"

#: settings.php:47
msgid "全局"
msgstr "全域"

#: settings.php:50
msgid "主题颜色"
msgstr "主題色彩"

#: settings.php:54
msgid "选择预置颜色 或"
msgstr "選取預設色彩 或"

#: settings.php:54
msgid "自定义色值"
msgstr "自訂色彩"

#: settings.php:55
msgid "预置颜色："
msgstr "預配色彩："

#: settings.php:57
msgid "默认"
msgstr "預設"

#: settings.php:58
msgid "粉"
msgstr "粉色"

#: settings.php:59
msgid "水鸭青"
msgstr "水鴨青"

#: settings.php:60
msgid "蓝灰"
msgstr "藍灰色"

#: settings.php:61
msgid "天蓝"
msgstr "天藍色"

#: settings.php:62
msgid "靛蓝"
msgstr "靛藍色"

#: settings.php:63
msgid "橙"
msgstr "橘色"

#: settings.php:64
msgid "绿"
msgstr "綠色"

#: settings.php:65
msgid "红"
msgstr "紅色"

#: settings.php:66
msgid "紫"
msgstr "紫色"

#: settings.php:67
msgid "黑"
msgstr "黑色"

#: settings.php:68
msgid "棕"
msgstr "咖啡色"

#: settings.php:70
msgid "主题色与 \"Banner 渐变背景样式\" 选项搭配使用效果更佳"
msgstr "主題色彩與「Banner 漸變背景樣式」選項配搭效果更佳"

#: settings.php:99
msgid "允许用户自定义主题色（位于博客浮动操作栏设置菜单中）"
msgstr "允許訪客自訂主題色彩 (於網頁懸浮操作設定選單中)"

#: settings.php:106
msgid "沉浸式主题色"
msgstr "沉浸式主題色"

#: settings.php:110 settings.php:356 settings.php:397 settings.php:1426
#: settings.php:1609
msgid "开启"
msgstr "開啟"

#: settings.php:113
msgid ""
"开启后，主题色将会全局沉浸。</br>页面背景、卡片及页面上的其它元素会变为沉浸"
"式主题色（气氛色）。类似 Material You。"
msgstr ""
"開啟後，主題色將會全局沉浸。 </br>頁面背景、卡片及頁面上的其它元素會變為沈浸"
"式主題色（氣氛色）。類似 Material You。"

#: settings.php:124
msgid "夜间模式切换方案"
msgstr "夜間模式切換模式"

#: settings.php:128
msgid "默认使用日间模式"
msgstr "預設使用日間模式"

#: settings.php:129
msgid "默认使用夜间模式"
msgstr "預設使用夜間模式"

#: settings.php:130
msgid "跟随系统夜间模式"
msgstr "使用系統夜間模式"

#: settings.php:131
msgid "根据时间切换夜间模式 (22:00 ~ 7:00)"
msgstr "根據時間切換夜間模式 (22:00 ~ 7:00)"

#: settings.php:133
msgid "Argon 主题会根据这里的选项来决定是否默认使用夜间模式。"
msgstr "Argon 主題會依據此處設定以配置預設的夜間模式。"

#: settings.php:133
msgid "用户也可以手动切换夜间模式，用户的设置将保留到标签页关闭为止。"
msgstr "訪客亦可手動切換夜間模式，其配置保留到分頁關閉為止。"

#: settings.php:137
msgid "夜间模式颜色方案"
msgstr "夜間模式色彩"

#: settings.php:141
msgid "灰黑"
msgstr "灰黑色"

#: settings.php:142
msgid "暗黑 (AMOLED Black)"
msgstr "暗黑色 (AMOLED Black)"

#: settings.php:144
msgid "夜间模式默认的配色方案。"
msgstr "配置夜間模式的預設配色方案。"

#: settings.php:147
msgid "卡片"
msgstr "卡片"

#: settings.php:149
msgid "卡片圆角大小"
msgstr "卡片圓角大小"

#: settings.php:152
msgid "卡片的圆角大小，默认为"
msgstr "卡片的圓角大小，預設"

#: settings.php:152
msgid "。建议设置为"
msgstr "。建議配置"

#: settings.php:156
msgid "卡片阴影"
msgstr "卡片陰影"

#: settings.php:169
msgid "卡片默认阴影大小。"
msgstr "配置卡片預設陰影大小。"

#: settings.php:172 settings.php:243 settings.php:249 settings.php:255
msgid "布局"
msgstr "布局"

#: settings.php:174
msgid "页面布局"
msgstr "頁面布局"

#: settings.php:181
msgid "双栏"
msgstr "雙欄"

#: settings.php:187
msgid "单栏"
msgstr "單欄"

#: settings.php:193
msgid "三栏"
msgstr "三欄"

#: settings.php:199
msgid "双栏(反转)"
msgstr "雙欄(反轉)"

#: settings.php:201
msgid "使用单栏时，关于左侧栏的设置将失效。"
msgstr "使用單欄時，關於左側欄的配置將失效。"

#: settings.php:201
msgid "使用三栏时，请前往 \"外观-小工具\" 设置页面配置右侧栏内容。"
msgstr "使用三欄時，請前往 [外觀] > [小工具] 設定頁面中配置右側欄內容。"

#: settings.php:205
msgid "文章列表布局"
msgstr "文章列表佈局"

#: settings.php:212
msgid "单列"
msgstr "單列"

#: settings.php:218
msgid "瀑布流 (2 列)"
msgstr "瀑布流 (2 列)"

#: settings.php:224
msgid "瀑布流 (3 列)"
msgstr "瀑布流 (3 列)"

#: settings.php:230
msgid "瀑布流 (列数自适应)"
msgstr "瀑布流 (列數自動調整)"

#: settings.php:232
msgid "列数自适应的瀑布流会根据可视区宽度自动调整瀑布流列数。"
msgstr "瀑布流 (列數自動調整)會依照可視區寬度自動調節瀑布流列數。"

#: settings.php:232
msgid "建议只有使用单栏页面布局时才开启 3 列瀑布流。"
msgstr "建議在僅使用單欄頁面佈局時才開啟 3 列瀑布流。"

#: settings.php:232
msgid "所有瀑布流布局都会在屏幕宽度过小时变为单列布局。"
msgstr "所有瀑布流佈局都會在螢幕寬度過小時變為單列佈局。"

#: settings.php:236
msgid "文章列表卡片布局"
msgstr "文章列表卡片佈局"

#: settings.php:261
msgid "默认字体"
msgstr "預設字型"

#: settings.php:274
msgid "默认使用无衬线字体/衬线字体。"
msgstr "預設使用無襯線字型/襯線字型。"

#: settings.php:288
msgid ""
"选择主题资源文件的引用地址。使用 CDN 可以加速资源文件的访问并减少服务器压"
"力。"
msgstr ""
"配置主題資源文件的引用地址。使用 CDN 以加速資源文件的訪問並減少伺服器壓力。"

#: settings.php:291
msgid "子目录"
msgstr "子目錄"

#: settings.php:293
msgid "Wordpress 安装目录"
msgstr "WordPress 安裝目錄"

#: settings.php:296
msgid "如果 Wordpress 安装在子目录中，请在此填写子目录地址（例如"
msgstr "如果 WordPress 安裝在子目錄中，請在此填寫子目錄地址 (例如"

#: settings.php:296
msgid "），注意前后各有一个斜杠。默认为"
msgstr ")，注意前後各有一個斜槓。預設"

#: settings.php:296
msgid "。"
msgstr "。"

#: settings.php:296
msgid "如果不清楚该选项的用处，请保持默认。"
msgstr "如果不清楚該選項的用處，請使用 [預設]。"

#: settings.php:299 settings.php:301
msgid "日期格式"
msgstr "日期格式"

#: settings.php:312
msgid "顶栏"
msgstr "導覽列"

#: settings.php:313
msgid "状态"
msgstr "狀態"

#: settings.php:315
msgid "顶栏显示状态"
msgstr "導覽列顯示狀態"

#: settings.php:319
msgid "始终固定悬浮"
msgstr "保持固定懸浮"

#: settings.php:320
msgid "滚动时自动折叠"
msgstr "滾動時自動折疊"

#: settings.php:321
msgid "不固定"
msgstr "不固定"

#: settings.php:323
msgid "始终固定悬浮: 永远固定悬浮在页面最上方"
msgstr "保持固定懸浮: 永遠固定導覽列懸浮在頁面最上方"

#: settings.php:323
msgid "滚动时自动折叠: 在页面向下滚动时隐藏顶栏，向上滚动时显示顶栏"
msgstr "滾動時自動折疊: 在頁面向下滾動時隱藏導覽列，向上滾動時顯示導覽列"

#: settings.php:323
msgid "不固定: 只有在滚动到页面最顶端时才显示顶栏"
msgstr "不固定: 只有在滾動到頁首時才顯示導覽列"

#: settings.php:326 settings.php:795
msgid "标题"
msgstr "標題"

#: settings.php:328
msgid "顶栏标题"
msgstr "導覽列標題"

#: settings.php:331
msgid "留空则显示博客名称，输入 <code>--hidden--</code> 可以隐藏标题"
msgstr "留空則顯示博客名稱，輸入 <code>--hidden--</code> 隱藏標題"

#: settings.php:334
msgid "顶栏图标"
msgstr "導覽列圖標"

#: settings.php:336
msgid "图标地址"
msgstr "圖標位址"

#: settings.php:339
msgid "图片地址，留空则不显示"
msgstr "圖片位址，留空則不顯示"

#: settings.php:343
msgid "图标链接"
msgstr "圖標連結"

#: settings.php:346
msgid "点击图标后会跳转到的链接，留空则不跳转"
msgstr "按下圖標後愈前往的連結，留空則不配置"

#: settings.php:349 settings.php:377 settings.php:898
msgid "外观"
msgstr "外觀"

#: settings.php:351
msgid "顶栏毛玻璃效果"
msgstr "導覽列毛玻璃效果"

#: settings.php:358
msgid "开启会带来微小的性能损失。"
msgstr "開啟會帶來微小的性能損失。"

#: settings.php:361
msgid "顶部 Banner (封面)"
msgstr "頂部 Banner (封面)"

#: settings.php:362
msgid "内容"
msgstr "內容"

#: settings.php:364
msgid "Banner 标题"
msgstr "Banner 標題"

#: settings.php:367 settings.php:496
msgid "留空则显示博客名称"
msgstr "留空則顯示博客名稱"

#: settings.php:371
msgid "Banner 副标题"
msgstr "Banner 副標題"

#: settings.php:374
msgid "显示在 Banner 标题下，留空则不显示"
msgstr "顯示在 Banner 標題下，留空則不顯示"

#: settings.php:379
msgid "Banner 显示状态"
msgstr "Banner 顯示狀態"

#: settings.php:383
msgid "完整"
msgstr "完整"

#: settings.php:384
msgid "迷你"
msgstr "迷你"

#: settings.php:385
msgid "全屏"
msgstr "全屏"

#: settings.php:388
msgid "完整: Banner 高度占用半屏"
msgstr "完整: Banner 高度佔用一半螢幕"

#: settings.php:388
msgid "迷你: 减小 Banner 的内边距"
msgstr "迷你: 減小 Banner 的內邊距"

#: settings.php:388
msgid "全屏: Banner 占用全屏作为封面（仅在首页生效）"
msgstr "全屏: Banner 佔用全屏作為封面（僅在首頁生效）"

#: settings.php:388
msgid "隐藏: 完全隐藏 Banner"
msgstr "隱藏: 完全隱藏 Banner"

#: settings.php:392
msgid "Banner 透明化"
msgstr "Banner 透明化"

#: settings.php:402
msgid ""
"在顶栏添加浅色遮罩，Banner 标题添加阴影（当背景过亮影响文字阅读时勾选）"
msgstr ""
"在導覽列增加淺色遮罩，為 Banner 標題增加陰影 (當背景過亮影響文字閱讀時勾選)"

#: settings.php:405
msgid ""
"Banner 透明化可以使博客背景沉浸。建议在设置背景时开启此选项。该选项仅会在设"
"置页面背景时生效。"
msgstr ""
"Banner 透明化可使博客背景沈浸。建議已配置背景後開啟此選項。此選項僅在已設定"
"頁面背景後生效。"

#: settings.php:405
msgid "开启后，Banner 背景图和渐变背景选项将失效。"
msgstr "開啟後，Banner 背景圖和漸變背景選項將失效。"

#: settings.php:409
msgid "Banner 背景图 (地址)"
msgstr "Banner 背景圖 (位址)"

#: settings.php:412
msgid "需带上 http(s) ，留空则显示默认背景"
msgstr "需保留 http(s) ，留空則顯示預設背景"

#: settings.php:412 settings.php:503
msgid "输入"
msgstr "輸入"

#: settings.php:412
msgid "调用必应每日一图"
msgstr "使用 Bing 每日一圖"

#: settings.php:416
msgid "Banner 渐变背景样式"
msgstr "Banner 漸變背景樣式"

#: settings.php:420 settings.php:421 settings.php:422 settings.php:423
#: settings.php:424 settings.php:425 settings.php:426 settings.php:435
#: settings.php:436 settings.php:437 settings.php:438 settings.php:439
#: settings.php:440 settings.php:441
msgid "样式"
msgstr "樣式"

#: settings.php:430
msgid "隐藏背景半透明圆"
msgstr "隱藏背景半透明圓"

#: settings.php:432
msgid "如果设置了背景图则不生效"
msgstr "如果配置了背景圖片則不生效"

#: settings.php:433
msgid "样式预览 (推荐选择前三个样式)"
msgstr "樣式預覽 (推薦選擇前三個樣式)"

#: settings.php:450 settings.php:1213
msgid "动画"
msgstr "動畫"

#: settings.php:452
msgid "Banner 标题打字动画"
msgstr "Banner 標題打字動畫"

#: settings.php:456 settings.php:932 settings.php:975 settings.php:1007
#: settings.php:1233 settings.php:1504 settings.php:1553
msgid "不启用"
msgstr "不啟用"

#: settings.php:457 settings.php:718 settings.php:933 settings.php:976
#: settings.php:1061 settings.php:1113 settings.php:1137 settings.php:1234
#: settings.php:1296 settings.php:1330 settings.php:1437 settings.php:1492
#: settings.php:1503 settings.php:1554
msgid "启用"
msgstr "啟用"

#: settings.php:459
msgid "启用后 Banner 标题会以打字的形式出现。"
msgstr "啟用後 Banner 標題會以打字機特效出現。"

#: settings.php:463
msgid "Banner 标题打字动画时长"
msgstr "Banner 標題打字機特效時長"

#: settings.php:465
msgid "ms/字"
msgstr "ms/字"

#: settings.php:469 settings.php:471
msgid "页面背景"
msgstr "網頁背景"

#: settings.php:474
msgid ""
"页面背景的地址，需带上 http(s)。留空则不设置页面背景。如果设置了背景，推荐开"
"启 Banner 透明化。"
msgstr ""
"配置網頁背景的位址，需保留 http(s)。留空則不配置網頁背景。如果已配置背景，推"
"薦開啟 Banner 透明化。"

#: settings.php:478
msgid "页面背景（夜间模式时）"
msgstr "網頁背景 (夜間模式時)"

#: settings.php:481
msgid ""
"夜间模式时页面背景的地址，需带上 http(s)。设置后日间模式和夜间模式会使用不同"
"的背景。留空则跟随日间模式背景。该选项仅在设置了日间模式背景时生效。"
msgstr ""
"配置夜間模式時的網頁背景圖片位址，需保留 http(s)。配置後日間模式和夜間模式會"
"使用不同的背景圖片。留空則使用日間模式背景圖片。此設定僅在已配置日間模式背景"
"圖片後生效。"

#: settings.php:485
msgid "背景不透明度"
msgstr "背景不透明度"

#: settings.php:488
msgid "0 ~ 1 的小数，越小透明度越高，默认为 1 不透明"
msgstr "0 ~ 1 的小數，越小透明度越高，預設 1 不透明"

#: settings.php:491
msgid "左侧栏"
msgstr "左側欄"

#: settings.php:493
msgid "左侧栏标题"
msgstr "左側欄標題"

#: settings.php:500
msgid "左侧栏子标题（格言）"
msgstr "左側欄子標題 (格言)"

#: settings.php:503 settings.php:524
msgid "留空则不显示"
msgstr "留空則不顯示"

#: settings.php:503
msgid "调用一言 API"
msgstr "使用一言 API"

#: settings.php:507
msgid "左侧栏作者名称"
msgstr "左側欄作者名稱"

#: settings.php:510
msgid "留空则显示博客名"
msgstr "留空則顯示博客名"

#: settings.php:514
msgid "左侧栏作者头像地址"
msgstr "左側欄作者大頭貼位址"

#: settings.php:517
msgid "需带上 http(s) 开头"
msgstr "需保留 http(s) 開頭"

#: settings.php:521
msgid "左侧栏作者简介"
msgstr "左側欄作者簡介"

#: settings.php:527
msgid "博客公告"
msgstr "博客公告"

#: settings.php:529
msgid "公告内容"
msgstr "公告內容"

#: settings.php:532
msgid "显示在左侧栏顶部，留空则不显示，支持 HTML 标签"
msgstr "顯示在左側欄頂部，留空則不顯示，支援 HTML 標籤"

#: settings.php:535
msgid "浮动操作按钮"
msgstr "懸浮操作按鈕"

#: settings.php:536
msgid "浮动操作按钮位于页面右下角（或左下角）"
msgstr "懸浮操作按鈕位於頁面右下角 (或左下角)"

#: settings.php:538
msgid "显示设置按钮"
msgstr "顯示設定按鈕"

#: settings.php:542 settings.php:554 settings.php:565 settings.php:625
#: settings.php:672 settings.php:752 settings.php:904 settings.php:965
#: settings.php:1462 settings.php:1574
msgid "显示"
msgstr "顯示"

#: settings.php:545
msgid ""
"是否在浮动操作按钮栏中显示设置按钮。点击设置按钮可以唤出设置菜单修改夜间模"
"式/字体/滤镜等外观选项。"
msgstr ""
"是否在懸浮操作按鈕欄中顯示設定按鈕。按下設定按鈕後可顯示設定選單，以修改夜間"
"模式/字型/濾鏡等外觀設定。"

#: settings.php:549
msgid "显示夜间模式切换按钮"
msgstr "顯示夜間模式切換按鈕"

#: settings.php:556
msgid ""
"如果开启了设置按钮显示，建议关闭此选项。（夜间模式选项在设置菜单中已经存在）"
msgstr ""
"如果開啟了顯示設定按鈕，建議關閉此選項。(夜間模式選項已存在於設定選單)"

#: settings.php:560
msgid "显示跳转到评论按钮"
msgstr "顯示評論按鈕捷徑"

#: settings.php:567
msgid "仅在允许评论的文章中显示"
msgstr "僅在允許評論的文章中顯示"

#: settings.php:572
msgid "网站描述 (Description Meta 标签)"
msgstr "網站描述 (Description Meta 標籤)"

#: settings.php:575
msgid "设置针对搜索引擎的 Description Meta 标签内容。"
msgstr "配置針對搜尋引擎的 Description Meta 標籤內容。"

#: settings.php:575
msgid ""
"在文章中，Argon 会自动根据文章内容生成描述。在其他页面中，Argon 将使用这里设"
"置的内容。如不填，Argon 将不会在其他页面输出 Description Meta 标签。"
msgstr ""
"在文章中，Argon 會自動依照文章內容產生描述。在其他頁面中，Argon 將使用此處設"
"定的內容。如不填，Argon 將不會在其他頁面輸出 Description Meta 標籤。"

#: settings.php:579
msgid "搜索引擎关键词（Keywords Meta 标签）"
msgstr "搜尋引擎關鍵詞 (Keywords Meta 標籤)"

#: settings.php:582
msgid ""
"设置针对搜索引擎使用的关键词（Keywords Meta 标签内容）。用英文逗号隔开。不设"
"置则不输出该 Meta 标签。"
msgstr ""
"配置針對搜尋引擎所使用的關鍵詞 (Keywords Meta 標籤內容)。以英文逗號分隔。留"
"空則不輸出 Meta 標籤。"

#: settings.php:585 sidebar.php:134
msgid "文章"
msgstr "文章"

#: settings.php:586
msgid "文章 Meta 信息"
msgstr "文章 Meta 資訊"

#: settings.php:588
msgid "第一行"
msgstr "第一行"

#: settings.php:623
msgid "拖动来自定义文章 Meta 信息的显示和顺序"
msgstr "拖移以自訂文章 Meta 資訊的顯示和排序"

#: settings.php:631 settings.php:791 settings.php:875
msgid "发布时间"
msgstr "發布時間"

#: settings.php:632 settings.php:792
msgid "修改时间"
msgstr "修改時間"

#: settings.php:633
msgid "浏览量"
msgstr "瀏覽量"

#: settings.php:634
msgid "评论数"
msgstr "評論數"

#: settings.php:635
msgid "所属分类"
msgstr "所屬分類"

#: settings.php:636 settings.php:796
msgid "作者"
msgstr "作者"

#: settings.php:666
msgid "第二行"
msgstr "第二行"

#: settings.php:678
msgid "每分钟阅读字数（中文）"
msgstr "每分鐘閱讀字數 (中文)"

#: settings.php:681
msgid "字/分钟"
msgstr "字/分鐘"

#: settings.php:686
msgid "每分钟阅读单词数（英文）"
msgstr "每分鐘閱讀單詞數 (英文)"

#: settings.php:689
msgid "单词/分钟"
msgstr "單詞/分鐘"

#: settings.php:693
msgid "每分钟阅读代码行数"
msgstr "每分鐘閱讀代碼行數"

#: settings.php:696
msgid "行/分钟"
msgstr "行/分鐘"

#: settings.php:697
msgid "预计阅读时间由每分钟阅读字数计算"
msgstr "預計閱讀時間由每分鐘閱讀字數計算"

#: settings.php:700
msgid "文章头图 (特色图片)"
msgstr "文章精選圖片"

#: settings.php:702
msgid "文章头图的位置"
msgstr "文章精選圖片的位置"

#: settings.php:706
msgid "文章卡片顶端"
msgstr "文章卡片頂端"

#: settings.php:707
msgid "Banner (顶部背景)"
msgstr "Banner (頂部背景)"

#: settings.php:709
msgid "阅读界面中文章头图的位置"
msgstr "閱讀界面中文章精選圖片的位置"

#: settings.php:713
msgid "默认使用文章中第一张图作为头图"
msgstr "預設使用文章中第一張圖作為頭圖"

#: settings.php:717 settings.php:1062 settings.php:1114 settings.php:1138
#: settings.php:1172 settings.php:1245 settings.php:1297 settings.php:1329
#: settings.php:1436 settings.php:1491 settings.php:1595 settings.php:1649
#: settings.php:1660
msgid "禁用"
msgstr "停用"

#: settings.php:720
msgid "也可以针对每篇文章单独设置"
msgstr "亦可針對每篇文章單獨配置"

#: settings.php:723
msgid "脚注(引用)"
msgstr "引用"

#: settings.php:725
msgid "脚注列表标题"
msgstr "引用列表標題"

#: settings.php:728
msgid ""
"脚注列表显示在文末，在文章中有脚注的时候会显示。</br>使用 <code>ref</code> "
"短代码可以在文中插入脚注。"
msgstr ""
"引用列表顯示在文末，在文章中有引用的時候會顯示。 </br>使用 <code>ref</code> "
"短代碼可以在文中插入引用。"

#: settings.php:731 template-parts/share.php:53
msgid "分享"
msgstr "分享"

#: settings.php:733
msgid "显示文章分享按钮"
msgstr "顯示文章分享按鈕"

#: settings.php:737
msgid "显示全部社交媒体"
msgstr "顯示全部社交媒體"

#: settings.php:738
msgid "显示国内社交媒体"
msgstr "顯示大陸社交媒體"

#: settings.php:739
msgid "显示国外社交媒体"
msgstr "顯示非大陸社交媒體"

#: settings.php:745
msgid "左侧栏文章目录"
msgstr "左側欄文章目錄"

#: settings.php:747
msgid "在目录中显示序号"
msgstr "在目錄中顯示序號"

#: settings.php:754
msgid "例：3.2.5"
msgstr "例：3.2.5"

#: settings.php:757 template-parts/content-single.php:100
msgid "赞赏"
msgstr "讚賞"

#: settings.php:759
msgid "赞赏二维码图片链接"
msgstr "讚賞 QR Code 圖片連結"

#: settings.php:762
msgid ""
"赞赏二维码图片链接，填写后会在文章最后显示赞赏按钮，留空则不显示赞赏按钮"
msgstr ""
"讚賞 QR Code 圖片連結，填寫後會在文末顯示讚賞按鈕，留空則不顯示讚賞按鈕"

#: settings.php:770
msgid "将会显示在每篇文章末尾，支持 HTML 标签，留空则不显示。"
msgstr "顯示於文末，支援 HTML，留空則不顯示。"

#: settings.php:770
msgid ""
"使用 <code>%url%</code> 来代替当前页面 URL，<code>%link%</code> 来代替当前页"
"面链接，<code>%title%</code> 来代替当前文章标题，<code>%author%</code> 来代"
"替当前文章作者。"
msgstr ""
"使用<code>%url%</code> 以代替目前頁面URL，<code>%link%</code> 來代替目前頁面"
"連結，<code>%title%</code> 來代替目前文章標題，<code>%author%</code> 來代替"
"目前文章作者。"

#: settings.php:773 settings.php:775
msgid "相似文章推荐"
msgstr "相似文章推薦"

#: settings.php:780
msgid "根据分类推荐"
msgstr "依照分類推薦"

#: settings.php:781
msgid "根据标签推荐"
msgstr "依照標籤推薦"

#: settings.php:782
msgid "根据分类和标签推荐"
msgstr "依照分類和標籤推薦"

#: settings.php:783
msgid "显示在文章卡片后"
msgstr "顯示在文章卡片後"

#: settings.php:787
msgid "排序依据"
msgstr "排序依據"

#: settings.php:793
msgid "阅读量"
msgstr "閱讀量"

#: settings.php:797
msgid "随机"
msgstr "隨機"

#: settings.php:802
msgid "顺序"
msgstr "順序"

#: settings.php:806
msgid "倒序"
msgstr "倒序"

#: settings.php:807
msgid "正序"
msgstr "正序"

#: settings.php:812
msgid "推荐文章数"
msgstr "推薦文章數"

#: settings.php:815
msgid "最多推荐多少篇文章"
msgstr "最多推薦多少篇文章"

#: settings.php:818 settings.php:820
msgid "文章内标题样式"
msgstr "文章內標題樣式"

#: settings.php:824 settings.php:829
msgid "默认样式"
msgstr "預設樣式"

#: settings.php:825 settings.php:830
msgid "样式 1"
msgstr "樣式 1"

#: settings.php:826 settings.php:831
msgid "样式 2"
msgstr "樣式 2"

#: settings.php:828
msgid "样式预览"
msgstr "樣式預覽"

#: settings.php:867
msgid "其他"
msgstr "其他"

#: settings.php:869
msgid "文章过时信息显示"
msgstr "文章過時資訊顯示"

#: settings.php:871
msgid "当一篇文章的"
msgstr "當一篇文章的"

#: settings.php:874
msgid "最后修改时间"
msgstr "最後修改時間"

#: settings.php:877
msgid "距离现在超过"
msgstr "距離現在超過"

#: settings.php:879
msgid "天时，用"
msgstr "天時，用"

#: settings.php:882
msgid "在文章顶部显示信息条"
msgstr "在文章頂部顯示資訊條"

#: settings.php:883
msgid "在页面右上角弹出提示条"
msgstr "在頁面右上角彈出提示條"

#: settings.php:885
msgid "的方式提示"
msgstr "的方式提示"

#: settings.php:887
msgid ""
"本文最后更新于 %date_delta% 天前，其中的信息可能已经有所发展或是发生改变。"
msgstr ""
"本文最後更新於 %date_delta% 天前，其中的資訊可能已經有所發展或是發生改變。"

#: settings.php:888
msgid "天数为 -1 表示永不提示。"
msgstr "天數為 -1 表示永不提示。"

#: settings.php:888
msgid "表示文章发布/修改时间与当前时间的差距，"
msgstr "表示文章發布/修改時間與目前時間的差距，"

#: settings.php:888
msgid "表示文章发布时间与当前时间的差距，"
msgstr "表示文章發布時間與目前時間的差距，"

#: settings.php:888
msgid "表示文章修改时间与当前时间的差距（单位: 天）。"
msgstr "表示文章修改時間與目前時間的差距(單位: 天)。"

#: settings.php:891
msgid "归档页面"
msgstr "歸檔頁面"

#: settings.php:893
msgid "介绍"
msgstr "介紹"

#: settings.php:895
msgid ""
"新建一个页面，并将其模板设为 \"归档时间轴\"，即可创建一个归档页面。归档页面"
"会按照时间顺序在时间轴上列出博客的所有文章。"
msgstr ""
"新建一個頁面，並將其模板設為 \"歸檔時間軸\"，即可創建一個歸檔頁面。歸檔頁面"
"會按照時間順序在時間軸上列出博客的所有文章。"

#: settings.php:900
msgid "在时间轴上显示月份"
msgstr "在時間軸上顯示月份"

#: settings.php:907
msgid "关闭后，时间轴只会按年份分节"
msgstr "關閉後，時間軸只會按年份分節"

#: settings.php:910
msgid "配置"
msgstr "配置"

#: settings.php:912
msgid "归档页面链接"
msgstr "歸檔頁面鏈接"

#: settings.php:915
msgid ""
"归档页面的 URL。点击左侧栏 \"博客概览\" 中的 \"博文总数\" 一栏时可跳转到该地"
"址。"
msgstr ""
"歸檔頁面的 URL。點擊左側欄 \"博客概覽\" 中的 \"博文總數\" 一欄時可跳轉到該地"
"址。"

#: settings.php:918
msgid "页脚"
msgstr "頁尾"

#: settings.php:920
msgid "页脚内容"
msgstr "頁尾內容"

#: settings.php:923 settings.php:1203 settings.php:1210
msgid "HTML , 支持 script 等标签"
msgstr "HTML，支援 script 等標籤"

#: settings.php:926
msgid "代码高亮"
msgstr "代碼標記"

#: settings.php:928
msgid "启用 Highlight.js 代码高亮"
msgstr "啟用 Highlight.js 代碼標記"

#: settings.php:935
msgid "所有 pre 下的 code 标签会被自动解析"
msgstr "所有 pre 下的 code 標籤將被自動解析"

#: settings.php:939
msgid "高亮配色方案（主题）"
msgstr "標記配色方案(主題)"

#: settings.php:957
msgid "查看所有主题预览"
msgstr "檢視所有主題預覽"

#: settings.php:961
msgid "默认显示行号"
msgstr "預設顯示行號"

#: settings.php:971
msgid "默认启用自动折行"
msgstr "預設啟用自動折行"

#: settings.php:981
msgid "行号背景透明"
msgstr "行號背景透明"

#: settings.php:985
msgid "不透明"
msgstr "不透明"

#: settings.php:986
msgid "透明"
msgstr "透明"

#: settings.php:988
msgid "适用于某些背景渐变的高亮主题"
msgstr "適用於某些背景漸變的高亮主題"

#: settings.php:992
msgid ""
"如果您想使用其他代码高亮插件，而非 Argon 自带高亮，请前往 \"杂项\" 打开 \"禁"
"用 Argon 代码块样式\" 来防止样式冲突"
msgstr ""
"如果您想使用其他代碼標記外掛，而非 Argon 自帶標記，請前往 [雜項] 開啟「停用 "
"Argon 代碼塊樣式」以防止樣式衝突"

#: settings.php:996
msgid "数学公式"
msgstr "數學公式"

#: settings.php:998
msgid "数学公式渲染方案"
msgstr "數學公式渲染方式"

#: settings.php:1017 settings.php:1030 settings.php:1043
msgid "地址"
msgstr "地址"

#: settings.php:1019 settings.php:1032
msgid "，默认为"
msgstr "，預設為"

#: settings.php:1045
msgid "Argon 会同时引用"
msgstr "Argon 會同時引用"

#: settings.php:1045
msgid "和"
msgstr "和"

#: settings.php:1045
msgid ""
"两个文件，所以在此填写的是上层的路径，而不是具体的文件。注意路径后要带一个斜"
"杠。"
msgstr ""
"兩個文件，所以在此填寫的是上層的路徑，而不是具體的文件。注意路徑後應要有一"
"個 \"/\"。"

#: settings.php:1045
msgid "默认为"
msgstr "預設為"

#: settings.php:1057
msgid "是否启用 Lazyload"
msgstr "是否啟用 Lazyload"

#: settings.php:1064
msgid "是否启用 Lazyload 加载文章内图片"
msgstr "是否啟用 Lazyload 載入文章內圖片"

#: settings.php:1068
msgid "提前加载阈值"
msgstr "提前載入閾值"

#: settings.php:1071
msgid "图片距离页面底部还有多少距离就开始提前加载"
msgstr "圖片距離頁面底部還有多少距離就開始提前載入"

#: settings.php:1075
msgid "LazyLoad 图片加载完成过渡"
msgstr "LazyLoad 圖片載入完成過渡"

#: settings.php:1081
msgid "不使用过渡"
msgstr "不使用過渡"

#: settings.php:1087
msgid "LazyLoad 图片加载动效"
msgstr "LazyLoad 圖片載入動效"

#: settings.php:1091 settings.php:1092 settings.php:1093 settings.php:1094
#: settings.php:1095 settings.php:1096 settings.php:1097 settings.php:1098
#: settings.php:1099 settings.php:1100 settings.php:1101
msgid "加载动画"
msgstr "載入動畫"

#: settings.php:1104
msgid "在图片被加载之前显示的加载效果"
msgstr "在圖片載入前所顯示的載入效果"

#: settings.php:1104
msgid "预览所有效果"
msgstr "預覽所有效果"

#: settings.php:1107
msgid "图片放大浏览"
msgstr "圖片放大瀏覽"

#: settings.php:1109
msgid "是否启用图片放大浏览 (Fancybox)"
msgstr "是否啟用圖片放大瀏覽 (Fancybox)"

#: settings.php:1116
msgid "开启后，文章中图片被单击时会放大预览"
msgstr "開啟後，文章中圖片被單擊時會放大預覽"

#: settings.php:1120
msgid "展开旧版图片放大浏览 (Zoomify) 设置 ▼"
msgstr "展開舊版圖片放大瀏覽 (Zoomify) 設定 ▼"

#: settings.php:1133
msgid "是否启用旧版图片放大浏览 (Zoomify)"
msgstr "是否啟用舊版圖片放大瀏覽 (Zoomify)"

#: settings.php:1140
msgid ""
"自 Argon 1.1.0 版本后，图片缩放预览库由 Zoomify 更换为 Fancybox，如果您还想"
"使用旧版图片预览，请开启此选项。注意: Zoomify 和 Fancybox 不能同时开启。"
msgstr ""
"自 Argon 1.1.0 版本後，圖片縮放預覽庫由 Zoomify 更換為 Fancybox，如果您愈使"
"用舊版圖片預覽，請開啟此選項。注意: Zoomify 和 Fancybox 不能同時開啟。"

#: settings.php:1144
msgid "缩放动画长度"
msgstr "縮放動畫長度"

#: settings.php:1147
msgid "图片被单击后缩放到全屏动画的时间长度"
msgstr "圖片被單次點選後，縮放到全螢幕動畫的時間長度"

#: settings.php:1151
msgid "缩放动画曲线"
msgstr "縮放動畫曲線"

#: settings.php:1155
msgid "例："
msgstr "例："

#: settings.php:1155
msgid "如果你不知道这是什么，参考"
msgstr "如果此為何物，請參考"

#: settings.php:1155
msgid "这里"
msgstr "這裡"

#: settings.php:1160
msgid "图片最大缩放比例"
msgstr "圖片最大縮放比例"

#: settings.php:1163
msgid "图片相对于页面的最大缩放比例 (0 ~ 1 的小数)"
msgstr "圖片相對於頁面的最大縮放比例 (0 ~ 1 的小數)"

#: settings.php:1168
msgid "启用 Pangu.js (自动在中英文之间添加空格)"
msgstr "啟用 Pangu.js (自動在中英文之間添加空格)"

#: settings.php:1173
msgid "格式化文章内容"
msgstr "格式化文章內容"

#: settings.php:1174
msgid "格式化说说"
msgstr "格式化說說"

#: settings.php:1175
msgid "格式化评论区"
msgstr "格式化評論區"

#: settings.php:1176
msgid "格式化文章内容和评论区"
msgstr "格式化文章內容和評論區"

#: settings.php:1177
msgid "格式化文章内容和说说"
msgstr "格式化文章內容和說說"

#: settings.php:1178
msgid "格式化说说和评论区"
msgstr "格式化說說和評論區"

#: settings.php:1179
msgid "格式化文章内容、说说和评论区"
msgstr "格式化文章內容、說說和評論區"

#: settings.php:1181
msgid "开启后，会自动在中文和英文之间添加空格"
msgstr "開啟後，會自動在中文和英文之間增加空格"

#: settings.php:1184
msgid "脚本"
msgstr "腳本"

#: settings.php:1186
msgid "注意"
msgstr "注意"

#: settings.php:1188
msgid ""
"Argon 使用 pjax 方式加载页面 (无刷新加载) , 所以除非页面手动刷新，否则您的脚"
"本只会被执行一次。"
msgstr ""
"Argon 使用 pjax 方式載入頁面 (無重新載入)，除因網頁手動重新載入，否則您的腳"
"本只會被執行一次。"

#: settings.php:1189
msgid "如果您想让每次页面跳转(加载新页面)时都执行脚本，请将脚本写入"
msgstr "如果您想讓每次頁面跳轉(載入新頁面)時都執行腳本，請將腳本寫入"

#: settings.php:1189
msgid "中"
msgstr "中"

#: settings.php:1189
msgid "示例写法"
msgstr "範例寫法"

#: settings.php:1192
msgid "页面每次跳转都会执行这里的代码"
msgstr "網頁每次導向都會執行這裡的代碼"

#: settings.php:1196
msgid "当页面第一次载入时，"
msgstr "當網頁第一次載入時，"

#: settings.php:1196
msgid "中的脚本不会执行，所以您可以手动执行"
msgstr "中的腳本不會執行，所以您可以手動執行"

#: settings.php:1196
msgid "来让页面初次加载时也执行脚本"
msgstr "來讓頁面初次載入時也執行腳本"

#: settings.php:1200
msgid "页头脚本"
msgstr "頁首腳本"

#: settings.php:1203
msgid "插入到 body 之前"
msgstr "插入到 body 之前"

#: settings.php:1207
msgid "页尾脚本"
msgstr "頁尾腳本"

#: settings.php:1210
msgid "插入到 body 之后"
msgstr "插入到 body 之後"

#: settings.php:1215
msgid "是否启用平滑滚动"
msgstr "是否啟用平滑滾動"

#: settings.php:1219
msgid "使用平滑滚动方案 1 (平滑) (推荐)"
msgstr "使用平滑滾動方案 1 (平滑) (推薦)"

#: settings.php:1220
msgid "使用平滑滚动方案 1 (脉冲式滚动) (仿 Edge) (推荐)"
msgstr "使用平滑滾動方案 1 (脈衝式滾動) (仿 Edge) (推薦)"

#: settings.php:1221
msgid "使用平滑滚动方案 2 (较稳)"
msgstr "使用平滑滾動方案 2 (較穩)"

#: settings.php:1222
msgid "使用平滑滚动方案 3"
msgstr "使用平滑滾動方案 3"

#: settings.php:1223
msgid "不使用平滑滚动"
msgstr "不使用平滑滾動"

#: settings.php:1225
msgid ""
"能增强浏览体验，但可能出现一些小问题，如果有问题请切换方案或关闭平滑滚动"
msgstr ""
"可增強瀏覽體驗，但可能出現一些小問題，如果有問題請切換方案或關閉平滑滾動"

#: settings.php:1229
msgid "是否启用进入文章动画"
msgstr "是否啟用進入文章動畫"

#: settings.php:1236
msgid "从首页或分类目录进入文章时，使用平滑过渡（可能影响加载文章时的性能）"
msgstr "從首頁或分類目錄進入文章時，使用平滑過渡(可能影響載入文章時的性能)"

#: settings.php:1240
msgid "禁用 Pjax 加载后的页面滚动动画"
msgstr "停用 Pjax 載入後的網頁滾動動畫"

#: settings.php:1244 settings.php:1648 settings.php:1659
msgid "不禁用"
msgstr "不停用"

#: settings.php:1247
msgid ""
"Pjax 替换页面内容后会平滑滚动到页面顶部，如果你不喜欢，可以禁用这个选项"
msgstr ""
"Pjax 替換頁面內容後會平滑滾動到頁面頂部，如果你不喜歡，可以停用這個選項"

#: settings.php:1251
msgid "评论分页"
msgstr "評論分頁"

#: settings.php:1253
msgid "评论分页方式"
msgstr "評論分頁方式"

#: settings.php:1257
msgid "无限加载"
msgstr "無限載入"

#: settings.php:1258
msgid "页码"
msgstr "頁碼"

#: settings.php:1261
msgid "无限加载：点击 \"加载更多\" 按钮来加载更多评论。"
msgstr "無限載入: 點擊 [載入更多] 以載入更多評論。"

#: settings.php:1262
msgid "页码：显示页码来分页。"
msgstr "頁碼: 顯示頁碼來分頁。"

#: settings.php:1263
msgid ""
"选择\"无限加载\"时，如果开启了评论分页，请将 Wordpress 的讨论设置设为 \"默认"
"显示<b>最后</b>一页，在每个页面顶部显示<b>新的</b>评论\"。"
msgstr ""
"選擇\"無限加載\"時，如果開啟了評論分頁，請將 Wordpress 的討論設置設為 \"默認"
"顯示<b>最後</b>一頁，在每個頁面頂部顯示<b>新的</b>評論\"。"

#: settings.php:1263
msgid "去设置"
msgstr "設定"

#: settings.php:1292
msgid "评论表情面板"
msgstr "評論表情面板"

#: settings.php:1299
msgid "开启后评论支持插入表情，会在评论输入框下显示表情键盘按钮。"
msgstr "開啟後評論支援插入表情，會在評論輸入框下顯示表情鍵盤按鈕。"

#: settings.php:1299
msgid "如何添加新的表情或修改已有表情列表？"
msgstr "如何新增新的表情或修改已有表情列表？"

#: settings.php:1303
msgid "是否隐藏 \"昵称\"、\"邮箱\"、\"网站\" 输入框"
msgstr "是否隱藏「暱稱」、「電子信箱」、「網站」輸入欄"

#: settings.php:1310
msgid ""
"选项仅在 \"设置-评论-评论作者必须填入姓名和电子邮件地址\" 选项未勾选的前提下"
"生效。如勾选了 \"评论作者必须填入姓名和电子邮件地址\"，则只有 \"网站\" 输入"
"框会被隐藏。"
msgstr ""
"選項僅在 [設定] > [討論] > [留言者必須填寫 [顯示名稱] 及 [電子郵件地址] 欄"
"位] 選項未勾選的前提下生效。如勾選了 [留言者必須填寫 [顯示名稱] 及 [電子郵件"
"地址] 欄位]，則只有 [網站] 輸入欄隱藏。"

#: settings.php:1314
msgid "评论是否需要验证码"
msgstr "評論是否需要驗證碼"

#: settings.php:1318
msgid "需要"
msgstr "需要"

#: settings.php:1319
msgid "不需要"
msgstr "不需要"

#: settings.php:1325
msgid "使用 Ajax 获取评论验证码"
msgstr "使用 Ajax 獲取評論驗證碼"

#: settings.php:1332
msgid "如果使用了 CDN 缓存，验证码不会刷新，请开启此选项，否则请不要开启。"
msgstr "如果使用了 CDN 緩存，驗證碼不會刷新，請開啟此選項，否則請不要開啟。"

#: settings.php:1336
msgid "是否允许在评论中使用 Markdown 语法"
msgstr "是否允許在評論中使用 Markdown 語法"

#: settings.php:1340 settings.php:1351 settings.php:1363 settings.php:1374
#: settings.php:1391
msgid "允许"
msgstr "允許"

#: settings.php:1341 settings.php:1352 settings.php:1362 settings.php:1373
#: settings.php:1390
msgid "不允许"
msgstr "不允許"

#: settings.php:1347
msgid "是否允许评论者再次编辑评论"
msgstr "是否允許評論者再次編輯評論"

#: settings.php:1354
msgid "同一个评论者可以再次编辑评论。"
msgstr "同一個評論者可以再次編輯評論。"

#: settings.php:1358
msgid "是否允许评论者使用悄悄话模式"
msgstr "是否允許評論者使用悄悄話模式"

#: settings.php:1365
msgid "评论者使用悄悄话模式发送的评论和其下的所有回复只有发送者和博主能看到。"
msgstr ""
"評論者使用悄悄話模式發送的評論和其下的所有回覆只有評論者和博主能看到。"

#: settings.php:1369
msgid "是否允许评论者接收评论回复邮件提醒"
msgstr "是否允許評論者接收評論回覆郵件提醒"

#: settings.php:1379
msgid "评论时默认勾选 \"启用邮件通知\" 复选框"
msgstr "評論時預設勾選 [啟用郵件通知] 複選框"

#: settings.php:1382
msgid "评论者开启邮件提醒后，其评论有回复时会有邮件通知。"
msgstr "評論者開啟郵件提醒後，其評論有回覆時會有郵件通知。"

#: settings.php:1386
msgid "允许评论者使用 QQ 头像"
msgstr "允許評論者使用 QQ 大頭貼"

#: settings.php:1393
msgid ""
"开启后，评论者可以使用 QQ 号代替邮箱输入，头像会根据评论者的 QQ 号获取。"
msgstr ""
"開啟後，評論者可以使用 QQ 號取代郵箱輸入，大頭貼會依照評論者的 QQ 號取得。"

#: settings.php:1396
msgid "评论区"
msgstr "評論區"

#: settings.php:1398
msgid "评论头像垂直位置"
msgstr "評論大頭貼垂直位置"

#: settings.php:1402
msgid "居上"
msgstr "靠上"

#: settings.php:1403
msgid "居中"
msgstr "置中"

#: settings.php:1409
msgid "谁可以查看评论编辑记录"
msgstr "誰可以檢視評論編輯記錄"

#: settings.php:1413
msgid "只有博主"
msgstr "只有博主"

#: settings.php:1414
msgid "评论发送者和博主"
msgstr "評論發送者和博主"

#: settings.php:1415
msgid "任何人"
msgstr "任何人"

#: settings.php:1417
msgid "点击评论右侧的 \"已编辑\" 标记来查看编辑记录"
msgstr "點選評論右側的 [已編輯] 標記來檢視編輯記錄"

#: settings.php:1421
msgid "开启评论置顶功能"
msgstr "開啟評論置頂功能"

#: settings.php:1428
msgid ""
"开启后，博主将可以置顶评论。已置顶的评论将会在评论区顶部显示。如果关闭，评论"
"将以正常顺序显示。"
msgstr ""
"開啟後，博主將可以置頂評論。已置頂的評論將會在評論區頂部顯示。如果關閉，評論"
"將以正常順序顯示。"

#: settings.php:1432
msgid "评论点赞"
msgstr "評論點讚"

#: settings.php:1439
msgid "开启后，每一条评论的头像下方会出现点赞按钮"
msgstr "開啟後，每一條評論的大頭貼下方將顯示按讚鈕"

#: settings.php:1443
msgid "评论者 UA 显示"
msgstr "評論者 UA 顯示"

#: settings.php:1448
msgid "浏览器"
msgstr "瀏覽器"

#: settings.php:1449
msgid "浏览器+版本号"
msgstr "瀏覽器+版本號"

#: settings.php:1450
msgid "平台+浏览器+版本号"
msgstr "平台+瀏覽器+版本號"

#: settings.php:1451
msgid "平台+浏览器"
msgstr "平台+瀏覽器"

#: settings.php:1452
msgid "平台"
msgstr "平台"

#: settings.php:1454
msgid "设置是否在评论区显示评论者 UA 及显示哪些部分"
msgstr "配置是否在評論區顯示評論者 UA 及顯示哪些部分"

#: settings.php:1458
msgid "在子评论中显示被回复者用户名"
msgstr "在子評論中顯示被回复者用戶名"

#: settings.php:1465
msgid ""
"开启后，被回复的评论者昵称会显示在子评论中，鼠标移上后会高亮被回复的评论"
msgstr ""
"開啟後，被回复的評論者暱稱會顯示在子評論中，鼠標移上後會高亮被回复的評論"

#: settings.php:1469
msgid "折叠过长评论"
msgstr "摺疊過長評論"

#: settings.php:1473 settings.php:1584
msgid "不折叠"
msgstr "不摺疊"

#: settings.php:1474 settings.php:1585
msgid "折叠"
msgstr "摺疊"

#: settings.php:1476
msgid "开启后，过长的评论会被折叠，需要手动展开"
msgstr "開啟後，過長的評論會被摺疊，需要手動展開"

#: settings.php:1483
msgid ""
"使用 CDN 来加速 Gravatar 在某些地区的访问，填写 CDN 地址，留空则不使用。"
msgstr ""
"使用 CDN 來加速 Gravatar 在某些地區的訪問，填寫 CDN 地址，留空則不使用。"

#: settings.php:1483
msgid "在中国速度较快的一些 CDN :"
msgstr "在中國速度較快的一些 CDN :"

#: settings.php:1487
msgid "评论文字头像"
msgstr "評論文字大頭貼"

#: settings.php:1494
msgid ""
"在评论者没有设置 Gravatar 时自动生成文字头像，头像颜色由邮箱哈希计算。生成时"
"会在 Console 中抛出 404 错误，但没有影响。"
msgstr ""
"在評論者沒有配置 Gravatar 時自動生成文字頭像，大頭貼色彩由郵箱哈希計算。生成"
"時會在 Console 中拋出 404 錯誤，但沒有影響。"

#: settings.php:1497
msgid "杂项"
msgstr "雜項"

#: settings.php:1499
msgid "是否启用 Pjax"
msgstr "是否啟用 Pjax"

#: settings.php:1506
msgid "Pjax 可以增强页面的跳转体验"
msgstr "Pjax 可以增強頁面的跳轉體驗"

#: settings.php:1510
msgid "首页隐藏特定 分类/Tag 下的文章"
msgstr "首頁隱藏特定 分類/標籤 下的文章"

#: settings.php:1513
msgid "输入要隐藏的 分类/Tag 的 ID，用英文逗号分隔，留空则不隐藏"
msgstr "輸入要隱藏的 分類/標籤 的 ID，用英文逗號分隔，留空則不隱藏"

#: settings.php:1513
msgid "点此查看"
msgstr "點此檢視"

#: settings.php:1513
msgid "所有分类和 Tag 的 ID"
msgstr "所有分類和標籤的識別碼"

#: settings.php:1549
msgid "美化登录界面"
msgstr "美化登入介面"

#: settings.php:1556
msgid "使用 Argon Design 风格的登录界面"
msgstr "使用 Argon Design 風格的登入介面"

#: settings.php:1560
msgid "美化后台界面"
msgstr "美化後台界面"

#: settings.php:1563
msgid "使用 Argon Design 风格的后台界面"
msgstr "使用 Argon Design 風格的後台界面"

#: settings.php:1564
#, php-format
msgid ""
"前往<a href=\"%s\" target=\"_blank\">个人资料</a>页面将 \"管理界面配色方案"
"\" 设为 \"Argon\" 即可开启。"
msgstr ""
"前往<a href=\"%s\" target=\"_blank\">個人資料</a>頁面將 \"管理界面配色方案"
"\" 設為 \"Argon\" 即可開啟。"

#: settings.php:1569
msgid "博客首页是否显示说说"
msgstr "博客首頁是否顯示說說"

#: settings.php:1576
msgid "开启后，博客首页文章和说说穿插显示"
msgstr "開啟後，博客首頁文章和說說穿插顯示"

#: settings.php:1580
msgid "折叠长说说"
msgstr "折疊長說說"

#: settings.php:1587
msgid "开启后，长说说在预览状态下会被折叠，需要手动展开"
msgstr "開啟後，長說說在預覽狀態下會被折疊，需要手動展開"

#: settings.php:1591
msgid "搜索结果类型过滤器"
msgstr "搜索結果類型過濾器"

#: settings.php:1596
msgid "启用，默认不包括说说"
msgstr "啟用，默認不包括說說"

#: settings.php:1597
msgid "启用，默认包括说说"
msgstr "啟用，默認包括說說"

#: settings.php:1598
msgid "启用，隐藏说说分类"
msgstr "啟用，隱藏說說分類"

#: settings.php:1600
msgid "开启后，将会在搜索结果界面显示一个过滤器，支持搜索说说"
msgstr "開啟後，將會在搜索結果界面顯示一個過濾器，支持搜索說說"

#: settings.php:1604
msgid "是否修正时区错误"
msgstr "是否修正時區錯誤"

#: settings.php:1611
msgid "如遇到时区错误（例如一条刚发的评论显示 8 小时前），这个选项"
msgstr "如遇到時區錯誤 (例如一條剛發的評論顯示 8 小時前)，這個選項"

#: settings.php:1611
msgid "可能"
msgstr "可能"

#: settings.php:1611
msgid "可以修复这个问题"
msgstr "可以修復這個問題"

#: settings.php:1615
msgid "是否在文章列表内容预览中隐藏短代码"
msgstr "是否在文章列表內容預覽中隱藏短代碼"

#: settings.php:1619 settings.php:1637
msgid "否"
msgstr "否"

#: settings.php:1620 settings.php:1638
msgid "是"
msgstr "是"

#: settings.php:1626
msgid "文章内容预览截取字数"
msgstr "文章內容預覽擷取字數"

#: settings.php:1629
msgid "设为 0 来隐藏文章内容预览"
msgstr "設為 0 以隱藏文章內容預覽"

#: settings.php:1633
msgid "是否允许移动端缩放页面"
msgstr "是否允許行動裝置縮放頁面"

#: settings.php:1644
msgid "禁用 Google 字体"
msgstr "停用 Google 字型"

#: settings.php:1651
msgid ""
"Google 字体在中国大陆访问可能会阻塞，禁用可以解决页面加载被阻塞的问题。禁用"
"后，Serif 字体将失效。"
msgstr ""
"Google 字型在中國大陸訪問可能會阻塞，停用即可解決頁面載入阻塞的問題。停用"
"後，Serif 字型將失效。"

#: settings.php:1655
msgid "禁用 Argon 代码块样式"
msgstr "停用 Argon 代碼塊樣式"

#: settings.php:1662
msgid ""
"如果您启用了其他代码高亮插件，发现代码块样式被 Argon 覆盖，出现了显示错误，"
"请将此选项设为禁用"
msgstr ""
"如果您啟用了其他代碼標記插件，發現代碼塊樣式被 Argon 覆蓋，出現了顯示錯誤，"
"請將此選項設為停用"

#: settings.php:1666
msgid "检测更新源"
msgstr "檢測更新來源"

#: settings.php:1674
msgid "暂停更新 (不推荐)"
msgstr "暫停更新 (不推薦)"

#: settings.php:1676
msgid "如更新主题速度较慢，可考虑更换更新源。"
msgstr "如更新主題速度較慢，可考慮更換更新源。"

#: settings.php:1680
msgid "页脚附加内容"
msgstr "頁尾附加內容"

#: settings.php:1693
msgid "保存更改"
msgstr "儲存變更"

#: settings.php:1694
msgid "导入设置"
msgstr "匯入配置"

#: settings.php:1695
msgid "导出设置"
msgstr "匯出配置"

#: settings.php:1700
msgid "收起"
msgstr "收起"

#: settings.php:1703
msgid "到顶部"
msgstr "最頂層"

#: settings.php:1703
msgid "到底部"
msgstr "最底層"

#: settings.php:1704
msgid "请复制并保存导出后的 JSON"
msgstr "請複製並儲存匯出後的 JSON"

#: settings.php:1704
msgid "确定"
msgstr "確定"

#: settings.php:1781
msgid "展开"
msgstr "展開"

#: settings.php:1947
msgid "字段导入失败"
msgstr "欄位匯入失敗"

#: settings.php:1958
msgid "请输入要导入的备份 JSON"
msgstr "請輸入要匯入的備份 JSON"

#: settings.php:1961
msgid "已导入，请保存更改"
msgstr "已匯入，請儲存變更"

#: shuoshuo.php:21
msgid "条说说"
msgstr "條說說"

#: sidebar.php:9
msgid "公告"
msgstr "公告"

#: sidebar.php:83
msgid "文章目录"
msgstr "文章目錄"

#: sidebar.php:87
msgid "站点概览"
msgstr "站點概覽"

#: sidebar.php:91
msgid "功能"
msgstr "功能"

#: sidebar.php:140 sidebar.php:232
msgid "分类"
msgstr "分類"

#: sidebar.php:146 sidebar.php:260
msgid "标签"
msgstr "標籤"

#: single.php:27
msgid "上一篇"
msgstr "上一篇"

#: single.php:32
msgid "下一篇"
msgstr "下一篇"

#: single.php:86
msgid "推荐文章"
msgstr "推薦文章"

#: template-parts/content-none-search.php:4
msgid "没有搜索结果"
msgstr "沒有搜尋結果"

#: template-parts/content-none-search.php:6
msgid "似乎没有勾选任何分类"
msgstr "似乎沒有勾選任何分類"

#: template-parts/content-none-search.php:8
msgid "换个关键词试试 ?"
msgstr "換個關鍵詞試試 ?"

#: template-parts/content-none-tag.php:4
msgid "此分类没有文章"
msgstr "此分類沒有文章"

#: template-parts/content-none-tag.php:5
msgid "这里什么都没有"
msgstr "這裡什麼都沒有"

#: template-parts/content-page.php:60 template-parts/content-single.php:57
#: template-parts/content-timeline.php:57
msgid "这是一篇受密码保护的文章，您需要提供访问密码"
msgstr "這篇文章受到密碼保護，請輸入密碼以查看"

#: template-parts/content-page.php:67 template-parts/content-single.php:64
msgid "密码"
msgstr "密碼"

#: template-parts/content-page.php:77 template-parts/content-single.php:74
msgid "确认"
msgstr "確認"

#: template-parts/content-preview-1.php:59
#: template-parts/content-preview-2.php:29
#: template-parts/content-preview-3.php:55
msgid "这篇文章受密码保护，输入密码才能阅读"
msgstr "這篇文章受到密碼保護，請輸入密碼以查看"

#: template-parts/content-preview-1.php:62
#: template-parts/content-preview-2.php:32
#: template-parts/content-preview-3.php:58
msgid "这篇文章没有摘要"
msgstr "這篇文章沒有摘要"

#: template-parts/content-shuoshuo-details.php:5
#: template-parts/content-shuoshuo-preview.php:13
#: template-parts/content-shuoshuo.php:5
msgid "月"
msgstr "月"

#: template-parts/content-shuoshuo-details.php:6
#: template-parts/content-shuoshuo-preview.php:14
#: template-parts/content-shuoshuo.php:6
msgid "日"
msgstr "日"

#: template-parts/share.php:4 template-parts/share.php:61
msgid "分享到微信"
msgstr "分享到微信"

#: template-parts/share.php:9
msgid "分享到豆瓣"
msgstr "分享到豆瓣"

#: template-parts/share.php:14
msgid "分享到 QQ"
msgstr "分享到 QQ"

#: template-parts/share.php:19
msgid "分享到 QQ 空间"
msgstr "分享到 QQ 空間"

#: template-parts/share.php:24
msgid "分享到微博"
msgstr "分享到微博"

#: template-parts/share.php:30
msgid "分享到 Facebook"
msgstr "分享到 Facebook"

#: template-parts/share.php:35
msgid "分享到 Twitter"
msgstr "分享到 Twitter"

#: template-parts/share.php:41
msgid "分享到 Telegram"
msgstr "分享到 Telegram"

#: template-parts/share.php:47
msgid "复制链接"
msgstr "複製連結"

#: template-parts/share.php:62
msgid "微信扫描二维码"
msgstr "微信掃描二維碼"

#: template-parts/share.php:77
msgid "链接已复制"
msgstr "連結已複製"

#: template-parts/share.php:78
msgid "链接已复制到剪贴板"
msgstr "連結已複製到剪貼簿"

#: template-parts/share.php:91
msgid "复制失败"
msgstr "複製失敗"

#: template-parts/share.php:92
msgid "请手动复制链接"
msgstr "請手動複製連結"

#: template-parts/shuoshuo-operations.php:10
msgid "已关闭"
msgstr "已關閉"

#: unsubscribe-comment-mailnotice.php:7
msgid "参数错误"
msgstr "參數錯誤"

#: unsubscribe-comment-mailnotice.php:8 unsubscribe-comment-mailnotice.php:13
msgid "错误"
msgstr "錯誤"

#: unsubscribe-comment-mailnotice.php:9
msgid "提供的参数错误"
msgstr "提供的參數錯誤"

#: unsubscribe-comment-mailnotice.php:14 unsubscribe-comment-mailnotice.php:19
msgid "评论 #"
msgstr "評論 #"

#: unsubscribe-comment-mailnotice.php:14
msgid " 不存在"
msgstr " 不存在"

#: unsubscribe-comment-mailnotice.php:17 unsubscribe-comment-mailnotice.php:18
msgid "无需退订"
msgstr "無需取消訂閱"

#: unsubscribe-comment-mailnotice.php:19
msgid " 的邮件通知已被退订或没有开启邮件通知"
msgstr " 的郵件通知已被取消訂閱或沒有開啟郵件通知"

#: unsubscribe-comment-mailnotice.php:22 unsubscribe-comment-mailnotice.php:23
msgid "退订失败"
msgstr "取消訂閱失敗"

#: unsubscribe-comment-mailnotice.php:24
msgid "Token 不正确"
msgstr "Token 不正確"

#: unsubscribe-comment-mailnotice.php:28 unsubscribe-comment-mailnotice.php:29
msgid "退订成功"
msgstr "取消訂閱成功"

#: unsubscribe-comment-mailnotice.php:30
msgid "您已成功退订评论 #"
msgstr "您已成功取消訂閱評論 #"

#: unsubscribe-comment-mailnotice.php:30
msgid " 的邮件通知<br>该评论下有新回复时您将不会再收到通知"
msgstr " 的郵件通知<br>該評論下有新回覆時您將不會再收到通知"

#, fuzzy
#~| msgid "显示"
#~ msgid "提示"
#~ msgstr "顯示"

#, fuzzy
#~| msgid "主题色"
#~ msgid "颜色"
#~ msgstr "主題色"

#, fuzzy
#~| msgid "发布时间"
#~ msgid "时间"
#~ msgstr "發布時間"

#~ msgid "自动折叠顶栏"
#~ msgstr "自動折疊頂欄"

#~ msgid "菜单"
#~ msgstr "菜單"

#~ msgid "是否使用 v2ex CDN 代理的 gravatar"
#~ msgstr "是否使用 v2ex CDN 代理的 gravatar"

#~ msgid "可以大幅增加国内 gravatar 头像加载的速度"
#~ msgstr "可以大幅增加國內 gravatar 頭像加載的速度"

#, fuzzy
#~| msgid "评论内容"
#~ msgid "评论内容不能为空"
#~ msgstr "Your comment..."

#, fuzzy
#~| msgid "发送"
#~ msgid "发送中"
#~ msgstr "Send"

#, fuzzy
#~| msgid "编辑"
#~ msgid "编辑中"
#~ msgstr "Edit"

#, fuzzy
#~| msgid "链接已复制到剪贴板"
#~ msgid "代码已复制到剪贴板"
#~ msgstr "The link has been copied to the clipboard."

#, fuzzy
#~| msgid "请手动复制链接"
#~ msgid "请手动复制代码"
#~ msgstr "Please copy the link manually."

#, fuzzy
#~| msgid "分钟"
#~ msgid "分钟前"
#~ msgstr "minutes"

#, fuzzy
#~| msgid "小时"
#~ msgid "小时前"
#~ msgstr "hour"

#, fuzzy
#~| msgid "前"
#~ msgid "前天"
#~ msgstr " ago"

#, fuzzy
#~| msgid "前"
#~ msgid "天前"
#~ msgstr " ago"

#~ msgid "的评论"
#~ msgstr "'s comment"

#~ msgid "您在xxx[的评论有了新的回复]"
#~ msgstr "Has a New Reply"
