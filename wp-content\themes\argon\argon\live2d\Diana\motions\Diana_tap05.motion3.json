{"Version": 3, "Meta": {"Duration": 6, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 64, "TotalSegmentCount": 699, "TotalPointCount": 2077, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param2", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, -0.031, 1, 0.044, -0.031, 0.089, -0.325, 0.133, -0.325, 1, 0.233, -0.325, 0.333, 0.321, 0.433, 0.321, 1, 0.533, 0.321, 0.633, -0.317, 0.733, -0.317, 1, 0.744, -0.317, 0.756, -0.271, 0.767, -0.271, 1, 0.822, -0.271, 0.878, -1.118, 0.933, -1.118, 1, 1.011, -1.118, 1.089, 3.58, 1.167, 3.58, 1, 1.256, 3.58, 1.344, -0.802, 1.433, -0.802, 1, 1.511, -0.802, 1.589, 6.193, 1.667, 6.193, 1, 1.756, 6.193, 1.844, -17.785, 1.933, -17.785, 1, 2.056, -17.785, 2.178, 18.069, 2.3, 18.069, 1, 2.433, 18.069, 2.567, -16.881, 2.7, -16.881, 1, 2.822, -16.881, 2.944, 10.541, 3.067, 10.541, 1, 3.178, 10.541, 3.289, -4.762, 3.4, -4.762, 1, 3.522, -4.762, 3.644, 1.284, 3.767, 1.284, 1, 3.889, 1.284, 4.011, -0.072, 4.133, -0.072, 1, 4.222, -0.072, 4.311, 0.221, 4.4, 0.221, 1, 4.533, 0.221, 4.667, -0.305, 4.8, -0.305, 1, 4.833, -0.305, 4.867, -0.276, 4.9, -0.276, 1, 4.922, -0.276, 4.944, -0.312, 4.967, -0.312, 1, 4.989, -0.312, 5.011, -0.289, 5.033, -0.289, 1, 5.056, -0.289, 5.078, -0.385, 5.1, -0.385, 1, 5.233, -0.385, 5.367, 0.129, 5.5, 0.129, 1, 5.622, 0.129, 5.744, -0.049, 5.867, -0.049, 1, 5.911, -0.049, 5.956, -0.04, 6, -0.029]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -29, 1, 0.211, -29, 0.422, -30, 0.633, -30, 1, 0.711, -30, 0.789, 30, 0.867, 30, 1, 1.911, 30, 2.956, 30, 4, 30, 1, 4.089, 30, 4.178, -30, 4.267, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.856, 0, 0.978, -4, 1.1, -4, 1, 1.222, -4, 1.344, 0, 1.467, 0, 1, 1.589, 0, 1.711, -4, 1.833, -4, 1, 1.967, -4, 2.1, 6, 2.233, 6, 1, 2.378, 6, 2.522, -6, 2.667, -6, 1, 2.811, -6, 2.956, 5, 3.1, 5, 1, 3.244, 5, 3.389, 0, 3.533, 0, 1, 3.833, 0, 4.133, 0, 4.433, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 1.478, 0, 2.956, 0, 4.433, 0, 1, 4.744, 0, 5.056, -2, 5.367, -2, 0, 6, -2]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 4.433, 0, 1, 4.744, 0, 5.056, 2, 5.367, 2, 0, 6, 2]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 0.533, 0, 1, 0.622, 0, 0.711, 0, 0.8, 0, 1, 0.9, 0, 1, 0, 1.1, 0, 1, 1.222, 0, 1.344, -14, 1.467, -14, 1, 1.589, -14, 1.711, 27, 1.833, 27, 1, 1.978, 27, 2.122, -25, 2.267, -25, 1, 2.4, -25, 2.533, 20, 2.667, 20, 1, 2.811, 20, 2.956, -9, 3.1, -9, 1, 3.256, -9, 3.411, 1, 3.567, 1, 1, 3.822, 1, 4.078, -1, 4.333, -1, 0, 6, -1]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -5, 0, 0.533, -5, 1, 0.622, -5, 0.711, -12, 0.8, -12, 1, 0.9, -12, 1, -12, 1.1, -12, 1, 1.222, -12, 1.344, -4.128, 1.467, -2, 1, 1.589, 0.128, 1.711, 0, 1.833, 0, 1, 1.978, 0, 2.122, -0.369, 2.267, -2, 1, 2.4, -3.506, 2.533, -5, 2.667, -5, 1, 2.811, -5, 2.956, 5, 3.1, 5, 1, 3.256, 5, 3.411, 1.074, 3.567, -1, 1, 3.822, -4.407, 4.078, -5, 4.333, -5, 0, 6, -5]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 4.433, 0, 1, 4.744, 0, 5.056, 3, 5.367, 3, 0, 6, 3]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, -0.001, 1, 0.011, -0.001, 0.022, -0.001, 0.033, -0.001, 1, 0.111, -0.001, 0.189, 0, 0.267, 0, 1, 0.356, 0, 0.444, 0, 0.533, 0, 1, 0.6, 0, 0.667, 30, 0.733, 30, 1, 0.767, 30, 0.8, 30, 0.833, 30, 1, 0.856, 30, 0.878, -30, 0.9, -30, 1, 0.944, -30, 0.989, -30, 1.033, -30, 1, 1.089, -30, 1.144, 25.736, 1.2, 25.736, 1, 1.278, 25.736, 1.356, -10.044, 1.433, -10.044, 1, 1.522, -10.044, 1.611, 4.194, 1.7, 4.194, 1, 1.778, 4.194, 1.856, -1.731, 1.933, -1.731, 1, 2.022, -1.731, 2.111, 0.728, 2.2, 0.728, 1, 2.278, 0.728, 2.356, -0.302, 2.433, -0.302, 1, 2.522, -0.302, 2.611, 0.127, 2.7, 0.127, 1, 2.778, 0.127, 2.856, -0.053, 2.933, -0.053, 1, 3.022, -0.053, 3.111, 0.022, 3.2, 0.022, 1, 3.278, 0.022, 3.356, -0.009, 3.433, -0.009, 1, 3.511, -0.009, 3.589, 30, 3.667, 30, 1, 3.689, 30, 3.711, 30, 3.733, 30, 1, 3.778, 30, 3.822, -30, 3.867, -30, 1, 3.878, -30, 3.889, -30, 3.9, -30, 1, 3.978, -30, 4.056, 12.766, 4.133, 12.766, 1, 4.222, 12.766, 4.311, -5.214, 4.4, -5.214, 1, 4.478, -5.214, 4.556, 2.185, 4.633, 2.185, 1, 4.722, 2.185, 4.811, -0.903, 4.9, -0.903, 1, 4.978, -0.903, 5.056, 0.38, 5.133, 0.38, 1, 5.222, 0.38, 5.311, -0.157, 5.4, -0.157, 1, 5.478, -0.157, 5.556, 0.066, 5.633, 0.066, 1, 5.722, 0.066, 5.811, -0.027, 5.9, -0.027, 1, 5.933, -0.027, 5.967, -0.02, 6, -0.012]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, -0.001, 1, 0.011, -0.001, 0.022, -0.001, 0.033, -0.001, 1, 0.111, -0.001, 0.189, 0, 0.267, 0, 1, 0.356, 0, 0.444, 0, 0.533, 0, 1, 0.6, 0, 0.667, 30, 0.733, 30, 1, 0.767, 30, 0.8, 30, 0.833, 30, 1, 0.856, 30, 0.878, -30, 0.9, -30, 1, 0.944, -30, 0.989, -30, 1.033, -30, 1, 1.089, -30, 1.144, 25.736, 1.2, 25.736, 1, 1.278, 25.736, 1.356, -10.044, 1.433, -10.044, 1, 1.522, -10.044, 1.611, 4.194, 1.7, 4.194, 1, 1.778, 4.194, 1.856, -1.731, 1.933, -1.731, 1, 2.022, -1.731, 2.111, 0.728, 2.2, 0.728, 1, 2.278, 0.728, 2.356, -0.302, 2.433, -0.302, 1, 2.522, -0.302, 2.611, 0.127, 2.7, 0.127, 1, 2.778, 0.127, 2.856, -0.053, 2.933, -0.053, 1, 3.022, -0.053, 3.111, 0.022, 3.2, 0.022, 1, 3.278, 0.022, 3.356, -0.009, 3.433, -0.009, 1, 3.511, -0.009, 3.589, 30, 3.667, 30, 1, 3.689, 30, 3.711, 30, 3.733, 30, 1, 3.778, 30, 3.822, -30, 3.867, -30, 1, 3.878, -30, 3.889, -30, 3.9, -30, 1, 3.978, -30, 4.056, 12.766, 4.133, 12.766, 1, 4.222, 12.766, 4.311, -5.214, 4.4, -5.214, 1, 4.478, -5.214, 4.556, 2.185, 4.633, 2.185, 1, 4.722, 2.185, 4.811, -0.903, 4.9, -0.903, 1, 4.978, -0.903, 5.056, 0.38, 5.133, 0.38, 1, 5.222, 0.38, 5.311, -0.157, 5.4, -0.157, 1, 5.478, -0.157, 5.556, 0.066, 5.633, 0.066, 1, 5.722, 0.066, 5.811, -0.027, 5.9, -0.027, 1, 5.933, -0.027, 5.967, -0.02, 6, -0.012]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.6, 1, 1, 0.678, 1, 0.756, 0, 0.833, 0, 1, 0.878, 0, 0.922, 1, 0.967, 1, 1, 1.833, 1, 2.7, 1, 3.567, 1, 1, 3.611, 1, 3.656, 0, 3.7, 0, 1, 3.756, 0, 3.811, 1, 3.867, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.6, 1, 1, 0.678, 1, 0.756, 0, 0.833, 0, 1, 0.878, 0, 0.922, 1, 0.967, 1, 1, 1.833, 1, 2.7, 1, 3.567, 1, 1, 3.611, 1, 3.656, 0, 3.7, 0, 1, 3.756, 0, 3.811, 1, 3.867, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.4, 0, 0.4, -0.4, 1, 0.533, -0.4, 0.667, 1, 0.8, 1, 1, 0.967, 1, 1.133, 1, 1.3, 1, 1, 1.433, 1, 1.567, 0.939, 1.7, 0.8, 1, 1.822, 0.673, 1.944, 0.471, 2.067, 0.4, 1, 2.222, 0.309, 2.378, 0.305, 2.533, 0.2, 1, 2.667, 0.11, 2.8, -0.2, 2.933, -0.2, 1, 3.089, -0.2, 3.244, 0.3, 3.4, 0.3, 1, 3.556, 0.3, 3.711, 0.1, 3.867, 0.1, 0, 6, 0.1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0.6, 0, 0.4, 0.6, 1, 0.533, 0.6, 0.667, 0, 0.8, 0, 1, 0.967, 0, 1.133, 0, 1.3, 0, 1, 1.433, 0, 1.567, 1, 1.7, 1, 1, 1.822, 1, 1.944, 0.5, 2.067, 0.5, 1, 2.222, 0.5, 2.378, 1, 2.533, 1, 1, 2.667, 1, 2.8, 0.649, 2.933, 0.6, 1, 3.089, 0.543, 3.244, 0.561, 3.4, 0.5, 1, 3.556, 0.439, 3.711, 0, 3.867, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, -1, 0, 6, -1]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -1, 1, 1.011, -1, 2.022, -1.085, 3.033, 0, 1, 3.711, 0.727, 4.389, 19, 5.067, 19, 0, 6, 19]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -30, 0, 6, -30]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.689, 0, 0.744, 17.112, 0.8, 17.112, 1, 0.867, 17.112, 0.933, -18.817, 1, -18.817, 1, 1.089, -18.817, 1.178, 3.488, 1.267, 3.488, 1, 1.367, 3.488, 1.467, -0.666, 1.567, -0.666, 1, 1.678, -0.666, 1.789, 0.129, 1.9, 0.129, 1, 2, 0.129, 2.1, -0.025, 2.2, -0.025, 1, 2.3, -0.025, 2.4, 0.005, 2.5, 0.005, 1, 2.611, 0.005, 2.722, -0.001, 2.833, -0.001, 1, 2.933, -0.001, 3.033, 0, 3.133, 0, 1, 3.233, 0, 3.333, 0, 3.433, 0, 1, 3.444, 0, 3.456, 0, 3.467, 0, 1, 3.556, 0, 3.644, 19.504, 3.733, 19.504, 1, 3.822, 19.504, 3.911, -3.605, 4, -3.605, 1, 4.1, -3.605, 4.2, 0.691, 4.3, 0.691, 1, 4.411, 0.691, 4.522, -0.133, 4.633, -0.133, 1, 4.733, -0.133, 4.833, 0.026, 4.933, 0.026, 1, 5.033, 0.026, 5.133, -0.005, 5.233, -0.005, 1, 5.344, -0.005, 5.456, 0.001, 5.567, 0.001, 1, 5.667, 0.001, 5.767, 0, 5.867, 0, 1, 5.911, 0, 5.956, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 0.856, 0, 0.944, 8.577, 1.033, 8.577, 1, 1.144, 8.577, 1.256, -13.817, 1.367, -13.817, 1, 1.489, -13.817, 1.611, 15.371, 1.733, 15.371, 1, 1.867, 15.371, 2, -26.179, 2.133, -26.179, 1, 2.244, -26.179, 2.356, 30, 2.467, 30, 1, 2.5, 30, 2.533, 30, 2.567, 30, 1, 2.678, 30, 2.789, -30, 2.9, -30, 1, 2.933, -30, 2.967, -30, 3, -30, 1, 3.122, -30, 3.244, 24.719, 3.367, 24.719, 1, 3.5, 24.719, 3.633, -11.445, 3.767, -11.445, 1, 3.9, -11.445, 4.033, 3.873, 4.167, 3.873, 1, 4.311, 3.873, 4.456, -1.275, 4.6, -1.275, 1, 4.789, -1.275, 4.978, 1.905, 5.167, 1.905, 1, 5.333, 1.905, 5.5, 1.707, 5.667, 1.707, 1, 5.778, 1.707, 5.889, 1.737, 6, 1.751]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 0.856, 0, 0.944, 8.577, 1.033, 8.577, 1, 1.144, 8.577, 1.256, -13.817, 1.367, -13.817, 1, 1.489, -13.817, 1.611, 15.371, 1.733, 15.371, 1, 1.867, 15.371, 2, -26.179, 2.133, -26.179, 1, 2.244, -26.179, 2.356, 30, 2.467, 30, 1, 2.5, 30, 2.533, 30, 2.567, 30, 1, 2.678, 30, 2.789, -30, 2.9, -30, 1, 2.933, -30, 2.967, -30, 3, -30, 1, 3.122, -30, 3.244, 24.719, 3.367, 24.719, 1, 3.5, 24.719, 3.633, -11.445, 3.767, -11.445, 1, 3.9, -11.445, 4.033, 3.873, 4.167, 3.873, 1, 4.311, 3.873, 4.456, -1.275, 4.6, -1.275, 1, 4.789, -1.275, 4.978, 1.905, 5.167, 1.905, 1, 5.333, 1.905, 5.5, 1.707, 5.667, 1.707, 1, 5.778, 1.707, 5.889, 1.737, 6, 1.751]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 0.856, 0, 0.944, 8.577, 1.033, 8.577, 1, 1.144, 8.577, 1.256, -13.817, 1.367, -13.817, 1, 1.489, -13.817, 1.611, 15.371, 1.733, 15.371, 1, 1.867, 15.371, 2, -26.179, 2.133, -26.179, 1, 2.244, -26.179, 2.356, 30, 2.467, 30, 1, 2.5, 30, 2.533, 30, 2.567, 30, 1, 2.678, 30, 2.789, -30, 2.9, -30, 1, 2.933, -30, 2.967, -30, 3, -30, 1, 3.122, -30, 3.244, 24.719, 3.367, 24.719, 1, 3.5, 24.719, 3.633, -11.445, 3.767, -11.445, 1, 3.9, -11.445, 4.033, 3.873, 4.167, 3.873, 1, 4.311, 3.873, 4.456, -1.275, 4.6, -1.275, 1, 4.789, -1.275, 4.978, 1.905, 5.167, 1.905, 1, 5.333, 1.905, 5.5, 1.707, 5.667, 1.707, 1, 5.778, 1.707, 5.889, 1.737, 6, 1.751]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, -0.095, 1, 0.067, -0.095, 0.133, 0.134, 0.2, 0.134, 1, 0.3, 0.134, 0.4, -0.132, 0.5, -0.132, 1, 0.667, -0.132, 0.833, 2.749, 1, 2.749, 1, 1.078, 2.749, 1.156, -0.862, 1.233, -0.862, 1, 1.3, -0.862, 1.367, 0.835, 1.433, 0.835, 1, 1.533, 0.835, 1.633, -12.214, 1.733, -12.214, 1, 1.844, -12.214, 1.956, 15.792, 2.067, 15.792, 1, 2.211, 15.792, 2.356, -14.293, 2.5, -14.293, 1, 2.622, -14.293, 2.744, 9.581, 2.867, 9.581, 1, 2.989, 9.581, 3.111, -4.084, 3.233, -4.084, 1, 3.333, -4.084, 3.433, 1.019, 3.533, 1.019, 1, 3.678, 1.019, 3.822, -0.01, 3.967, -0.01, 1, 4.044, -0.01, 4.122, 0.077, 4.2, 0.077, 1, 4.289, 0.077, 4.378, -0.137, 4.467, -0.137, 1, 4.756, -0.137, 5.044, 1.327, 5.333, 1.327, 1, 5.456, 1.327, 5.578, 1.186, 5.7, 1.186, 1, 5.8, 1.186, 5.9, 1.225, 6, 1.233]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, -0.095, 1, 0.067, -0.095, 0.133, 0.134, 0.2, 0.134, 1, 0.3, 0.134, 0.4, -0.132, 0.5, -0.132, 1, 0.667, -0.132, 0.833, 2.749, 1, 2.749, 1, 1.078, 2.749, 1.156, -0.862, 1.233, -0.862, 1, 1.3, -0.862, 1.367, 0.835, 1.433, 0.835, 1, 1.533, 0.835, 1.633, -12.214, 1.733, -12.214, 1, 1.844, -12.214, 1.956, 15.792, 2.067, 15.792, 1, 2.211, 15.792, 2.356, -14.293, 2.5, -14.293, 1, 2.622, -14.293, 2.744, 9.581, 2.867, 9.581, 1, 2.989, 9.581, 3.111, -4.084, 3.233, -4.084, 1, 3.333, -4.084, 3.433, 1.019, 3.533, 1.019, 1, 3.678, 1.019, 3.822, -0.01, 3.967, -0.01, 1, 4.044, -0.01, 4.122, 0.077, 4.2, 0.077, 1, 4.289, 0.077, 4.378, -0.137, 4.467, -0.137, 1, 4.756, -0.137, 5.044, 1.327, 5.333, 1.327, 1, 5.456, 1.327, 5.578, 1.186, 5.7, 1.186, 1, 5.8, 1.186, 5.9, 1.225, 6, 1.233]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, -0.043, 1, 0.044, -0.043, 0.089, -0.455, 0.133, -0.455, 1, 0.233, -0.455, 0.333, 0.449, 0.433, 0.449, 1, 0.533, 0.449, 0.633, -0.444, 0.733, -0.444, 1, 0.744, -0.444, 0.756, -0.379, 0.767, -0.379, 1, 0.822, -0.379, 0.878, -1.566, 0.933, -1.566, 1, 1.011, -1.566, 1.089, 5.012, 1.167, 5.012, 1, 1.256, 5.012, 1.344, -1.123, 1.433, -1.123, 1, 1.511, -1.123, 1.589, 8.67, 1.667, 8.67, 1, 1.756, 8.67, 1.844, -24.899, 1.933, -24.899, 1, 2.056, -24.899, 2.178, 25.296, 2.3, 25.296, 1, 2.433, 25.296, 2.567, -23.633, 2.7, -23.633, 1, 2.822, -23.633, 2.944, 14.758, 3.067, 14.758, 1, 3.178, 14.758, 3.289, -6.667, 3.4, -6.667, 1, 3.522, -6.667, 3.644, 1.797, 3.767, 1.797, 1, 3.889, 1.797, 4.011, -0.101, 4.133, -0.101, 1, 4.222, -0.101, 4.311, 0.309, 4.4, 0.309, 1, 4.533, 0.309, 4.667, -0.427, 4.8, -0.427, 1, 4.833, -0.427, 4.867, -0.387, 4.9, -0.387, 1, 4.922, -0.387, 4.944, -0.437, 4.967, -0.437, 1, 4.989, -0.437, 5.011, -0.405, 5.033, -0.405, 1, 5.056, -0.405, 5.078, -0.538, 5.1, -0.538, 1, 5.233, -0.538, 5.367, 0.18, 5.5, 0.18, 1, 5.622, 0.18, 5.744, -0.068, 5.867, -0.068, 1, 5.911, -0.068, 5.956, -0.056, 6, -0.04]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, -0.095, 1, 0.067, -0.095, 0.133, 0.134, 0.2, 0.134, 1, 0.3, 0.134, 0.4, -0.132, 0.5, -0.132, 1, 0.667, -0.132, 0.833, 2.749, 1, 2.749, 1, 1.078, 2.749, 1.156, -0.862, 1.233, -0.862, 1, 1.3, -0.862, 1.367, 0.835, 1.433, 0.835, 1, 1.533, 0.835, 1.633, -12.214, 1.733, -12.214, 1, 1.844, -12.214, 1.956, 15.792, 2.067, 15.792, 1, 2.211, 15.792, 2.356, -14.293, 2.5, -14.293, 1, 2.622, -14.293, 2.744, 9.581, 2.867, 9.581, 1, 2.989, 9.581, 3.111, -4.084, 3.233, -4.084, 1, 3.333, -4.084, 3.433, 1.019, 3.533, 1.019, 1, 3.678, 1.019, 3.822, -0.01, 3.967, -0.01, 1, 4.044, -0.01, 4.122, 0.077, 4.2, 0.077, 1, 4.289, 0.077, 4.378, -0.137, 4.467, -0.137, 1, 4.756, -0.137, 5.044, 1.327, 5.333, 1.327, 1, 5.456, 1.327, 5.578, 1.186, 5.7, 1.186, 1, 5.8, 1.186, 5.9, 1.225, 6, 1.233]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, -0.031, 1, 0.044, -0.031, 0.089, -0.325, 0.133, -0.325, 1, 0.233, -0.325, 0.333, 0.321, 0.433, 0.321, 1, 0.533, 0.321, 0.633, -0.317, 0.733, -0.317, 1, 0.744, -0.317, 0.756, -0.271, 0.767, -0.271, 1, 0.822, -0.271, 0.878, -1.118, 0.933, -1.118, 1, 1.011, -1.118, 1.089, 3.58, 1.167, 3.58, 1, 1.256, 3.58, 1.344, -0.802, 1.433, -0.802, 1, 1.511, -0.802, 1.589, 6.193, 1.667, 6.193, 1, 1.756, 6.193, 1.844, -17.785, 1.933, -17.785, 1, 2.056, -17.785, 2.178, 18.069, 2.3, 18.069, 1, 2.433, 18.069, 2.567, -16.881, 2.7, -16.881, 1, 2.822, -16.881, 2.944, 10.541, 3.067, 10.541, 1, 3.178, 10.541, 3.289, -4.762, 3.4, -4.762, 1, 3.522, -4.762, 3.644, 1.284, 3.767, 1.284, 1, 3.889, 1.284, 4.011, -0.072, 4.133, -0.072, 1, 4.222, -0.072, 4.311, 0.221, 4.4, 0.221, 1, 4.533, 0.221, 4.667, -0.305, 4.8, -0.305, 1, 4.833, -0.305, 4.867, -0.276, 4.9, -0.276, 1, 4.922, -0.276, 4.944, -0.312, 4.967, -0.312, 1, 4.989, -0.312, 5.011, -0.289, 5.033, -0.289, 1, 5.056, -0.289, 5.078, -0.385, 5.1, -0.385, 1, 5.233, -0.385, 5.367, 0.129, 5.5, 0.129, 1, 5.622, 0.129, 5.744, -0.049, 5.867, -0.049, 1, 5.911, -0.049, 5.956, -0.04, 6, -0.029]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, -0.095, 1, 0.067, -0.095, 0.133, 0.134, 0.2, 0.134, 1, 0.3, 0.134, 0.4, -0.132, 0.5, -0.132, 1, 0.667, -0.132, 0.833, 2.749, 1, 2.749, 1, 1.078, 2.749, 1.156, -0.862, 1.233, -0.862, 1, 1.3, -0.862, 1.367, 0.835, 1.433, 0.835, 1, 1.533, 0.835, 1.633, -12.214, 1.733, -12.214, 1, 1.844, -12.214, 1.956, 15.792, 2.067, 15.792, 1, 2.211, 15.792, 2.356, -14.293, 2.5, -14.293, 1, 2.622, -14.293, 2.744, 9.581, 2.867, 9.581, 1, 2.989, 9.581, 3.111, -4.084, 3.233, -4.084, 1, 3.333, -4.084, 3.433, 1.019, 3.533, 1.019, 1, 3.678, 1.019, 3.822, -0.01, 3.967, -0.01, 1, 4.044, -0.01, 4.122, 0.077, 4.2, 0.077, 1, 4.289, 0.077, 4.378, -0.137, 4.467, -0.137, 1, 4.756, -0.137, 5.044, 1.327, 5.333, 1.327, 1, 5.456, 1.327, 5.578, 1.186, 5.7, 1.186, 1, 5.8, 1.186, 5.9, 1.225, 6, 1.233]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, -0.031, 1, 0.044, -0.031, 0.089, -0.325, 0.133, -0.325, 1, 0.233, -0.325, 0.333, 0.321, 0.433, 0.321, 1, 0.533, 0.321, 0.633, -0.317, 0.733, -0.317, 1, 0.744, -0.317, 0.756, -0.271, 0.767, -0.271, 1, 0.822, -0.271, 0.878, -1.118, 0.933, -1.118, 1, 1.011, -1.118, 1.089, 3.58, 1.167, 3.58, 1, 1.256, 3.58, 1.344, -0.802, 1.433, -0.802, 1, 1.511, -0.802, 1.589, 6.193, 1.667, 6.193, 1, 1.756, 6.193, 1.844, -17.785, 1.933, -17.785, 1, 2.056, -17.785, 2.178, 18.069, 2.3, 18.069, 1, 2.433, 18.069, 2.567, -16.881, 2.7, -16.881, 1, 2.822, -16.881, 2.944, 10.541, 3.067, 10.541, 1, 3.178, 10.541, 3.289, -4.762, 3.4, -4.762, 1, 3.522, -4.762, 3.644, 1.284, 3.767, 1.284, 1, 3.889, 1.284, 4.011, -0.072, 4.133, -0.072, 1, 4.222, -0.072, 4.311, 0.221, 4.4, 0.221, 1, 4.533, 0.221, 4.667, -0.305, 4.8, -0.305, 1, 4.833, -0.305, 4.867, -0.276, 4.9, -0.276, 1, 4.922, -0.276, 4.944, -0.312, 4.967, -0.312, 1, 4.989, -0.312, 5.011, -0.289, 5.033, -0.289, 1, 5.056, -0.289, 5.078, -0.385, 5.1, -0.385, 1, 5.233, -0.385, 5.367, 0.129, 5.5, 0.129, 1, 5.622, 0.129, 5.744, -0.049, 5.867, -0.049, 1, 5.911, -0.049, 5.956, -0.04, 6, -0.029]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, -0.114, 1, 0.067, -0.114, 0.133, 0.16, 0.2, 0.16, 1, 0.3, 0.16, 0.4, -0.158, 0.5, -0.158, 1, 0.667, -0.158, 0.833, 3.299, 1, 3.299, 1, 1.078, 3.299, 1.156, -1.035, 1.233, -1.035, 1, 1.3, -1.035, 1.367, 1.002, 1.433, 1.002, 1, 1.533, 1.002, 1.633, -14.656, 1.733, -14.656, 1, 1.844, -14.656, 1.956, 18.951, 2.067, 18.951, 1, 2.211, 18.951, 2.356, -17.151, 2.5, -17.151, 1, 2.622, -17.151, 2.744, 11.497, 2.867, 11.497, 1, 2.989, 11.497, 3.111, -4.901, 3.233, -4.901, 1, 3.333, -4.901, 3.433, 1.223, 3.533, 1.223, 1, 3.678, 1.223, 3.822, -0.012, 3.967, -0.012, 1, 4.044, -0.012, 4.122, 0.092, 4.2, 0.092, 1, 4.289, 0.092, 4.378, -0.164, 4.467, -0.164, 1, 4.756, -0.164, 5.044, 1.592, 5.333, 1.592, 1, 5.456, 1.592, 5.578, 1.423, 5.7, 1.423, 1, 5.8, 1.423, 5.9, 1.47, 6, 1.48]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, -0.037, 1, 0.044, -0.037, 0.089, -0.39, 0.133, -0.39, 1, 0.233, -0.39, 0.333, 0.385, 0.433, 0.385, 1, 0.533, 0.385, 0.633, -0.38, 0.733, -0.38, 1, 0.744, -0.38, 0.756, -0.325, 0.767, -0.325, 1, 0.822, -0.325, 0.878, -1.342, 0.933, -1.342, 1, 1.011, -1.342, 1.089, 4.296, 1.167, 4.296, 1, 1.256, 4.296, 1.344, -0.962, 1.433, -0.962, 1, 1.511, -0.962, 1.589, 7.431, 1.667, 7.431, 1, 1.756, 7.431, 1.844, -21.342, 1.933, -21.342, 1, 2.056, -21.342, 2.178, 21.682, 2.3, 21.682, 1, 2.433, 21.682, 2.567, -20.257, 2.7, -20.257, 1, 2.822, -20.257, 2.944, 12.649, 3.067, 12.649, 1, 3.178, 12.649, 3.289, -5.715, 3.4, -5.715, 1, 3.522, -5.715, 3.644, 1.54, 3.767, 1.54, 1, 3.889, 1.54, 4.011, -0.086, 4.133, -0.086, 1, 4.222, -0.086, 4.311, 0.265, 4.4, 0.265, 1, 4.533, 0.265, 4.667, -0.366, 4.8, -0.366, 1, 4.833, -0.366, 4.867, -0.332, 4.9, -0.332, 1, 4.922, -0.332, 4.944, -0.374, 4.967, -0.374, 1, 4.989, -0.374, 5.011, -0.347, 5.033, -0.347, 1, 5.056, -0.347, 5.078, -0.462, 5.1, -0.462, 1, 5.233, -0.462, 5.367, 0.154, 5.5, 0.154, 1, 5.622, 0.154, 5.744, -0.059, 5.867, -0.059, 1, 5.911, -0.059, 5.956, -0.048, 6, -0.035]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, -0.114, 1, 0.067, -0.114, 0.133, 0.16, 0.2, 0.16, 1, 0.3, 0.16, 0.4, -0.158, 0.5, -0.158, 1, 0.667, -0.158, 0.833, 3.299, 1, 3.299, 1, 1.078, 3.299, 1.156, -1.035, 1.233, -1.035, 1, 1.3, -1.035, 1.367, 1.002, 1.433, 1.002, 1, 1.533, 1.002, 1.633, -14.656, 1.733, -14.656, 1, 1.844, -14.656, 1.956, 18.951, 2.067, 18.951, 1, 2.211, 18.951, 2.356, -17.151, 2.5, -17.151, 1, 2.622, -17.151, 2.744, 11.497, 2.867, 11.497, 1, 2.989, 11.497, 3.111, -4.901, 3.233, -4.901, 1, 3.333, -4.901, 3.433, 1.223, 3.533, 1.223, 1, 3.678, 1.223, 3.822, -0.012, 3.967, -0.012, 1, 4.044, -0.012, 4.122, 0.092, 4.2, 0.092, 1, 4.289, 0.092, 4.378, -0.164, 4.467, -0.164, 1, 4.756, -0.164, 5.044, 1.592, 5.333, 1.592, 1, 5.456, 1.592, 5.578, 1.423, 5.7, 1.423, 1, 5.8, 1.423, 5.9, 1.47, 6, 1.48]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, -0.037, 1, 0.044, -0.037, 0.089, -0.39, 0.133, -0.39, 1, 0.233, -0.39, 0.333, 0.385, 0.433, 0.385, 1, 0.533, 0.385, 0.633, -0.38, 0.733, -0.38, 1, 0.744, -0.38, 0.756, -0.325, 0.767, -0.325, 1, 0.822, -0.325, 0.878, -1.342, 0.933, -1.342, 1, 1.011, -1.342, 1.089, 4.296, 1.167, 4.296, 1, 1.256, 4.296, 1.344, -0.962, 1.433, -0.962, 1, 1.511, -0.962, 1.589, 7.431, 1.667, 7.431, 1, 1.756, 7.431, 1.844, -21.342, 1.933, -21.342, 1, 2.056, -21.342, 2.178, 21.682, 2.3, 21.682, 1, 2.433, 21.682, 2.567, -20.257, 2.7, -20.257, 1, 2.822, -20.257, 2.944, 12.649, 3.067, 12.649, 1, 3.178, 12.649, 3.289, -5.715, 3.4, -5.715, 1, 3.522, -5.715, 3.644, 1.54, 3.767, 1.54, 1, 3.889, 1.54, 4.011, -0.086, 4.133, -0.086, 1, 4.222, -0.086, 4.311, 0.265, 4.4, 0.265, 1, 4.533, 0.265, 4.667, -0.366, 4.8, -0.366, 1, 4.833, -0.366, 4.867, -0.332, 4.9, -0.332, 1, 4.922, -0.332, 4.944, -0.374, 4.967, -0.374, 1, 4.989, -0.374, 5.011, -0.347, 5.033, -0.347, 1, 5.056, -0.347, 5.078, -0.462, 5.1, -0.462, 1, 5.233, -0.462, 5.367, 0.154, 5.5, 0.154, 1, 5.622, 0.154, 5.744, -0.059, 5.867, -0.059, 1, 5.911, -0.059, 5.956, -0.048, 6, -0.035]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.689, 0, 0.744, 5.704, 0.8, 5.704, 1, 0.867, 5.704, 0.933, -6.272, 1, -6.272, 1, 1.089, -6.272, 1.178, 1.163, 1.267, 1.163, 1, 1.367, 1.163, 1.467, -0.222, 1.567, -0.222, 1, 1.678, -0.222, 1.789, 0.043, 1.9, 0.043, 1, 2, 0.043, 2.1, -0.008, 2.2, -0.008, 1, 2.3, -0.008, 2.4, 0.002, 2.5, 0.002, 1, 2.611, 0.002, 2.722, 0, 2.833, 0, 1, 2.933, 0, 3.033, 0, 3.133, 0, 1, 3.233, 0, 3.333, 0, 3.433, 0, 1, 3.444, 0, 3.456, 0, 3.467, 0, 1, 3.556, 0, 3.644, 6.501, 3.733, 6.501, 1, 3.822, 6.501, 3.911, -1.202, 4, -1.202, 1, 4.1, -1.202, 4.2, 0.23, 4.3, 0.23, 1, 4.411, 0.23, 4.522, -0.044, 4.633, -0.044, 1, 4.733, -0.044, 4.833, 0.009, 4.933, 0.009, 1, 5.033, 0.009, 5.133, -0.002, 5.233, -0.002, 1, 5.344, -0.002, 5.456, 0, 5.567, 0, 1, 5.667, 0, 5.767, 0, 5.867, 0, 1, 5.911, 0, 5.956, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.076, 1, 0.067, -0.076, 0.133, 0.107, 0.2, 0.107, 1, 0.3, 0.107, 0.4, -0.106, 0.5, -0.106, 1, 0.667, -0.106, 0.833, 2.199, 1, 2.199, 1, 1.078, 2.199, 1.156, -0.69, 1.233, -0.69, 1, 1.3, -0.69, 1.367, 0.668, 1.433, 0.668, 1, 1.533, 0.668, 1.633, -9.771, 1.733, -9.771, 1, 1.844, -9.771, 1.956, 12.634, 2.067, 12.634, 1, 2.211, 12.634, 2.356, -11.434, 2.5, -11.434, 1, 2.622, -11.434, 2.744, 7.665, 2.867, 7.665, 1, 2.989, 7.665, 3.111, -3.268, 3.233, -3.268, 1, 3.333, -3.268, 3.433, 0.815, 3.533, 0.815, 1, 3.678, 0.815, 3.822, -0.008, 3.967, -0.008, 1, 4.044, -0.008, 4.122, 0.062, 4.2, 0.062, 1, 4.289, 0.062, 4.378, -0.11, 4.467, -0.11, 1, 4.756, -0.11, 5.044, 1.062, 5.333, 1.062, 1, 5.456, 1.062, 5.578, 0.948, 5.7, 0.948, 1, 5.8, 0.948, 5.9, 0.98, 6, 0.987]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -0.024, 1, 0.044, -0.024, 0.089, -0.26, 0.133, -0.26, 1, 0.233, -0.26, 0.333, 0.257, 0.433, 0.257, 1, 0.533, 0.257, 0.633, -0.254, 0.733, -0.254, 1, 0.744, -0.254, 0.756, -0.217, 0.767, -0.217, 1, 0.822, -0.217, 0.878, -0.895, 0.933, -0.895, 1, 1.011, -0.895, 1.089, 2.864, 1.167, 2.864, 1, 1.256, 2.864, 1.344, -0.642, 1.433, -0.642, 1, 1.511, -0.642, 1.589, 4.954, 1.667, 4.954, 1, 1.756, 4.954, 1.844, -14.228, 1.933, -14.228, 1, 2.056, -14.228, 2.178, 14.455, 2.3, 14.455, 1, 2.433, 14.455, 2.567, -13.504, 2.7, -13.504, 1, 2.822, -13.504, 2.944, 8.433, 3.067, 8.433, 1, 3.178, 8.433, 3.289, -3.81, 3.4, -3.81, 1, 3.522, -3.81, 3.644, 1.027, 3.767, 1.027, 1, 3.889, 1.027, 4.011, -0.058, 4.133, -0.058, 1, 4.222, -0.058, 4.311, 0.177, 4.4, 0.177, 1, 4.533, 0.177, 4.667, -0.244, 4.8, -0.244, 1, 4.833, -0.244, 4.867, -0.221, 4.9, -0.221, 1, 4.922, -0.221, 4.944, -0.25, 4.967, -0.25, 1, 4.989, -0.25, 5.011, -0.231, 5.033, -0.231, 1, 5.056, -0.231, 5.078, -0.308, 5.1, -0.308, 1, 5.233, -0.308, 5.367, 0.103, 5.5, 0.103, 1, 5.622, 0.103, 5.744, -0.039, 5.867, -0.039, 1, 5.911, -0.039, 5.956, -0.032, 6, -0.023]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -0.153, 1, 0.067, -0.153, 0.133, 0.214, 0.2, 0.214, 1, 0.3, 0.214, 0.4, -0.211, 0.5, -0.211, 1, 0.667, -0.211, 0.833, 4.399, 1, 4.399, 1, 1.078, 4.399, 1.156, -1.38, 1.233, -1.38, 1, 1.3, -1.38, 1.367, 1.337, 1.433, 1.337, 1, 1.533, 1.337, 1.633, -19.542, 1.733, -19.542, 1, 1.844, -19.542, 1.956, 25.268, 2.067, 25.268, 1, 2.211, 25.268, 2.356, -22.868, 2.5, -22.868, 1, 2.622, -22.868, 2.744, 15.33, 2.867, 15.33, 1, 2.989, 15.33, 3.111, -6.535, 3.233, -6.535, 1, 3.333, -6.535, 3.433, 1.631, 3.533, 1.631, 1, 3.678, 1.631, 3.822, -0.016, 3.967, -0.016, 1, 4.044, -0.016, 4.122, 0.123, 4.2, 0.123, 1, 4.289, 0.123, 4.378, -0.219, 4.467, -0.219, 1, 4.756, -0.219, 5.044, 2.123, 5.333, 2.123, 1, 5.456, 2.123, 5.578, 1.897, 5.7, 1.897, 1, 5.8, 1.897, 5.9, 1.96, 6, 1.973]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 0.844, 0, 0.922, 0.979, 1, 0.979, 1, 1.078, 0.979, 1.156, 0.05, 1.233, 0.05, 1, 1.3, 0.05, 1.367, 0.341, 1.433, 0.341, 1, 1.533, 0.341, 1.633, -4.177, 1.733, -4.177, 1, 1.867, -4.177, 2, 4.733, 2.133, 4.733, 1, 2.267, 4.733, 2.4, -4.239, 2.533, -4.239, 1, 2.667, -4.239, 2.8, 2.251, 2.933, 2.251, 1, 3.056, 2.251, 3.178, -0.658, 3.3, -0.658, 1, 3.478, -0.658, 3.656, 0.201, 3.833, 0.201, 1, 4.044, 0.201, 4.256, -0.047, 4.467, -0.047, 1, 4.778, -0.047, 5.089, 0.396, 5.4, 0.396, 1, 5.533, 0.396, 5.667, 0.359, 5.8, 0.359, 1, 5.867, 0.359, 5.933, 0.362, 6, 0.364]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 0.833, 0, 0.9, -0.505, 0.967, -0.505, 1, 1.044, -0.505, 1.122, 0.663, 1.2, 0.663, 1, 1.278, 0.663, 1.356, -0.51, 1.433, -0.51, 1, 1.522, -0.51, 1.611, 2.189, 1.7, 2.189, 1, 1.789, 2.189, 1.878, -4.022, 1.967, -4.022, 1, 2.089, -4.022, 2.211, 4.387, 2.333, 4.387, 1, 2.467, 4.387, 2.6, -4.811, 2.733, -4.811, 1, 2.856, -4.811, 2.978, 3.535, 3.1, 3.535, 1, 3.222, 3.535, 3.344, -2.034, 3.467, -2.034, 1, 3.589, -2.034, 3.711, 0.768, 3.833, 0.768, 1, 3.967, 0.768, 4.1, -0.32, 4.233, -0.32, 1, 4.344, -0.32, 4.456, 0.06, 4.567, 0.06, 1, 4.667, 0.06, 4.767, -0.046, 4.867, -0.046, 1, 4.933, -0.046, 5, -0.006, 5.067, -0.006, 1, 5.078, -0.006, 5.089, -0.007, 5.1, -0.007, 1, 5.233, -0.007, 5.367, 0.036, 5.5, 0.036, 1, 5.633, 0.036, 5.767, -0.022, 5.9, -0.022, 1, 5.933, -0.022, 5.967, -0.019, 6, -0.016]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.045, 1, 0.044, -0.045, 0.089, 0, 0.133, 0, 1, 0.356, 0, 0.578, 0, 0.8, 0, 1, 0.878, 0, 0.956, -0.343, 1.033, -0.343, 1, 1.133, -0.343, 1.233, 0.994, 1.333, 0.994, 1, 1.411, 0.994, 1.489, -0.299, 1.567, -0.299, 1, 1.633, -0.299, 1.7, 1.022, 1.767, 1.022, 1, 1.867, 1.022, 1.967, -5.104, 2.067, -5.104, 1, 2.189, -5.104, 2.311, 8.442, 2.433, 8.442, 1, 2.567, 8.442, 2.7, -9.612, 2.833, -9.612, 1, 2.967, -9.612, 3.1, 8.128, 3.233, 8.128, 1, 3.356, 8.128, 3.478, -5.3, 3.6, -5.3, 1, 3.733, -5.3, 3.867, 2.894, 4, 2.894, 1, 4.133, 2.894, 4.267, -1.362, 4.4, -1.362, 1, 4.522, -1.362, 4.644, 0.505, 4.767, 0.505, 1, 4.878, 0.505, 4.989, -0.265, 5.1, -0.265, 1, 5.244, -0.265, 5.389, 0.137, 5.533, 0.137, 1, 5.678, 0.137, 5.822, -0.071, 5.967, -0.071, 1, 5.978, -0.071, 5.989, -0.07, 6, -0.069]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0.061, 1, 0.044, 0.061, 0.089, -0.088, 0.133, -0.088, 1, 0.256, -0.088, 0.378, 0.062, 0.5, 0.062, 1, 0.611, 0.062, 0.722, -0.059, 0.833, -0.059, 1, 0.856, -0.059, 0.878, -0.047, 0.9, -0.047, 1, 0.989, -0.047, 1.078, -0.564, 1.167, -0.564, 1, 1.278, -0.564, 1.389, 1.106, 1.5, 1.106, 1, 1.567, 1.106, 1.633, 0.115, 1.7, 0.115, 1, 1.767, 0.115, 1.833, 1.192, 1.9, 1.192, 1, 2.011, 1.192, 2.122, -6.196, 2.233, -6.196, 1, 2.356, -6.196, 2.478, 10.815, 2.6, 10.815, 1, 2.733, 10.815, 2.867, -12.812, 3, -12.812, 1, 3.133, -12.812, 3.267, 11.658, 3.4, 11.658, 1, 3.522, 11.658, 3.644, -8.761, 3.767, -8.761, 1, 3.9, -8.761, 4.033, 5.725, 4.167, 5.725, 1, 4.3, 5.725, 4.433, -3.283, 4.567, -3.283, 1, 4.689, -3.283, 4.811, 1.673, 4.933, 1.673, 1, 5.056, 1.673, 5.178, -0.842, 5.3, -0.842, 1, 5.433, -0.842, 5.567, 0.433, 5.7, 0.433, 1, 5.8, 0.433, 5.9, 0.066, 6, -0.118]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 0.844, 0, 0.922, 0.979, 1, 0.979, 1, 1.078, 0.979, 1.156, 0.05, 1.233, 0.05, 1, 1.3, 0.05, 1.367, 0.341, 1.433, 0.341, 1, 1.533, 0.341, 1.633, -4.177, 1.733, -4.177, 1, 1.867, -4.177, 2, 4.733, 2.133, 4.733, 1, 2.267, 4.733, 2.4, -4.239, 2.533, -4.239, 1, 2.667, -4.239, 2.8, 2.251, 2.933, 2.251, 1, 3.056, 2.251, 3.178, -0.658, 3.3, -0.658, 1, 3.478, -0.658, 3.656, 0.201, 3.833, 0.201, 1, 4.044, 0.201, 4.256, -0.047, 4.467, -0.047, 1, 4.778, -0.047, 5.089, 0.396, 5.4, 0.396, 1, 5.533, 0.396, 5.667, 0.359, 5.8, 0.359, 1, 5.867, 0.359, 5.933, 0.362, 6, 0.364]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 0.833, 0, 0.9, -0.505, 0.967, -0.505, 1, 1.044, -0.505, 1.122, 0.663, 1.2, 0.663, 1, 1.278, 0.663, 1.356, -0.51, 1.433, -0.51, 1, 1.522, -0.51, 1.611, 2.189, 1.7, 2.189, 1, 1.789, 2.189, 1.878, -4.022, 1.967, -4.022, 1, 2.089, -4.022, 2.211, 4.387, 2.333, 4.387, 1, 2.467, 4.387, 2.6, -4.811, 2.733, -4.811, 1, 2.856, -4.811, 2.978, 3.535, 3.1, 3.535, 1, 3.222, 3.535, 3.344, -2.034, 3.467, -2.034, 1, 3.589, -2.034, 3.711, 0.768, 3.833, 0.768, 1, 3.967, 0.768, 4.1, -0.32, 4.233, -0.32, 1, 4.344, -0.32, 4.456, 0.06, 4.567, 0.06, 1, 4.667, 0.06, 4.767, -0.046, 4.867, -0.046, 1, 4.933, -0.046, 5, -0.006, 5.067, -0.006, 1, 5.078, -0.006, 5.089, -0.007, 5.1, -0.007, 1, 5.233, -0.007, 5.367, 0.036, 5.5, 0.036, 1, 5.633, 0.036, 5.767, -0.022, 5.9, -0.022, 1, 5.933, -0.022, 5.967, -0.019, 6, -0.016]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.045, 1, 0.044, -0.045, 0.089, 0, 0.133, 0, 1, 0.356, 0, 0.578, 0, 0.8, 0, 1, 0.878, 0, 0.956, -0.343, 1.033, -0.343, 1, 1.133, -0.343, 1.233, 0.994, 1.333, 0.994, 1, 1.411, 0.994, 1.489, -0.299, 1.567, -0.299, 1, 1.633, -0.299, 1.7, 1.022, 1.767, 1.022, 1, 1.867, 1.022, 1.967, -5.104, 2.067, -5.104, 1, 2.189, -5.104, 2.311, 8.442, 2.433, 8.442, 1, 2.567, 8.442, 2.7, -9.612, 2.833, -9.612, 1, 2.967, -9.612, 3.1, 8.128, 3.233, 8.128, 1, 3.356, 8.128, 3.478, -5.3, 3.6, -5.3, 1, 3.733, -5.3, 3.867, 2.894, 4, 2.894, 1, 4.133, 2.894, 4.267, -1.362, 4.4, -1.362, 1, 4.522, -1.362, 4.644, 0.505, 4.767, 0.505, 1, 4.878, 0.505, 4.989, -0.265, 5.1, -0.265, 1, 5.244, -0.265, 5.389, 0.137, 5.533, 0.137, 1, 5.678, 0.137, 5.822, -0.071, 5.967, -0.071, 1, 5.978, -0.071, 5.989, -0.07, 6, -0.069]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0.061, 1, 0.044, 0.061, 0.089, -0.088, 0.133, -0.088, 1, 0.256, -0.088, 0.378, 0.062, 0.5, 0.062, 1, 0.611, 0.062, 0.722, -0.059, 0.833, -0.059, 1, 0.856, -0.059, 0.878, -0.047, 0.9, -0.047, 1, 0.989, -0.047, 1.078, -0.564, 1.167, -0.564, 1, 1.278, -0.564, 1.389, 1.106, 1.5, 1.106, 1, 1.567, 1.106, 1.633, 0.115, 1.7, 0.115, 1, 1.767, 0.115, 1.833, 1.192, 1.9, 1.192, 1, 2.011, 1.192, 2.122, -6.196, 2.233, -6.196, 1, 2.356, -6.196, 2.478, 10.815, 2.6, 10.815, 1, 2.733, 10.815, 2.867, -12.812, 3, -12.812, 1, 3.133, -12.812, 3.267, 11.658, 3.4, 11.658, 1, 3.522, 11.658, 3.644, -8.761, 3.767, -8.761, 1, 3.9, -8.761, 4.033, 5.725, 4.167, 5.725, 1, 4.3, 5.725, 4.433, -3.283, 4.567, -3.283, 1, 4.689, -3.283, 4.811, 1.673, 4.933, 1.673, 1, 5.056, 1.673, 5.178, -0.842, 5.3, -0.842, 1, 5.433, -0.842, 5.567, 0.433, 5.7, 0.433, 1, 5.8, 0.433, 5.9, 0.066, 6, -0.118]}]}