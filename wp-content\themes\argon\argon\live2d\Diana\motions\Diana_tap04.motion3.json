{"Version": 3, "Meta": {"Duration": 9.1, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 54, "TotalSegmentCount": 888, "TotalPointCount": 2660, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, 0.558, 0.733, 0.558, 1, 0.811, 0.558, 0.889, -0.625, 0.967, -0.625, 1, 1.078, -0.625, 1.189, 0.209, 1.3, 0.209, 1, 1.4, 0.209, 1.5, -0.072, 1.6, -0.072, 1, 1.722, -0.072, 1.844, 0.1, 1.967, 0.1, 1, 2.056, 0.1, 2.144, 0.049, 2.233, 0.049, 1, 2.267, 0.049, 2.3, 0.139, 2.333, 0.139, 1, 2.4, 0.139, 2.467, 0.04, 2.533, 0.04, 1, 2.644, 0.04, 2.756, 0.263, 2.867, 0.263, 1, 3.189, 0.263, 3.511, -0.582, 3.833, -0.582, 1, 3.978, -0.582, 4.122, 0.251, 4.267, 0.251, 1, 4.322, 0.251, 4.378, 0.195, 4.433, 0.195, 1, 4.556, 0.195, 4.678, 0.861, 4.8, 0.861, 1, 4.933, 0.861, 5.067, -2.496, 5.2, -2.496, 1, 5.3, -2.496, 5.4, 1.729, 5.5, 1.729, 1, 5.622, 1.729, 5.744, -0.78, 5.867, -0.78, 1, 6.011, -0.78, 6.156, 1.111, 6.3, 1.111, 1, 6.433, 1.111, 6.567, -2.279, 6.7, -2.279, 1, 6.822, -2.279, 6.944, 1.991, 7.067, 1.991, 1, 7.2, 1.991, 7.333, -1.235, 7.467, -1.235, 1, 7.578, -1.235, 7.689, 0.448, 7.8, 0.448, 1, 7.922, 0.448, 8.044, -0.231, 8.167, -0.231, 1, 8.278, -0.231, 8.389, 0.047, 8.5, 0.047, 1, 8.611, 0.047, 8.722, -0.037, 8.833, -0.037, 1, 8.922, -0.037, 9.011, -0.019, 9.1, -0.005]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, -30, 0, 2.3, -30, 1, 2.567, -30, 2.833, 30, 3.1, 30, 1, 4.567, 30, 6.033, 30, 7.5, 30, 1, 7.678, 30, 7.856, -30, 8.033, -30, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, -30, 0, 2.3, -30, 1, 2.567, -30, 2.833, 30, 3.1, 30, 1, 3.378, 30, 3.656, 30, 3.933, 30, 1, 4, 30, 4.067, -30, 4.133, -30, 1, 4.222, -30, 4.311, -30, 4.4, -30, 1, 4.422, -30, 4.444, 30, 4.467, 30, 1, 4.733, 30, 5, 30, 5.267, 30, 1, 5.333, 30, 5.4, -30, 5.467, -30, 1, 5.556, -30, 5.644, -30, 5.733, -30, 1, 5.756, -30, 5.778, 30, 5.8, 30, 1, 6.111, 30, 6.422, 30, 6.733, 30, 1, 6.8, 30, 6.867, -30, 6.933, -30, 1, 7.122, -30, 7.311, -30, 7.5, -30, 1, 7.644, -30, 7.789, -30, 7.933, -30, 1, 8.067, -30, 8.2, 30, 8.333, 30, 1, 8.489, 30, 8.644, 30, 8.8, 30, 1, 8.856, 30, 8.911, -30, 8.967, -30, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, -30, 0, 3.1, -30, 1, 3.444, -30, 3.789, 30, 4.133, 30, 1, 4.222, 30, 4.311, -30, 4.4, -30, 1, 4.789, -30, 5.178, 30, 5.567, 30, 1, 5.622, 30, 5.678, -30, 5.733, -30, 1, 6.156, -30, 6.578, 30, 7, 30, 1, 7.167, 30, 7.333, 30, 7.5, 30, 1, 7.644, 30, 7.789, -30, 7.933, -30, 1, 8.278, -30, 8.622, 30, 8.967, 30, 0, 9.1, 30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 4.633, 0, 1, 4.811, 0, 4.989, -4, 5.167, -4, 1, 5.344, -4, 5.522, 3, 5.7, 3, 1, 5.867, 3, 6.033, -5, 6.2, -5, 1, 6.344, -5, 6.489, 4, 6.633, 4, 1, 6.8, 4, 6.967, 0, 7.133, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, -4, 0, 1.367, -4, 1, 1.456, -4, 1.544, -3, 1.633, -3, 1, 1.744, -3, 1.856, -4, 1.967, -4, 1, 2.056, -4, 2.144, -3, 2.233, -3, 1, 2.311, -3, 2.389, -4, 2.467, -4, 1, 2.556, -4, 2.644, -3, 2.733, -3, 1, 2.833, -3, 2.933, -4, 3.033, -4, 1, 3.122, -4, 3.211, -3.789, 3.3, -3, 1, 3.733, 0.848, 4.167, 3, 4.6, 3, 1, 4.922, 3, 5.244, -3, 5.567, -3, 1, 5.889, -3, 6.211, 4, 6.533, 4, 1, 6.867, 4, 7.2, -5, 7.533, -5, 0, 9.1, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.778, 0, 1.556, 0, 2.333, 0, 1, 2.744, 0, 3.156, -6, 3.567, -6, 1, 3.933, -6, 4.3, 5, 4.667, 5, 1, 5.478, 5, 6.289, 0, 7.1, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.433, 0, 0.867, 1, 1.3, 1, 1, 1.656, 1, 2.011, 0, 2.367, 0, 1, 2.767, 0, 3.167, 2, 3.567, 2, 1, 3.922, 2, 4.278, -9, 4.633, -9, 1, 4.811, -9, 4.989, 8, 5.167, 8, 1, 5.344, 8, 5.522, -6, 5.7, -6, 1, 5.867, -6, 6.033, 8, 6.2, 8, 1, 6.344, 8, 6.489, -5, 6.633, -5, 1, 6.789, -5, 6.944, -1.373, 7.1, -1, 1, 7.767, 0.598, 8.433, 1, 9.1, 1]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.433, -1, 0.867, 8, 1.3, 8, 1, 1.656, 8, 2.011, 8.406, 2.367, 4, 1, 2.767, -0.957, 3.167, -16, 3.567, -16, 1, 3.922, -16, 4.278, -13.425, 4.633, -10, 1, 4.811, -8.288, 4.989, -6.888, 5.167, -6, 1, 5.344, -5.112, 5.522, -5, 5.7, -5, 1, 5.867, -5, 6.033, -10, 6.2, -10, 1, 6.344, -10, 6.489, -10, 6.633, -10, 1, 6.789, -10, 6.944, -10, 7.1, -10, 1, 7.767, -10, 8.433, 6, 9.1, 6]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.144, 0, 0.156, 0, 0.167, 0, 1, 0.233, 0, 0.3, 0, 0.367, 0, 1, 0.378, 0, 0.389, 0, 0.4, 0, 1, 0.467, 0, 0.533, 0, 0.6, 0, 1, 0.744, 0, 0.889, 0, 1.033, 0, 1, 1.056, 0, 1.078, 30, 1.1, 30, 1, 1.133, 30, 1.167, 30, 1.2, 30, 1, 1.233, 30, 1.267, -30, 1.3, -30, 1, 1.344, -30, 1.389, -30, 1.433, -30, 1, 1.467, -30, 1.5, 30, 1.533, 30, 1, 1.556, 30, 1.578, 30, 1.6, 30, 1, 1.678, 30, 1.756, -13.007, 1.833, -13.007, 1, 1.911, -13.007, 1.989, 5.334, 2.067, 5.334, 1, 2.133, 5.334, 2.2, -1.518, 2.267, -1.518, 1, 2.311, -1.518, 2.356, 30, 2.4, 30, 1, 2.422, 30, 2.444, 30, 2.467, 30, 1, 2.511, 30, 2.556, -30, 2.6, -30, 1, 2.644, -30, 2.689, -30, 2.733, -30, 1, 2.8, -30, 2.867, 11.154, 2.933, 11.154, 1, 3, 11.154, 3.067, 0.371, 3.133, 0.371, 1, 3.156, 0.371, 3.178, 0.474, 3.2, 0.474, 1, 3.289, 0.474, 3.378, 0.22, 3.467, 0.22, 1, 3.533, 0.22, 3.6, 0.288, 3.667, 0.288, 1, 3.833, 0.288, 4, -0.033, 4.167, -0.033, 1, 4.256, -0.033, 4.344, 0.014, 4.433, 0.014, 1, 4.511, 0.014, 4.589, -0.006, 4.667, -0.006, 1, 4.756, -0.006, 4.844, 0.002, 4.933, 0.002, 1, 5.011, 0.002, 5.089, -0.001, 5.167, -0.001, 1, 5.267, -0.001, 5.367, 25.058, 5.467, 25.058, 1, 5.544, 25.058, 5.622, -27.604, 5.7, -27.604, 1, 5.789, -27.604, 5.878, 10.747, 5.967, 10.747, 1, 6.056, 10.747, 6.144, -6.173, 6.233, -6.173, 1, 6.311, -6.173, 6.389, 0.494, 6.467, 0.494, 1, 6.556, 0.494, 6.644, -2.096, 6.733, -2.096, 1, 6.811, -2.096, 6.889, -0.588, 6.967, -0.588, 1, 6.989, -0.588, 7.011, -0.627, 7.033, -0.627, 1, 7.044, -0.627, 7.056, -0.623, 7.067, -0.623, 1, 7.1, -0.623, 7.133, -0.692, 7.167, -0.692, 1, 7.256, -0.692, 7.344, -0.209, 7.433, -0.209, 1, 7.456, -0.209, 7.478, -0.241, 7.5, -0.241, 1, 7.511, -0.241, 7.522, -0.228, 7.533, -0.228, 1, 7.556, -0.228, 7.578, -0.261, 7.6, -0.261, 1, 7.622, -0.261, 7.644, -0.208, 7.667, -0.208, 1, 7.678, -0.208, 7.689, -0.234, 7.7, -0.234, 1, 8.011, -0.234, 8.322, 0.084, 8.633, 0.084, 1, 8.644, 0.084, 8.656, 0.079, 8.667, 0.079, 1, 8.811, 0.079, 8.956, 0.116, 9.1, 0.121]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.144, 0, 0.156, 0, 0.167, 0, 1, 0.233, 0, 0.3, 0, 0.367, 0, 1, 0.378, 0, 0.389, 0, 0.4, 0, 1, 0.467, 0, 0.533, 0, 0.6, 0, 1, 0.744, 0, 0.889, 0, 1.033, 0, 1, 1.056, 0, 1.078, 30, 1.1, 30, 1, 1.133, 30, 1.167, 30, 1.2, 30, 1, 1.233, 30, 1.267, -30, 1.3, -30, 1, 1.344, -30, 1.389, -30, 1.433, -30, 1, 1.467, -30, 1.5, 30, 1.533, 30, 1, 1.556, 30, 1.578, 30, 1.6, 30, 1, 1.678, 30, 1.756, -13.007, 1.833, -13.007, 1, 1.911, -13.007, 1.989, 5.334, 2.067, 5.334, 1, 2.133, 5.334, 2.2, -1.518, 2.267, -1.518, 1, 2.311, -1.518, 2.356, 30, 2.4, 30, 1, 2.422, 30, 2.444, 30, 2.467, 30, 1, 2.511, 30, 2.556, -30, 2.6, -30, 1, 2.644, -30, 2.689, -30, 2.733, -30, 1, 2.8, -30, 2.867, 11.154, 2.933, 11.154, 1, 3, 11.154, 3.067, 0.371, 3.133, 0.371, 1, 3.156, 0.371, 3.178, 0.474, 3.2, 0.474, 1, 3.289, 0.474, 3.378, 0.22, 3.467, 0.22, 1, 3.533, 0.22, 3.6, 0.288, 3.667, 0.288, 1, 3.833, 0.288, 4, -0.033, 4.167, -0.033, 1, 4.256, -0.033, 4.344, 0.014, 4.433, 0.014, 1, 4.511, 0.014, 4.589, -0.006, 4.667, -0.006, 1, 4.756, -0.006, 4.844, 0.002, 4.933, 0.002, 1, 5.011, 0.002, 5.089, -0.001, 5.167, -0.001, 1, 5.267, -0.001, 5.367, 25.058, 5.467, 25.058, 1, 5.544, 25.058, 5.622, -27.604, 5.7, -27.604, 1, 5.789, -27.604, 5.878, 10.747, 5.967, 10.747, 1, 6.056, 10.747, 6.144, -6.173, 6.233, -6.173, 1, 6.311, -6.173, 6.389, 0.494, 6.467, 0.494, 1, 6.556, 0.494, 6.644, -2.096, 6.733, -2.096, 1, 6.811, -2.096, 6.889, -0.588, 6.967, -0.588, 1, 6.989, -0.588, 7.011, -0.627, 7.033, -0.627, 1, 7.044, -0.627, 7.056, -0.623, 7.067, -0.623, 1, 7.1, -0.623, 7.133, -0.692, 7.167, -0.692, 1, 7.256, -0.692, 7.344, -0.209, 7.433, -0.209, 1, 7.456, -0.209, 7.478, -0.241, 7.5, -0.241, 1, 7.511, -0.241, 7.522, -0.228, 7.533, -0.228, 1, 7.556, -0.228, 7.578, -0.261, 7.6, -0.261, 1, 7.622, -0.261, 7.644, -0.208, 7.667, -0.208, 1, 7.678, -0.208, 7.689, -0.234, 7.7, -0.234, 1, 8.011, -0.234, 8.322, 0.084, 8.633, 0.084, 1, 8.644, 0.084, 8.656, 0.079, 8.667, 0.079, 1, 8.811, 0.079, 8.956, 0.116, 9.1, 0.121]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.067, 1, 1.133, 0, 1.2, 0, 1, 1.267, 0, 1.333, 1, 1.4, 1, 1, 1.678, 1, 1.956, 1, 2.233, 1, 1, 2.333, 1, 2.433, 0, 2.533, 0, 1, 2.722, 0, 2.911, 0.437, 3.1, 0.5, 1, 3.4, 0.601, 3.7, 0.6, 4, 0.6, 1, 4.422, 0.6, 4.844, 0.6, 5.267, 0.6, 1, 5.378, 0.6, 5.489, 0, 5.6, 0, 1, 5.733, 0, 5.867, 0, 6, 0, 1, 6.389, 0, 6.778, 0.3, 7.167, 0.5, 1, 7.811, 0.831, 8.456, 0.9, 9.1, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.7, 0, 2.4, 0, 3.1, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.067, 1, 1.133, 0, 1.2, 0, 1, 1.267, 0, 1.333, 1, 1.4, 1, 1, 1.678, 1, 1.956, 1, 2.233, 1, 1, 2.333, 1, 2.433, 0, 2.533, 0, 1, 2.722, 0, 2.911, 0.437, 3.1, 0.5, 1, 3.4, 0.601, 3.7, 0.6, 4, 0.6, 1, 4.422, 0.6, 4.844, 0.6, 5.267, 0.6, 1, 5.378, 0.6, 5.489, 0, 5.6, 0, 1, 5.733, 0, 5.867, 0, 6, 0, 1, 6.389, 0, 6.778, 0.3, 7.167, 0.5, 1, 7.811, 0.831, 8.456, 0.9, 9.1, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0.6, 0, 0.633, 0.6, 1, 1.144, 0.6, 1.656, 0, 2.167, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0.7, 0, 0.633, 0.7, 1, 1.144, 0.7, 1.656, 0, 2.167, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -0.8, 1, 0.311, -0.8, 0.622, -1, 0.933, -1, 1, 3.178, -1, 5.422, -0.8, 7.667, -0.8, 0, 9.1, -0.8]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -0.8, 1, 0.311, -0.8, 0.622, -1, 0.933, -1, 1, 3.178, -1, 5.422, -0.8, 7.667, -0.8, 0, 9.1, -0.8]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.4, 1, 0.311, -0.4, 0.622, -0.4, 0.933, -0.4, 1, 1.367, -0.4, 1.8, -0.4, 2.233, -0.4, 1, 2.689, -0.4, 3.144, -1, 3.6, -1, 1, 3.833, -1, 4.067, -0.5, 4.3, -0.5, 1, 4.656, -0.5, 5.011, -1, 5.367, -1, 1, 5.567, -1, 5.767, -0.7, 5.967, -0.7, 1, 6.156, -0.7, 6.344, -1, 6.533, -1, 1, 6.911, -1, 7.289, -0.4, 7.667, -0.4, 0, 9.1, -0.4]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 1.633, 0, 2.333, 0, 3.033, 0, 1, 3.222, 0, 3.411, 1, 3.6, 1, 1, 3.833, 1, 4.067, 0.5, 4.3, 0.5, 1, 4.656, 0.5, 5.011, 1, 5.367, 1, 1, 5.567, 1, 5.767, 0.9, 5.967, 0.9, 1, 6.156, 0.9, 6.344, 1, 6.533, 1, 1, 6.911, 1, 7.289, 0, 7.667, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, -10, 0, 0.7, -10, 1, 0.8, -10, 0.9, -30, 1, -30, 1, 1.111, -30, 1.222, -10, 1.333, -10, 1, 1.433, -10, 1.533, -30, 1.633, -30, 1, 1.733, -30, 1.833, -10, 1.933, -10, 1, 2.033, -10, 2.133, -30, 2.233, -30, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, -30, 1, 0.933, -30, 1.867, -30, 2.8, -30, 1, 3.056, -30, 3.311, 30, 3.567, 30, 1, 4.956, 30, 6.344, 30, 7.733, 30, 1, 7.978, 30, 8.222, -30, 8.467, -30, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, -30, 0, 2.1, -30, 1, 2.589, -30, 3.078, -21.543, 3.567, 1, 1, 3.933, 17.908, 4.3, 30, 4.667, 30, 1, 5.111, 30, 5.556, 12, 6, 12, 1, 6.589, 12, 7.178, 30, 7.767, 30, 1, 7.967, 30, 8.167, -30, 8.367, -30, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -30, 1, 0.944, -30, 1.889, -30, 2.833, -30, 1, 3.044, -30, 3.256, 30, 3.467, 30, 1, 4.889, 30, 6.311, 30, 7.733, 30, 1, 7.989, 30, 8.244, -30, 8.5, -30, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -2, 1, 0.233, -2, 0.467, -8, 0.7, -8, 1, 0.933, -8, 1.167, 3, 1.4, 3, 0, 9.1, 3]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -30, 1, 0.544, -30, 1.089, -30, 1.633, -30, 1, 2.289, -30, 2.944, 30, 3.6, 30, 1, 4.878, 30, 6.156, 30, 7.433, 30, 1, 7.756, 30, 8.078, -30, 8.4, -30, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.344, 0, 0.689, 0, 1.033, 0, 1, 1.089, 0, 1.144, 17.962, 1.2, 17.962, 1, 1.267, 17.962, 1.333, -18.119, 1.4, -18.119, 1, 1.489, -18.119, 1.578, 3.863, 1.667, 3.863, 1, 1.767, 3.863, 1.867, -0.739, 1.967, -0.739, 1, 2.133, -0.739, 2.3, 14.92, 2.467, 14.92, 1, 2.567, 14.92, 2.667, -7.773, 2.767, -7.773, 1, 2.922, -7.773, 3.078, 0.651, 3.233, 0.651, 1, 3.322, 0.651, 3.411, -0.414, 3.5, -0.414, 1, 3.622, -0.414, 3.744, 0.067, 3.867, 0.067, 1, 3.989, 0.067, 4.111, -0.01, 4.233, -0.01, 1, 4.344, -0.01, 4.456, 0.002, 4.567, 0.002, 1, 4.667, 0.002, 4.767, 0, 4.867, 0, 1, 4.967, 0, 5.067, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 1, 5.367, 0, 5.433, 9.282, 5.5, 9.282, 1, 5.589, 9.282, 5.678, -3.582, 5.767, -3.582, 1, 5.867, -3.582, 5.967, 0.631, 6.067, 0.631, 1, 6.2, 0.631, 6.333, -1.386, 6.467, -1.386, 1, 6.478, -1.386, 6.489, -1.384, 6.5, -1.384, 1, 6.511, -1.384, 6.522, -1.387, 6.533, -1.387, 1, 6.544, -1.387, 6.556, -1.377, 6.567, -1.377, 1, 6.578, -1.377, 6.589, -1.384, 6.6, -1.384, 1, 6.611, -1.384, 6.622, -1.381, 6.633, -1.381, 1, 6.711, -1.381, 6.789, -1.474, 6.867, -1.474, 1, 6.878, -1.474, 6.889, -1.468, 6.9, -1.468, 1, 6.911, -1.468, 6.922, -1.471, 6.933, -1.471, 1, 7.656, -1.471, 8.378, -0.1, 9.1, 0.021]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.856, 0, 1.711, 0, 2.567, 0, 1, 2.911, 0, 3.256, -5.536, 3.6, -5.536, 1, 4.056, -5.536, 4.511, 10.078, 4.967, 10.078, 1, 5.133, 10.078, 5.3, -9.55, 5.467, -9.55, 1, 5.633, -9.55, 5.8, 19.688, 5.967, 19.688, 1, 6.133, 19.688, 6.3, -23.236, 6.467, -23.236, 1, 6.6, -23.236, 6.733, 18.528, 6.867, 18.528, 1, 7.011, 18.528, 7.156, -8.735, 7.3, -8.735, 1, 7.444, -8.735, 7.589, 2.958, 7.733, 2.958, 1, 7.867, 2.958, 8, -1.01, 8.133, -1.01, 1, 8.278, -1.01, 8.422, 0.27, 8.567, 0.27, 1, 8.7, 0.27, 8.833, -0.145, 8.967, -0.145, 1, 9.011, -0.145, 9.056, -0.081, 9.1, -0.038]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.856, 0, 1.711, 0, 2.567, 0, 1, 2.911, 0, 3.256, -5.536, 3.6, -5.536, 1, 4.056, -5.536, 4.511, 10.078, 4.967, 10.078, 1, 5.133, 10.078, 5.3, -9.55, 5.467, -9.55, 1, 5.633, -9.55, 5.8, 19.688, 5.967, 19.688, 1, 6.133, 19.688, 6.3, -23.236, 6.467, -23.236, 1, 6.6, -23.236, 6.733, 18.528, 6.867, 18.528, 1, 7.011, 18.528, 7.156, -8.735, 7.3, -8.735, 1, 7.444, -8.735, 7.589, 2.958, 7.733, 2.958, 1, 7.867, 2.958, 8, -1.01, 8.133, -1.01, 1, 8.278, -1.01, 8.422, 0.27, 8.567, 0.27, 1, 8.7, 0.27, 8.833, -0.145, 8.967, -0.145, 1, 9.011, -0.145, 9.056, -0.081, 9.1, -0.038]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.856, 0, 1.711, 0, 2.567, 0, 1, 2.911, 0, 3.256, -5.536, 3.6, -5.536, 1, 4.056, -5.536, 4.511, 10.078, 4.967, 10.078, 1, 5.133, 10.078, 5.3, -9.55, 5.467, -9.55, 1, 5.633, -9.55, 5.8, 19.688, 5.967, 19.688, 1, 6.133, 19.688, 6.3, -23.236, 6.467, -23.236, 1, 6.6, -23.236, 6.733, 18.528, 6.867, 18.528, 1, 7.011, 18.528, 7.156, -8.735, 7.3, -8.735, 1, 7.444, -8.735, 7.589, 2.958, 7.733, 2.958, 1, 7.867, 2.958, 8, -1.01, 8.133, -1.01, 1, 8.278, -1.01, 8.422, 0.27, 8.567, 0.27, 1, 8.7, 0.27, 8.833, -0.145, 8.967, -0.145, 1, 9.011, -0.145, 9.056, -0.081, 9.1, -0.038]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, -0.558, 0.733, -0.558, 1, 0.856, -0.558, 0.978, 0.172, 1.1, 0.172, 1, 1.2, 0.172, 1.3, -0.017, 1.4, -0.017, 1, 1.522, -0.017, 1.644, 0.093, 1.767, 0.093, 1, 2.356, 0.093, 2.944, -2.144, 3.533, -2.144, 1, 3.878, -2.144, 4.222, 1.712, 4.567, 1.712, 1, 4.689, 1.712, 4.811, -0.414, 4.933, -0.414, 1, 5.056, -0.414, 5.178, 3.082, 5.3, 3.082, 1, 5.422, 3.082, 5.544, 0.541, 5.667, 0.541, 1, 5.767, 0.541, 5.867, 1.443, 5.967, 1.443, 1, 6.133, 1.443, 6.3, -1.437, 6.467, -1.437, 1, 6.589, -1.437, 6.711, 1.916, 6.833, 1.916, 1, 6.978, 1.916, 7.122, -1.196, 7.267, -1.196, 1, 7.378, -1.196, 7.489, 0.355, 7.6, 0.355, 1, 7.722, 0.355, 7.844, -0.167, 7.967, -0.167, 1, 8.089, -0.167, 8.211, 0.035, 8.333, 0.035, 1, 8.444, 0.035, 8.556, -0.021, 8.667, -0.021, 1, 8.8, -0.021, 8.933, 0.01, 9.067, 0.01, 1, 9.078, 0.01, 9.089, 0.01, 9.1, 0.01]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, -0.558, 0.733, -0.558, 1, 0.856, -0.558, 0.978, 0.172, 1.1, 0.172, 1, 1.2, 0.172, 1.3, -0.017, 1.4, -0.017, 1, 1.522, -0.017, 1.644, 0.093, 1.767, 0.093, 1, 2.356, 0.093, 2.944, -2.144, 3.533, -2.144, 1, 3.878, -2.144, 4.222, 1.712, 4.567, 1.712, 1, 4.689, 1.712, 4.811, -0.414, 4.933, -0.414, 1, 5.056, -0.414, 5.178, 3.082, 5.3, 3.082, 1, 5.422, 3.082, 5.544, 0.541, 5.667, 0.541, 1, 5.767, 0.541, 5.867, 1.443, 5.967, 1.443, 1, 6.133, 1.443, 6.3, -1.437, 6.467, -1.437, 1, 6.589, -1.437, 6.711, 1.916, 6.833, 1.916, 1, 6.978, 1.916, 7.122, -1.196, 7.267, -1.196, 1, 7.378, -1.196, 7.489, 0.355, 7.6, 0.355, 1, 7.722, 0.355, 7.844, -0.167, 7.967, -0.167, 1, 8.089, -0.167, 8.211, 0.035, 8.333, 0.035, 1, 8.444, 0.035, 8.556, -0.021, 8.667, -0.021, 1, 8.8, -0.021, 8.933, 0.01, 9.067, 0.01, 1, 9.078, 0.01, 9.089, 0.01, 9.1, 0.01]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, 0.781, 0.733, 0.781, 1, 0.811, 0.781, 0.889, -0.876, 0.967, -0.876, 1, 1.078, -0.876, 1.189, 0.292, 1.3, 0.292, 1, 1.4, 0.292, 1.5, -0.101, 1.6, -0.101, 1, 1.722, -0.101, 1.844, 0.141, 1.967, 0.141, 1, 2.056, 0.141, 2.144, 0.068, 2.233, 0.068, 1, 2.267, 0.068, 2.3, 0.194, 2.333, 0.194, 1, 2.4, 0.194, 2.467, 0.056, 2.533, 0.056, 1, 2.644, 0.056, 2.756, 0.368, 2.867, 0.368, 1, 3.189, 0.368, 3.511, -0.814, 3.833, -0.814, 1, 3.978, -0.814, 4.122, 0.352, 4.267, 0.352, 1, 4.322, 0.352, 4.378, 0.273, 4.433, 0.273, 1, 4.556, 0.273, 4.678, 1.206, 4.8, 1.206, 1, 4.933, 1.206, 5.067, -3.495, 5.2, -3.495, 1, 5.3, -3.495, 5.4, 2.421, 5.5, 2.421, 1, 5.622, 2.421, 5.744, -1.092, 5.867, -1.092, 1, 6.011, -1.092, 6.156, 1.556, 6.3, 1.556, 1, 6.433, 1.556, 6.567, -3.19, 6.7, -3.19, 1, 6.822, -3.19, 6.944, 2.787, 7.067, 2.787, 1, 7.2, 2.787, 7.333, -1.73, 7.467, -1.73, 1, 7.578, -1.73, 7.689, 0.627, 7.8, 0.627, 1, 7.922, 0.627, 8.044, -0.324, 8.167, -0.324, 1, 8.278, -0.324, 8.389, 0.066, 8.5, 0.066, 1, 8.611, 0.066, 8.722, -0.051, 8.833, -0.051, 1, 8.922, -0.051, 9.011, -0.026, 9.1, -0.007]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, -0.558, 0.733, -0.558, 1, 0.856, -0.558, 0.978, 0.172, 1.1, 0.172, 1, 1.2, 0.172, 1.3, -0.017, 1.4, -0.017, 1, 1.522, -0.017, 1.644, 0.093, 1.767, 0.093, 1, 2.356, 0.093, 2.944, -2.144, 3.533, -2.144, 1, 3.878, -2.144, 4.222, 1.712, 4.567, 1.712, 1, 4.689, 1.712, 4.811, -0.414, 4.933, -0.414, 1, 5.056, -0.414, 5.178, 3.082, 5.3, 3.082, 1, 5.422, 3.082, 5.544, 0.541, 5.667, 0.541, 1, 5.767, 0.541, 5.867, 1.443, 5.967, 1.443, 1, 6.133, 1.443, 6.3, -1.437, 6.467, -1.437, 1, 6.589, -1.437, 6.711, 1.916, 6.833, 1.916, 1, 6.978, 1.916, 7.122, -1.196, 7.267, -1.196, 1, 7.378, -1.196, 7.489, 0.355, 7.6, 0.355, 1, 7.722, 0.355, 7.844, -0.167, 7.967, -0.167, 1, 8.089, -0.167, 8.211, 0.035, 8.333, 0.035, 1, 8.444, 0.035, 8.556, -0.021, 8.667, -0.021, 1, 8.8, -0.021, 8.933, 0.01, 9.067, 0.01, 1, 9.078, 0.01, 9.089, 0.01, 9.1, 0.01]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, 0.558, 0.733, 0.558, 1, 0.811, 0.558, 0.889, -0.625, 0.967, -0.625, 1, 1.078, -0.625, 1.189, 0.209, 1.3, 0.209, 1, 1.4, 0.209, 1.5, -0.072, 1.6, -0.072, 1, 1.722, -0.072, 1.844, 0.1, 1.967, 0.1, 1, 2.056, 0.1, 2.144, 0.049, 2.233, 0.049, 1, 2.267, 0.049, 2.3, 0.139, 2.333, 0.139, 1, 2.4, 0.139, 2.467, 0.04, 2.533, 0.04, 1, 2.644, 0.04, 2.756, 0.263, 2.867, 0.263, 1, 3.189, 0.263, 3.511, -0.582, 3.833, -0.582, 1, 3.978, -0.582, 4.122, 0.251, 4.267, 0.251, 1, 4.322, 0.251, 4.378, 0.195, 4.433, 0.195, 1, 4.556, 0.195, 4.678, 0.861, 4.8, 0.861, 1, 4.933, 0.861, 5.067, -2.496, 5.2, -2.496, 1, 5.3, -2.496, 5.4, 1.729, 5.5, 1.729, 1, 5.622, 1.729, 5.744, -0.78, 5.867, -0.78, 1, 6.011, -0.78, 6.156, 1.111, 6.3, 1.111, 1, 6.433, 1.111, 6.567, -2.279, 6.7, -2.279, 1, 6.822, -2.279, 6.944, 1.991, 7.067, 1.991, 1, 7.2, 1.991, 7.333, -1.235, 7.467, -1.235, 1, 7.578, -1.235, 7.689, 0.448, 7.8, 0.448, 1, 7.922, 0.448, 8.044, -0.231, 8.167, -0.231, 1, 8.278, -0.231, 8.389, 0.047, 8.5, 0.047, 1, 8.611, 0.047, 8.722, -0.037, 8.833, -0.037, 1, 8.922, -0.037, 9.011, -0.019, 9.1, -0.005]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, -0.558, 0.733, -0.558, 1, 0.856, -0.558, 0.978, 0.172, 1.1, 0.172, 1, 1.2, 0.172, 1.3, -0.017, 1.4, -0.017, 1, 1.522, -0.017, 1.644, 0.093, 1.767, 0.093, 1, 2.356, 0.093, 2.944, -2.144, 3.533, -2.144, 1, 3.878, -2.144, 4.222, 1.712, 4.567, 1.712, 1, 4.689, 1.712, 4.811, -0.414, 4.933, -0.414, 1, 5.056, -0.414, 5.178, 3.082, 5.3, 3.082, 1, 5.422, 3.082, 5.544, 0.541, 5.667, 0.541, 1, 5.767, 0.541, 5.867, 1.443, 5.967, 1.443, 1, 6.133, 1.443, 6.3, -1.437, 6.467, -1.437, 1, 6.589, -1.437, 6.711, 1.916, 6.833, 1.916, 1, 6.978, 1.916, 7.122, -1.196, 7.267, -1.196, 1, 7.378, -1.196, 7.489, 0.355, 7.6, 0.355, 1, 7.722, 0.355, 7.844, -0.167, 7.967, -0.167, 1, 8.089, -0.167, 8.211, 0.035, 8.333, 0.035, 1, 8.444, 0.035, 8.556, -0.021, 8.667, -0.021, 1, 8.8, -0.021, 8.933, 0.01, 9.067, 0.01, 1, 9.078, 0.01, 9.089, 0.01, 9.1, 0.01]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, 0.558, 0.733, 0.558, 1, 0.811, 0.558, 0.889, -0.625, 0.967, -0.625, 1, 1.078, -0.625, 1.189, 0.209, 1.3, 0.209, 1, 1.4, 0.209, 1.5, -0.072, 1.6, -0.072, 1, 1.722, -0.072, 1.844, 0.1, 1.967, 0.1, 1, 2.056, 0.1, 2.144, 0.049, 2.233, 0.049, 1, 2.267, 0.049, 2.3, 0.139, 2.333, 0.139, 1, 2.4, 0.139, 2.467, 0.04, 2.533, 0.04, 1, 2.644, 0.04, 2.756, 0.263, 2.867, 0.263, 1, 3.189, 0.263, 3.511, -0.582, 3.833, -0.582, 1, 3.978, -0.582, 4.122, 0.251, 4.267, 0.251, 1, 4.322, 0.251, 4.378, 0.195, 4.433, 0.195, 1, 4.556, 0.195, 4.678, 0.861, 4.8, 0.861, 1, 4.933, 0.861, 5.067, -2.496, 5.2, -2.496, 1, 5.3, -2.496, 5.4, 1.729, 5.5, 1.729, 1, 5.622, 1.729, 5.744, -0.78, 5.867, -0.78, 1, 6.011, -0.78, 6.156, 1.111, 6.3, 1.111, 1, 6.433, 1.111, 6.567, -2.279, 6.7, -2.279, 1, 6.822, -2.279, 6.944, 1.991, 7.067, 1.991, 1, 7.2, 1.991, 7.333, -1.235, 7.467, -1.235, 1, 7.578, -1.235, 7.689, 0.448, 7.8, 0.448, 1, 7.922, 0.448, 8.044, -0.231, 8.167, -0.231, 1, 8.278, -0.231, 8.389, 0.047, 8.5, 0.047, 1, 8.611, 0.047, 8.722, -0.037, 8.833, -0.037, 1, 8.922, -0.037, 9.011, -0.019, 9.1, -0.005]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, -0.669, 0.733, -0.669, 1, 0.856, -0.669, 0.978, 0.206, 1.1, 0.206, 1, 1.2, 0.206, 1.3, -0.02, 1.4, -0.02, 1, 1.522, -0.02, 1.644, 0.112, 1.767, 0.112, 1, 2.356, 0.112, 2.944, -2.573, 3.533, -2.573, 1, 3.878, -2.573, 4.222, 2.055, 4.567, 2.055, 1, 4.689, 2.055, 4.811, -0.497, 4.933, -0.497, 1, 5.056, -0.497, 5.178, 3.698, 5.3, 3.698, 1, 5.422, 3.698, 5.544, 0.65, 5.667, 0.65, 1, 5.767, 0.65, 5.867, 1.732, 5.967, 1.732, 1, 6.133, 1.732, 6.3, -1.724, 6.467, -1.724, 1, 6.589, -1.724, 6.711, 2.299, 6.833, 2.299, 1, 6.978, 2.299, 7.122, -1.435, 7.267, -1.435, 1, 7.378, -1.435, 7.489, 0.426, 7.6, 0.426, 1, 7.722, 0.426, 7.844, -0.2, 7.967, -0.2, 1, 8.089, -0.2, 8.211, 0.043, 8.333, 0.043, 1, 8.444, 0.043, 8.556, -0.025, 8.667, -0.025, 1, 8.8, -0.025, 8.933, 0.012, 9.067, 0.012, 1, 9.078, 0.012, 9.089, 0.012, 9.1, 0.012]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, 0.669, 0.733, 0.669, 1, 0.811, 0.669, 0.889, -0.75, 0.967, -0.75, 1, 1.078, -0.75, 1.189, 0.25, 1.3, 0.25, 1, 1.4, 0.25, 1.5, -0.087, 1.6, -0.087, 1, 1.722, -0.087, 1.844, 0.12, 1.967, 0.12, 1, 2.056, 0.12, 2.144, 0.058, 2.233, 0.058, 1, 2.267, 0.058, 2.3, 0.167, 2.333, 0.167, 1, 2.4, 0.167, 2.467, 0.048, 2.533, 0.048, 1, 2.644, 0.048, 2.756, 0.316, 2.867, 0.316, 1, 3.189, 0.316, 3.511, -0.698, 3.833, -0.698, 1, 3.978, -0.698, 4.122, 0.302, 4.267, 0.302, 1, 4.322, 0.302, 4.378, 0.234, 4.433, 0.234, 1, 4.556, 0.234, 4.678, 1.033, 4.8, 1.033, 1, 4.933, 1.033, 5.067, -2.996, 5.2, -2.996, 1, 5.3, -2.996, 5.4, 2.075, 5.5, 2.075, 1, 5.622, 2.075, 5.744, -0.936, 5.867, -0.936, 1, 6.011, -0.936, 6.156, 1.333, 6.3, 1.333, 1, 6.433, 1.333, 6.567, -2.734, 6.7, -2.734, 1, 6.822, -2.734, 6.944, 2.389, 7.067, 2.389, 1, 7.2, 2.389, 7.333, -1.482, 7.467, -1.482, 1, 7.578, -1.482, 7.689, 0.537, 7.8, 0.537, 1, 7.922, 0.537, 8.044, -0.278, 8.167, -0.278, 1, 8.278, -0.278, 8.389, 0.057, 8.5, 0.057, 1, 8.611, 0.057, 8.722, -0.044, 8.833, -0.044, 1, 8.922, -0.044, 9.011, -0.023, 9.1, -0.006]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, -0.669, 0.733, -0.669, 1, 0.856, -0.669, 0.978, 0.206, 1.1, 0.206, 1, 1.2, 0.206, 1.3, -0.02, 1.4, -0.02, 1, 1.522, -0.02, 1.644, 0.112, 1.767, 0.112, 1, 2.356, 0.112, 2.944, -2.573, 3.533, -2.573, 1, 3.878, -2.573, 4.222, 2.055, 4.567, 2.055, 1, 4.689, 2.055, 4.811, -0.497, 4.933, -0.497, 1, 5.056, -0.497, 5.178, 3.698, 5.3, 3.698, 1, 5.422, 3.698, 5.544, 0.65, 5.667, 0.65, 1, 5.767, 0.65, 5.867, 1.732, 5.967, 1.732, 1, 6.133, 1.732, 6.3, -1.724, 6.467, -1.724, 1, 6.589, -1.724, 6.711, 2.299, 6.833, 2.299, 1, 6.978, 2.299, 7.122, -1.435, 7.267, -1.435, 1, 7.378, -1.435, 7.489, 0.426, 7.6, 0.426, 1, 7.722, 0.426, 7.844, -0.2, 7.967, -0.2, 1, 8.089, -0.2, 8.211, 0.043, 8.333, 0.043, 1, 8.444, 0.043, 8.556, -0.025, 8.667, -0.025, 1, 8.8, -0.025, 8.933, 0.012, 9.067, 0.012, 1, 9.078, 0.012, 9.089, 0.012, 9.1, 0.012]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, 0.669, 0.733, 0.669, 1, 0.811, 0.669, 0.889, -0.75, 0.967, -0.75, 1, 1.078, -0.75, 1.189, 0.25, 1.3, 0.25, 1, 1.4, 0.25, 1.5, -0.087, 1.6, -0.087, 1, 1.722, -0.087, 1.844, 0.12, 1.967, 0.12, 1, 2.056, 0.12, 2.144, 0.058, 2.233, 0.058, 1, 2.267, 0.058, 2.3, 0.167, 2.333, 0.167, 1, 2.4, 0.167, 2.467, 0.048, 2.533, 0.048, 1, 2.644, 0.048, 2.756, 0.316, 2.867, 0.316, 1, 3.189, 0.316, 3.511, -0.698, 3.833, -0.698, 1, 3.978, -0.698, 4.122, 0.302, 4.267, 0.302, 1, 4.322, 0.302, 4.378, 0.234, 4.433, 0.234, 1, 4.556, 0.234, 4.678, 1.033, 4.8, 1.033, 1, 4.933, 1.033, 5.067, -2.996, 5.2, -2.996, 1, 5.3, -2.996, 5.4, 2.075, 5.5, 2.075, 1, 5.622, 2.075, 5.744, -0.936, 5.867, -0.936, 1, 6.011, -0.936, 6.156, 1.333, 6.3, 1.333, 1, 6.433, 1.333, 6.567, -2.734, 6.7, -2.734, 1, 6.822, -2.734, 6.944, 2.389, 7.067, 2.389, 1, 7.2, 2.389, 7.333, -1.482, 7.467, -1.482, 1, 7.578, -1.482, 7.689, 0.537, 7.8, 0.537, 1, 7.922, 0.537, 8.044, -0.278, 8.167, -0.278, 1, 8.278, -0.278, 8.389, 0.057, 8.5, 0.057, 1, 8.611, 0.057, 8.722, -0.044, 8.833, -0.044, 1, 8.922, -0.044, 9.011, -0.023, 9.1, -0.006]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.344, 0, 0.689, 0, 1.033, 0, 1, 1.089, 0, 1.144, 5.987, 1.2, 5.987, 1, 1.267, 5.987, 1.333, -6.04, 1.4, -6.04, 1, 1.489, -6.04, 1.578, 1.288, 1.667, 1.288, 1, 1.767, 1.288, 1.867, -0.246, 1.967, -0.246, 1, 2.133, -0.246, 2.3, 4.973, 2.467, 4.973, 1, 2.567, 4.973, 2.667, -2.591, 2.767, -2.591, 1, 2.922, -2.591, 3.078, 0.217, 3.233, 0.217, 1, 3.322, 0.217, 3.411, -0.138, 3.5, -0.138, 1, 3.622, -0.138, 3.744, 0.022, 3.867, 0.022, 1, 3.989, 0.022, 4.111, -0.003, 4.233, -0.003, 1, 4.344, -0.003, 4.456, 0.001, 4.567, 0.001, 1, 4.667, 0.001, 4.767, 0, 4.867, 0, 1, 4.967, 0, 5.067, 0, 5.167, 0, 1, 5.211, 0, 5.256, 0, 5.3, 0, 1, 5.367, 0, 5.433, 3.094, 5.5, 3.094, 1, 5.589, 3.094, 5.678, -1.194, 5.767, -1.194, 1, 5.867, -1.194, 5.967, 0.21, 6.067, 0.21, 1, 6.2, 0.21, 6.333, -0.462, 6.467, -0.462, 1, 6.478, -0.462, 6.489, -0.461, 6.5, -0.461, 1, 6.511, -0.461, 6.522, -0.462, 6.533, -0.462, 1, 6.544, -0.462, 6.556, -0.459, 6.567, -0.459, 1, 6.578, -0.459, 6.589, -0.461, 6.6, -0.461, 1, 6.611, -0.461, 6.622, -0.46, 6.633, -0.46, 1, 6.711, -0.46, 6.789, -0.491, 6.867, -0.491, 1, 6.878, -0.491, 6.889, -0.489, 6.9, -0.489, 1, 6.911, -0.489, 6.922, -0.49, 6.933, -0.49, 1, 7.656, -0.49, 8.378, -0.033, 9.1, 0.007]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, -0.892, 0.733, -0.892, 1, 0.856, -0.892, 0.978, 0.275, 1.1, 0.275, 1, 1.2, 0.275, 1.3, -0.027, 1.4, -0.027, 1, 1.522, -0.027, 1.644, 0.149, 1.767, 0.149, 1, 2.356, 0.149, 2.944, -3.431, 3.533, -3.431, 1, 3.878, -3.431, 4.222, 2.74, 4.567, 2.74, 1, 4.689, 2.74, 4.811, -0.663, 4.933, -0.663, 1, 5.056, -0.663, 5.178, 4.93, 5.3, 4.93, 1, 5.422, 4.93, 5.544, 0.866, 5.667, 0.866, 1, 5.767, 0.866, 5.867, 2.309, 5.967, 2.309, 1, 6.133, 2.309, 6.3, -2.299, 6.467, -2.299, 1, 6.589, -2.299, 6.711, 3.065, 6.833, 3.065, 1, 6.978, 3.065, 7.122, -1.913, 7.267, -1.913, 1, 7.378, -1.913, 7.489, 0.568, 7.6, 0.568, 1, 7.722, 0.568, 7.844, -0.267, 7.967, -0.267, 1, 8.089, -0.267, 8.211, 0.057, 8.333, 0.057, 1, 8.444, 0.057, 8.556, -0.034, 8.667, -0.034, 1, 8.8, -0.034, 8.933, 0.017, 9.067, 0.017, 1, 9.078, 0.017, 9.089, 0.017, 9.1, 0.016]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, 0.892, 0.733, 0.892, 1, 0.811, 0.892, 0.889, -1.001, 0.967, -1.001, 1, 1.078, -1.001, 1.189, 0.334, 1.3, 0.334, 1, 1.4, 0.334, 1.5, -0.116, 1.6, -0.116, 1, 1.722, -0.116, 1.844, 0.161, 1.967, 0.161, 1, 2.056, 0.161, 2.144, 0.078, 2.233, 0.078, 1, 2.267, 0.078, 2.3, 0.222, 2.333, 0.222, 1, 2.4, 0.222, 2.467, 0.064, 2.533, 0.064, 1, 2.644, 0.064, 2.756, 0.421, 2.867, 0.421, 1, 3.189, 0.421, 3.511, -0.931, 3.833, -0.931, 1, 3.978, -0.931, 4.122, 0.402, 4.267, 0.402, 1, 4.322, 0.402, 4.378, 0.312, 4.433, 0.312, 1, 4.556, 0.312, 4.678, 1.378, 4.8, 1.378, 1, 4.933, 1.378, 5.067, -3.994, 5.2, -3.994, 1, 5.3, -3.994, 5.4, 2.767, 5.5, 2.767, 1, 5.622, 2.767, 5.744, -1.248, 5.867, -1.248, 1, 6.011, -1.248, 6.156, 1.778, 6.3, 1.778, 1, 6.433, 1.778, 6.567, -3.646, 6.7, -3.646, 1, 6.822, -3.646, 6.944, 3.185, 7.067, 3.185, 1, 7.2, 3.185, 7.333, -1.977, 7.467, -1.977, 1, 7.578, -1.977, 7.689, 0.716, 7.8, 0.716, 1, 7.922, 0.716, 8.044, -0.37, 8.167, -0.37, 1, 8.278, -0.37, 8.389, 0.076, 8.5, 0.076, 1, 8.611, 0.076, 8.722, -0.059, 8.833, -0.059, 1, 8.922, -0.059, 9.011, -0.03, 9.1, -0.008]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.267, 0, 0.5, -0.892, 0.733, -0.892, 1, 0.856, -0.892, 0.978, 0.275, 1.1, 0.275, 1, 1.2, 0.275, 1.3, -0.027, 1.4, -0.027, 1, 1.522, -0.027, 1.644, 0.149, 1.767, 0.149, 1, 2.356, 0.149, 2.944, -3.431, 3.533, -3.431, 1, 3.878, -3.431, 4.222, 2.74, 4.567, 2.74, 1, 4.689, 2.74, 4.811, -0.663, 4.933, -0.663, 1, 5.056, -0.663, 5.178, 4.93, 5.3, 4.93, 1, 5.422, 4.93, 5.544, 0.866, 5.667, 0.866, 1, 5.767, 0.866, 5.867, 2.309, 5.967, 2.309, 1, 6.133, 2.309, 6.3, -2.299, 6.467, -2.299, 1, 6.589, -2.299, 6.711, 3.065, 6.833, 3.065, 1, 6.978, 3.065, 7.122, -1.913, 7.267, -1.913, 1, 7.378, -1.913, 7.489, 0.568, 7.6, 0.568, 1, 7.722, 0.568, 7.844, -0.267, 7.967, -0.267, 1, 8.089, -0.267, 8.211, 0.057, 8.333, 0.057, 1, 8.444, 0.057, 8.556, -0.034, 8.667, -0.034, 1, 8.8, -0.034, 8.933, 0.017, 9.067, 0.017, 1, 9.078, 0.017, 9.089, 0.017, 9.1, 0.016]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.289, 0, 0.544, -0.19, 0.8, -0.19, 1, 0.944, -0.19, 1.089, 0.037, 1.233, 0.037, 1, 1.322, 0.037, 1.411, 0.016, 1.5, 0.016, 1, 1.622, 0.016, 1.744, 0.032, 1.867, 0.032, 1, 2.422, 0.032, 2.978, -0.652, 3.533, -0.652, 1, 3.856, -0.652, 4.178, 0.59, 4.5, 0.59, 1, 4.656, 0.59, 4.811, -0.281, 4.967, -0.281, 1, 5.089, -0.281, 5.211, 0.833, 5.333, 0.833, 1, 5.478, 0.833, 5.622, 0.218, 5.767, 0.218, 1, 5.856, 0.218, 5.944, 0.412, 6.033, 0.412, 1, 6.189, 0.412, 6.344, -0.511, 6.5, -0.511, 1, 6.633, -0.511, 6.767, 0.568, 6.9, 0.568, 1, 7.044, 0.568, 7.189, -0.28, 7.333, -0.28, 1, 7.456, -0.28, 7.578, 0.014, 7.7, 0.014, 1, 7.822, 0.014, 7.944, -0.024, 8.067, -0.024, 1, 8.411, -0.024, 8.756, -0.003, 9.1, 0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.289, 0, 0.544, 0.19, 0.8, 0.19, 1, 0.889, 0.19, 0.978, -0.202, 1.067, -0.202, 1, 1.189, -0.202, 1.311, 0.099, 1.433, 0.099, 1, 1.556, 0.099, 1.678, -0.048, 1.8, -0.048, 1, 1.922, -0.048, 2.044, 0.012, 2.167, 0.012, 1, 2.2, 0.012, 2.233, 0.008, 2.267, 0.008, 1, 2.3, 0.008, 2.333, 0.035, 2.367, 0.035, 1, 2.433, 0.035, 2.5, 0.011, 2.567, 0.011, 1, 2.667, 0.011, 2.767, 0.058, 2.867, 0.058, 1, 3.2, 0.058, 3.533, -0.216, 3.867, -0.216, 1, 4, -0.216, 4.133, -0.028, 4.267, -0.028, 1, 4.333, -0.028, 4.4, -0.047, 4.467, -0.047, 1, 4.6, -0.047, 4.733, 0.344, 4.867, 0.344, 1, 4.989, 0.344, 5.111, -0.493, 5.233, -0.493, 1, 5.333, -0.493, 5.433, 0.568, 5.533, 0.568, 1, 5.656, 0.568, 5.778, -0.388, 5.9, -0.388, 1, 6.056, -0.388, 6.211, 0.369, 6.367, 0.369, 1, 6.489, 0.369, 6.611, -0.538, 6.733, -0.538, 1, 6.844, -0.538, 6.956, 0.473, 7.067, 0.473, 1, 7.2, 0.473, 7.333, -0.414, 7.467, -0.414, 1, 7.589, -0.414, 7.711, 0.247, 7.833, 0.247, 1, 7.956, 0.247, 8.078, -0.089, 8.2, -0.089, 1, 8.333, -0.089, 8.467, 0.039, 8.6, 0.039, 1, 8.733, 0.039, 8.867, -0.012, 9, -0.012, 1, 9.033, -0.012, 9.067, -0.011, 9.1, -0.009]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.867, 0, 0.933, 0.194, 1, 0.194, 1, 1.089, 0.194, 1.178, -0.285, 1.267, -0.285, 1, 1.389, -0.285, 1.511, 0.217, 1.633, 0.217, 1, 1.744, 0.217, 1.856, -0.113, 1.967, -0.113, 1, 2.122, -0.113, 2.278, 0.079, 2.433, 0.079, 1, 2.522, 0.079, 2.611, 0.012, 2.7, 0.012, 1, 2.811, 0.012, 2.922, 0.088, 3.033, 0.088, 1, 3.211, 0.088, 3.389, -0.041, 3.567, -0.041, 1, 3.578, -0.041, 3.589, -0.041, 3.6, -0.041, 1, 3.722, -0.041, 3.844, -0.228, 3.967, -0.228, 1, 4.111, -0.228, 4.256, 0.172, 4.4, 0.172, 1, 4.5, 0.172, 4.6, 0.003, 4.7, 0.003, 1, 4.778, 0.003, 4.856, 0.21, 4.933, 0.21, 1, 5.056, 0.21, 5.178, -0.872, 5.3, -0.872, 1, 5.422, -0.872, 5.544, 1.135, 5.667, 1.135, 1, 5.789, 1.135, 5.911, -0.849, 6.033, -0.849, 1, 6.167, -0.849, 6.3, 0.77, 6.433, 0.77, 1, 6.567, 0.77, 6.7, -1.052, 6.833, -1.052, 1, 6.956, -1.052, 7.078, 1.229, 7.2, 1.229, 1, 7.333, 1.229, 7.467, -1.054, 7.6, -1.054, 1, 7.733, -1.054, 7.867, 0.682, 8, 0.682, 1, 8.122, 0.682, 8.244, -0.369, 8.367, -0.369, 1, 8.5, -0.369, 8.633, 0.174, 8.767, 0.174, 1, 8.878, 0.174, 8.989, -0.034, 9.1, -0.072]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0.217, 1.167, 0.217, 1, 1.267, 0.217, 1.367, -0.356, 1.467, -0.356, 1, 1.578, -0.356, 1.689, 0.325, 1.8, 0.325, 1, 1.922, 0.325, 2.044, -0.206, 2.167, -0.206, 1, 2.311, -0.206, 2.456, 0.169, 2.6, 0.169, 1, 2.711, 0.169, 2.822, -0.033, 2.933, -0.033, 1, 3.033, -0.033, 3.133, 0.094, 3.233, 0.094, 1, 3.378, 0.094, 3.522, -0.072, 3.667, -0.072, 1, 3.711, -0.072, 3.756, -0.063, 3.8, -0.063, 1, 3.911, -0.063, 4.022, -0.266, 4.133, -0.266, 1, 4.278, -0.266, 4.422, 0.207, 4.567, 0.207, 1, 4.656, 0.207, 4.744, 0.012, 4.833, 0.012, 1, 4.922, 0.012, 5.011, 0.318, 5.1, 0.318, 1, 5.222, 0.318, 5.344, -1.08, 5.467, -1.08, 1, 5.589, -1.08, 5.711, 1.542, 5.833, 1.542, 1, 5.956, 1.542, 6.078, -1.438, 6.2, -1.438, 1, 6.333, -1.438, 6.467, 1.434, 6.6, 1.434, 1, 6.722, 1.434, 6.844, -1.689, 6.967, -1.689, 1, 7.1, -1.689, 7.233, 1.898, 7.367, 1.898, 1, 7.5, 1.898, 7.633, -1.747, 7.767, -1.747, 1, 7.889, -1.747, 8.011, 1.305, 8.133, 1.305, 1, 8.267, 1.305, 8.4, -0.834, 8.533, -0.834, 1, 8.667, -0.834, 8.8, 0.456, 8.933, 0.456, 1, 8.989, 0.456, 9.044, 0.312, 9.1, 0.155]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.289, 0, 0.544, -0.19, 0.8, -0.19, 1, 0.944, -0.19, 1.089, 0.037, 1.233, 0.037, 1, 1.322, 0.037, 1.411, 0.016, 1.5, 0.016, 1, 1.622, 0.016, 1.744, 0.032, 1.867, 0.032, 1, 2.422, 0.032, 2.978, -0.652, 3.533, -0.652, 1, 3.856, -0.652, 4.178, 0.59, 4.5, 0.59, 1, 4.656, 0.59, 4.811, -0.281, 4.967, -0.281, 1, 5.089, -0.281, 5.211, 0.833, 5.333, 0.833, 1, 5.478, 0.833, 5.622, 0.218, 5.767, 0.218, 1, 5.856, 0.218, 5.944, 0.412, 6.033, 0.412, 1, 6.189, 0.412, 6.344, -0.511, 6.5, -0.511, 1, 6.633, -0.511, 6.767, 0.568, 6.9, 0.568, 1, 7.044, 0.568, 7.189, -0.28, 7.333, -0.28, 1, 7.456, -0.28, 7.578, 0.014, 7.7, 0.014, 1, 7.822, 0.014, 7.944, -0.024, 8.067, -0.024, 1, 8.411, -0.024, 8.756, -0.003, 9.1, 0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.289, 0, 0.544, 0.19, 0.8, 0.19, 1, 0.889, 0.19, 0.978, -0.202, 1.067, -0.202, 1, 1.189, -0.202, 1.311, 0.099, 1.433, 0.099, 1, 1.556, 0.099, 1.678, -0.048, 1.8, -0.048, 1, 1.922, -0.048, 2.044, 0.012, 2.167, 0.012, 1, 2.2, 0.012, 2.233, 0.008, 2.267, 0.008, 1, 2.3, 0.008, 2.333, 0.035, 2.367, 0.035, 1, 2.433, 0.035, 2.5, 0.011, 2.567, 0.011, 1, 2.667, 0.011, 2.767, 0.058, 2.867, 0.058, 1, 3.2, 0.058, 3.533, -0.216, 3.867, -0.216, 1, 4, -0.216, 4.133, -0.028, 4.267, -0.028, 1, 4.333, -0.028, 4.4, -0.047, 4.467, -0.047, 1, 4.6, -0.047, 4.733, 0.344, 4.867, 0.344, 1, 4.989, 0.344, 5.111, -0.493, 5.233, -0.493, 1, 5.333, -0.493, 5.433, 0.568, 5.533, 0.568, 1, 5.656, 0.568, 5.778, -0.388, 5.9, -0.388, 1, 6.056, -0.388, 6.211, 0.369, 6.367, 0.369, 1, 6.489, 0.369, 6.611, -0.538, 6.733, -0.538, 1, 6.844, -0.538, 6.956, 0.473, 7.067, 0.473, 1, 7.2, 0.473, 7.333, -0.414, 7.467, -0.414, 1, 7.589, -0.414, 7.711, 0.247, 7.833, 0.247, 1, 7.956, 0.247, 8.078, -0.089, 8.2, -0.089, 1, 8.333, -0.089, 8.467, 0.039, 8.6, 0.039, 1, 8.733, 0.039, 8.867, -0.012, 9, -0.012, 1, 9.033, -0.012, 9.067, -0.011, 9.1, -0.009]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0, 0.8, 0, 1, 0.867, 0, 0.933, 0.194, 1, 0.194, 1, 1.089, 0.194, 1.178, -0.285, 1.267, -0.285, 1, 1.389, -0.285, 1.511, 0.217, 1.633, 0.217, 1, 1.744, 0.217, 1.856, -0.113, 1.967, -0.113, 1, 2.122, -0.113, 2.278, 0.079, 2.433, 0.079, 1, 2.522, 0.079, 2.611, 0.012, 2.7, 0.012, 1, 2.811, 0.012, 2.922, 0.088, 3.033, 0.088, 1, 3.211, 0.088, 3.389, -0.041, 3.567, -0.041, 1, 3.578, -0.041, 3.589, -0.041, 3.6, -0.041, 1, 3.722, -0.041, 3.844, -0.228, 3.967, -0.228, 1, 4.111, -0.228, 4.256, 0.172, 4.4, 0.172, 1, 4.5, 0.172, 4.6, 0.003, 4.7, 0.003, 1, 4.778, 0.003, 4.856, 0.21, 4.933, 0.21, 1, 5.056, 0.21, 5.178, -0.872, 5.3, -0.872, 1, 5.422, -0.872, 5.544, 1.135, 5.667, 1.135, 1, 5.789, 1.135, 5.911, -0.849, 6.033, -0.849, 1, 6.167, -0.849, 6.3, 0.77, 6.433, 0.77, 1, 6.567, 0.77, 6.7, -1.052, 6.833, -1.052, 1, 6.956, -1.052, 7.078, 1.229, 7.2, 1.229, 1, 7.333, 1.229, 7.467, -1.054, 7.6, -1.054, 1, 7.733, -1.054, 7.867, 0.682, 8, 0.682, 1, 8.122, 0.682, 8.244, -0.369, 8.367, -0.369, 1, 8.5, -0.369, 8.633, 0.174, 8.767, 0.174, 1, 8.878, 0.174, 8.989, -0.034, 9.1, -0.072]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0.217, 1.167, 0.217, 1, 1.267, 0.217, 1.367, -0.356, 1.467, -0.356, 1, 1.578, -0.356, 1.689, 0.325, 1.8, 0.325, 1, 1.922, 0.325, 2.044, -0.206, 2.167, -0.206, 1, 2.311, -0.206, 2.456, 0.169, 2.6, 0.169, 1, 2.711, 0.169, 2.822, -0.033, 2.933, -0.033, 1, 3.033, -0.033, 3.133, 0.094, 3.233, 0.094, 1, 3.378, 0.094, 3.522, -0.072, 3.667, -0.072, 1, 3.711, -0.072, 3.756, -0.063, 3.8, -0.063, 1, 3.911, -0.063, 4.022, -0.266, 4.133, -0.266, 1, 4.278, -0.266, 4.422, 0.207, 4.567, 0.207, 1, 4.656, 0.207, 4.744, 0.012, 4.833, 0.012, 1, 4.922, 0.012, 5.011, 0.318, 5.1, 0.318, 1, 5.222, 0.318, 5.344, -1.08, 5.467, -1.08, 1, 5.589, -1.08, 5.711, 1.542, 5.833, 1.542, 1, 5.956, 1.542, 6.078, -1.438, 6.2, -1.438, 1, 6.333, -1.438, 6.467, 1.434, 6.6, 1.434, 1, 6.722, 1.434, 6.844, -1.689, 6.967, -1.689, 1, 7.1, -1.689, 7.233, 1.898, 7.367, 1.898, 1, 7.5, 1.898, 7.633, -1.747, 7.767, -1.747, 1, 7.889, -1.747, 8.011, 1.305, 8.133, 1.305, 1, 8.267, 1.305, 8.4, -0.834, 8.533, -0.834, 1, 8.667, -0.834, 8.8, 0.456, 8.933, 0.456, 1, 8.989, 0.456, 9.044, 0.312, 9.1, 0.155]}]}