{"Version": 3, "Meta": {"Duration": 5.73, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 111, "TotalSegmentCount": 901, "TotalPointCount": 2678, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -1, 1, 0.067, -1, 0.133, -1, 0.2, -1, 1, 0.433, -1, 0.667, -1, 0.9, -1, 1, 0.989, -1, 1.078, 0, 1.167, 0, 1, 1.333, 0, 1.5, -1.848, 1.667, -8, 1, 1.833, -14.152, 2, -19, 2.167, -19, 1, 2.644, -19, 3.122, 17, 3.6, 17, 1, 3.756, 17, 3.911, 17, 4.067, 17, 1, 4.4, 17, 4.733, 5, 5.067, 5, 0, 5.733, 5]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -8, 1, 0.067, -8, 0.133, 25, 0.2, 25, 1, 0.433, 25, 0.667, 25, 0.9, 25, 1, 0.989, 25, 1.078, 25.039, 1.167, 21, 1, 1.333, 13.426, 1.5, -10.25, 1.667, -12, 1, 1.833, -13.75, 2, -14.71, 2.167, -15, 1, 2.644, -15.831, 3.122, -16, 3.6, -16, 1, 3.756, -16, 3.911, -16, 4.067, -16, 1, 4.4, -16, 4.733, -15, 5.067, -15, 0, 5.733, -15]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.389, 0, 0.778, 0, 1.167, 0, 1, 1.333, 0, 1.5, -8, 1.667, -8, 1, 1.833, -8, 2, -8, 2.167, -8, 1, 2.644, -8, 3.122, 13, 3.6, 13, 1, 3.756, 13, 3.911, 13, 4.067, 13, 1, 4.4, 13, 4.733, 7, 5.067, 7, 0, 5.733, 7]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 1.167, 0, 1, 1.333, 0, 1.5, -5, 1.667, -5, 1, 2.311, -5, 2.956, 6, 3.6, 6, 1, 4.089, 6, 4.578, 3, 5.067, 3, 0, 5.733, 3]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 1.167, 0, 1, 1.333, 0, 1.5, -8, 1.667, -8, 1, 2.322, -8, 2.978, 8, 3.633, 8, 1, 3.789, 8, 3.944, 8, 4.1, 8, 1, 4.422, 8, 4.744, 3, 5.067, 3, 0, 5.733, 3]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 0.144, 0, 0.289, 1, 0.433, 1, 0, 5.733, 1]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.378, 0, 0.389, 30, 0.4, 30, 1, 0.422, 30, 0.444, 30, 0.467, 30, 1, 0.478, 30, 0.489, -30, 0.5, -30, 1, 0.533, -30, 0.567, -30, 0.6, -30, 1, 0.633, -30, 0.667, 30, 0.7, 30, 1, 0.722, 30, 0.744, 30, 0.767, 30, 1, 0.833, 30, 0.9, -13.616, 0.967, -13.616, 1, 1.033, -13.616, 1.1, 5.274, 1.167, 5.274, 1, 1.244, 5.274, 1.322, -2.116, 1.4, -2.116, 1, 1.478, -2.116, 1.556, 0.847, 1.633, 0.847, 1, 1.711, 0.847, 1.789, -0.338, 1.867, -0.338, 1, 1.9, -0.338, 1.933, 30, 1.967, 30, 1, 1.989, 30, 2.011, 30, 2.033, 30, 1, 2.044, 30, 2.056, -30, 2.067, -30, 1, 2.089, -30, 2.111, -30, 2.133, -30, 1, 2.156, -30, 2.178, 30, 2.2, 30, 1, 2.233, 30, 2.267, 30, 2.3, 30, 1, 2.367, 30, 2.433, -14.752, 2.5, -14.752, 1, 2.567, -14.752, 2.633, 5.781, 2.7, 5.781, 1, 2.778, 5.781, 2.856, -2.312, 2.933, -2.312, 1, 3.011, -2.312, 3.089, 0.924, 3.167, 0.924, 1, 3.244, 0.924, 3.322, -0.368, 3.4, -0.368, 1, 3.478, -0.368, 3.556, 0.146, 3.633, 0.146, 1, 3.711, 0.146, 3.789, -0.058, 3.867, -0.058, 1, 3.944, -0.058, 4.022, 0.023, 4.1, 0.023, 1, 4.167, 0.023, 4.233, -0.009, 4.3, -0.009, 1, 4.311, -0.009, 4.322, -0.009, 4.333, -0.009, 1, 4.356, -0.009, 4.378, 30, 4.4, 30, 1, 4.422, 30, 4.444, 30, 4.467, 30, 1, 4.478, 30, 4.489, -30, 4.5, -30, 1, 4.522, -30, 4.544, -30, 4.567, -30, 1, 4.589, -30, 4.611, 30, 4.633, 30, 1, 4.656, 30, 4.678, 30, 4.7, 30, 1, 4.711, 30, 4.722, -30, 4.733, -30, 1, 4.756, -30, 4.778, -30, 4.8, -30, 1, 4.822, -30, 4.844, 30, 4.867, 30, 1, 4.9, 30, 4.933, 30, 4.967, 30, 1, 5.022, 30, 5.078, -15.628, 5.133, -15.628, 1, 5.211, -15.628, 5.289, 6.116, 5.367, 6.116, 1, 5.444, 6.116, 5.522, -2.439, 5.6, -2.439, 1, 5.644, -2.439, 5.689, -1.325, 5.733, -0.37]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.367, 1, 0.4, 0, 0.433, 0, 1, 0.478, 0, 0.522, 1, 0.567, 1, 1, 1.011, 1, 1.456, 1, 1.9, 1, 1, 1.933, 1, 1.967, 0, 2, 0, 1, 2.033, 0, 2.067, 1, 2.1, 1, 1, 2.844, 1, 3.589, 1, 4.333, 1, 1, 4.367, 1, 4.4, 0, 4.433, 0, 1, 4.467, 0, 4.5, 1, 4.533, 1, 1, 4.544, 1, 4.556, 1, 4.567, 1, 1, 4.6, 1, 4.633, 0, 4.667, 0, 1, 4.7, 0, 4.733, 1, 4.767, 1, 0, 5.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.367, 1, 0.4, 0, 0.433, 0, 1, 0.478, 0, 0.522, 1, 0.567, 1, 1, 1.011, 1, 1.456, 1, 1.9, 1, 1, 1.933, 1, 1.967, 0, 2, 0, 1, 2.033, 0, 2.067, 1, 2.1, 1, 1, 2.844, 1, 3.589, 1, 4.333, 1, 1, 4.367, 1, 4.4, 0, 4.433, 0, 1, 4.467, 0, 4.5, 1, 4.533, 1, 1, 4.544, 1, 4.556, 1, 4.567, 1, 1, 4.6, 1, 4.633, 0, 4.667, 0, 1, 4.7, 0, 4.733, 1, 4.767, 1, 0, 5.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.511, 0, 1.022, 0, 1.533, 0, 1, 1.633, 0, 1.733, 1, 1.833, 1, 1, 1.989, 1, 2.144, -1, 2.3, -1, 1, 2.811, -1, 3.322, -1, 3.833, -1, 1, 3.922, -1, 4.011, 0.9, 4.1, 0.9, 1, 4.4, 0.9, 4.7, -0.1, 5, -0.1, 0, 5.733, -0.1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.511, 0, 1.022, 0, 1.533, 0, 1, 1.633, 0, 1.733, 0.534, 1.833, 0.6, 1, 1.989, 0.702, 2.144, 0.7, 2.3, 0.7, 1, 2.811, 0.7, 3.322, 0.6, 3.833, 0.6, 1, 3.922, 0.6, 4.011, 1, 4.1, 1, 1, 4.4, 1, 4.7, 0.2, 5, 0.2, 0, 5.733, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.622, 0, 1.244, -0.5, 1.867, -0.5, 0, 5.733, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.622, 0, 1.244, -0.5, 1.867, -0.5, 0, 5.733, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.422, 0, 0.844, 0, 1.267, 0, 1, 1.467, 0, 1.667, -0.5, 1.867, -0.5, 1, 2.833, -0.5, 3.8, -0.5, 4.767, -0.5, 1, 4.889, -0.5, 5.011, 0, 5.133, 0, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.422, 0, 0.844, 0, 1.267, 0, 1, 1.467, 0, 1.667, -0.5, 1.867, -0.5, 1, 2.833, -0.5, 3.8, -0.5, 4.767, -0.5, 1, 4.889, -0.5, 5.011, 0.1, 5.133, 0.1, 0, 5.733, 0.1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.8, 1, 0.511, -0.8, 1.022, -0.775, 1.533, -0.7, 1, 1.611, -0.689, 1.689, -0.6, 1.767, -0.6, 1, 1.822, -0.6, 1.878, -0.6, 1.933, -0.6, 1, 2.078, -0.6, 2.222, -0.6, 2.367, -0.6, 1, 2.4, -0.6, 2.433, -0.6, 2.467, -0.6, 1, 2.544, -0.6, 2.622, -0.6, 2.7, -0.6, 1, 2.722, -0.6, 2.744, -0.6, 2.767, -0.6, 1, 2.867, -0.6, 2.967, -0.1, 3.067, -0.1, 1, 3.156, -0.1, 3.244, -0.5, 3.333, -0.5, 1, 3.367, -0.5, 3.4, -0.5, 3.433, -0.5, 1, 3.5, -0.5, 3.567, -0.4, 3.633, -0.4, 1, 3.7, -0.4, 3.767, -0.5, 3.833, -0.5, 1, 3.889, -0.5, 3.944, -0.4, 4, -0.4, 1, 4.067, -0.4, 4.133, -0.4, 4.2, -0.4, 1, 4.267, -0.4, 4.333, -0.6, 4.4, -0.6, 1, 4.478, -0.6, 4.556, -0.4, 4.633, -0.4, 1, 4.678, -0.4, 4.722, -0.5, 4.767, -0.5, 0, 5.733, -0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.511, 0, 1.022, 0, 1.533, 0, 1, 1.611, 0, 1.689, 0.4, 1.767, 0.4, 1, 1.822, 0.4, 1.878, 0, 1.933, 0, 1, 2.078, 0, 2.222, 0, 2.367, 0, 1, 2.4, 0, 2.433, 0.4, 2.467, 0.4, 1, 2.544, 0.4, 2.622, 0, 2.7, 0, 1, 2.722, 0, 2.744, 0.282, 2.767, 0.3, 1, 2.867, 0.38, 2.967, 0.4, 3.067, 0.4, 1, 3.156, 0.4, 3.244, 0, 3.333, 0, 1, 3.367, 0, 3.4, 0.214, 3.433, 0.3, 1, 3.5, 0.471, 3.567, 0.5, 3.633, 0.5, 1, 3.7, 0.5, 3.767, 0.3, 3.833, 0.3, 1, 3.889, 0.3, 3.944, 0.6, 4, 0.6, 1, 4.067, 0.6, 4.133, 0.6, 4.2, 0.6, 1, 4.267, 0.6, 4.333, 0.4, 4.4, 0.4, 1, 4.478, 0.4, 4.556, 0.5, 4.633, 0.5, 1, 4.678, 0.5, 4.722, 0, 4.767, 0, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.411, 0, 1.722, 18, 2.033, 18, 0, 5.733, 18]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.411, 0, 1.722, 16, 2.033, 16, 0, 5.733, 16]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.411, 0, 1.722, 18, 2.033, 18, 0, 5.733, 18]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.411, 0, 1.722, 16, 2.033, 16, 0, 5.733, 16]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, -30, 0, 2.1, -30, 1, 2.389, -30, 2.678, 30, 2.967, 30, 1, 3.289, 30, 3.611, -30, 3.933, -30, 1, 4.222, -30, 4.511, 30, 4.8, 30, 0, 5.733, 30]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, -30, 0, 2.1, -30, 1, 2.256, -30, 2.411, 30, 2.567, 30, 1, 2.711, 30, 2.856, -30, 3, -30, 1, 3.311, -30, 3.622, -30, 3.933, -30, 1, 4.089, -30, 4.244, 30, 4.4, 30, 1, 4.544, 30, 4.689, -30, 4.833, -30, 0, 5.733, -30]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, -0.067, 1, 0.067, -0.319, 0.133, -1.204, 0.2, -1.204, 1, 0.278, -1.204, 0.356, 0.552, 0.433, 0.552, 1, 0.544, 0.552, 0.656, -0.143, 0.767, -0.143, 1, 0.833, -0.143, 0.9, -0.005, 0.967, -0.005, 1, 1.011, -0.005, 1.056, -0.058, 1.1, -0.058, 1, 1.189, -0.058, 1.278, 0.69, 1.367, 0.69, 1, 1.511, 0.69, 1.656, -1.952, 1.8, -1.952, 1, 1.9, -1.952, 2, -1.349, 2.1, -1.349, 1, 2.189, -1.349, 2.278, -1.491, 2.367, -1.491, 1, 2.811, -1.491, 3.256, 2.257, 3.7, 2.257, 1, 3.8, 2.257, 3.9, 1.805, 4, 1.805, 1, 4.122, 1.805, 4.244, 2.247, 4.367, 2.247, 1, 4.633, 2.247, 4.9, 0.474, 5.167, 0.474, 1, 5.267, 0.474, 5.367, 0.774, 5.467, 0.774, 1, 5.556, 0.774, 5.644, 0.724, 5.733, 0.704]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -0.067, 1, 0.067, -0.319, 0.133, -1.204, 0.2, -1.204, 1, 0.278, -1.204, 0.356, 0.552, 0.433, 0.552, 1, 0.544, 0.552, 0.656, -0.143, 0.767, -0.143, 1, 0.833, -0.143, 0.9, -0.005, 0.967, -0.005, 1, 1.011, -0.005, 1.056, -0.058, 1.1, -0.058, 1, 1.189, -0.058, 1.278, 0.69, 1.367, 0.69, 1, 1.511, 0.69, 1.656, -1.952, 1.8, -1.952, 1, 1.9, -1.952, 2, -1.349, 2.1, -1.349, 1, 2.189, -1.349, 2.278, -1.491, 2.367, -1.491, 1, 2.811, -1.491, 3.256, 2.257, 3.7, 2.257, 1, 3.8, 2.257, 3.9, 1.805, 4, 1.805, 1, 4.122, 1.805, 4.244, 2.247, 4.367, 2.247, 1, 4.633, 2.247, 4.9, 0.474, 5.167, 0.474, 1, 5.267, 0.474, 5.367, 0.774, 5.467, 0.774, 1, 5.556, 0.774, 5.644, 0.724, 5.733, 0.704]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.4, 0, 0.8, 0, 1.2, 0, 1, 1.256, 0, 1.311, 2.774, 1.367, 2.774, 1, 1.489, 2.774, 1.611, -9.654, 1.733, -9.654, 1, 1.833, -9.654, 1.933, -2.088, 2.033, -2.088, 1, 2.111, -2.088, 2.189, -3.828, 2.267, -3.828, 1, 2.733, -3.828, 3.2, 4.895, 3.667, 4.895, 1, 3.767, 4.895, 3.867, 4.307, 3.967, 4.307, 1, 4.033, 4.307, 4.1, 4.497, 4.167, 4.497, 1, 4.478, 4.497, 4.789, 1.174, 5.1, 1.174, 1, 5.2, 1.174, 5.3, 1.748, 5.4, 1.748, 1, 5.489, 1.748, 5.578, 1.493, 5.667, 1.493, 1, 5.689, 1.493, 5.711, 1.499, 5.733, 1.507]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.4, 0, 0.8, 0, 1.2, 0, 1, 1.244, 0, 1.289, -0.526, 1.333, -0.526, 1, 1.411, -0.526, 1.489, 1.6, 1.567, 1.6, 1, 1.667, 1.6, 1.767, -2.409, 1.867, -2.409, 1, 1.956, -2.409, 2.044, 1.889, 2.133, 1.889, 1, 2.233, 1.889, 2.333, -1.318, 2.433, -1.318, 1, 2.522, -1.318, 2.611, 0.701, 2.7, 0.701, 1, 2.8, 0.701, 2.9, -0.437, 3, -0.437, 1, 3.089, -0.437, 3.178, 0.214, 3.267, 0.214, 1, 3.367, 0.214, 3.467, -0.099, 3.567, -0.099, 1, 3.656, -0.099, 3.744, 0.166, 3.833, 0.166, 1, 3.911, 0.166, 3.989, -0.145, 4.067, -0.145, 1, 4.167, -0.145, 4.267, 0.171, 4.367, 0.171, 1, 4.378, 0.171, 4.389, 0.171, 4.4, 0.171, 1, 4.5, 0.171, 4.6, -0.06, 4.7, -0.06, 1, 4.778, -0.06, 4.856, 0.029, 4.933, 0.029, 1, 5.033, 0.029, 5.133, -0.125, 5.233, -0.125, 1, 5.322, -0.125, 5.411, 0.133, 5.5, 0.133, 1, 5.578, 0.133, 5.656, -0.005, 5.733, -0.066]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.4, 0, 0.8, 0, 1.2, 0, 1, 1.244, 0, 1.289, -0.526, 1.333, -0.526, 1, 1.411, -0.526, 1.489, 1.6, 1.567, 1.6, 1, 1.667, 1.6, 1.767, -2.409, 1.867, -2.409, 1, 1.956, -2.409, 2.044, 1.889, 2.133, 1.889, 1, 2.233, 1.889, 2.333, -1.318, 2.433, -1.318, 1, 2.522, -1.318, 2.611, 0.701, 2.7, 0.701, 1, 2.8, 0.701, 2.9, -0.437, 3, -0.437, 1, 3.089, -0.437, 3.178, 0.214, 3.267, 0.214, 1, 3.367, 0.214, 3.467, -0.099, 3.567, -0.099, 1, 3.656, -0.099, 3.744, 0.166, 3.833, 0.166, 1, 3.911, 0.166, 3.989, -0.145, 4.067, -0.145, 1, 4.167, -0.145, 4.267, 0.171, 4.367, 0.171, 1, 4.378, 0.171, 4.389, 0.171, 4.4, 0.171, 1, 4.5, 0.171, 4.6, -0.06, 4.7, -0.06, 1, 4.778, -0.06, 4.856, 0.029, 4.933, 0.029, 1, 5.033, 0.029, 5.133, -0.125, 5.233, -0.125, 1, 5.322, -0.125, 5.411, 0.133, 5.5, 0.133, 1, 5.578, 0.133, 5.656, -0.005, 5.733, -0.066]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -0.001, 1, 0.022, -0.001, 0.044, -0.001, 0.067, -0.001, 1, 0.167, -0.001, 0.267, 0.001, 0.367, 0.001, 1, 0.467, 0.001, 0.567, 0, 0.667, 0, 1, 0.756, 0, 0.844, 0, 0.933, 0, 1, 0.989, 0, 1.044, -0.176, 1.1, -0.176, 1, 1.2, -0.176, 1.3, 0.8, 1.4, 0.8, 1, 1.522, 0.8, 1.644, -2.441, 1.767, -2.441, 1, 1.867, -2.441, 1.967, -1.294, 2.067, -1.294, 1, 2.156, -1.294, 2.244, -1.75, 2.333, -1.75, 1, 2.778, -1.75, 3.222, 2.379, 3.667, 2.379, 1, 3.767, 2.379, 3.867, 1.663, 3.967, 1.663, 1, 4.078, 1.663, 4.189, 2.217, 4.3, 2.217, 1, 4.578, 2.217, 4.856, 0.468, 5.133, 0.468, 1, 5.233, 0.468, 5.333, 0.975, 5.433, 0.975, 1, 5.533, 0.975, 5.633, 0.787, 5.733, 0.75]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0.025, 1, 0.011, 0.026, 0.022, 0.027, 0.033, 0.027, 1, 0.144, 0.027, 0.256, -0.013, 0.367, -0.013, 1, 0.478, -0.013, 0.589, 0.006, 0.7, 0.006, 1, 0.778, 0.006, 0.856, -0.002, 0.933, -0.002, 1, 0.978, -0.002, 1.022, 0.092, 1.067, 0.092, 1, 1.144, 0.092, 1.222, -0.477, 1.3, -0.477, 1, 1.4, -0.477, 1.5, 1.75, 1.6, 1.75, 1, 1.711, 1.75, 1.822, -2.081, 1.933, -2.081, 1, 2.033, -2.081, 2.133, 1.393, 2.233, 1.393, 1, 2.344, 1.393, 2.456, -1.309, 2.567, -1.309, 1, 2.678, -1.309, 2.789, 0.571, 2.9, 0.571, 1, 3, 0.571, 3.1, -0.422, 3.2, -0.422, 1, 3.322, -0.422, 3.444, 0.313, 3.567, 0.313, 1, 3.6, 0.313, 3.633, 0.27, 3.667, 0.27, 1, 3.711, 0.27, 3.756, 0.405, 3.8, 0.405, 1, 3.911, 0.405, 4.022, -0.516, 4.133, -0.516, 1, 4.256, -0.516, 4.378, 0.774, 4.5, 0.774, 1, 4.611, 0.774, 4.722, -0.468, 4.833, -0.468, 1, 4.933, -0.468, 5.033, 0.149, 5.133, 0.149, 1, 5.2, 0.149, 5.267, -0.282, 5.333, -0.282, 1, 5.433, -0.282, 5.533, 0.33, 5.633, 0.33, 1, 5.667, 0.33, 5.7, 0.265, 5.733, 0.179]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, -0.001, 1, 0.022, -0.001, 0.044, -0.002, 0.067, -0.002, 1, 0.167, -0.002, 0.267, 0.001, 0.367, 0.001, 1, 0.467, 0.001, 0.567, 0, 0.667, 0, 1, 0.756, 0, 0.844, 0, 0.933, 0, 1, 0.989, 0, 1.044, -0.245, 1.1, -0.245, 1, 1.2, -0.245, 1.3, 1.111, 1.4, 1.111, 1, 1.522, 1.111, 1.644, -3.391, 1.767, -3.391, 1, 1.867, -3.391, 1.967, -1.797, 2.067, -1.797, 1, 2.156, -1.797, 2.244, -2.431, 2.333, -2.431, 1, 2.778, -2.431, 3.222, 3.305, 3.667, 3.305, 1, 3.767, 3.305, 3.867, 2.31, 3.967, 2.31, 1, 4.078, 2.31, 4.189, 3.079, 4.3, 3.079, 1, 4.578, 3.079, 4.856, 0.649, 5.133, 0.649, 1, 5.233, 0.649, 5.333, 1.354, 5.433, 1.354, 1, 5.533, 1.354, 5.633, 1.094, 5.733, 1.042]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, -0.001, 1, 0.022, -0.001, 0.044, -0.002, 0.067, -0.002, 1, 0.167, -0.002, 0.267, 0.001, 0.367, 0.001, 1, 0.467, 0.001, 0.567, 0, 0.667, 0, 1, 0.756, 0, 0.844, 0, 0.933, 0, 1, 0.989, 0, 1.044, -0.245, 1.1, -0.245, 1, 1.2, -0.245, 1.3, 1.111, 1.4, 1.111, 1, 1.522, 1.111, 1.644, -3.391, 1.767, -3.391, 1, 1.867, -3.391, 1.967, -1.797, 2.067, -1.797, 1, 2.156, -1.797, 2.244, -2.431, 2.333, -2.431, 1, 2.778, -2.431, 3.222, 3.305, 3.667, 3.305, 1, 3.767, 3.305, 3.867, 2.31, 3.967, 2.31, 1, 4.078, 2.31, 4.189, 3.079, 4.3, 3.079, 1, 4.578, 3.079, 4.856, 0.649, 5.133, 0.649, 1, 5.233, 0.649, 5.333, 1.354, 5.433, 1.354, 1, 5.533, 1.354, 5.633, 1.094, 5.733, 1.042]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, -0.001, 1, 0.022, -0.001, 0.044, -0.002, 0.067, -0.002, 1, 0.167, -0.002, 0.267, 0.001, 0.367, 0.001, 1, 0.467, 0.001, 0.567, 0, 0.667, 0, 1, 0.756, 0, 0.844, 0, 0.933, 0, 1, 0.989, 0, 1.044, -0.245, 1.1, -0.245, 1, 1.2, -0.245, 1.3, 1.111, 1.4, 1.111, 1, 1.522, 1.111, 1.644, -3.391, 1.767, -3.391, 1, 1.867, -3.391, 1.967, -1.797, 2.067, -1.797, 1, 2.156, -1.797, 2.244, -2.431, 2.333, -2.431, 1, 2.778, -2.431, 3.222, 3.305, 3.667, 3.305, 1, 3.767, 3.305, 3.867, 2.31, 3.967, 2.31, 1, 4.078, 2.31, 4.189, 3.079, 4.3, 3.079, 1, 4.578, 3.079, 4.856, 0.649, 5.133, 0.649, 1, 5.233, 0.649, 5.333, 1.354, 5.433, 1.354, 1, 5.533, 1.354, 5.633, 1.094, 5.733, 1.042]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -0.001, 1, 0.022, -0.001, 0.044, -0.001, 0.067, -0.001, 1, 0.167, -0.001, 0.267, 0.001, 0.367, 0.001, 1, 0.467, 0.001, 0.567, 0, 0.667, 0, 1, 0.756, 0, 0.844, 0, 0.933, 0, 1, 0.989, 0, 1.044, -0.176, 1.1, -0.176, 1, 1.2, -0.176, 1.3, 0.8, 1.4, 0.8, 1, 1.522, 0.8, 1.644, -2.441, 1.767, -2.441, 1, 1.867, -2.441, 1.967, -1.294, 2.067, -1.294, 1, 2.156, -1.294, 2.244, -1.75, 2.333, -1.75, 1, 2.778, -1.75, 3.222, 2.379, 3.667, 2.379, 1, 3.767, 2.379, 3.867, 1.663, 3.967, 1.663, 1, 4.078, 1.663, 4.189, 2.217, 4.3, 2.217, 1, 4.578, 2.217, 4.856, 0.468, 5.133, 0.468, 1, 5.233, 0.468, 5.333, 0.975, 5.433, 0.975, 1, 5.533, 0.975, 5.633, 0.787, 5.733, 0.75]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0.025, 1, 0.011, 0.026, 0.022, 0.027, 0.033, 0.027, 1, 0.144, 0.027, 0.256, -0.013, 0.367, -0.013, 1, 0.478, -0.013, 0.589, 0.006, 0.7, 0.006, 1, 0.778, 0.006, 0.856, -0.002, 0.933, -0.002, 1, 0.978, -0.002, 1.022, 0.092, 1.067, 0.092, 1, 1.144, 0.092, 1.222, -0.477, 1.3, -0.477, 1, 1.4, -0.477, 1.5, 1.75, 1.6, 1.75, 1, 1.711, 1.75, 1.822, -2.081, 1.933, -2.081, 1, 2.033, -2.081, 2.133, 1.393, 2.233, 1.393, 1, 2.344, 1.393, 2.456, -1.309, 2.567, -1.309, 1, 2.678, -1.309, 2.789, 0.571, 2.9, 0.571, 1, 3, 0.571, 3.1, -0.422, 3.2, -0.422, 1, 3.322, -0.422, 3.444, 0.313, 3.567, 0.313, 1, 3.6, 0.313, 3.633, 0.27, 3.667, 0.27, 1, 3.711, 0.27, 3.756, 0.405, 3.8, 0.405, 1, 3.911, 0.405, 4.022, -0.516, 4.133, -0.516, 1, 4.256, -0.516, 4.378, 0.774, 4.5, 0.774, 1, 4.611, 0.774, 4.722, -0.468, 4.833, -0.468, 1, 4.933, -0.468, 5.033, 0.149, 5.133, 0.149, 1, 5.2, 0.149, 5.267, -0.282, 5.333, -0.282, 1, 5.433, -0.282, 5.533, 0.33, 5.633, 0.33, 1, 5.667, 0.33, 5.7, 0.265, 5.733, 0.179]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -0.001, 1, 0.022, -0.001, 0.044, -0.002, 0.067, -0.002, 1, 0.167, -0.002, 0.267, 0.001, 0.367, 0.001, 1, 0.467, 0.001, 0.567, 0, 0.667, 0, 1, 0.756, 0, 0.844, 0, 0.933, 0, 1, 0.989, 0, 1.044, -0.245, 1.1, -0.245, 1, 1.2, -0.245, 1.3, 1.111, 1.4, 1.111, 1, 1.522, 1.111, 1.644, -3.391, 1.767, -3.391, 1, 1.867, -3.391, 1.967, -1.797, 2.067, -1.797, 1, 2.156, -1.797, 2.244, -2.431, 2.333, -2.431, 1, 2.778, -2.431, 3.222, 3.305, 3.667, 3.305, 1, 3.767, 3.305, 3.867, 2.31, 3.967, 2.31, 1, 4.078, 2.31, 4.189, 3.079, 4.3, 3.079, 1, 4.578, 3.079, 4.856, 0.649, 5.133, 0.649, 1, 5.233, 0.649, 5.333, 1.354, 5.433, 1.354, 1, 5.533, 1.354, 5.633, 1.094, 5.733, 1.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0.005, 1, 0.022, 0.005, 0.044, 0.005, 0.067, 0.005, 1, 0.256, 0.005, 0.444, -0.002, 0.633, -0.002, 1, 0.733, -0.002, 0.833, 0, 0.933, 0, 1, 1.011, 0, 1.089, -0.349, 1.167, -0.349, 1, 1.289, -0.349, 1.411, 2.189, 1.533, 2.189, 1, 1.811, 2.189, 2.089, -4.085, 2.367, -4.085, 1, 2.833, -4.085, 3.3, 4.157, 3.767, 4.157, 1, 3.911, 4.157, 4.056, 2.805, 4.2, 2.805, 1, 4.311, 2.805, 4.422, 3.415, 4.533, 3.415, 1, 4.767, 3.415, 5, 0.029, 5.233, 0.029, 1, 5.4, 0.029, 5.567, 1.022, 5.733, 1.353]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, -0.002, 1, 0.011, -0.003, 0.022, -0.003, 0.033, -0.003, 1, 0.3, -0.003, 0.567, 0.001, 0.833, 0.001, 1, 0.867, 0.001, 0.9, 0.001, 0.933, 0.001, 1, 1, 0.001, 1.067, 0.276, 1.133, 0.276, 1, 1.289, 0.276, 1.444, -2.381, 1.6, -2.381, 1, 1.933, -2.381, 2.267, 2.174, 2.6, 2.174, 1, 2.633, 2.174, 2.667, 2.152, 2.7, 2.152, 1, 2.711, 2.152, 2.722, 2.153, 2.733, 2.153, 1, 3.078, 2.153, 3.422, -0.828, 3.767, -0.828, 1, 3.889, -0.828, 4.011, -0.412, 4.133, -0.412, 1, 4.3, -0.412, 4.467, -0.926, 4.633, -0.926, 1, 4.844, -0.926, 5.056, 0.502, 5.267, 0.502, 1, 5.422, 0.502, 5.578, 0.215, 5.733, 0.023]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.01, 1, 0.022, -0.013, 0.044, -0.017, 0.067, -0.017, 1, 0.178, -0.017, 0.289, 0.009, 0.4, 0.009, 1, 0.511, 0.009, 0.622, -0.005, 0.733, -0.005, 1, 0.889, -0.005, 1.044, 0.053, 1.2, 0.053, 1, 1.278, 0.053, 1.356, -0.105, 1.433, -0.105, 1, 1.533, -0.105, 1.633, 0.679, 1.733, 0.679, 1, 1.856, 0.679, 1.978, -0.786, 2.1, -0.786, 1, 2.222, -0.786, 2.344, 0.318, 2.467, 0.318, 1, 2.578, 0.318, 2.689, -0.425, 2.8, -0.425, 1, 2.911, -0.425, 3.022, 0.167, 3.133, 0.167, 1, 3.244, 0.167, 3.356, -0.124, 3.467, -0.124, 1, 3.633, -0.124, 3.8, 0.301, 3.967, 0.301, 1, 4.1, 0.301, 4.233, -0.354, 4.367, -0.354, 1, 4.489, -0.354, 4.611, 0.478, 4.733, 0.478, 1, 4.878, 0.478, 5.022, -0.302, 5.167, -0.302, 1, 5.356, -0.302, 5.544, 0.056, 5.733, 0.163]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0.127, 1, 0.067, 0.072, 0.133, -0.12, 0.2, -0.12, 1, 0.322, -0.12, 0.444, 0.066, 0.567, 0.066, 1, 0.678, 0.066, 0.789, -0.037, 0.9, -0.037, 1, 1.022, -0.037, 1.144, 0.07, 1.267, 0.07, 1, 1.356, 0.07, 1.444, -0.109, 1.533, -0.109, 1, 1.644, -0.109, 1.756, 0.875, 1.867, 0.875, 1, 1.989, 0.875, 2.111, -1.374, 2.233, -1.374, 1, 2.344, -1.374, 2.456, 0.959, 2.567, 0.959, 1, 2.689, 0.959, 2.811, -1.017, 2.933, -1.017, 1, 3.044, -1.017, 3.156, 0.659, 3.267, 0.659, 1, 3.378, 0.659, 3.489, -0.448, 3.6, -0.448, 1, 3.733, -0.448, 3.867, 0.556, 4, 0.556, 1, 4.144, 0.556, 4.289, -0.609, 4.433, -0.609, 1, 4.567, -0.609, 4.7, 0.844, 4.833, 0.844, 1, 4.967, 0.844, 5.1, -0.713, 5.233, -0.713, 1, 5.378, -0.713, 5.522, 0.226, 5.667, 0.226, 1, 5.689, 0.226, 5.711, 0.221, 5.733, 0.213]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0.005, 1, 0.022, 0.005, 0.044, 0.005, 0.067, 0.005, 1, 0.256, 0.005, 0.444, -0.002, 0.633, -0.002, 1, 0.733, -0.002, 0.833, 0, 0.933, 0, 1, 1.011, 0, 1.089, -0.349, 1.167, -0.349, 1, 1.289, -0.349, 1.411, 2.189, 1.533, 2.189, 1, 1.811, 2.189, 2.089, -4.085, 2.367, -4.085, 1, 2.833, -4.085, 3.3, 4.157, 3.767, 4.157, 1, 3.911, 4.157, 4.056, 2.805, 4.2, 2.805, 1, 4.311, 2.805, 4.422, 3.415, 4.533, 3.415, 1, 4.767, 3.415, 5, 0.029, 5.233, 0.029, 1, 5.4, 0.029, 5.567, 1.022, 5.733, 1.353]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, -0.002, 1, 0.011, -0.003, 0.022, -0.003, 0.033, -0.003, 1, 0.3, -0.003, 0.567, 0.001, 0.833, 0.001, 1, 0.867, 0.001, 0.9, 0.001, 0.933, 0.001, 1, 1, 0.001, 1.067, 0.276, 1.133, 0.276, 1, 1.289, 0.276, 1.444, -2.381, 1.6, -2.381, 1, 1.933, -2.381, 2.267, 2.174, 2.6, 2.174, 1, 2.633, 2.174, 2.667, 2.152, 2.7, 2.152, 1, 2.711, 2.152, 2.722, 2.153, 2.733, 2.153, 1, 3.078, 2.153, 3.422, -0.828, 3.767, -0.828, 1, 3.889, -0.828, 4.011, -0.412, 4.133, -0.412, 1, 4.3, -0.412, 4.467, -0.926, 4.633, -0.926, 1, 4.844, -0.926, 5.056, 0.502, 5.267, 0.502, 1, 5.422, 0.502, 5.578, 0.215, 5.733, 0.023]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.01, 1, 0.022, -0.013, 0.044, -0.017, 0.067, -0.017, 1, 0.178, -0.017, 0.289, 0.009, 0.4, 0.009, 1, 0.511, 0.009, 0.622, -0.005, 0.733, -0.005, 1, 0.889, -0.005, 1.044, 0.053, 1.2, 0.053, 1, 1.278, 0.053, 1.356, -0.105, 1.433, -0.105, 1, 1.533, -0.105, 1.633, 0.679, 1.733, 0.679, 1, 1.856, 0.679, 1.978, -0.786, 2.1, -0.786, 1, 2.222, -0.786, 2.344, 0.318, 2.467, 0.318, 1, 2.578, 0.318, 2.689, -0.425, 2.8, -0.425, 1, 2.911, -0.425, 3.022, 0.167, 3.133, 0.167, 1, 3.244, 0.167, 3.356, -0.124, 3.467, -0.124, 1, 3.633, -0.124, 3.8, 0.301, 3.967, 0.301, 1, 4.1, 0.301, 4.233, -0.354, 4.367, -0.354, 1, 4.489, -0.354, 4.611, 0.478, 4.733, 0.478, 1, 4.878, 0.478, 5.022, -0.302, 5.167, -0.302, 1, 5.356, -0.302, 5.544, 0.056, 5.733, 0.163]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0.005, 1, 0.022, 0.005, 0.044, 0.005, 0.067, 0.005, 1, 0.256, 0.005, 0.444, -0.002, 0.633, -0.002, 1, 0.733, -0.002, 0.833, 0, 0.933, 0, 1, 1.011, 0, 1.089, -0.349, 1.167, -0.349, 1, 1.289, -0.349, 1.411, 2.189, 1.533, 2.189, 1, 1.811, 2.189, 2.089, -4.085, 2.367, -4.085, 1, 2.833, -4.085, 3.3, 4.157, 3.767, 4.157, 1, 3.911, 4.157, 4.056, 2.805, 4.2, 2.805, 1, 4.311, 2.805, 4.422, 3.415, 4.533, 3.415, 1, 4.767, 3.415, 5, 0.029, 5.233, 0.029, 1, 5.4, 0.029, 5.567, 1.022, 5.733, 1.353]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, -0.002, 1, 0.011, -0.003, 0.022, -0.003, 0.033, -0.003, 1, 0.3, -0.003, 0.567, 0.001, 0.833, 0.001, 1, 0.867, 0.001, 0.9, 0.001, 0.933, 0.001, 1, 1, 0.001, 1.067, 0.276, 1.133, 0.276, 1, 1.289, 0.276, 1.444, -2.381, 1.6, -2.381, 1, 1.933, -2.381, 2.267, 2.174, 2.6, 2.174, 1, 2.633, 2.174, 2.667, 2.152, 2.7, 2.152, 1, 2.711, 2.152, 2.722, 2.153, 2.733, 2.153, 1, 3.078, 2.153, 3.422, -0.828, 3.767, -0.828, 1, 3.889, -0.828, 4.011, -0.412, 4.133, -0.412, 1, 4.3, -0.412, 4.467, -0.926, 4.633, -0.926, 1, 4.844, -0.926, 5.056, 0.502, 5.267, 0.502, 1, 5.422, 0.502, 5.578, 0.215, 5.733, 0.023]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, -0.01, 1, 0.022, -0.013, 0.044, -0.017, 0.067, -0.017, 1, 0.178, -0.017, 0.289, 0.009, 0.4, 0.009, 1, 0.511, 0.009, 0.622, -0.005, 0.733, -0.005, 1, 0.889, -0.005, 1.044, 0.053, 1.2, 0.053, 1, 1.278, 0.053, 1.356, -0.105, 1.433, -0.105, 1, 1.533, -0.105, 1.633, 0.679, 1.733, 0.679, 1, 1.856, 0.679, 1.978, -0.786, 2.1, -0.786, 1, 2.222, -0.786, 2.344, 0.318, 2.467, 0.318, 1, 2.578, 0.318, 2.689, -0.425, 2.8, -0.425, 1, 2.911, -0.425, 3.022, 0.167, 3.133, 0.167, 1, 3.244, 0.167, 3.356, -0.124, 3.467, -0.124, 1, 3.633, -0.124, 3.8, 0.301, 3.967, 0.301, 1, 4.1, 0.301, 4.233, -0.354, 4.367, -0.354, 1, 4.489, -0.354, 4.611, 0.478, 4.733, 0.478, 1, 4.878, 0.478, 5.022, -0.302, 5.167, -0.302, 1, 5.356, -0.302, 5.544, 0.056, 5.733, 0.163]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, 0.127, 1, 0.067, 0.072, 0.133, -0.12, 0.2, -0.12, 1, 0.322, -0.12, 0.444, 0.066, 0.567, 0.066, 1, 0.678, 0.066, 0.789, -0.037, 0.9, -0.037, 1, 1.022, -0.037, 1.144, 0.07, 1.267, 0.07, 1, 1.356, 0.07, 1.444, -0.109, 1.533, -0.109, 1, 1.644, -0.109, 1.756, 0.875, 1.867, 0.875, 1, 1.989, 0.875, 2.111, -1.374, 2.233, -1.374, 1, 2.344, -1.374, 2.456, 0.959, 2.567, 0.959, 1, 2.689, 0.959, 2.811, -1.017, 2.933, -1.017, 1, 3.044, -1.017, 3.156, 0.659, 3.267, 0.659, 1, 3.378, 0.659, 3.489, -0.448, 3.6, -0.448, 1, 3.733, -0.448, 3.867, 0.556, 4, 0.556, 1, 4.144, 0.556, 4.289, -0.609, 4.433, -0.609, 1, 4.567, -0.609, 4.7, 0.844, 4.833, 0.844, 1, 4.967, 0.844, 5.1, -0.713, 5.233, -0.713, 1, 5.378, -0.713, 5.522, 0.226, 5.667, 0.226, 1, 5.689, 0.226, 5.711, 0.221, 5.733, 0.213]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0.005, 1, 0.022, 0.005, 0.044, 0.005, 0.067, 0.005, 1, 0.256, 0.005, 0.444, -0.002, 0.633, -0.002, 1, 0.733, -0.002, 0.833, 0, 0.933, 0, 1, 1.011, 0, 1.089, -0.349, 1.167, -0.349, 1, 1.289, -0.349, 1.411, 2.189, 1.533, 2.189, 1, 1.811, 2.189, 2.089, -4.085, 2.367, -4.085, 1, 2.833, -4.085, 3.3, 4.157, 3.767, 4.157, 1, 3.911, 4.157, 4.056, 2.805, 4.2, 2.805, 1, 4.311, 2.805, 4.422, 3.415, 4.533, 3.415, 1, 4.767, 3.415, 5, 0.029, 5.233, 0.029, 1, 5.4, 0.029, 5.567, 1.022, 5.733, 1.353]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, -0.002, 1, 0.011, -0.003, 0.022, -0.003, 0.033, -0.003, 1, 0.3, -0.003, 0.567, 0.001, 0.833, 0.001, 1, 0.867, 0.001, 0.9, 0.001, 0.933, 0.001, 1, 1, 0.001, 1.067, 0.276, 1.133, 0.276, 1, 1.289, 0.276, 1.444, -2.381, 1.6, -2.381, 1, 1.933, -2.381, 2.267, 2.174, 2.6, 2.174, 1, 2.633, 2.174, 2.667, 2.152, 2.7, 2.152, 1, 2.711, 2.152, 2.722, 2.153, 2.733, 2.153, 1, 3.078, 2.153, 3.422, -0.828, 3.767, -0.828, 1, 3.889, -0.828, 4.011, -0.412, 4.133, -0.412, 1, 4.3, -0.412, 4.467, -0.926, 4.633, -0.926, 1, 4.844, -0.926, 5.056, 0.502, 5.267, 0.502, 1, 5.422, 0.502, 5.578, 0.215, 5.733, 0.023]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, -0.01, 1, 0.022, -0.013, 0.044, -0.017, 0.067, -0.017, 1, 0.178, -0.017, 0.289, 0.009, 0.4, 0.009, 1, 0.511, 0.009, 0.622, -0.005, 0.733, -0.005, 1, 0.889, -0.005, 1.044, 0.053, 1.2, 0.053, 1, 1.278, 0.053, 1.356, -0.105, 1.433, -0.105, 1, 1.533, -0.105, 1.633, 0.679, 1.733, 0.679, 1, 1.856, 0.679, 1.978, -0.786, 2.1, -0.786, 1, 2.222, -0.786, 2.344, 0.318, 2.467, 0.318, 1, 2.578, 0.318, 2.689, -0.425, 2.8, -0.425, 1, 2.911, -0.425, 3.022, 0.167, 3.133, 0.167, 1, 3.244, 0.167, 3.356, -0.124, 3.467, -0.124, 1, 3.633, -0.124, 3.8, 0.301, 3.967, 0.301, 1, 4.1, 0.301, 4.233, -0.354, 4.367, -0.354, 1, 4.489, -0.354, 4.611, 0.478, 4.733, 0.478, 1, 4.878, 0.478, 5.022, -0.302, 5.167, -0.302, 1, 5.356, -0.302, 5.544, 0.056, 5.733, 0.163]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0.005, 1, 0.022, 0.005, 0.044, 0.005, 0.067, 0.005, 1, 0.256, 0.005, 0.444, -0.002, 0.633, -0.002, 1, 0.733, -0.002, 0.833, 0, 0.933, 0, 1, 1.011, 0, 1.089, -0.349, 1.167, -0.349, 1, 1.289, -0.349, 1.411, 2.189, 1.533, 2.189, 1, 1.811, 2.189, 2.089, -4.085, 2.367, -4.085, 1, 2.833, -4.085, 3.3, 4.157, 3.767, 4.157, 1, 3.911, 4.157, 4.056, 2.805, 4.2, 2.805, 1, 4.311, 2.805, 4.422, 3.415, 4.533, 3.415, 1, 4.767, 3.415, 5, 0.029, 5.233, 0.029, 1, 5.4, 0.029, 5.567, 1.022, 5.733, 1.353]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, -0.002, 1, 0.011, -0.003, 0.022, -0.003, 0.033, -0.003, 1, 0.3, -0.003, 0.567, 0.001, 0.833, 0.001, 1, 0.867, 0.001, 0.9, 0.001, 0.933, 0.001, 1, 1, 0.001, 1.067, 0.276, 1.133, 0.276, 1, 1.289, 0.276, 1.444, -2.381, 1.6, -2.381, 1, 1.933, -2.381, 2.267, 2.174, 2.6, 2.174, 1, 2.633, 2.174, 2.667, 2.152, 2.7, 2.152, 1, 2.711, 2.152, 2.722, 2.153, 2.733, 2.153, 1, 3.078, 2.153, 3.422, -0.828, 3.767, -0.828, 1, 3.889, -0.828, 4.011, -0.412, 4.133, -0.412, 1, 4.3, -0.412, 4.467, -0.926, 4.633, -0.926, 1, 4.844, -0.926, 5.056, 0.502, 5.267, 0.502, 1, 5.422, 0.502, 5.578, 0.215, 5.733, 0.023]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, -0.01, 1, 0.022, -0.013, 0.044, -0.017, 0.067, -0.017, 1, 0.178, -0.017, 0.289, 0.009, 0.4, 0.009, 1, 0.511, 0.009, 0.622, -0.005, 0.733, -0.005, 1, 0.889, -0.005, 1.044, 0.053, 1.2, 0.053, 1, 1.278, 0.053, 1.356, -0.105, 1.433, -0.105, 1, 1.533, -0.105, 1.633, 0.679, 1.733, 0.679, 1, 1.856, 0.679, 1.978, -0.786, 2.1, -0.786, 1, 2.222, -0.786, 2.344, 0.318, 2.467, 0.318, 1, 2.578, 0.318, 2.689, -0.425, 2.8, -0.425, 1, 2.911, -0.425, 3.022, 0.167, 3.133, 0.167, 1, 3.244, 0.167, 3.356, -0.124, 3.467, -0.124, 1, 3.633, -0.124, 3.8, 0.301, 3.967, 0.301, 1, 4.1, 0.301, 4.233, -0.354, 4.367, -0.354, 1, 4.489, -0.354, 4.611, 0.478, 4.733, 0.478, 1, 4.878, 0.478, 5.022, -0.302, 5.167, -0.302, 1, 5.356, -0.302, 5.544, 0.056, 5.733, 0.163]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0.005, 1, 0.022, 0.005, 0.044, 0.005, 0.067, 0.005, 1, 0.256, 0.005, 0.444, -0.002, 0.633, -0.002, 1, 0.733, -0.002, 0.833, 0, 0.933, 0, 1, 1.011, 0, 1.089, -0.349, 1.167, -0.349, 1, 1.289, -0.349, 1.411, 2.189, 1.533, 2.189, 1, 1.811, 2.189, 2.089, -4.085, 2.367, -4.085, 1, 2.833, -4.085, 3.3, 4.157, 3.767, 4.157, 1, 3.911, 4.157, 4.056, 2.805, 4.2, 2.805, 1, 4.311, 2.805, 4.422, 3.415, 4.533, 3.415, 1, 4.767, 3.415, 5, 0.029, 5.233, 0.029, 1, 5.4, 0.029, 5.567, 1.022, 5.733, 1.353]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, -0.002, 1, 0.011, -0.003, 0.022, -0.003, 0.033, -0.003, 1, 0.3, -0.003, 0.567, 0.001, 0.833, 0.001, 1, 0.867, 0.001, 0.9, 0.001, 0.933, 0.001, 1, 1, 0.001, 1.067, 0.276, 1.133, 0.276, 1, 1.289, 0.276, 1.444, -2.381, 1.6, -2.381, 1, 1.933, -2.381, 2.267, 2.174, 2.6, 2.174, 1, 2.633, 2.174, 2.667, 2.152, 2.7, 2.152, 1, 2.711, 2.152, 2.722, 2.153, 2.733, 2.153, 1, 3.078, 2.153, 3.422, -0.828, 3.767, -0.828, 1, 3.889, -0.828, 4.011, -0.412, 4.133, -0.412, 1, 4.3, -0.412, 4.467, -0.926, 4.633, -0.926, 1, 4.844, -0.926, 5.056, 0.502, 5.267, 0.502, 1, 5.422, 0.502, 5.578, 0.215, 5.733, 0.023]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, -0.01, 1, 0.022, -0.013, 0.044, -0.017, 0.067, -0.017, 1, 0.178, -0.017, 0.289, 0.009, 0.4, 0.009, 1, 0.511, 0.009, 0.622, -0.005, 0.733, -0.005, 1, 0.889, -0.005, 1.044, 0.053, 1.2, 0.053, 1, 1.278, 0.053, 1.356, -0.105, 1.433, -0.105, 1, 1.533, -0.105, 1.633, 0.679, 1.733, 0.679, 1, 1.856, 0.679, 1.978, -0.786, 2.1, -0.786, 1, 2.222, -0.786, 2.344, 0.318, 2.467, 0.318, 1, 2.578, 0.318, 2.689, -0.425, 2.8, -0.425, 1, 2.911, -0.425, 3.022, 0.167, 3.133, 0.167, 1, 3.244, 0.167, 3.356, -0.124, 3.467, -0.124, 1, 3.633, -0.124, 3.8, 0.301, 3.967, 0.301, 1, 4.1, 0.301, 4.233, -0.354, 4.367, -0.354, 1, 4.489, -0.354, 4.611, 0.478, 4.733, 0.478, 1, 4.878, 0.478, 5.022, -0.302, 5.167, -0.302, 1, 5.356, -0.302, 5.544, 0.056, 5.733, 0.163]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, -0.071, 1, 0.067, -0.338, 0.133, -1.275, 0.2, -1.275, 1, 0.278, -1.275, 0.356, 0.585, 0.433, 0.585, 1, 0.544, 0.585, 0.656, -0.152, 0.767, -0.152, 1, 0.833, -0.152, 0.9, -0.005, 0.967, -0.005, 1, 1.011, -0.005, 1.056, -0.062, 1.1, -0.062, 1, 1.189, -0.062, 1.278, 0.731, 1.367, 0.731, 1, 1.511, 0.731, 1.656, -2.066, 1.8, -2.066, 1, 1.9, -2.066, 2, -1.429, 2.1, -1.429, 1, 2.189, -1.429, 2.278, -1.579, 2.367, -1.579, 1, 2.811, -1.579, 3.256, 2.39, 3.7, 2.39, 1, 3.8, 2.39, 3.9, 1.912, 4, 1.912, 1, 4.122, 1.912, 4.244, 2.38, 4.367, 2.38, 1, 4.633, 2.38, 4.9, 0.502, 5.167, 0.502, 1, 5.267, 0.502, 5.367, 0.82, 5.467, 0.82, 1, 5.556, 0.82, 5.644, 0.766, 5.733, 0.745]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, 0.872, 0.167, 0.872, 1, 0.222, 0.872, 0.278, -1.282, 0.333, -1.282, 1, 0.433, -1.282, 0.533, 0.734, 0.633, 0.734, 1, 0.733, 0.734, 0.833, -0.307, 0.933, -0.307, 1, 1, -0.307, 1.067, 0.027, 1.133, 0.027, 1, 1.189, 0.027, 1.244, -0.361, 1.3, -0.361, 1, 1.4, -0.361, 1.5, 1.195, 1.6, 1.195, 1, 1.711, 1.195, 1.822, -0.649, 1.933, -0.649, 1, 2.044, -0.649, 2.156, 0.308, 2.267, 0.308, 1, 2.389, 0.308, 2.511, -0.432, 2.633, -0.432, 1, 2.711, -0.432, 2.789, -0.282, 2.867, -0.282, 1, 2.878, -0.282, 2.889, -0.284, 2.9, -0.284, 1, 2.911, -0.284, 2.922, -0.277, 2.933, -0.277, 1, 2.944, -0.277, 2.956, -0.285, 2.967, -0.285, 1, 2.978, -0.285, 2.989, -0.285, 3, -0.285, 1, 3.078, -0.285, 3.156, -0.35, 3.233, -0.35, 1, 3.444, -0.35, 3.656, 0.344, 3.867, 0.344, 1, 3.989, 0.344, 4.111, -0.295, 4.233, -0.295, 1, 4.356, -0.295, 4.478, 0.42, 4.6, 0.42, 1, 4.722, 0.42, 4.844, 0.131, 4.967, 0.131, 1, 4.978, 0.131, 4.989, 0.132, 5, 0.132, 1, 5.111, 0.132, 5.222, -0.225, 5.333, -0.225, 1, 5.433, -0.225, 5.533, 0.114, 5.633, 0.114, 1, 5.667, 0.114, 5.7, 0.1, 5.733, 0.08]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0.027, 1, 0.078, 0.151, 0.156, 0.645, 0.233, 0.645, 1, 0.311, 0.645, 0.389, -1.538, 0.467, -1.538, 1, 0.567, -1.538, 0.667, 1.141, 0.767, 1.141, 1, 0.878, 1.141, 0.989, -0.513, 1.1, -0.513, 1, 1.311, -0.513, 1.522, 2.356, 1.733, 2.356, 1, 1.856, 2.356, 1.978, -1.174, 2.1, -1.174, 1, 2.2, -1.174, 2.3, 0.455, 2.4, 0.455, 1, 2.533, 0.455, 2.667, -1.194, 2.8, -1.194, 1, 2.922, -1.194, 3.044, -0.771, 3.167, -0.771, 1, 3.222, -0.771, 3.278, -0.804, 3.333, -0.804, 1, 3.556, -0.804, 3.778, 0.583, 4, 0.583, 1, 4.111, 0.583, 4.222, -0.473, 4.333, -0.473, 1, 4.467, -0.473, 4.6, 0.987, 4.733, 0.987, 1, 4.978, 0.987, 5.222, -0.361, 5.467, -0.361, 1, 5.556, -0.361, 5.644, 0.007, 5.733, 0.154]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.001, 0.033, -0.001, 1, 0.133, -0.001, 0.233, 0.8, 0.333, 0.8, 1, 0.433, 0.8, 0.533, -1.563, 0.633, -1.563, 1, 0.733, -1.563, 0.833, 1.32, 0.933, 1.32, 1, 1.056, 1.32, 1.178, -0.717, 1.3, -0.717, 1, 1.489, -0.717, 1.678, 2.091, 1.867, 2.091, 1, 1.989, 2.091, 2.111, -1.556, 2.233, -1.556, 1, 2.344, -1.556, 2.456, 0.974, 2.567, 0.974, 1, 2.689, 0.974, 2.811, -0.852, 2.933, -0.852, 1, 3.044, -0.852, 3.156, -0.184, 3.267, -0.184, 1, 3.389, -0.184, 3.511, -0.513, 3.633, -0.513, 1, 3.8, -0.513, 3.967, 0.64, 4.133, 0.64, 1, 4.244, 0.64, 4.356, -0.745, 4.467, -0.745, 1, 4.6, -0.745, 4.733, 0.889, 4.867, 0.889, 1, 5.111, 0.889, 5.356, -0.356, 5.6, -0.356, 1, 5.644, -0.356, 5.689, -0.256, 5.733, -0.136]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0.004, 1, 0.022, 0.003, 0.044, 0.002, 0.067, 0.002, 1, 0.189, 0.002, 0.311, 0.752, 0.433, 0.752, 1, 0.533, 0.752, 0.633, -1.672, 0.733, -1.672, 1, 0.844, -1.672, 0.956, 1.588, 1.067, 1.588, 1, 1.2, 1.588, 1.333, -1.149, 1.467, -1.149, 1, 1.633, -1.149, 1.8, 2.427, 1.967, 2.427, 1, 2.089, 2.427, 2.211, -2.003, 2.333, -2.003, 1, 2.456, -2.003, 2.578, 1.314, 2.7, 1.314, 1, 2.822, 1.314, 2.944, -1.189, 3.067, -1.189, 1, 3.178, -1.189, 3.289, -0.104, 3.4, -0.104, 1, 3.522, -0.104, 3.644, -0.624, 3.767, -0.624, 1, 3.922, -0.624, 4.078, 0.78, 4.233, 0.78, 1, 4.356, 0.78, 4.478, -0.929, 4.6, -0.929, 1, 4.733, -0.929, 4.867, 1.168, 5, 1.168, 1, 5.222, 1.168, 5.444, -0.38, 5.667, -0.38, 1, 5.689, -0.38, 5.711, -0.36, 5.733, -0.327]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, -0.071, 1, 0.067, -0.338, 0.133, -1.275, 0.2, -1.275, 1, 0.278, -1.275, 0.356, 0.585, 0.433, 0.585, 1, 0.544, 0.585, 0.656, -0.152, 0.767, -0.152, 1, 0.833, -0.152, 0.9, -0.005, 0.967, -0.005, 1, 1.011, -0.005, 1.056, -0.062, 1.1, -0.062, 1, 1.189, -0.062, 1.278, 0.731, 1.367, 0.731, 1, 1.511, 0.731, 1.656, -2.066, 1.8, -2.066, 1, 1.9, -2.066, 2, -1.429, 2.1, -1.429, 1, 2.189, -1.429, 2.278, -1.579, 2.367, -1.579, 1, 2.811, -1.579, 3.256, 2.39, 3.7, 2.39, 1, 3.8, 2.39, 3.9, 1.912, 4, 1.912, 1, 4.122, 1.912, 4.244, 2.38, 4.367, 2.38, 1, 4.633, 2.38, 4.9, 0.502, 5.167, 0.502, 1, 5.267, 0.502, 5.367, 0.82, 5.467, 0.82, 1, 5.556, 0.82, 5.644, 0.766, 5.733, 0.745]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, 0.872, 0.167, 0.872, 1, 0.222, 0.872, 0.278, -1.282, 0.333, -1.282, 1, 0.433, -1.282, 0.533, 0.734, 0.633, 0.734, 1, 0.733, 0.734, 0.833, -0.307, 0.933, -0.307, 1, 1, -0.307, 1.067, 0.027, 1.133, 0.027, 1, 1.189, 0.027, 1.244, -0.361, 1.3, -0.361, 1, 1.4, -0.361, 1.5, 1.195, 1.6, 1.195, 1, 1.711, 1.195, 1.822, -0.649, 1.933, -0.649, 1, 2.044, -0.649, 2.156, 0.308, 2.267, 0.308, 1, 2.389, 0.308, 2.511, -0.432, 2.633, -0.432, 1, 2.711, -0.432, 2.789, -0.282, 2.867, -0.282, 1, 2.878, -0.282, 2.889, -0.284, 2.9, -0.284, 1, 2.911, -0.284, 2.922, -0.277, 2.933, -0.277, 1, 2.944, -0.277, 2.956, -0.285, 2.967, -0.285, 1, 2.978, -0.285, 2.989, -0.285, 3, -0.285, 1, 3.078, -0.285, 3.156, -0.35, 3.233, -0.35, 1, 3.444, -0.35, 3.656, 0.344, 3.867, 0.344, 1, 3.989, 0.344, 4.111, -0.295, 4.233, -0.295, 1, 4.356, -0.295, 4.478, 0.42, 4.6, 0.42, 1, 4.722, 0.42, 4.844, 0.131, 4.967, 0.131, 1, 4.978, 0.131, 4.989, 0.132, 5, 0.132, 1, 5.111, 0.132, 5.222, -0.225, 5.333, -0.225, 1, 5.433, -0.225, 5.533, 0.114, 5.633, 0.114, 1, 5.667, 0.114, 5.7, 0.1, 5.733, 0.08]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0.027, 1, 0.078, 0.151, 0.156, 0.645, 0.233, 0.645, 1, 0.311, 0.645, 0.389, -1.538, 0.467, -1.538, 1, 0.567, -1.538, 0.667, 1.141, 0.767, 1.141, 1, 0.878, 1.141, 0.989, -0.513, 1.1, -0.513, 1, 1.311, -0.513, 1.522, 2.356, 1.733, 2.356, 1, 1.856, 2.356, 1.978, -1.174, 2.1, -1.174, 1, 2.2, -1.174, 2.3, 0.455, 2.4, 0.455, 1, 2.533, 0.455, 2.667, -1.194, 2.8, -1.194, 1, 2.922, -1.194, 3.044, -0.771, 3.167, -0.771, 1, 3.222, -0.771, 3.278, -0.804, 3.333, -0.804, 1, 3.556, -0.804, 3.778, 0.583, 4, 0.583, 1, 4.111, 0.583, 4.222, -0.473, 4.333, -0.473, 1, 4.467, -0.473, 4.6, 0.987, 4.733, 0.987, 1, 4.978, 0.987, 5.222, -0.361, 5.467, -0.361, 1, 5.556, -0.361, 5.644, 0.007, 5.733, 0.154]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.001, 0.033, -0.001, 1, 0.133, -0.001, 0.233, 0.8, 0.333, 0.8, 1, 0.433, 0.8, 0.533, -1.563, 0.633, -1.563, 1, 0.733, -1.563, 0.833, 1.32, 0.933, 1.32, 1, 1.056, 1.32, 1.178, -0.717, 1.3, -0.717, 1, 1.489, -0.717, 1.678, 2.091, 1.867, 2.091, 1, 1.989, 2.091, 2.111, -1.556, 2.233, -1.556, 1, 2.344, -1.556, 2.456, 0.974, 2.567, 0.974, 1, 2.689, 0.974, 2.811, -0.852, 2.933, -0.852, 1, 3.044, -0.852, 3.156, -0.184, 3.267, -0.184, 1, 3.389, -0.184, 3.511, -0.513, 3.633, -0.513, 1, 3.8, -0.513, 3.967, 0.64, 4.133, 0.64, 1, 4.244, 0.64, 4.356, -0.745, 4.467, -0.745, 1, 4.6, -0.745, 4.733, 0.889, 4.867, 0.889, 1, 5.111, 0.889, 5.356, -0.356, 5.6, -0.356, 1, 5.644, -0.356, 5.689, -0.256, 5.733, -0.136]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0.004, 1, 0.022, 0.003, 0.044, 0.002, 0.067, 0.002, 1, 0.189, 0.002, 0.311, 0.752, 0.433, 0.752, 1, 0.533, 0.752, 0.633, -1.672, 0.733, -1.672, 1, 0.844, -1.672, 0.956, 1.588, 1.067, 1.588, 1, 1.2, 1.588, 1.333, -1.149, 1.467, -1.149, 1, 1.633, -1.149, 1.8, 2.427, 1.967, 2.427, 1, 2.089, 2.427, 2.211, -2.003, 2.333, -2.003, 1, 2.456, -2.003, 2.578, 1.314, 2.7, 1.314, 1, 2.822, 1.314, 2.944, -1.189, 3.067, -1.189, 1, 3.178, -1.189, 3.289, -0.104, 3.4, -0.104, 1, 3.522, -0.104, 3.644, -0.624, 3.767, -0.624, 1, 3.922, -0.624, 4.078, 0.78, 4.233, 0.78, 1, 4.356, 0.78, 4.478, -0.929, 4.6, -0.929, 1, 4.733, -0.929, 4.867, 1.168, 5, 1.168, 1, 5.222, 1.168, 5.444, -0.38, 5.667, -0.38, 1, 5.689, -0.38, 5.711, -0.36, 5.733, -0.327]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 1.911, 0, 3.822, 0, 5.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, -30, 1, 1.911, -30, 3.822, -30, 5.733, -30]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, -30, 1, 1.911, -30, 3.822, -30, 5.733, -30]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 5.73, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 5.73, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 5.73, 1]}]}