{"version": 3, "sources": ["webpack://user-profile-picture/./node_modules/axios/index.js", "webpack://user-profile-picture/./node_modules/axios/lib/adapters/xhr.js", "webpack://user-profile-picture/./node_modules/axios/lib/axios.js", "webpack://user-profile-picture/./node_modules/axios/lib/cancel/Cancel.js", "webpack://user-profile-picture/./node_modules/axios/lib/cancel/CancelToken.js", "webpack://user-profile-picture/./node_modules/axios/lib/cancel/isCancel.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/Axios.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/InterceptorManager.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/buildFullPath.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/createError.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/dispatchRequest.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/enhanceError.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/mergeConfig.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/settle.js", "webpack://user-profile-picture/./node_modules/axios/lib/core/transformData.js", "webpack://user-profile-picture/./node_modules/axios/lib/defaults.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/bind.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/buildURL.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/combineURLs.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/cookies.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/isAbsoluteURL.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/isURLSameOrigin.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/normalizeHeaderName.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/parseHeaders.js", "webpack://user-profile-picture/./node_modules/axios/lib/helpers/spread.js", "webpack://user-profile-picture/./node_modules/axios/lib/utils.js", "webpack://user-profile-picture/./node_modules/classnames/index.js", "webpack://user-profile-picture/webpack/bootstrap", "webpack://user-profile-picture/webpack/runtime/compat get default export", "webpack://user-profile-picture/webpack/runtime/define property getters", "webpack://user-profile-picture/webpack/runtime/hasOwnProperty shorthand", "webpack://user-profile-picture/./src/block/profile.js", "webpack://user-profile-picture/./src/block/profile-legacy.js", "webpack://user-profile-picture/./src/block/block.js"], "names": ["module", "exports", "utils", "settle", "buildURL", "buildFullPath", "parseHeaders", "isURLSameOrigin", "createError", "config", "Promise", "resolve", "reject", "requestData", "data", "requestHeaders", "headers", "isFormData", "request", "XMLHttpRequest", "auth", "username", "password", "Authorization", "btoa", "fullPath", "baseURL", "url", "open", "method", "toUpperCase", "params", "paramsSerializer", "timeout", "onreadystatechange", "readyState", "status", "responseURL", "indexOf", "responseHeaders", "getAllResponseHeaders", "response", "responseType", "responseText", "statusText", "<PERSON>ab<PERSON>", "onerror", "ontimeout", "timeoutErrorMessage", "isStandardBrowserEnv", "cookies", "xsrfValue", "withCredentials", "xsrfCookieName", "read", "undefined", "xsrfHeaderName", "for<PERSON>ach", "val", "key", "toLowerCase", "setRequestHeader", "isUndefined", "e", "onDownloadProgress", "addEventListener", "onUploadProgress", "upload", "cancelToken", "promise", "then", "cancel", "abort", "send", "bind", "A<PERSON>os", "mergeConfig", "createInstance", "defaultConfig", "context", "instance", "prototype", "extend", "axios", "create", "instanceConfig", "defaults", "Cancel", "CancelToken", "isCancel", "all", "promises", "spread", "default", "message", "this", "toString", "__CANCEL__", "executor", "TypeError", "resolvePromise", "token", "reason", "throwIfRequested", "source", "c", "value", "InterceptorManager", "dispatchRequest", "interceptors", "arguments", "chain", "interceptor", "unshift", "fulfilled", "rejected", "push", "length", "shift", "get<PERSON><PERSON>", "replace", "merge", "handlers", "use", "eject", "id", "fn", "h", "isAbsoluteURL", "combineURLs", "requestedURL", "enhanceError", "code", "error", "Error", "transformData", "throwIfCancellationRequested", "transformRequest", "common", "adapter", "transformResponse", "isAxiosError", "toJSON", "name", "description", "number", "fileName", "lineNumber", "columnNumber", "stack", "config1", "config2", "valueFromConfig2Keys", "mergeDeepPropertiesKeys", "defaultToConfig2Keys", "prop", "isObject", "deepMerge", "axios<PERSON><PERSON><PERSON>", "concat", "otherKeys", "Object", "keys", "filter", "validateStatus", "fns", "normalizeHeaderName", "DEFAULT_CONTENT_TYPE", "setContentTypeIfUnset", "process", "call", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isStream", "isFile", "isBlob", "isArrayBuffer<PERSON>iew", "buffer", "isURLSearchParams", "JSON", "stringify", "parse", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thisArg", "args", "Array", "i", "apply", "encode", "encodeURIComponent", "serializedParams", "parts", "isArray", "v", "isDate", "toISOString", "join", "hashmarkIndex", "slice", "relativeURL", "write", "expires", "path", "domain", "secure", "cookie", "isNumber", "Date", "toGMTString", "isString", "document", "match", "RegExp", "decodeURIComponent", "remove", "now", "test", "originURL", "msie", "navigator", "userAgent", "urlParsingNode", "createElement", "resolveURL", "href", "setAttribute", "protocol", "host", "search", "hash", "hostname", "port", "pathname", "char<PERSON>t", "window", "location", "requestURL", "parsed", "normalizedName", "ignoreDuplicateOf", "split", "line", "trim", "substr", "callback", "arr", "isFunction", "obj", "l", "hasOwnProperty", "constructor", "FormData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pipe", "URLSearchParams", "product", "result", "assignValue", "a", "b", "str", "hasOwn", "classNames", "classes", "arg", "argType", "inner", "__webpack_module_cache__", "__webpack_require__", "moduleId", "__webpack_modules__", "n", "getter", "__esModule", "d", "definition", "o", "defineProperty", "enumerable", "get", "wp", "element", "Component", "Fragment", "__", "i18n", "components", "PanelBody", "Placeholder", "RangeControl", "QueryControls", "SelectControl", "Spinner", "TextControl", "ToggleControl", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "withAPIData", "ColorPalette", "editor", "InspectorCont<PERSON><PERSON>", "MediaUpload", "BlockControls", "RichText", "PanelColorSettings", "AlignmentToolbar", "mpp_gutenberg", "rest_url", "nonce", "users", "user_list", "active_user", "profile_picture", "profile_picture_id", "profile_name", "profile_description", "profile_title", "profile_url", "show_website", "j<PERSON><PERSON><PERSON>", "each", "ID", "profile_pictures", "has_profile_picture", "display_name", "is_user_logged_in", "default_image", "permalink", "label", "props", "attributes", "user_id", "active_user_profile", "profileImgURL", "profileImgID", "profileName", "profileTitle", "profileContent", "showWebsite", "setState", "loading", "setAttributes", "profileURL", "showSocialMedia", "state", "mystery_man", "tabbedAuthorSubHeading", "tabbedAuthorProfileTitle", "socialFacebook", "socialGitHub", "socialInstagram", "socialLinkedIn", "socialPinterest", "socialTwitter", "socialWordPress", "socialYouTube", "profile_name_unfiltered", "getLatestPosts", "loadingLatestPosts", "postJSX", "map", "post_title", "latestPosts", "get_users", "image_id", "image_url", "theme", "website", "socialMediaOptions", "tabbedAuthorProfile", "activeTab", "tabbedAuthorProfileHeading", "tabbedAuthorLatestPosts", "profileTabColor", "profileTabPostsColor", "profileTabHeadlineColor", "profileTabPostsTextColor", "profileTabHeadlineTextColor", "profileTabTextColor", "profileLatestPostsOptionsValue", "profileCompactAlignment", "theme_list", "profileViewPosts", "profileViewWebsite", "themes", "socialMediaColors", "profileAlignment", "profileFontSize", "buttonFontSize", "headerFontSize", "profileBackgroundColor", "profileTextColor", "profileAvatar<PERSON>hape", "profileViewPostsBackgroundColor", "profileViewPostsTextColor", "showTitle", "showName", "showDescription", "showViewPosts", "showPostsWidth", "profileWebsiteBackgroundColor", "profileWebsiteTextColor", "padding", "border", "borderRounded", "borderColor", "profileLinkColor", "isSelected", "editable", "className", "profileAvatarShapeOptions", "profileSocialMediaOptions", "profileLatestPostsOptions", "profileCompactOptions", "profileFloat", "<PERSON><PERSON><PERSON><PERSON>", "data-name", "xmlns", "width", "height", "viewBox", "transform", "fill", "title", "options", "onChange", "on_user_change", "Number", "onThemeChange", "onCompactAlignmentChange", "handleWebsiteChange", "checked", "handleSocialMediaChange", "latestPostsTheme", "initialOpen", "colorSettings", "onChangeProfileTabColor", "onChangeProfileTabColorText", "onChangePostsTabColor", "onChangeProfileTabPostColorText", "onChangePostsTabHeadlineColor", "profileTabHeadlineColorText", "onChangeProfileTabHeadlineColorText", "onLatestPostsChange", "min", "max", "step", "handleSocialMediaOptionChange", "handleFacebookChange", "handleTwitterChange", "handleInstagramChange", "handleLinkedInChange", "handleYouTubeChange", "handleGitHubChange", "handlePinterestChange", "handleWordPressChange", "classnames", "style", "borderRadius", "backgroundColor", "color", "float", "margin", "buttonProps", "onSelect", "img", "handleImageChange", "type", "render", "onClick", "src", "alt", "tagName", "placeholder", "fontSize", "onChangeName", "onChangeTitle", "formattingControls", "onChangeProfileText", "role", "onChangeActiveProfileTab", "onChangetabbedAuthorProfile", "onChangeActivePostTab", "onChangetabbedAuthorLatestPosts", "onChangetabbedAuthorProfileHeading", "onChangeTabbedSubHeading", "$", "class", "registerBlockType", "blocks", "icon", "category", "attribute", "selector", "profileViewPostsWidth", "edit", "legacyEdit", "save", "Content", "getEditWrapperProps"], "mappings": ";2FAAAA,OAAOC,QAAU,oBAAjB,M,yECEA,IAAIC,MAAQ,oBAAQ,KAChBC,OAAS,oBAAQ,IACjBC,SAAW,oBAAQ,KACnBC,cAAgB,oBAAQ,IACxBC,aAAe,oBAAQ,KACvBC,gBAAkB,oBAAQ,KAC1BC,YAAc,oBAAQ,IAE1BR,OAAOC,QAAU,SAAoBQ,QACnC,OAAO,IAAIC,SAAQ,SAA4BC,QAASC,QACtD,IAAIC,YAAcJ,OAAOK,KACrBC,eAAiBN,OAAOO,QAExBd,MAAMe,WAAWJ,qBACZE,eAAe,gBAGxB,IAAIG,QAAU,IAAIC,eAGlB,GAAIV,OAAOW,KAAM,CACf,IAAIC,SAAWZ,OAAOW,KAAKC,UAAY,GACnCC,SAAWb,OAAOW,KAAKE,UAAY,GACvCP,eAAeQ,cAAgB,SAAWC,KAAKH,SAAW,IAAMC,UAGlE,IAAIG,SAAWpB,cAAcI,OAAOiB,QAASjB,OAAOkB,KA4EpD,GA3EAT,QAAQU,KAAKnB,OAAOoB,OAAOC,cAAe1B,SAASqB,SAAUhB,OAAOsB,OAAQtB,OAAOuB,mBAAmB,GAGtGd,QAAQe,QAAUxB,OAAOwB,QAGzBf,QAAQgB,mBAAqB,WAC3B,GAAKhB,SAAkC,IAAvBA,QAAQiB,aAQD,IAAnBjB,QAAQkB,QAAkBlB,QAAQmB,aAAwD,IAAzCnB,QAAQmB,YAAYC,QAAQ,UAAjF,CAKA,IAAIC,gBAAkB,0BAA2BrB,QAAUZ,aAAaY,QAAQsB,yBAA2B,KAEvGC,SAAW,CACb3B,KAFkBL,OAAOiC,cAAwC,SAAxBjC,OAAOiC,aAAiDxB,QAAQuB,SAA/BvB,QAAQyB,aAGlFP,OAAQlB,QAAQkB,OAChBQ,WAAY1B,QAAQ0B,WACpB5B,QAASuB,gBACT9B,OACAS,SAGFf,OAAOQ,QAASC,OAAQ6B,UAGxBvB,QAAU,OAIZA,QAAQ2B,QAAU,WACX3B,UAILN,OAAOJ,YAAY,kBAAmBC,OAAQ,eAAgBS,UAG9DA,QAAU,OAIZA,QAAQ4B,QAAU,WAGhBlC,OAAOJ,YAAY,gBAAiBC,OAAQ,KAAMS,UAGlDA,QAAU,MAIZA,QAAQ6B,UAAY,WAClB,IAAIC,oBAAsB,cAAgBvC,OAAOwB,QAAU,cACvDxB,OAAOuC,sBACTA,oBAAsBvC,OAAOuC,qBAE/BpC,OAAOJ,YAAYwC,oBAAqBvC,OAAQ,eAC9CS,UAGFA,QAAU,MAMRhB,MAAM+C,uBAAwB,CAChC,IAAIC,QAAU,oBAAQ,KAGlBC,WAAa1C,OAAO2C,iBAAmB7C,gBAAgBkB,YAAchB,OAAO4C,eAC9EH,QAAQI,KAAK7C,OAAO4C,qBACpBE,EAEEJ,YACFpC,eAAeN,OAAO+C,gBAAkBL,WAuB5C,GAlBI,qBAAsBjC,SACxBhB,MAAMuD,QAAQ1C,gBAAgB,SAA0B2C,IAAKC,UAChC,IAAhB9C,aAAqD,iBAAtB8C,IAAIC,qBAErC7C,eAAe4C,KAGtBzC,QAAQ2C,iBAAiBF,IAAKD,QAM/BxD,MAAM4D,YAAYrD,OAAO2C,mBAC5BlC,QAAQkC,kBAAoB3C,OAAO2C,iBAIjC3C,OAAOiC,aACT,IACExB,QAAQwB,aAAejC,OAAOiC,aAC9B,MAAOqB,GAGP,GAA4B,SAAxBtD,OAAOiC,aACT,MAAMqB,EAM6B,mBAA9BtD,OAAOuD,oBAChB9C,QAAQ+C,iBAAiB,WAAYxD,OAAOuD,oBAIP,mBAA5BvD,OAAOyD,kBAAmChD,QAAQiD,QAC3DjD,QAAQiD,OAAOF,iBAAiB,WAAYxD,OAAOyD,kBAGjDzD,OAAO2D,aAET3D,OAAO2D,YAAYC,QAAQC,MAAK,SAAoBC,QAC7CrD,UAILA,QAAQsD,QACR5D,OAAO2D,QAEPrD,QAAU,cAIMqC,IAAhB1C,cACFA,YAAc,MAIhBK,QAAQuD,KAAK5D,kB,yEC/KjB,IAAIX,MAAQ,oBAAQ,KAChBwE,KAAO,oBAAQ,KACfC,MAAQ,oBAAQ,KAChBC,YAAc,oBAAQ,KAS1B,SAASC,eAAeC,eACtB,IAAIC,QAAU,IAAIJ,MAAMG,eACpBE,SAAWN,KAAKC,MAAMM,UAAU/D,QAAS6D,SAQ7C,OALA7E,MAAMgF,OAAOF,SAAUL,MAAMM,UAAWF,SAGxC7E,MAAMgF,OAAOF,SAAUD,SAEhBC,SAIT,IAAIG,MAAQN,eAtBG,oBAAQ,MAyBvBM,MAAMR,MAAQA,MAGdQ,MAAMC,OAAS,SAAgBC,gBAC7B,OAAOR,eAAeD,YAAYO,MAAMG,SAAUD,kBAIpDF,MAAMI,OAAS,oBAAQ,KACvBJ,MAAMK,YAAc,oBAAQ,KAC5BL,MAAMM,SAAW,oBAAQ,KAGzBN,MAAMO,IAAM,SAAaC,UACvB,OAAOjF,QAAQgF,IAAIC,WAErBR,MAAMS,OAAS,oBAAQ,KAEvB5F,OAAOC,QAAUkF,MAGjBnF,OAAOC,QAAQ4F,QAAUV,O,0BC5CzB,SAASI,OAAOO,SACdC,KAAKD,QAAUA,QAGjBP,OAAON,UAAUe,SAAW,WAC1B,MAAO,UAAYD,KAAKD,QAAU,KAAOC,KAAKD,QAAU,KAG1DP,OAAON,UAAUgB,YAAa,EAE9BjG,OAAOC,QAAUsF,Q,yEChBjB,IAAIA,OAAS,oBAAQ,KAQrB,SAASC,YAAYU,UACnB,GAAwB,mBAAbA,SACT,MAAM,IAAIC,UAAU,gCAGtB,IAAIC,eACJL,KAAK1B,QAAU,IAAI3D,SAAQ,SAAyBC,SAClDyF,eAAiBzF,WAGnB,IAAI0F,MAAQN,KACZG,UAAS,SAAgBJ,SACnBO,MAAMC,SAKVD,MAAMC,OAAS,IAAIf,OAAOO,SAC1BM,eAAeC,MAAMC,YAOzBd,YAAYP,UAAUsB,iBAAmB,WACvC,GAAIR,KAAKO,OACP,MAAMP,KAAKO,QAQfd,YAAYgB,OAAS,WACnB,IAAIjC,OAIJ,MAAO,CACL8B,MAJU,IAAIb,aAAY,SAAkBiB,GAC5ClC,OAASkC,KAITlC,SAIJvE,OAAOC,QAAUuF,a,0BCtDjBxF,OAAOC,QAAU,SAAkByG,OACjC,SAAUA,QAASA,MAAMT,c,yECD3B,IAAI/F,MAAQ,oBAAQ,KAChBE,SAAW,oBAAQ,KACnBuG,mBAAqB,oBAAQ,KAC7BC,gBAAkB,oBAAQ,KAC1BhC,YAAc,oBAAQ,KAO1B,SAASD,MAAMU,gBACbU,KAAKT,SAAWD,eAChBU,KAAKc,aAAe,CAClB3F,QAAS,IAAIyF,mBACblE,SAAU,IAAIkE,oBASlBhC,MAAMM,UAAU/D,QAAU,SAAiBT,QAGnB,iBAAXA,QACTA,OAASqG,UAAU,IAAM,IAClBnF,IAAMmF,UAAU,GAEvBrG,OAASA,QAAU,IAGrBA,OAASmE,YAAYmB,KAAKT,SAAU7E,SAGzBoB,OACTpB,OAAOoB,OAASpB,OAAOoB,OAAO+B,cACrBmC,KAAKT,SAASzD,OACvBpB,OAAOoB,OAASkE,KAAKT,SAASzD,OAAO+B,cAErCnD,OAAOoB,OAAS,MAIlB,IAAIkF,MAAQ,CAACH,qBAAiBrD,GAC1Bc,QAAU3D,QAAQC,QAAQF,QAU9B,IARAsF,KAAKc,aAAa3F,QAAQuC,SAAQ,SAAoCuD,aACpED,MAAME,QAAQD,YAAYE,UAAWF,YAAYG,aAGnDpB,KAAKc,aAAapE,SAASgB,SAAQ,SAAkCuD,aACnED,MAAMK,KAAKJ,YAAYE,UAAWF,YAAYG,aAGzCJ,MAAMM,QACXhD,QAAUA,QAAQC,KAAKyC,MAAMO,QAASP,MAAMO,SAG9C,OAAOjD,SAGTM,MAAMM,UAAUsC,OAAS,SAAgB9G,QAEvC,OADAA,OAASmE,YAAYmB,KAAKT,SAAU7E,QAC7BL,SAASK,OAAOkB,IAAKlB,OAAOsB,OAAQtB,OAAOuB,kBAAkBwF,QAAQ,MAAO,KAIrFtH,MAAMuD,QAAQ,CAAC,SAAU,MAAO,OAAQ,YAAY,SAA6B5B,QAE/E8C,MAAMM,UAAUpD,QAAU,SAASF,IAAKlB,QACtC,OAAOsF,KAAK7E,QAAQhB,MAAMuH,MAAMhH,QAAU,GAAI,CAC5CoB,OACAF,WAKNzB,MAAMuD,QAAQ,CAAC,OAAQ,MAAO,UAAU,SAA+B5B,QAErE8C,MAAMM,UAAUpD,QAAU,SAASF,IAAKb,KAAML,QAC5C,OAAOsF,KAAK7E,QAAQhB,MAAMuH,MAAMhH,QAAU,GAAI,CAC5CoB,OACAF,IACAb,YAKNd,OAAOC,QAAU0E,O,yEC3FjB,IAAIzE,MAAQ,oBAAQ,KAEpB,SAASyG,qBACPZ,KAAK2B,SAAW,GAWlBf,mBAAmB1B,UAAU0C,IAAM,SAAaT,UAAWC,UAKzD,OAJApB,KAAK2B,SAASN,KAAK,CACjBF,UACAC,WAEKpB,KAAK2B,SAASL,OAAS,GAQhCV,mBAAmB1B,UAAU2C,MAAQ,SAAeC,IAC9C9B,KAAK2B,SAASG,MAChB9B,KAAK2B,SAASG,IAAM,OAYxBlB,mBAAmB1B,UAAUxB,QAAU,SAAiBqE,IACtD5H,MAAMuD,QAAQsC,KAAK2B,UAAU,SAAwBK,GACzC,OAANA,GACFD,GAAGC,OAKT/H,OAAOC,QAAU0G,oB,wECjDjB,IAAIqB,cAAgB,oBAAQ,KACxBC,YAAc,oBAAQ,KAW1BjI,OAAOC,QAAU,SAAuByB,QAASwG,cAC/C,OAAIxG,UAAYsG,cAAcE,cACrBD,YAAYvG,QAASwG,cAEvBA,e,wEChBT,IAAIC,aAAe,oBAAQ,KAY3BnI,OAAOC,QAAU,SAAqB6F,QAASrF,OAAQ2H,KAAMlH,QAASuB,UACpE,IAAI4F,MAAQ,IAAIC,MAAMxC,SACtB,OAAOqC,aAAaE,MAAO5H,OAAQ2H,KAAMlH,QAASuB,Y,yECdpD,IAAIvC,MAAQ,oBAAQ,KAChBqI,cAAgB,oBAAQ,KACxB9C,SAAW,oBAAQ,KACnBH,SAAW,oBAAQ,KAKvB,SAASkD,6BAA6B/H,QAChCA,OAAO2D,aACT3D,OAAO2D,YAAYmC,mBAUvBvG,OAAOC,QAAU,SAAyBQ,QA6BxC,OA5BA+H,6BAA6B/H,QAG7BA,OAAOO,QAAUP,OAAOO,SAAW,GAGnCP,OAAOK,KAAOyH,cACZ9H,OAAOK,KACPL,OAAOO,QACPP,OAAOgI,kBAIThI,OAAOO,QAAUd,MAAMuH,MACrBhH,OAAOO,QAAQ0H,QAAU,GACzBjI,OAAOO,QAAQP,OAAOoB,SAAW,GACjCpB,OAAOO,SAGTd,MAAMuD,QACJ,CAAC,SAAU,MAAO,OAAQ,OAAQ,MAAO,QAAS,WAClD,SAA2B5B,eAClBpB,OAAOO,QAAQa,YAIZpB,OAAOkI,SAAWrD,SAASqD,SAE1BlI,QAAQ6D,MAAK,SAA6B7B,UAUvD,OATA+F,6BAA6B/H,QAG7BgC,SAAS3B,KAAOyH,cACd9F,SAAS3B,KACT2B,SAASzB,QACTP,OAAOmI,mBAGFnG,YACN,SAA4B6D,QAc7B,OAbKb,SAASa,UACZkC,6BAA6B/H,QAGzB6F,QAAUA,OAAO7D,WACnB6D,OAAO7D,SAAS3B,KAAOyH,cACrBjC,OAAO7D,SAAS3B,KAChBwF,OAAO7D,SAASzB,QAChBP,OAAOmI,qBAKNlI,QAAQE,OAAO0F,a,0BChE1BtG,OAAOC,QAAU,SAAsBoI,MAAO5H,OAAQ2H,KAAMlH,QAASuB,UA4BnE,OA3BA4F,MAAM5H,OAASA,OACX2H,OACFC,MAAMD,KAAOA,MAGfC,MAAMnH,QAAUA,QAChBmH,MAAM5F,SAAWA,SACjB4F,MAAMQ,cAAe,EAErBR,MAAMS,OAAS,WACb,MAAO,CAELhD,QAASC,KAAKD,QACdiD,KAAMhD,KAAKgD,KAEXC,YAAajD,KAAKiD,YAClBC,OAAQlD,KAAKkD,OAEbC,SAAUnD,KAAKmD,SACfC,WAAYpD,KAAKoD,WACjBC,aAAcrD,KAAKqD,aACnBC,MAAOtD,KAAKsD,MAEZ5I,OAAQsF,KAAKtF,OACb2H,KAAMrC,KAAKqC,OAGRC,Q,yECtCT,IAAInI,MAAQ,oBAAQ,KAUpBF,OAAOC,QAAU,SAAqBqJ,QAASC,SAE7CA,QAAUA,SAAW,GACrB,IAAI9I,OAAS,GAET+I,qBAAuB,CAAC,MAAO,SAAU,SAAU,QACnDC,wBAA0B,CAAC,UAAW,OAAQ,SAC9CC,qBAAuB,CACzB,UAAW,MAAO,mBAAoB,oBAAqB,mBAC3D,UAAW,kBAAmB,UAAW,eAAgB,iBACzD,iBAAkB,mBAAoB,qBACtC,mBAAoB,iBAAkB,eAAgB,YACtD,aAAc,cAAe,cAG/BxJ,MAAMuD,QAAQ+F,sBAAsB,SAA0BG,WAC/B,IAAlBJ,QAAQI,QACjBlJ,OAAOkJ,MAAQJ,QAAQI,UAI3BzJ,MAAMuD,QAAQgG,yBAAyB,SAA6BE,MAC9DzJ,MAAM0J,SAASL,QAAQI,OACzBlJ,OAAOkJ,MAAQzJ,MAAM2J,UAAUP,QAAQK,MAAOJ,QAAQI,YACpB,IAAlBJ,QAAQI,MACxBlJ,OAAOkJ,MAAQJ,QAAQI,MACdzJ,MAAM0J,SAASN,QAAQK,OAChClJ,OAAOkJ,MAAQzJ,MAAM2J,UAAUP,QAAQK,YACL,IAAlBL,QAAQK,QACxBlJ,OAAOkJ,MAAQL,QAAQK,UAI3BzJ,MAAMuD,QAAQiG,sBAAsB,SAA0BC,WAC/B,IAAlBJ,QAAQI,MACjBlJ,OAAOkJ,MAAQJ,QAAQI,WACW,IAAlBL,QAAQK,QACxBlJ,OAAOkJ,MAAQL,QAAQK,UAI3B,IAAIG,UAAYN,qBACbO,OAAON,yBACPM,OAAOL,sBAENM,UAAYC,OACbC,KAAKX,SACLY,QAAO,SAAyBxG,KAC/B,OAAmC,IAA5BmG,UAAUxH,QAAQqB,QAW7B,OARAzD,MAAMuD,QAAQuG,WAAW,SAAmCL,WAC7B,IAAlBJ,QAAQI,MACjBlJ,OAAOkJ,MAAQJ,QAAQI,WACW,IAAlBL,QAAQK,QACxBlJ,OAAOkJ,MAAQL,QAAQK,UAIpBlJ,S,wECrET,IAAID,YAAc,oBAAQ,IAS1BR,OAAOC,QAAU,SAAgBU,QAASC,OAAQ6B,UAChD,IAAI2H,eAAiB3H,SAAShC,OAAO2J,gBAChCA,gBAAkBA,eAAe3H,SAASL,QAC7CzB,QAAQ8B,UAER7B,OAAOJ,YACL,mCAAqCiC,SAASL,OAC9CK,SAAShC,OACT,KACAgC,SAASvB,QACTuB,a,yECnBN,IAAIvC,MAAQ,oBAAQ,KAUpBF,OAAOC,QAAU,SAAuBa,KAAME,QAASqJ,KAMrD,OAJAnK,MAAMuD,QAAQ4G,KAAK,SAAmBvC,IACpChH,KAAOgH,GAAGhH,KAAME,YAGXF,O,yEChBT,IAAIZ,MAAQ,oBAAQ,KAChBoK,oBAAsB,oBAAQ,IAE9BC,qBAAuB,CACzB,eAAgB,qCAGlB,SAASC,sBAAsBxJ,QAAS0F,QACjCxG,MAAM4D,YAAY9C,UAAYd,MAAM4D,YAAY9C,QAAQ,mBAC3DA,QAAQ,gBAAkB0F,OAgB9B,IAXMiC,QAWFrD,SAAW,CACbqD,UAX8B,oBAAnBxH,gBAGmB,oBAAZsJ,SAAuE,qBAA5CR,OAAOhF,UAAUe,SAAS0E,KAAKD,YAD1E9B,QAAU,oBAAQ,MAKbA,SAMPF,iBAAkB,CAAC,SAA0B3H,KAAME,SAGjD,OAFAsJ,oBAAoBtJ,QAAS,UAC7BsJ,oBAAoBtJ,QAAS,gBACzBd,MAAMe,WAAWH,OACnBZ,MAAMyK,cAAc7J,OACpBZ,MAAM0K,SAAS9J,OACfZ,MAAM2K,SAAS/J,OACfZ,MAAM4K,OAAOhK,OACbZ,MAAM6K,OAAOjK,MAENA,KAELZ,MAAM8K,kBAAkBlK,MACnBA,KAAKmK,OAEV/K,MAAMgL,kBAAkBpK,OAC1B0J,sBAAsBxJ,QAAS,mDACxBF,KAAKkF,YAEV9F,MAAM0J,SAAS9I,OACjB0J,sBAAsBxJ,QAAS,kCACxBmK,KAAKC,UAAUtK,OAEjBA,OAGT8H,kBAAmB,CAAC,SAA2B9H,MAE7C,GAAoB,iBAATA,KACT,IACEA,KAAOqK,KAAKE,MAAMvK,MAClB,MAAOiD,IAEX,OAAOjD,OAOTmB,QAAS,EAEToB,eAAgB,aAChBG,eAAgB,eAEhB8H,kBAAmB,EAEnBlB,eAAgB,SAAwBhI,QACtC,OAAOA,QAAU,KAAOA,OAAS,MAIrCkD,SAAStE,QAAU,CACjB0H,OAAQ,CACN,OAAU,sCAIdxI,MAAMuD,QAAQ,CAAC,SAAU,MAAO,SAAS,SAA6B5B,QACpEyD,SAAStE,QAAQa,QAAU,MAG7B3B,MAAMuD,QAAQ,CAAC,OAAQ,MAAO,UAAU,SAA+B5B,QACrEyD,SAAStE,QAAQa,QAAU3B,MAAMuH,MAAM8C,yBAGzCvK,OAAOC,QAAUqF,U,0BC9FjBtF,OAAOC,QAAU,SAAc6H,GAAIyD,SACjC,OAAO,WAEL,IADA,IAAIC,KAAO,IAAIC,MAAM3E,UAAUO,QACtBqE,EAAI,EAAGA,EAAIF,KAAKnE,OAAQqE,IAC/BF,KAAKE,GAAK5E,UAAU4E,GAEtB,OAAO5D,GAAG6D,MAAMJ,QAASC,S,yECN7B,IAAItL,MAAQ,oBAAQ,KAEpB,SAAS0L,OAAOlI,KACd,OAAOmI,mBAAmBnI,KACxB8D,QAAQ,QAAS,KACjBA,QAAQ,QAAS,KACjBA,QAAQ,OAAQ,KAChBA,QAAQ,QAAS,KACjBA,QAAQ,OAAQ,KAChBA,QAAQ,QAAS,KACjBA,QAAQ,QAAS,KAUrBxH,OAAOC,QAAU,SAAkB0B,IAAKI,OAAQC,kBAE9C,IAAKD,OACH,OAAOJ,IAGT,IAAImK,iBACJ,GAAI9J,iBACF8J,iBAAmB9J,iBAAiBD,aAC/B,GAAI7B,MAAMgL,kBAAkBnJ,QACjC+J,iBAAmB/J,OAAOiE,eACrB,CACL,IAAI+F,MAAQ,GAEZ7L,MAAMuD,QAAQ1B,QAAQ,SAAmB2B,IAAKC,KACxCD,YAIAxD,MAAM8L,QAAQtI,KAChBC,KAAY,KAEZD,IAAM,CAACA,KAGTxD,MAAMuD,QAAQC,KAAK,SAAoBuI,GACjC/L,MAAMgM,OAAOD,GACfA,EAAIA,EAAEE,cACGjM,MAAM0J,SAASqC,KACxBA,EAAId,KAAKC,UAAUa,IAErBF,MAAM3E,KAAKwE,OAAOjI,KAAO,IAAMiI,OAAOK,WAI1CH,iBAAmBC,MAAMK,KAAK,KAGhC,GAAIN,iBAAkB,CACpB,IAAIO,cAAgB1K,IAAIW,QAAQ,MACT,IAAnB+J,gBACF1K,IAAMA,IAAI2K,MAAM,EAAGD,gBAGrB1K,OAA8B,IAAtBA,IAAIW,QAAQ,KAAc,IAAM,KAAOwJ,iBAGjD,OAAOnK,M,0BC5DT3B,OAAOC,QAAU,SAAqByB,QAAS6K,aAC7C,OAAOA,YACH7K,QAAQ8F,QAAQ,OAAQ,IAAM,IAAM+E,YAAY/E,QAAQ,OAAQ,IAChE9F,U,yECVN,IAAIxB,MAAQ,oBAAQ,KAEpBF,OAAOC,QACLC,MAAM+C,uBAIK,CACLuJ,MAAO,SAAezD,KAAMrC,MAAO+F,QAASC,KAAMC,OAAQC,QACxD,IAAIC,OAAS,GACbA,OAAOzF,KAAK2B,KAAO,IAAM8C,mBAAmBnF,QAExCxG,MAAM4M,SAASL,UACjBI,OAAOzF,KAAK,WAAa,IAAI2F,KAAKN,SAASO,eAGzC9M,MAAM+M,SAASP,OACjBG,OAAOzF,KAAK,QAAUsF,MAGpBxM,MAAM+M,SAASN,SACjBE,OAAOzF,KAAK,UAAYuF,SAGX,IAAXC,QACFC,OAAOzF,KAAK,UAGd8F,SAASL,OAASA,OAAOT,KAAK,OAGhC9I,KAAM,SAAcyF,MAClB,IAAIoE,MAAQD,SAASL,OAAOM,MAAM,IAAIC,OAAO,aAAerE,KAAO,cACnE,OAAQoE,MAAQE,mBAAmBF,MAAM,IAAM,MAGjDG,OAAQ,SAAgBvE,MACtBhD,KAAKyG,MAAMzD,KAAM,GAAIgE,KAAKQ,MAAQ,SAO/B,CACLf,MAAO,aACPlJ,KAAM,WAAkB,OAAO,MAC/BgK,OAAQ,e,0BCzChBtN,OAAOC,QAAU,SAAuB0B,KAItC,MAAO,gCAAgC6L,KAAK7L,O,yECV9C,IAAIzB,MAAQ,oBAAQ,KAEpBF,OAAOC,QACLC,MAAM+C,uBAIJ,WACE,IAEIwK,UAFAC,KAAO,kBAAkBF,KAAKG,UAAUC,WACxCC,eAAiBX,SAASY,cAAc,KAS5C,SAASC,WAAWpM,KAClB,IAAIqM,KAAOrM,IAWX,OATI+L,OAEFG,eAAeI,aAAa,OAAQD,MACpCA,KAAOH,eAAeG,MAGxBH,eAAeI,aAAa,OAAQD,MAG7B,CACLA,KAAMH,eAAeG,KACrBE,SAAUL,eAAeK,SAAWL,eAAeK,SAAS1G,QAAQ,KAAM,IAAM,GAChF2G,KAAMN,eAAeM,KACrBC,OAAQP,eAAeO,OAASP,eAAeO,OAAO5G,QAAQ,MAAO,IAAM,GAC3E6G,KAAMR,eAAeQ,KAAOR,eAAeQ,KAAK7G,QAAQ,KAAM,IAAM,GACpE8G,SAAUT,eAAeS,SACzBC,KAAMV,eAAeU,KACrBC,SAAiD,MAAtCX,eAAeW,SAASC,OAAO,GACxCZ,eAAeW,SACf,IAAMX,eAAeW,UAY3B,OARAf,UAAYM,WAAWW,OAAOC,SAASX,MAQhC,SAAyBY,YAC9B,IAAIC,OAAU3O,MAAM+M,SAAS2B,YAAeb,WAAWa,YAAcA,WACrE,OAAQC,OAAOX,WAAaT,UAAUS,UAClCW,OAAOV,OAASV,UAAUU,MAhDlC,GAsDS,WACL,OAAO,I,wEC9Df,IAAIjO,MAAQ,oBAAQ,KAEpBF,OAAOC,QAAU,SAA6Be,QAAS8N,gBACrD5O,MAAMuD,QAAQzC,SAAS,SAAuB0F,MAAOqC,MAC/CA,OAAS+F,gBAAkB/F,KAAKjH,gBAAkBgN,eAAehN,gBACnEd,QAAQ8N,gBAAkBpI,aACnB1F,QAAQ+H,Y,yECNrB,IAAI7I,MAAQ,oBAAQ,KAIhB6O,kBAAoB,CACtB,MAAO,gBAAiB,iBAAkB,eAAgB,OAC1D,UAAW,OAAQ,OAAQ,oBAAqB,sBAChD,gBAAiB,WAAY,eAAgB,sBAC7C,UAAW,cAAe,cAgB5B/O,OAAOC,QAAU,SAAsBe,SACrC,IACI2C,IACAD,IACAgI,EAHAmD,OAAS,GAKb,OAAK7N,SAELd,MAAMuD,QAAQzC,QAAQgO,MAAM,OAAO,SAAgBC,MAKjD,GAJAvD,EAAIuD,KAAK3M,QAAQ,KACjBqB,IAAMzD,MAAMgP,KAAKD,KAAKE,OAAO,EAAGzD,IAAI9H,cACpCF,IAAMxD,MAAMgP,KAAKD,KAAKE,OAAOzD,EAAI,IAE7B/H,IAAK,CACP,GAAIkL,OAAOlL,MAAQoL,kBAAkBzM,QAAQqB,MAAQ,EACnD,OAGAkL,OAAOlL,KADG,eAARA,KACakL,OAAOlL,KAAOkL,OAAOlL,KAAO,IAAIoG,OAAO,CAACrG,MAEzCmL,OAAOlL,KAAOkL,OAAOlL,KAAO,KAAOD,IAAMA,QAKtDmL,QAnBgBA,S,0BCVzB7O,OAAOC,QAAU,SAAgBmP,UAC/B,OAAO,SAAcC,KACnB,OAAOD,SAASzD,MAAM,KAAM0D,Q,yECtBhC,IAAI3K,KAAO,oBAAQ,KAMfsB,SAAWiE,OAAOhF,UAAUe,SAQhC,SAASgG,QAAQtI,KACf,MAA8B,mBAAvBsC,SAAS0E,KAAKhH,KASvB,SAASI,YAAYJ,KACnB,YAAsB,IAARA,IA4EhB,SAASkG,SAASlG,KAChB,OAAe,OAARA,KAA+B,iBAARA,IAuChC,SAAS4L,WAAW5L,KAClB,MAA8B,sBAAvBsC,SAAS0E,KAAKhH,KAwEvB,SAASD,QAAQ8L,IAAKzH,IAEpB,GAAIyH,UAUJ,GALmB,iBAARA,MAETA,IAAM,CAACA,MAGLvD,QAAQuD,KAEV,IAAK,IAAI7D,EAAI,EAAG8D,EAAID,IAAIlI,OAAQqE,EAAI8D,EAAG9D,IACrC5D,GAAG4C,KAAK,KAAM6E,IAAI7D,GAAIA,EAAG6D,UAI3B,IAAK,IAAI5L,OAAO4L,IACVtF,OAAOhF,UAAUwK,eAAe/E,KAAK6E,IAAK5L,MAC5CmE,GAAG4C,KAAK,KAAM6E,IAAI5L,KAAMA,IAAK4L,KAoFrCvP,OAAOC,QAAU,CACf+L,QACArB,cApRF,SAAuBjH,KACrB,MAA8B,yBAAvBsC,SAAS0E,KAAKhH,MAoRrBkH,SAhSF,SAAkBlH,KAChB,OAAe,OAARA,MAAiBI,YAAYJ,MAA4B,OAApBA,IAAIgM,cAAyB5L,YAAYJ,IAAIgM,cAChD,mBAA7BhM,IAAIgM,YAAY9E,UAA2BlH,IAAIgM,YAAY9E,SAASlH,MA+RhFzC,WA5QF,SAAoByC,KAClB,MAA4B,oBAAbiM,UAA8BjM,eAAeiM,UA4Q5D3E,kBAnQF,SAA2BtH,KAOzB,MAL4B,oBAAhBkM,aAAiCA,YAAkB,OACpDA,YAAYC,OAAOnM,KAEnB,KAAUA,IAAU,QAAMA,IAAIuH,kBAAkB2E,aA+P3D3C,SApPF,SAAkBvJ,KAChB,MAAsB,iBAARA,KAoPdoJ,SA3OF,SAAkBpJ,KAChB,MAAsB,iBAARA,KA2OdkG,SACA9F,YACAoI,OA1NF,SAAgBxI,KACd,MAA8B,kBAAvBsC,SAAS0E,KAAKhH,MA0NrBoH,OAjNF,SAAgBpH,KACd,MAA8B,kBAAvBsC,SAAS0E,KAAKhH,MAiNrBqH,OAxMF,SAAgBrH,KACd,MAA8B,kBAAvBsC,SAAS0E,KAAKhH,MAwMrB4L,WACAzE,SAtLF,SAAkBnH,KAChB,OAAOkG,SAASlG,MAAQ4L,WAAW5L,IAAIoM,OAsLvC5E,kBA7KF,SAA2BxH,KACzB,MAAkC,oBAApBqM,iBAAmCrM,eAAeqM,iBA6KhE9M,qBAjJF,WACE,OAAyB,oBAAd0K,WAAoD,gBAAtBA,UAAUqC,SACY,iBAAtBrC,UAAUqC,SACY,OAAtBrC,UAAUqC,WAI/B,oBAAXtB,QACa,oBAAbxB,WA0ITzJ,QACAgE,MA/EF,SAASA,QACP,IAAIwI,OAAS,GACb,SAASC,YAAYxM,IAAKC,KACG,iBAAhBsM,OAAOtM,MAAoC,iBAARD,IAC5CuM,OAAOtM,KAAO8D,MAAMwI,OAAOtM,KAAMD,KAEjCuM,OAAOtM,KAAOD,IAIlB,IAAK,IAAIgI,EAAI,EAAG8D,EAAI1I,UAAUO,OAAQqE,EAAI8D,EAAG9D,IAC3CjI,QAAQqD,UAAU4E,GAAIwE,aAExB,OAAOD,QAmEPpG,UAxDF,SAASA,YACP,IAAIoG,OAAS,GACb,SAASC,YAAYxM,IAAKC,KACG,iBAAhBsM,OAAOtM,MAAoC,iBAARD,IAC5CuM,OAAOtM,KAAOkG,UAAUoG,OAAOtM,KAAMD,KAErCuM,OAAOtM,KADiB,iBAARD,IACFmG,UAAU,GAAInG,KAEdA,IAIlB,IAAK,IAAIgI,EAAI,EAAG8D,EAAI1I,UAAUO,OAAQqE,EAAI8D,EAAG9D,IAC3CjI,QAAQqD,UAAU4E,GAAIwE,aAExB,OAAOD,QA0CP/K,OA/BF,SAAgBiL,EAAGC,EAAG7E,SAQpB,OAPA9H,QAAQ2M,GAAG,SAAqB1M,IAAKC,KAEjCwM,EAAExM,KADA4H,SAA0B,mBAAR7H,IACXgB,KAAKhB,IAAK6H,SAEV7H,OAGNyM,GAwBPjB,KAzKF,SAAcmB,KACZ,OAAOA,IAAI7I,QAAQ,OAAQ,IAAIA,QAAQ,OAAQ,O,uBC9KjD,mCAOC,WACA,aAEA,IAAI8I,OAAS,GAAGb,eAEhB,SAASc,aAGR,IAFA,IAAIC,QAAU,GAEL9E,EAAI,EAAGA,EAAI5E,UAAUO,OAAQqE,IAAK,CAC1C,IAAI+E,IAAM3J,UAAU4E,GACpB,GAAK+E,IAAL,CAEA,IAAIC,eAAiBD,IAErB,GAAgB,WAAZC,SAAoC,WAAZA,QAC3BF,QAAQpJ,KAAKqJ,UACP,GAAIhF,MAAMO,QAAQyE,MAAQA,IAAIpJ,OAAQ,CAC5C,IAAIsJ,MAAQJ,WAAW5E,MAAM,KAAM8E,KAC/BE,OACHH,QAAQpJ,KAAKuJ,YAER,GAAgB,WAAZD,QACV,IAAK,IAAI/M,OAAO8M,IACXH,OAAO5F,KAAK+F,IAAK9M,MAAQ8M,IAAI9M,MAChC6M,QAAQpJ,KAAKzD,MAMjB,OAAO6M,QAAQpE,KAAK,KAGgBpM,OAAOC,SAC3CsQ,WAAW1K,QAAU0K,WACrBvQ,OAAOC,QAAUsQ,iBAKhB,KAFwB,8BAAF,WACtB,OAAOA,YACP,cAFoB,OAEpB,8CAxCH,I,uBCNIK,yBAA2B,GAG/B,SAASC,oBAAoBC,UAE5B,GAAGF,yBAAyBE,UAC3B,OAAOF,yBAAyBE,UAAU7Q,QAG3C,IAAID,OAAS4Q,yBAAyBE,UAAY,CAGjD7Q,QAAS,IAOV,OAHA8Q,oBAAoBD,UAAU9Q,OAAQA,OAAOC,QAAS4Q,qBAG/C7Q,OAAOC,QCnBf4Q,oBAAoBG,EAAKhR,SACxB,IAAIiR,OAASjR,QAAUA,OAAOkR,WAC7B,IAAMlR,OAAgB,QACtB,IAAMA,OAEP,OADA6Q,oBAAoBM,EAAEF,OAAQ,CAAEd,EAAGc,SAC5BA,QCLRJ,oBAAoBM,EAAI,CAAClR,QAASmR,cACjC,IAAI,IAAIzN,OAAOyN,WACXP,oBAAoBQ,EAAED,WAAYzN,OAASkN,oBAAoBQ,EAAEpR,QAAS0D,MAC5EsG,OAAOqH,eAAerR,QAAS0D,IAAK,CAAE4N,YAAY,EAAMC,IAAKJ,WAAWzN,QCJ3EkN,oBAAoBQ,EAAI,CAAC9B,IAAK5F,OAASM,OAAOhF,UAAUwK,eAAe/E,KAAK6E,IAAK5F,M,2sECIjD8H,GAAGC,QAA3BC,U,YAAAA,UAAWC,S,YAAAA,SAEXC,GAAOJ,GAAGK,KAAVD,G,eAeJJ,GAAGM,WAZNC,U,eAAAA,UACAC,Y,eAAAA,YAEAC,c,eADAC,c,eACAD,cACAE,c,eAAAA,cACAC,Q,eAAAA,QACAC,Y,eAAAA,YACAC,c,eAAAA,cAIAC,Q,eAHAC,Q,eACAC,Y,eACAC,a,eACAH,Q,WAUGf,GAAGmB,OANNC,kB,WAAAA,kBAEAC,a,WADAC,c,WACAD,aACAE,S,WAAAA,SAEAC,oB,WADAC,iB,WACAD,oBAmhDD,c,wdA3gDC,kCAAc,8FACb,wBAAUnM,YADG,aAqDF,WACX3B,qBAAWgO,cAAcC,SAAd,aAAuC,GAAI,CAAE,QAAW,CAAE,aAAcD,cAAcE,SAAY/O,MAAM,SAAC7B,UAAa,mBAC5H6Q,MAAQ7H,QACR8H,UAAY9H,QACZ+H,YAAc,EACdC,gBAAkB,GAClBC,mBAAqB,EACrBC,aAAe,GACfC,oBAAsB,GACtBC,cAAgB,GAChBC,YAAc,GACdC,aAAe,GACnBC,OAAOC,KAAMxR,SAAS3B,MAAM,SAAU6C,IAAK+C,OAC1C4M,MAAM5M,MAAMwN,IAAM,CACjBC,iBAAkBzN,MAAMyN,iBACxBC,oBAAqB1N,MAAM0N,oBAC3BC,aAAc3N,MAAM2N,aACpBrL,YAAatC,MAAMsC,YACnBsL,kBAAmB5N,MAAM4N,kBACzBZ,mBAAoBhN,MAAMgN,mBAC1Ba,cAAe7N,MAAM6N,cACrBC,UAAW9N,MAAM8N,WAEb9N,MAAM4N,oBACVd,YAAc9M,MAAMwN,IAErBX,UAAUnM,KAAM,CAAEV,MAAOA,MAAMwN,GAAIO,MAAO/N,MAAM2N,kBAEX,IAAlC,MAAKK,MAAMC,WAAWC,UACzBpB,YAAc,MAAKkB,MAAMC,WAAWC,SAErC,IAAIC,oBAAsBvB,MAAME,aAC5BqB,oBAAoBT,qBACvBX,gBAAkB,MAAKiB,MAAMC,WAAWG,cAAczN,OAAS,EAAI,MAAKqN,MAAMC,WAAWG,cAAgBD,oBAAoBV,iBAApB,UACzGT,mBAAqB,MAAKgB,MAAMC,WAAWI,aAAa1N,OAAS,EAAI,MAAKqN,MAAMC,WAAWI,aAAeF,oBAAoBnB,mBAC9HC,aAAe,MAAKe,MAAMC,WAAWK,YAAY3N,OAAS,EAAI,MAAKqN,MAAMC,WAAWK,YAAeH,oBAAoBR,aACvHR,cAAgB,MAAKa,MAAMC,WAAWM,aAAa5N,OAAS,EAAI,MAAKqN,MAAMC,WAAWM,aAAgB,GACtGnB,YAAce,oBAAoBL,UAClCZ,oBAAsB,MAAKc,MAAMC,WAAWO,eAAe7N,OAAS,EAAI,MAAKqN,MAAMC,WAAWO,eAAiBL,oBAAoB7L,YACnI+K,aAAe,MAAKW,MAAMC,WAAWQ,cAErCxB,aAAe,MAAKe,MAAMC,WAAWK,YAAY3N,OAAS,EAAI,MAAKqN,MAAMC,WAAWK,YAAeH,oBAAoBR,aACvHR,cAAgB,MAAKa,MAAMC,WAAWM,aAAa5N,OAAS,EAAI,MAAKqN,MAAMC,WAAWM,aAAgB,GACtGrB,oBAAsB,MAAKc,MAAMC,WAAWO,eAAe7N,OAAS,EAAI,MAAKqN,MAAMC,WAAWO,eAAiBL,oBAAoB7L,YACnIyK,gBAAkB,MAAKiB,MAAMC,WAAWG,cAAczN,OAAS,EAAI,MAAKqN,MAAMC,WAAWG,cAAgBD,oBAAoBN,cAC7Hb,mBAAqB,MAAKgB,MAAMC,WAAWI,aAAa1N,OAAS,EAAI,MAAKqN,MAAMC,WAAWI,aAAe,EAC1GjB,YAAce,oBAAoBL,UAClCT,aAAe,MAAKW,MAAMC,WAAWQ,aAElC5R,MAAaqQ,sBAChBA,oBAAsB,IAEvB,MAAKwB,UAAL,gCAEEC,SAAS,EACT/B,MACAE,YACAD,UACAE,gBACAC,oBAPF,cAQeF,aARf,8CASgBG,cAThB,yDAU2BkB,oBAAoBR,cAV/C,+CAWiBR,eAXjB,qDAYuBD,qBAZvB,6CAaeE,aAbf,8CAcgBC,cAdhB,iBAiBA,MAAKW,MAAMY,cAAX,iBACCJ,eAAgBtB,oBAChBoB,YAAarB,aACbsB,aAAcpB,cACd0B,WAAYzB,YACZiB,aAAcrB,mBACdoB,cAAerB,gBACf0B,YAAapB,aACbyB,iBAAiB,GARlB,cAScX,oBAAoBR,qBAnItB,gEAuIG,SAAEO,SAClB,IAGIjB,aAFAF,gBAAkB,GAClBC,mBAAqB,EAFd,MAAK+B,MAAMnC,MAAMsB,SAIlBR,qBAITX,gBAAkB,MAAKgC,MAAMnC,MAAMsB,SAAjB,2BAClBlB,mBAAqB,MAAK+B,MAAMnC,MAAMsB,SAAjB,qBAJrBnB,gBAAkBN,cAAcuC,YAChChC,mBAAqB,GAKtB,IAAI1K,YAAc,MAAKyM,MAAMnC,MAAMsB,SAAS5L,iBACxCzF,IAAcyF,cACjBA,YAAc,IAEf2K,aAAe,MAAK8B,MAAMnC,MAAMsB,SAASP,aACzC,MAAKK,MAAMY,cAAX,iBACCN,YAAarB,aACbuB,eAAgBlM,YAChBiM,aAAc,GACdM,WAAY,MAAKE,MAAMnC,MAAMsB,SAASJ,UACtCM,cAAerB,gBACfkC,uBAAwB,GACxBC,yBAA0B,GAC1BC,eAAgB,GAChBC,aAAc,GACdC,gBAAiB,GACjBC,eAAgB,GAChBC,gBAAiB,GACjBC,cAAe,GACfC,gBAAiB,GACjBC,cAAe,IAfhB,cAgBc,MAAKX,MAAMnC,MAAMsB,SAASP,eAExC,MAAKe,SACJ,CACCiB,wBAAyB,MAAKZ,MAAMnC,MAAMsB,SAASP,aACnDV,aACAC,oBAAqB5K,YACrB6K,cAAe,GACfJ,gBACAC,mBACAF,YAAaoB,QACbd,YAAa,MAAK2B,MAAMnC,MAAMsB,SAASJ,UACvCqB,eAAgB,GAChBC,aAAc,GACdC,gBAAiB,GACjBC,eAAgB,GAChBC,gBAAiB,GACjBC,cAAe,GACfC,gBAAiB,GACjBC,cAAe,KAGjB,MAAKE,oBA9LQ,gEAgMG,WAChB,MAAKlB,SACJ,CACCmB,oBAAoB,IAGP,uBAAH,OACZpR,qBAAWgO,cAAcC,SAAd,aAAuC,CAACwB,QAAS,MAAKa,MAAMjC,aAAe,CAAE,QAAW,CAAE,aAAcL,cAAcE,SAAY/O,MAAM,SAAC7B,UACnJ,IACI+T,QADgB/T,SAAS3B,KACH2V,KAAK,SAAS3V,MACvC,OACC,0BAAI6C,IAAK7C,KAAKoT,IAAI,yBAAGlG,KAAMlN,KAAK0T,WAAY1T,KAAK4V,gBAInD,MAAKtB,SAAU,CACdmB,oBAAoB,EACpBI,YAAaH,gBAjNF,mEAsNM,WACnB,MAAKI,eAvNQ,mEAyNM,SAAEC,SAAUC,WAC/B,MAAK1B,SAAU,CACd3B,gBAAiBqD,UACjBpD,mBAAoBmD,cA5NR,8DA+NC,SAACnQ,OACf,MAAK0O,SACJ,CACCzB,aAAcjN,WAlOH,+DAsOE,SAACA,OAChB,MAAK0O,SACJ,CACCvB,cAAenN,WAzOJ,qEA6OQ,SAACA,OACtB,MAAK0O,SACJ,CACCxB,oBAAqBlN,WAhPV,+DAoPE,SAAEA,OACjB,MAAK0O,SACJ,CACC2B,MAAOrQ,WAvPI,sEA2PS,SAAEA,OACxB,MAAK0O,SACJ,CACCS,eAAgBnP,WA9PL,qEAkQQ,SAAEA,OACvB,MAAK0O,SACJ,CACCgB,cAAe1P,WArQJ,oEAyQO,SAAEA,OACtB,MAAK0O,SACJ,CACCU,aAAcpP,WA5QH,sEAgRS,SAAEA,OACxB,MAAK0O,SACJ,CACCY,eAAgBtP,WAnRL,qEAuRQ,SAAEA,OACvB,MAAK0O,SACJ,CACCc,cAAexP,WA1RJ,uEA8RU,SAAEA,OACzB,MAAK0O,SACJ,CACCe,gBAAiBzP,WAjSN,qEAqSQ,SAAEA,OACvB,MAAK0O,SACJ,CACC4B,QAAStQ,QAGP,KAAOA,OACV,MAAKgO,MAAMY,cAAe,CACzBH,aAAa,OA7SF,uEAkTU,SAAEzO,OACzB,MAAK0O,SACJ,CACCW,gBAAiBrP,WArTN,uEAyTU,SAAEA,OACzB,MAAK0O,SACJ,CACCa,gBAAiBvP,WA5TN,yEAgUY,SAAEA,OAC3B,MAAK0O,SACJ,CACCI,gBAAiB9O,QAGnB,MAAKgO,MAAMY,cAAe,CAAEE,gBAAiB9O,WAtUhC,+EAwUkB,SAAEA,OACjC,MAAK0O,SACJ,CACC6B,mBAAoBvQ,WA3UT,2EA+Uc,SAAEA,OAC7B,MAAK0O,SACJ,CACC8B,oBAAqBxQ,WAlVV,0EAsVa,SAAEA,OAC5B,MAAK0O,SACJ,CACCO,uBAAwBjP,WAzVb,0EA6Va,WAC1B,MAAK0O,SACJ,CACC+B,UAAW,eAhWA,uEAoWU,WACvB,MAAK/B,SACJ,CACC+B,UAAW,SACXZ,oBAAoB,IAGtB,MAAKD,oBA3WQ,6EA6WgB,SAAE5P,OAC/B,MAAK0O,SAAU,CACd8B,oBAAqBxQ,WA/WT,oFAkXuB,SAAEA,OACtC,MAAK0O,SAAU,CACdgC,2BAA4B1Q,WApXhB,iFAuXoB,SAAEA,OACnC,MAAK0O,SAAU,CACdiC,wBAAyB3Q,WAzXb,yEA4XY,SAAEA,OAC3B,MAAK0O,SAAU,CACdkC,gBAAiB5Q,QAElB,MAAKgO,MAAMY,cAAe,CAAEgC,gBAAiB5Q,WAhYhC,uEAkYU,SAAEA,OACzB,MAAK0O,SAAU,CACdmC,qBAAsB7Q,QAEvB,MAAKgO,MAAMY,cAAe,CAAEiC,qBAAsB7Q,WAtYrC,+EAyYkB,SAAEA,OACjC,MAAK0O,SAAU,CACdoC,wBAAyB9Q,QAE1B,MAAKgO,MAAMY,cAAe,CAAEkC,wBAAyB9Q,WA7YxC,iFA+YoB,SAAEA,OACnC,MAAK0O,SAAU,CACdqC,yBAA0B/Q,QAE3B,MAAKgO,MAAMY,cAAe,CAAEmC,yBAA0B/Q,WAnZzC,qFAqZwB,SAAEA,OACvC,MAAK0O,SAAU,CACdsC,4BAA6BhR,QAE9B,MAAKgO,MAAMY,cAAe,CAAEoC,4BAA6BhR,WAzZ5C,6EA2ZgB,SAAEA,OAC/B,MAAK0O,SAAU,CACduC,oBAAqBjR,QAEtB,MAAKgO,MAAMY,cAAe,CAAEqC,oBAAqBjR,WA/ZpC,qEAiaQ,SAAEA,OACvB,MAAK0O,SACJ,CACCwC,+BAAgClR,WAparB,0EAwaa,SAAEA,OAC5B,MAAK0O,SACJ,CACCyC,wBAAyBnR,WAxa3B,IAAIoR,WAAarM,QAHJ,OAIbqM,WAAW1Q,KAAM,CAAEV,MAAO,UAAW+N,MAAO5C,GAAI,UAAW,8BAC3DiG,WAAW1Q,KAAM,CAAEV,MAAO,UAAW+N,MAAO5C,GAAI,UAAW,8BAC3DiG,WAAW1Q,KAAM,CAAEV,MAAO,SAAU+N,MAAO5C,GAAI,SAAU,8BACzDiG,WAAW1Q,KAAM,CAAEV,MAAO,UAAW+N,MAAO5C,GAAI,UAAW,8BAC3D,MAAK4D,MAAQ,CACZJ,SAAS,EACT/B,OAAO,EACPC,WAAW,EACXE,gBAAiB,MAAKiB,MAAMC,WAAWG,cACvCpB,mBAAoB,MAAKgB,MAAMC,WAAWI,aAC1CvB,aAAa,EACbI,oBAAqB,GACrBD,aAAc,GACd0C,wBAAyB,GACzBxC,cAAe,GACfE,aAAc,MAAKW,MAAMC,WAAWQ,YACpC4C,iBAAkB,MAAKrD,MAAMC,WAAWoD,iBACxCC,mBAAoB,MAAKtD,MAAMC,WAAWqD,mBAC1CjB,MAAO,MAAKrC,MAAMC,WAAWoC,MAC7BkB,OAAQH,WACRjC,eAAgB,MAAKnB,MAAMC,WAAWkB,eACtCC,aAAc,MAAKpB,MAAMC,WAAWmB,aACpCE,eAAgB,MAAKtB,MAAMC,WAAWqB,eACtCC,gBAAiB,MAAKvB,MAAMC,WAAWsB,gBACvCC,cAAe,MAAKxB,MAAMC,WAAWuB,cACrCC,gBAAiB,MAAKzB,MAAMC,WAAWwB,gBACvCC,cAAe,MAAK1B,MAAMC,WAAWyB,cACrCL,gBAAiB,MAAKrB,MAAMC,WAAWoB,gBACvCiB,QAAS,MAAKtC,MAAMC,WAAWqC,QAC/BxB,iBAAiB,EACjByB,mBAAoB,MAAKvC,MAAMC,WAAWsC,mBAC1CiB,kBAAmB,MAAKxD,MAAMC,WAAWuD,kBACzChB,oBAAqB,MAAKxC,MAAMC,WAAWuC,oBAC3CG,wBAAyB,MAAK3C,MAAMC,WAAW0C,wBAC/C1B,uBAAwB,MAAKjB,MAAMC,WAAWgB,uBAC9CyB,2BAA4B,MAAK1C,MAAMC,WAAWyC,2BAClDD,UAAW,UACXZ,oBAAoB,EACpBI,YAAa,GACbW,gBAAiB,MAAK5C,MAAMC,WAAW2C,gBACvCE,wBAAyB,MAAK9C,MAAMC,WAAW6C,wBAC/CD,qBAAsB,MAAK7C,MAAMC,WAAW4C,qBAC5CG,4BAA6B,MAAKhD,MAAMC,WAAW+C,4BACnDC,oBAAqB,MAAKjD,MAAMC,WAAWgD,oBAC3CF,yBAA0B,MAAK/C,MAAMC,WAAW8C,yBAChDG,+BAAgC,MAAKlD,MAAMC,WAAWiD,+BACtDC,wBAAyB,MAAKnD,MAAMC,WAAWkD,yBAlDnC,M,sFA+aL,4BAwDJ9R,KAAK2O,MAxDD,kCAGPC,WACCK,YAJM,sBAINA,YACAC,aALM,sBAKNA,aACAC,eANM,sBAMNA,eACAiD,iBAPM,sBAONA,iBACArD,cARM,sBAQNA,cACAC,aATM,sBASNA,aACAQ,WAVM,sBAUNA,WACA6C,gBAXM,sBAWNA,gBACAC,eAZM,sBAYNA,eACAC,eAbM,sBAaNA,eACAC,uBAdM,sBAcNA,uBACAC,iBAfM,sBAeNA,iBACAC,mBAhBM,sBAgBNA,mBACAC,gCAjBM,sBAiBNA,gCACAC,0BAlBM,sBAkBNA,0BACAZ,iBAnBM,sBAmBNA,iBACAC,mBApBM,sBAoBNA,mBACAY,UArBM,sBAqBNA,UACAC,SAtBM,sBAsBNA,SACAC,gBAvBM,sBAuBNA,gBACAC,cAxBM,sBAwBNA,cACAC,eAzBM,sBAyBNA,eAEA7D,aA3BM,sBA0BNK,gBA1BM,sBA2BNL,aAUA+C,mBArCM,sBA4BNnB,MA5BM,sBA6BNe,WA7BM,sBA8BNjC,eA9BM,sBA+BNC,aA/BM,sBAgCNE,eAhCM,sBAiCNC,gBAjCM,sBAkCNC,cAlCM,sBAmCNC,gBAnCM,sBAoCNC,cApCM,sBAqCN8B,mBACAe,8BAtCM,sBAsCNA,8BACAC,wBAvCM,sBAuCNA,wBACAC,QAxCM,sBAwCNA,QACAC,OAzCM,sBAyCNA,OACAC,cA1CM,sBA0CNA,cACAC,YA3CM,sBA2CNA,YACAC,iBA5CM,sBA4CNA,iBAGA3D,0BA/CM,sBA6CNsB,oBA7CM,sBA8CNvB,uBA9CM,sBA+CNC,0BAQDN,eAvDO,sBAgDNsC,+BAhDM,YAmDPjD,WAnDO,YAoDP6E,WApDO,YAqDPC,SArDO,YAsDPC,UAtDO,YAuDPpE,eAEsBvP,KAAK0P,MAAMtB,iBAClCY,aAAehP,KAAK0P,MAAM/B,mBAC1BoB,cAAgB/O,KAAK0P,MAAMhC,gBAC3BuB,YAAcjP,KAAK0P,MAAM9B,aACzBuB,eAAiBnP,KAAK0P,MAAM7B,oBAC5BqB,aAAelP,KAAK0P,MAAM5B,cAC1B0B,WAAaxP,KAAK0P,MAAM3B,YACxBkF,eAAwC,KAAvBjT,KAAK0P,MAAMuB,SAAmBjR,KAAK2O,MAAMC,WAAWQ,YAAuB,GAAT,OACnFG,cAAc,CAAC0D,iBAEf,IAWMW,0BAA4B,CACjC,CAAEjT,MAAO,SAAU+N,MAAO5C,GAAI,SAAU,6BACxC,CAAEnL,MAAO,QAAS+N,MAAO5C,GAAI,QAAS,8BAIjC+H,0BAA4B,CACjC,CAAElT,MAAO,SAAU+N,MAAO5C,GAAI,eAAgB,6BAC9C,CAAEnL,MAAO,SAAU+N,MAAO5C,GAAI,SAAU,8BAInCgI,0BAA4B,CACjC,CAAEnT,MAAO,OAAQ+N,MAAO5C,GAAI,OAAQ,6BACpC,CAAEnL,MAAO,QAAS+N,MAAO5C,GAAI,QAAS,6BACtC,CAAEnL,MAAO,QAAS+N,MAAO5C,GAAI,QAAS,6BACtC,CAAEnL,MAAO,QAAS+N,MAAO5C,GAAI,QAAS,6BACtC,CAAEnL,MAAO,UAAW+N,MAAO5C,GAAI,UAAW,6BAC1C,CAAEnL,MAAO,OAAQ+N,MAAO5C,GAAI,OAAQ,6BACpC,CAAEnL,MAAO,QAAS+N,MAAO5C,GAAI,QAAS,8BAIjCiI,sBAAwB,CAC7B,CAAEpT,MAAO,OAAQ+N,MAAO5C,GAAI,OAAQ,6BACpC,CAAEnL,MAAO,SAAU+N,MAAO5C,GAAI,SAAU,6BACxC,CAAEnL,MAAO,QAAS+N,MAAO5C,GAAI,QAAS,8BAEnCkI,aAAe,OACfC,cAAgB,GAapB,MAZ2C,WAAvCjU,KAAK0P,MAAMoC,0BACdkC,aAAe,OACfC,cAAgB,UAE0B,SAAvCjU,KAAK0P,MAAMoC,0BACdkC,aAAe,OACfC,cAAgB,KAE0B,UAAvCjU,KAAK0P,MAAMoC,0BACdkC,aAAe,QACfC,cAAgB,KAGhB,oBAACpI,SAAD,KACE7L,KAAK0P,MAAMJ,SACZ,oBAACzD,SAAD,KACC,oBAACK,YAAD,KACC,+BACC,2BAAKpK,GAAG,UAAUoS,YAAU,UAAUC,MAAM,6BAA6BC,MAAM,QAAQC,OAAO,QAAQC,QAAQ,qBAAoB,wCAAkB,4BAAMlJ,EAAE,wHAAwHmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,0HAA0HmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,8JAA8JmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,iIAAiImJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,sGAAsGmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,uKAAuKmJ,UAAU,yBAAyBC,KAAK,aACzwC,2BAAKb,UAAU,eAAc,oBAACrH,QAAD,WAK9BtM,KAAK0P,MAAMJ,SACZ,oBAACzD,SAAD,KACC,oBAACiB,kBAAD,KACC,oBAACb,UAAD,CAAWwI,MAAQ3I,GAAI,wBAAyB,6BAC/C,oBAACO,cAAD,CACEqC,MAAQ5C,GAAI,gBAAiB,4BAC7BnL,MAAOX,KAAK0P,MAAMjC,YAClBiH,QAAU1U,KAAK0P,MAAMlC,UACrBmH,SAAW,SAAEhU,OAAa,OAAKiU,eAAejU,OAAQ4O,cAAc,CAACV,QAASgG,OAAOlU,YAEvF,oBAAC0L,cAAD,CACEqC,MAAQ5C,GAAI,iBAAkB,4BAC9BnL,MAAOX,KAAK0P,MAAMsB,MAClB0D,QAAU1U,KAAK0P,MAAMwC,OACrByC,SAAW,SAAEhU,OAAa,OAAKmU,cAAcnU,OAAQ4O,cAAc,CAACyB,MAAOrQ,WAEtD,YAArBX,KAAK0P,MAAMsB,OAEZ,oBAAC3E,cAAD,CACCqC,MAAQ5C,GAAI,sBAAuB,4BACnCnL,MAAOX,KAAK0P,MAAMoC,wBAClB4C,QAAUX,sBACVY,SAAW,SAAEhU,OAAa,OAAKoU,yBAAyBpU,OAAQ4O,cAAc,CAACuC,wBAAyBnR,WAG1G,oBAAC0L,cAAD,CACCqC,MAAQ5C,GAAI,eAAgB,4BAC5B7I,YAAc6I,GAAI,iDAAkD,4BACpE4I,QAAUd,0BACVjT,MAAQ+R,mBACRiC,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAEmD,mBAAoB/R,WAElD,WAArBX,KAAK0P,MAAMsB,OACb,oBAACzE,YAAD,CACCmC,MAAO5C,GAAG,UAAW,4BACrBnL,MAAOX,KAAK0P,MAAMuB,QAClB0D,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAE0B,QAAStQ,QAAU,OAAKqU,oBAAoBrU,UAGnG,oBAAC6L,cAAD,CACCkC,MAAQ5C,GAAI,YAAa,4BACzBmJ,QAAUnC,SACV6B,SAAW,kBAAM,OAAKhG,MAAMY,cAAe,CAAEuD,UAAYA,cAE1D,oBAACtG,cAAD,CACCkC,MAAQ5C,GAAI,aAAc,4BAC1BmJ,QAAUpC,UACV8B,SAAW,kBAAM,OAAKhG,MAAMY,cAAe,CAAEsD,WAAaA,eAE3D,oBAACrG,cAAD,CACCkC,MAAQ5C,GAAI,mBAAoB,4BAChCmJ,QAAUlC,gBACV4B,SAAW,kBAAM,OAAKhG,MAAMY,cAAe,CAAEwD,iBAAmBA,qBAE1C,WAArB/S,KAAK0P,MAAMsB,OACb,oBAACnF,SAAD,KACC,oBAACW,cAAD,CACCkC,MAAQ5C,GAAI,kBAAmB,4BAC/BmJ,QAAUjC,cACV2B,SAAW,kBAAM,OAAKhG,MAAMY,cAAe,CAAEyD,eAAiBA,mBAE7DA,eACD,oBAACzG,YAAD,CACCmC,MAAO5C,GAAG,kBAAmB,4BAC7BnL,MAAOqR,iBACP2C,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEyC,iBAAkBrR,WAG1E,oBAAC6L,cAAD,CACCkC,MAAQ5C,GAAI,eAAgB,4BAC5BmJ,QAAUjV,KAAK0P,MAAM1B,aACrB2G,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEH,YAAazO,QAAW,OAAK0O,SAAS,CAACrB,aAAcrN,WAE1GX,KAAK0P,MAAM1B,cACZ,oBAACzB,YAAD,CACCmC,MAAO5C,GAAG,oBAAqB,4BAC/BnL,MAAOsR,mBACP0C,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAE0C,mBAAoBtR,YAK7E,oBAAC6L,cAAD,CACCkC,MAAQ5C,GAAI,oBAAqB,4BACjCmJ,QAAUjV,KAAK0P,MAAMD,gBACrBkF,SAAW,SAAEhU,OAAY,OAAKgO,MAAMY,cAAe,CAAEE,gBAAiB9O,QAAW,OAAKuU,wBAAyBvU,WAG3F,WAArBX,KAAK0P,MAAMsB,OACZ,oBAAC/E,UAAD,CAAWwI,MAAQ3I,GAAI,wBAAyB,6BAC/C,oBAACO,cAAD,CACEqC,MAAQ5C,GAAI,iBAAkB,4BAC9BnL,MAAOX,KAAK0P,MAAMyF,iBAClBT,QAAU,GAGVC,SAAW,SAAEhU,OAAa,OAAKiU,eAAejU,OAAQ4O,cAAc,CAACV,QAASgG,OAAOlU,aAIxF,oBAACsL,UAAD,CAAWwI,MAAQ3I,GAAI,SAAU,4BAA+BsJ,aAAa,GAC5E,oBAAClI,mBAAD,CACAuH,MAAQ3I,GAAI,mBAAoB,4BAChCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAO6R,uBACPmC,SA3KyB,SAAAhU,OAAK,OAAI4O,cAAe,CAAEiD,uBAAwB7R,SA4K3E+N,MAAO5C,GAAI,mBAAoB,gCAIhC,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,aAAc,4BAC1BsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAO8R,iBACPkC,SApL0B,SAAAhU,OAAK,OAAI4O,cAAe,CAAEkD,iBAAkB9R,SAqLtE+N,MAAO5C,GAAI,aAAc,gCAIH,YAArB9L,KAAK0P,MAAMsB,OACZ,oBAAC9D,mBAAD,CACAuH,MAAQ3I,GAAI,aAAc,4BAC1BsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAO6S,iBACPmB,SAxLyB,SAAAhU,OAAK,OAAI4O,cAAe,CAAEiE,iBAAkB7S,SAyLrE+N,MAAO5C,GAAI,aAAc,gCAKL,WAArB9L,KAAK0P,MAAMsB,OACX,oBAACnF,SAAD,KACC,oBAACqB,mBAAD,CACAuH,MAAQ3I,GAAI,oBAAqB,4BACjCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOX,KAAK0P,MAAM6B,gBAClBoD,SAAU3U,KAAKsV,wBACf5G,MAAO5C,GAAI,QAAS,gCAIrB,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,yBAA0B,4BACtCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOX,KAAK0P,MAAMkC,oBAClB+C,SAAU3U,KAAKuV,4BACf7G,MAAO5C,GAAI,QAAS,gCAIrB,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,sBAAuB,4BACnCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOX,KAAK0P,MAAM8B,qBAClBmD,SAAU3U,KAAKwV,sBACf9G,MAAO5C,GAAI,QAAS,gCAIrB,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,0BAA2B,4BACvCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOX,KAAK0P,MAAMgC,yBAClBiD,SAAU3U,KAAKyV,gCACf/G,MAAO5C,GAAI,QAAS,gCAIrB,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,yBAA0B,4BACtCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOX,KAAK0P,MAAM+B,wBAClBkD,SAAU3U,KAAK0V,8BACfhH,MAAO5C,GAAI,QAAS,gCAGrB,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,8BAA+B,4BAC3CsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOX,KAAK0P,MAAMiG,4BAClBhB,SAAU3U,KAAK4V,oCACflH,MAAO5C,GAAI,QAAS,gCAIrB,oBAACO,cAAD,CACEqC,MAAQ5C,GAAI,sBAAuB,4BACnCnL,MAAOX,KAAK0P,MAAMmC,+BAClB6C,QAASZ,0BACTa,SAAW,SAAEhU,OAAa,OAAKkV,oBAAoBlV,OAAQ4O,cAAc,CAACsC,+BAAgClR,YAIxF,WAArBX,KAAK0P,MAAMsB,OAA2C,YAArBhR,KAAK0P,MAAMsB,OAC7C,oBAACnF,SAAD,KACC,oBAACqB,mBAAD,CACAuH,MAAQ3I,GAAI,8BAA+B,4BAC3CsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOgS,gCACPgC,SAhRiC,SAAAhU,OAAK,OAAI4O,cAAe,CAAEoD,gCAAiChS,SAiR5F+N,MAAO5C,GAAI,wBAAyB,gCAIrC,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,wBAAyB,4BACrCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOiS,0BACP+B,SAzR2B,SAAAhU,OAAK,OAAI4O,cAAe,CAAEqD,0BAA2BjS,SA0RhF+N,MAAO5C,GAAI,wBAAyB,gCAIrC,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,2BAA4B,4BACxCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOuS,8BACPyB,SAlSgC,SAAAhU,OAAK,OAAI4O,cAAe,CAAE2D,8BAA+BvS,SAmSzF+N,MAAO5C,GAAI,0BAA2B,gCAIvC,oBAACoB,mBAAD,CACAuH,MAAQ3I,GAAI,0BAA2B,4BACvCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOwS,wBACPwB,SA3SyB,SAAAhU,OAAK,OAAI4O,cAAe,CAAE4D,wBAAyBxS,SA4S5E+N,MAAO5C,GAAI,0BAA2B,kCAOzC,oBAACG,UAAD,CAAWwI,MAAQ3I,GAAI,4BAA6B,4BAA+BsJ,aAAa,GAChG,oBAACjJ,aAAD,CACEuC,MAAQ5C,GAAI,mBAAoB,4BAChCnL,MAAQ4R,eACRoC,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAEgD,eAAgB5R,SACpEmV,IAAM,GACNC,IAAM,GACNC,KAAO,IAER,oBAAC7J,aAAD,CACCuC,MAAQ5C,GAAI,YAAa,4BACzBnL,MAAQ0R,gBACRsC,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAE8C,gBAAiB1R,SACrEmV,IAAM,GACNC,IAAM,GACNC,KAAO,IAEc,WAArBhW,KAAK0P,MAAMsB,OACZ,oBAAC7E,aAAD,CACCuC,MAAQ5C,GAAI,cAAe,4BAC3BnL,MAAQ2R,eACRqC,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAE+C,eAAgB3R,SACpEmV,IAAM,GACNC,IAAM,GACNC,KAAO,IAGR,oBAAC7J,aAAD,CACCuC,MAAQ5C,GAAI,UAAW,4BACvBnL,MAAQyS,QACRuB,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAE6D,QAASzS,SAC7DmV,IAAM,EACNC,IAAM,GACNC,KAAO,IAER,oBAAC7J,aAAD,CACCuC,MAAQ5C,GAAI,SAAU,4BACtBnL,MAAQ0S,OACRsB,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAE8D,OAAQ1S,SAC5DmV,IAAM,EACNC,IAAM,GACNC,KAAO,IAER,oBAAC7J,aAAD,CACCuC,MAAQ5C,GAAI,iBAAkB,4BAC9BnL,MAAQ2S,cACRqB,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAE+D,cAAe3S,SACnEmV,IAAM,EACNC,IAAM,GACNC,KAAO,IAER,oBAAC9I,mBAAD,CACAuH,MAAQ3I,GAAI,eAAgB,4BAC5BsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAO4S,YACPoB,SAzWqB,SAAAhU,OAAK,OAAI4O,cAAe,CAAEgE,YAAa5S,SA0W5D+N,MAAO5C,GAAI,eAAgB,iCAI7B,oBAACG,UAAD,CAAWwI,MAAQ3I,GAAI,wBAAyB,4BAA+BsJ,aAAa,GAC3F,oBAAC/I,cAAD,CACEqC,MAAQ5C,GAAI,sBAAuB,4BACnCnL,MAAOX,KAAK0P,MAAMwB,mBAClBwD,QAAUb,0BACVc,SAAW,SAAEhU,OAAa4O,cAAc,CAAC2B,mBAAoBvQ,QAAS,OAAKsV,8BAA8BtV,UAEvE,WAAlCX,KAAK0P,MAAMwB,oBACZ,oBAAChE,mBAAD,CACCuH,MAAQ3I,GAAI,qBAAsB,4BAClCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOwR,kBACPwC,SA5XwB,SAAAhU,OAAK,OAAI4O,cAAe,CAAE4C,kBAAmBxR,SA6XrE+N,MAAO5C,GAAI,qBAAsB,gCAKpC,oBAACS,YAAD,CACCmC,MAAO5C,GAAG,WAAY,4BACtBnL,MAAOX,KAAK0P,MAAMI,eAClB6E,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEO,eAAgBnP,QAAU,OAAKuV,qBAAqBvV,UAE3G,oBAAC4L,YAAD,CACCmC,MAAO5C,GAAG,UAAW,4BACrBnL,MAAOX,KAAK0P,MAAMS,cAClBwE,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEY,cAAexP,QAAU,OAAKwV,oBAAoBxV,UAEzG,oBAAC4L,YAAD,CACCmC,MAAO5C,GAAG,YAAa,4BACvBnL,MAAOX,KAAK0P,MAAMM,gBAClB2E,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAES,gBAAiBrP,QAAU,OAAKyV,sBAAsBzV,UAE7G,oBAAC4L,YAAD,CACCmC,MAAO5C,GAAG,WAAY,4BACtBnL,MAAOX,KAAK0P,MAAMO,eAClB0E,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEU,eAAgBtP,QAAU,OAAK0V,qBAAqB1V,UAE3G,oBAAC4L,YAAD,CACCmC,MAAO5C,GAAG,UAAW,4BACrBnL,MAAOX,KAAK0P,MAAMW,cAClBsE,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEc,cAAe1P,QAAU,OAAK2V,oBAAoB3V,UAEzG,oBAAC4L,YAAD,CACCmC,MAAO5C,GAAG,SAAU,4BACpBnL,MAAOX,KAAK0P,MAAMK,aAClB4E,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEQ,aAAcpP,QAAU,OAAK4V,mBAAmB5V,UAEvG,oBAAC4L,YAAD,CACCmC,MAAO5C,GAAG,YAAa,4BACvBnL,MAAOX,KAAK0P,MAAMQ,gBAClByE,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEW,gBAAiBvP,QAAU,OAAK6V,sBAAsB7V,UAE7G,oBAAC4L,YAAD,CACCmC,MAAO5C,GAAG,YAAa,4BACvBnL,MAAOX,KAAK0P,MAAMU,gBAClBuE,SAAW,SAAEhU,OAAa,OAAKgO,MAAMY,cAAe,CAAEa,gBAAiBzP,QAAU,OAAK8V,sBAAsB9V,YAIxF,WAArBX,KAAK0P,MAAMsB,OACZ,2BACC2C,UACC+C,qBACC,4BACA1W,KAAK0P,MAAMsB,MACXoB,iBACAM,mBACA,qBAGFiE,MAAQ,CACPvD,QAASA,QAAU,KACnBC,OAAQA,OAAS,YAAcE,YAC/BqD,aAActD,cAAgB,KAC9BuD,gBAAiBrE,uBACjBsE,MAAOrE,iBACPsE,MAAO/C,aACPgD,OAAQ/C,gBAIY,YAArBjU,KAAK0P,MAAMsB,OACZ,oBAACnF,SAAD,KACA,2BAAK8H,UACJ+C,qBACC,6BACA,gBAAkBrE,kBAInB,2BAAKsB,UAAU,6BACd,2BAAKA,UAAU,4BACd,oBAAC5G,YAAD,CACCkK,YAAc,CACbtD,UAAW,gBAEZuD,SAAW,SAAEC,KAAW,OAAKC,kBAAmBD,IAAIrV,GAAIqV,IAAIvb,KAAO2T,cAAe,CAAEP,aAAcmI,IAAIrV,GAAIiN,cAAeoI,IAAIvb,OAC7Hyb,KAAK,QACL1W,MAAQqO,aACRsI,OAAS,mBAAIzb,KAAJ,KAAIA,KAAJ,OACR,oBAAC4Q,OAAD,CACA8K,QAAU1b,MACLmT,aAA+D,2BAClE2E,UAAU,iBACV6D,IAAMzI,cACN0I,IAAI,WAHc,2BAAKD,IAAKzI,cAAe0I,IAAI,sBAWrD,2BAAK9D,UAAU,oBACbb,UACD,oBAAC7F,SAAD,CACCyK,QAAQ,KACRC,YAAc7L,GAAI,WAAY,4BAC9BnL,MAAQsO,YACR0E,UAAU,mBACVgD,MAAQ,CACPG,MAAOrE,iBACPmF,SAAUrF,eAAiB,MAE5BoC,SAAW,SAAEhU,OAAa,OAAKkX,aAAalX,OAAQ4O,cAAe,CAAEN,YAAatO,WAGlFkS,WACD,oBAAC5F,SAAD,CACCyK,QAAQ,IACRC,YAAc7L,GAAI,YAAa,iBAC/BnL,MAAQuO,aACRyE,UAAU,oBACVgD,MAAQ,CACPG,MAAOrE,kBAERkC,SAAW,SAAEhU,OAAY,OAAKmX,cAAcnX,OAAQ4O,cAAe,CAAEL,aAAcvO,WAGnFoS,iBACD,oBAAC9F,SAAD,CACCyK,QAAQ,MACR/D,UAAU,mBACVgE,YAAc7L,GAAI,sBAAuB,4BACzCnL,MAAQwO,eACR4I,mBAAqB,CAAE,OAAQ,SAAU,gBAAiB,QAC1DpD,SAAW,SAAEhU,OAAY,OAAKqX,oBAAoBrX,OAAQ4O,cAAe,CAAEJ,eAAgBxO,aAK7F6O,cAAiBA,WAAWlO,QAC7B,2BAAKqS,UAAU,2BAA2BgD,MAAO,CAACvC,MAAOnB,iBACxDD,eACA,2BACCW,UAAU,yBACVgD,MAAQ,CACPE,gBAAiBlE,gCACjBmE,MAAOlE,0BACPwB,MAAOnB,eACP2E,SAAUtF,eAAiB,OAG5B,yBACCrK,KAAMuH,WACNmH,MAAQ,CACPE,gBAAiBlE,gCACjBmE,MAAOlE,4BAEPZ,mBAGoB,IAAtBhS,KAAK0P,MAAMuB,SAAiB7B,aAC7B,2BACAuE,UAAU,2BACVgD,MAAQ,CACPE,gBAAiB3D,8BACjB4D,MAAO3D,wBACPyE,SAAUtF,eAAiB,OAG5B,yBACCrK,KAAMjI,KAAK0P,MAAMuB,QACjB0F,MAAQ,CACPE,gBAAiB3D,8BACjB4D,MAAO3D,0BAEPlB,uBAOmB,YAArBjS,KAAK0P,MAAMsB,OACZ,2BAAK2C,UACJ+C,qBACC,6BACA,gBAAkBrE,kBAIlBS,UACA,oBAAC7F,SAAD,CACCyK,QAAQ,KACRC,YAAc7L,GAAI,WAAY,4BAC9BnL,MAAQsO,YACR0E,UAAU,mBACVgD,MAAQ,CACPG,MAAOrE,iBACPmF,SAAUrF,eAAiB,MAE5BoC,SAAW,SAAEhU,OAAa,OAAKkX,aAAalX,OAAQ4O,cAAe,CAAEN,YAAatO,WAGpF,2BAAKgT,UAAU,6BACd,2BAAKA,UAAU,4BACd,oBAAC5G,YAAD,CACCkK,YAAc,CACbtD,UAAW,gBAEZuD,SAAW,SAAEC,KAAW,OAAKC,kBAAmBD,IAAIrV,GAAIqV,IAAIvb,KAAO2T,cAAe,CAAEP,aAAcmI,IAAIrV,GAAIiN,cAAeoI,IAAIvb,OAC7Hyb,KAAK,QACL1W,MAAQqO,aACRsI,OAAS,oBAAIzb,KAAJ,MAAIA,KAAJ,OACR,oBAAC4Q,OAAD,CAAQ8K,QAAU1b,MACbmT,aAA+D,2BAClE2E,UAAU,iBACV6D,IAAMzI,cACN0I,IAAI,WAHc,2BAAKD,IAAKzI,cAAe0I,IAAI,sBAWpD1E,iBACA,oBAAC9F,SAAD,CACCyK,QAAQ,MACR/D,UAAU,mBACVgE,YAAc7L,GAAI,sBAAuB,4BACzCnL,MAAQwO,eACR4I,mBAAqB,CAAE,OAAQ,SAAU,gBAAiB,QAC1DpD,SAAW,SAAEhU,OAAY,OAAKqX,oBAAoBrX,OAAQ4O,cAAe,CAAEJ,eAAgBxO,WAG7F,2BAAKgT,UAAU,mBAAmBgD,MAAO,CAACiB,SAAUtF,eAAiB,OACnEU,eACD,2BAAKW,UAAU,8BACd,yBAAG1L,KAAMjI,KAAK0P,MAAM3B,YACpB4I,MAAQ,CACPG,MAAOtD,mBAEN1H,GAAI,oBAAqB,4BAJ3B,IAI0D9L,KAAK0P,MAAMY,0BAG/C,IAAtBtQ,KAAK0P,MAAMuB,SAAiB7B,aAC7B,2BAAKuE,UAAU,+BACd,yBAAG1L,KAAMjI,KAAK0P,MAAMuB,QACpB0F,MAAQ,CACPG,MAAOtD,mBAEN1H,GAAI,UAAW,gCAQE,YAArB9L,KAAK0P,MAAMsB,OACZ,2BAAK2C,UACJ+C,qBACC,6BACA,gBAAkBrE,kBAIlBS,UACA,oBAAC7F,SAAD,CACCyK,QAAQ,KACRC,YAAc7L,GAAI,WAAY,4BAC9BnL,MAAQsO,YACR0E,UAAU,mBACVgD,MAAQ,CACPG,MAAOrE,iBACPmF,SAAUrF,eAAiB,MAE5BoC,SAAW,SAAEhU,OAAa,OAAKkX,aAAalX,OAAQ4O,cAAe,CAAEN,YAAatO,WAGpF,2BAAKgT,UAAU,6BACd,2BAAKA,UAAU,4BACd,oBAAC5G,YAAD,CACCkK,YAAc,CACbtD,UAAW,gBAEZuD,SAAW,SAAEC,KAAW,OAAKC,kBAAmBD,IAAIrV,GAAIqV,IAAIvb,KAAO2T,cAAe,CAAEP,aAAcmI,IAAIrV,GAAIiN,cAAeoI,IAAIvb,OAC7Hyb,KAAK,QACL1W,MAAQqO,aACRsI,OAAS,oBAAIzb,KAAJ,MAAIA,KAAJ,OACR,oBAAC4Q,OAAD,CAAQ8K,QAAU1b,MACbmT,aAA+D,2BAClE2E,UAAU,iBACV6D,IAAMzI,cACN0I,IAAI,WAHc,2BAAKD,IAAKzI,cAAe0I,IAAI,sBAWpD1E,iBACA,oBAAC9F,SAAD,CACCyK,QAAQ,MACR/D,UAAU,mBACVgE,YAAc7L,GAAI,sBAAuB,4BACzCnL,MAAQwO,eACR4I,mBAAqB,CAAE,OAAQ,SAAU,gBAAiB,QAC1DpD,SAAW,SAAEhU,OAAY,OAAKqX,oBAAoBrX,OAAQ4O,cAAe,CAAEJ,eAAgBxO,WAG7F,2BAAKgT,UAAU,oBACbX,eACD,2BACCW,UAAU,yBACVgD,MAAQ,CACPE,gBAAiBlE,gCACjBmE,MAAOlE,0BACPwB,MAAO,MACP4C,OAAQ,mBACRY,SAAUtF,eAAiB,OAG5B,yBACCrK,KAAMuH,WACNmH,MAAQ,CACPE,gBAAiBlE,gCACjBmE,MAAOlE,4BAEP9G,GAAG,aAAc,8BAGG,IAAtB9L,KAAK0P,MAAMuB,SAAiB7B,aAC7B,2BACAuE,UAAU,2BACVgD,MAAQ,CACPE,gBAAiB3D,8BACjB4D,MAAO3D,wBACPyE,SAAUtF,eAAiB,KAC3B8B,MAAO,MACP4C,OAAQ,WAGT,yBACC/O,KAAMjI,KAAK0P,MAAMuB,QACjB0F,MAAQ,CACPE,gBAAiB3D,8BACjB4D,MAAO3D,0BAEPrH,GAAG,eAAgB,gCAMQ,GAA9B9L,KAAK0P,MAAMD,kBAAkD,YAArBzP,KAAK0P,MAAMsB,OAA4C,YAArBhR,KAAK0P,MAAMsB,OAA4C,YAArBhR,KAAK0P,MAAMsB,QACxH,2BAAK2C,UAAU,cACiB,IAA7B3T,KAAK0P,MAAMI,gBACZ,yBAAG7H,KAAMjI,KAAK0P,MAAMI,gBACnB,2BAAK6D,UAAU,qBAAqBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC7H,2BAAKlK,KAAK,gBAIiB,IAA5BjI,KAAK0P,MAAMS,eACZ,yBAAGlI,KAAMjI,KAAK0P,MAAMS,eACnB,2BAAKwD,UAAU,oBAAoBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC5H,2BAAKlK,KAAK,eAImB,IAA9BjI,KAAK0P,MAAMM,iBACZ,yBAAG/H,KAAMjI,KAAK0P,MAAMM,iBACnB,2BAAK2D,UAAU,sBAAsBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC9H,2BAAKlK,KAAK,iBAImB,IAA9BjI,KAAK0P,MAAMQ,iBACZ,yBAAGjI,KAAMjI,KAAK0P,MAAMQ,iBACnB,2BAAKyD,UAAU,sBAAsBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC9H,2BAAKlK,KAAK,iBAIkB,IAA7BjI,KAAK0P,MAAMO,gBACZ,yBAAGhI,KAAMjI,KAAK0P,MAAMO,gBACnB,2BAAK0D,UAAU,qBAAqBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC7H,2BAAKlK,KAAK,gBAIiB,IAA5BjI,KAAK0P,MAAMW,eACZ,yBAAGpI,KAAMjI,KAAK0P,MAAMW,eACnB,2BAAKsD,UAAU,oBAAoBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC5H,2BAAKlK,KAAK,eAIgB,IAA3BjI,KAAK0P,MAAMK,cACZ,yBAAG9H,KAAMjI,KAAK0P,MAAMK,cACnB,2BAAK4D,UAAU,mBAAmBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC3H,2BAAKlK,KAAK,cAImB,IAA9BjI,KAAK0P,MAAMU,iBACZ,yBAAGnI,KAAMjI,KAAK0P,MAAMU,iBACnB,2BAAKuD,UAAU,sBAAsBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC9H,2BAAKlK,KAAK,mBAQQ,WAArBjI,KAAK0P,MAAMsB,OACZ,oBAACnF,SAAD,KACA,2BACC8H,UACC+C,qBACC,oBACA1W,KAAK0P,MAAMsB,MACXoB,iBACAM,mBACA,sBAIF,0BAAIiB,UAAU,mBACb,0BAAIA,UACH+C,qBACC,kBACyB,YAAzB1W,KAAK0P,MAAM0B,UAA0B,SAAW,IAIlDmG,QAASvX,KAAKkY,yBACdvB,MAAO,CAACE,gBAAiB7W,KAAK0P,MAAM6B,gBAAiBuF,MAAO9W,KAAK0P,MAAMkC,sBAEvE,oBAAC3E,SAAD,CACEyK,QAAQ,OACRC,YAAc7L,GAAI,gBAAiB,4BACnCnL,MAAOX,KAAK0P,MAAMyB,oBAClB4G,mBAAoB,GACpBpD,SAAW,SAAEhU,OAAY,OAAKwX,4BAA4BxX,OAAQ4O,cAAe,CAAE4B,oBAAqBxQ,YAG1G,0BAAIgT,UACH+C,qBACC,gBACyB,WAAzB1W,KAAK0P,MAAM0B,UAAyB,SAAW,IAEhDmG,QAASvX,KAAKoY,sBACdzB,MAAO,CAACE,gBAAiB7W,KAAK0P,MAAM8B,qBAAsBsF,MAAO9W,KAAK0P,MAAMgC,2BAE5E,oBAACzE,SAAD,CACCyK,QAAQ,OACRC,YAAc7L,GAAI,gBAAiB,4BACnCnL,MAAOX,KAAK0P,MAAM4B,wBAClByG,mBAAoB,GACpBpD,SAAW,SAAEhU,OAAY,OAAK0X,gCAAgC1X,OAAQ4O,cAAe,CAAE+B,wBAAyB3Q,aAInH,2BAAKgT,UAAU,kBACdgD,MAAQ,CACPvD,QAASA,QAAU,KACnBC,OAAQA,OAAS,YAAcE,YAC/BqD,aAActD,cAAgB,KAC9BuD,gBAAiBrE,uBACjBsE,MAAOrE,mBAGkB,YAAzBzS,KAAK0P,MAAM0B,WACZ,oBAACvF,SAAD,KACD,2BAAK8H,UAAU,6BACd,2BAAKA,UAAU,sBACd,oBAAC1G,SAAD,CACEyK,QAAQ,MACR/D,UAAU,6BACVhT,MAAQX,KAAK0P,MAAM2B,2BACnB0G,mBAAoB,GACpBpD,SAAW,SAAEhU,OAAY,OAAK2X,mCAAmC3X,OAAQ4O,cAAe,CAAEoC,4BAA6BhR,SACvHgW,MAAO,CAACE,gBAAiB7W,KAAK0P,MAAM+B,wBAAyBqF,MAAO9W,KAAK0P,MAAMiC,gCAGjF3R,KAAK0P,MAAMD,iBACX,2BAAKkE,UAAU,qBACd,2BAAKA,UAAU,cACiB,IAA7B3T,KAAK0P,MAAMI,gBACZ,yBAAG7H,KAAMjI,KAAK0P,MAAMI,gBACnB,2BAAK6D,UAAU,qBAAqBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC7H,2BAAKlK,KAAK,gBAIiB,IAA5BjI,KAAK0P,MAAMS,eACZ,yBAAGlI,KAAMjI,KAAK0P,MAAMS,eACnB,2BAAKwD,UAAU,oBAAoBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC5H,2BAAKlK,KAAK,eAImB,IAA9BjI,KAAK0P,MAAMM,iBACZ,yBAAG/H,KAAMjI,KAAK0P,MAAMM,iBACnB,2BAAK2D,UAAU,sBAAsBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC9H,2BAAKlK,KAAK,iBAImB,IAA9BjI,KAAK0P,MAAMQ,iBACZ,yBAAGjI,KAAMjI,KAAK0P,MAAMQ,iBACnB,2BAAKyD,UAAU,sBAAsBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC9H,2BAAKlK,KAAK,iBAIkB,IAA7BjI,KAAK0P,MAAMO,gBACZ,yBAAGhI,KAAMjI,KAAK0P,MAAMO,gBACnB,2BAAK0D,UAAU,qBAAqBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC7H,2BAAKlK,KAAK,gBAIiB,IAA5BjI,KAAK0P,MAAMW,eACZ,yBAAGpI,KAAMjI,KAAK0P,MAAMW,eACnB,2BAAKsD,UAAU,oBAAoBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC5H,2BAAKlK,KAAK,eAIgB,IAA3BjI,KAAK0P,MAAMK,cACZ,yBAAG9H,KAAMjI,KAAK0P,MAAMK,cACnB,2BAAK4D,UAAU,mBAAmBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC3H,2BAAKlK,KAAK,cAImB,IAA9BjI,KAAK0P,MAAMU,iBACZ,yBAAGnI,KAAMjI,KAAK0P,MAAMU,iBACnB,2BAAKuD,UAAU,sBAAsBsE,KAAK,MAAMtB,MAAO,CAACnC,KAAwC,WAAlCxU,KAAK0P,MAAMwB,mBAAkCiB,kBAAoB,KAC9H,2BAAKlK,KAAK,oBAQhB,2BAAK0L,UAAU,6BACd,2BAAKA,UAAU,4BACd,oBAAC5G,YAAD,CACCkK,YAAc,CACbtD,UAAW,gBAEZuD,SAAW,SAAEC,KAAW,OAAKC,kBAAmBD,IAAIrV,GAAIqV,IAAIvb,KAAO2T,cAAe,CAAEP,aAAcmI,IAAIrV,GAAIiN,cAAeoI,IAAIvb,OAC7Hyb,KAAK,QACL1W,MAAQqO,aACRsI,OAAS,oBAAIzb,KAAJ,MAAIA,KAAJ,OACR,oBAAC4Q,OAAD,CAAQ8K,QAAU1b,MACbmT,aAA+D,2BAClE2E,UAAU,iBACV6D,IAAMzI,cACN0I,IAAI,WAHc,2BAAKD,IAAKzI,cAAe0I,IAAI,oBASnD,oBAACxK,SAAD,CACCyK,QAAQ,MACR/D,UAAU,iCACVgE,YAAc7L,GAAI,6BAA8B,4BAChDnL,MAAQX,KAAK0P,MAAME,uBACnBmI,mBAAqB,CAAE,OAAQ,SAAU,gBAAiB,QAC1DpD,SAAW,SAAEhU,OAAY,OAAK4X,yBAAyB5X,OAAQ4O,cAAe,CAAEK,uBAAwBjP,aAK3G,2BAAKgT,UAAU,kCACZd,WACF,oBAAC5F,SAAD,CACEyK,QAAQ,MACR/D,UAAU,2BACVgE,YAAc7L,GAAI,uBAAwB,4BAC1CnL,MAAQkP,yBACRkI,mBAAqB,CAAE,OAAQ,SAAU,gBAAiB,QAC1DpD,SAAW,SAAEhU,OAAa4O,cAAe,CAAEM,yBAA0BlP,WAGrEmS,UACF,oBAAC7F,SAAD,CACCyK,QAAQ,KACRC,YAAc7L,GAAI,WAAY,4BAC9BnL,MAAQsO,YACR0E,UAAU,mBACVgD,MAAQ,CACPG,MAAOrE,iBACPmF,SAAUrF,eAAiB,MAE5BoC,SAAW,SAAEhU,OAAa,OAAKkX,aAAalX,OAAQ4O,cAAe,CAAEN,YAAatO,WAGjFoS,iBACF,oBAAC9F,SAAD,CACCyK,QAAQ,MACR/D,UACC+C,qBACC,mBACA,gBAAkBrE,iBAGpBsF,YAAc7L,GAAI,sBAAuB,4BACzCnL,MAAQwO,eACR4I,mBAAqB,CAAE,OAAQ,SAAU,gBAAiB,QAC1DpD,SAAW,SAAEhU,OAAY,OAAKqX,oBAAoBrX,OAAQ4O,cAAe,CAAEJ,eAAgBxO,aAMpE,WAAzBX,KAAK0P,MAAM0B,WACX,oBAACvF,SAAD,KACE7L,KAAK0P,MAAMc,oBACX,oBAAC3E,SAAD,KACC,+BACC,2BAAK8H,UAAU,eAAc,oBAACrH,QAAD,UAK9BtM,KAAK0P,MAAMc,oBACZ,oBAAC3E,SAAD,KACC,0BACA8H,UACC+C,qBACC,yBACA1W,KAAK0P,MAAMmC,iCAGZ7R,KAAK0P,MAAMkB,yB,oIA1/CahF,W,2tEChCLF,GAAGC,QAA3BC,yB,0BAAAA,UAAWC,wB,0BAAAA,SAEXC,gBAAOJ,GAAGK,KAAVD,G,6BAeJJ,GAAGM,WAZNC,yB,6BAAAA,UACAC,2B,6BAAAA,YAEAC,6B,6BADAC,c,6BACAD,cACAE,6B,6BAAAA,cACAC,uB,6BAAAA,QAEAE,8B,6BADAD,Y,6BACAC,eAIAC,uB,6BAHAC,Q,6BACAC,Y,6BACAC,a,6BACAH,Q,yBAUGf,GAAGmB,OANNC,iC,yBAAAA,kBACAE,6B,yBAAAA,cACAD,2B,yBAAAA,YACAE,wB,yBAAAA,SACAE,gC,yBAAAA,iBACAD,kC,yBAAAA,mBAqaD,qB,keA7ZC,yBAAc,sIACb,wBAAUnM,YADG,aAeF,WACX3B,qBAAWgO,cAAcC,SAAd,aAAuC,GAAI,CAAE,QAAW,CAAE,aAAcD,cAAcE,SAAY/O,MAAM,SAAC7B,UAAa,mBAC5H6Q,MAAQ7H,QACR8H,UAAY9H,QACZ+H,YAAc,EACdC,gBAAkB,GAClBC,mBAAqB,EACrBC,aAAe,GACfC,oBAAsB,GACtBC,cAAgB,GAChBC,YAAc,GAClByK,EAAEtK,KAAMxR,SAAS3B,MAAM,SAAU6C,IAAK+C,OACrC4M,MAAM5M,MAAMwN,IAAM,CACjBC,iBAAkBzN,MAAMyN,iBACxBC,oBAAqB1N,MAAM0N,oBAC3BC,aAAc3N,MAAM2N,aACpBrL,YAAatC,MAAMsC,YACnBsL,kBAAmB5N,MAAM4N,kBACzBZ,mBAAoBhN,MAAMgN,mBAC1Ba,cAAe7N,MAAM6N,cACrBC,UAAW9N,MAAM8N,WAEb9N,MAAM4N,oBACVd,YAAc9M,MAAMwN,IAErBX,UAAUnM,KAAM,CAAEV,MAAOA,MAAMwN,GAAIO,MAAO/N,MAAM2N,kBAEX,IAAlC,MAAKK,MAAMC,WAAWC,UACzBpB,YAAc,MAAKkB,MAAMC,WAAWC,SAErC,IAAIC,oBAAsBvB,MAAME,aAC5BqB,oBAAoBT,qBACvBX,gBAAkB,MAAKiB,MAAMC,WAAWG,cAAczN,OAAS,EAAI,MAAKqN,MAAMC,WAAWG,cAAgBD,oBAAoBV,iBAApB,UACzGT,mBAAqB,MAAKgB,MAAMC,WAAWI,aAAa1N,OAAS,EAAI,MAAKqN,MAAMC,WAAWI,aAAeF,oBAAoBnB,mBAC9HC,aAAe,MAAKe,MAAMC,WAAWK,YAAY3N,OAAS,EAAI,MAAKqN,MAAMC,WAAWK,YAAeH,oBAAoBR,aACvHR,cAAgB,MAAKa,MAAMC,WAAWM,aAAa5N,OAAS,EAAI,MAAKqN,MAAMC,WAAWM,aAAgB,GACtGnB,YAAce,oBAAoBL,UAClCZ,oBAAsB,MAAKc,MAAMC,WAAWO,eAAe7N,OAAS,EAAI,MAAKqN,MAAMC,WAAWO,eAAiBL,oBAAoB7L,cAEnI2K,aAAe,MAAKe,MAAMC,WAAWK,YAAY3N,OAAS,EAAI,MAAKqN,MAAMC,WAAWK,YAAeH,oBAAoBR,aACvHR,cAAgB,MAAKa,MAAMC,WAAWM,aAAa5N,OAAS,EAAI,MAAKqN,MAAMC,WAAWM,aAAgB,GACtGrB,oBAAsB,MAAKc,MAAMC,WAAWO,eAAe7N,OAAS,EAAI,MAAKqN,MAAMC,WAAWO,eAAiBL,oBAAoB7L,YACnIyK,gBAAkB,MAAKiB,MAAMC,WAAWG,cAAczN,OAAS,EAAI,MAAKqN,MAAMC,WAAWG,cAAgBD,oBAAoBN,cAC7Hb,mBAAqB,MAAKgB,MAAMC,WAAWI,aAAa1N,OAAS,EAAI,MAAKqN,MAAMC,WAAWI,aAAe,EAC1GjB,YAAce,oBAAoBL,WAE/BjR,MAAaqQ,sBAChBA,oBAAsB,IAEvB,MAAKwB,UAAL,8CAEEC,SAAS,EACT/B,MACAE,YACAD,UACAE,gBACAC,oBAPF,cAQeF,aARf,4DASgBG,cAThB,6DAUiBE,eAVjB,mEAWuBD,qBAXvB,2DAYeE,aAZf,iBAeA,MAAKY,MAAMY,cAAe,CACzBJ,eAAgBtB,oBAChBoB,YAAarB,aACbsB,aAAcpB,cACd0B,WAAYzB,YACZiB,aAAcrB,mBACdoB,cAAerB,wBArFJ,4FAyFG,SAAEmB,SAClB,IACInB,gBAAkB,GAClBC,mBAAqB,EAFd,MAAK+B,MAAMnC,MAAMsB,SAGlBR,qBAITX,gBAAkB,MAAKgC,MAAMnC,MAAMsB,SAAjB,2BAClBlB,mBAAqB,MAAK+B,MAAMnC,MAAMsB,SAAjB,qBAJrBnB,gBAAkBN,cAAcuC,YAChChC,mBAAqB,GAKtB,IAAI1K,YAAc,MAAKyM,MAAMnC,MAAMsB,SAAS5L,iBACxCzF,IAAcyF,cACjBA,YAAc,IAEf,MAAK0L,MAAMY,cAAe,CACzBN,YAAa,MAAKS,MAAMnC,MAAMsB,SAASP,aACvCa,eAAgBlM,YAChBiM,aAAc,GACdM,WAAY,MAAKE,MAAMnC,MAAMsB,SAASJ,UACtCM,cAAerB,kBAEhB,MAAK2B,SACJ,CACCzB,aAAc,MAAK8B,MAAMnC,MAAMsB,SAASP,aACxCT,oBAAqB5K,YACrB6K,cAAe,GACfJ,gBACAC,mBACAF,YAAaoB,QACbd,YAAa,MAAK2B,MAAMnC,MAAMsB,SAASJ,eAvH5B,+FA2HM,WACnB,MAAKoC,eA5HQ,+FA8HM,SAAEC,SAAUC,WAC/B,MAAK1B,SAAU,CACd3B,gBAAiBqD,UACjBpD,mBAAoBmD,cAjIR,0FAoIC,SAACnQ,OACf,MAAK0O,SACJ,CACCzB,aAAcjN,WAvIH,2FA2IE,SAACA,OAChB,MAAK0O,SACJ,CACCvB,cAAenN,WA9IJ,iGAkJQ,SAACA,OACtB,MAAK0O,SACJ,CACCxB,oBAAqBlN,WAlJvB,MAAK+O,MAAQ,CACZJ,SAAS,EACT/B,OAAO,EACPC,WAAW,EACXE,iBAAiB,EACjBC,mBAAoB,EACpBF,aAAa,EACbI,oBAAqB,GACrBD,aAAc,GACdE,cAAe,IAZH,M,6EAyJL,4BA2BJ9N,KAAK2O,MA3BD,kCAGPC,WACCK,YAJM,sBAINA,YACAC,aALM,sBAKNA,aACAC,eANM,sBAMNA,eACAiD,iBAPM,sBAONA,iBACArD,cARM,sBAQNA,cACAC,aATM,sBASNA,aACAQ,WAVM,sBAUNA,WACA6C,gBAXM,sBAWNA,gBACAG,uBAZM,sBAYNA,uBACAC,iBAbM,sBAaNA,iBACAC,mBAdM,sBAcNA,mBACAC,gCAfM,sBAeNA,gCACAC,0BAhBM,sBAgBNA,0BACAC,UAjBM,sBAiBNA,UACAC,SAlBM,sBAkBNA,SACAC,gBAnBM,sBAmBNA,gBACAC,cApBM,sBAoBNA,cAMDzD,eA1BO,YAsBPX,WAtBO,YAuBP6E,WAvBO,YAwBPC,SAxBO,YAyBPC,UAzBO,YA0BPpE,eAEsBvP,KAAK0P,MAAMtB,iBAClCY,aAAehP,KAAK0P,MAAM/B,mBAC1BoB,cAAgB/O,KAAK0P,MAAMhC,gBAC3BuB,YAAcjP,KAAK0P,MAAM9B,aACzBuB,eAAiBnP,KAAK0P,MAAM7B,oBAC5BqB,aAAelP,KAAK0P,MAAM5B,cAC1B0B,WAAaxP,KAAK0P,MAAM3B,YAExB,IAMM6F,0BAA4B,CACjC,CAAEjT,MAAO,SAAU+N,MAAO5C,gBAAI,SAAU,6BACxC,CAAEnL,MAAO,QAAS+N,MAAO5C,gBAAI,QAAS,8BAEvC,OACC,oBAAC,wBAAD,KACE9L,KAAK0P,MAAMJ,SACZ,oBAAC,wBAAD,KACC,oBAAC,2BAAD,KACExD,gBAAG,aAAc,4BAClB,oBAAC,uBAAD,SAIA9L,KAAK0P,MAAMJ,SACZ,oBAAC,wBAAD,KACC,oBAAC,iCAAD,KACC,oBAAC,yBAAD,CAAWmF,MAAQ3I,gBAAI,wBAAyB,6BAC/C,oBAAC,6BAAD,CACE4C,MAAQ5C,gBAAI,gBAAiB,4BAC7BnL,MAAOX,KAAK0P,MAAMjC,YAClBiH,QAAU1U,KAAK0P,MAAMlC,UACrBmH,SAAW,SAAEhU,OAAa,OAAKiU,eAAejU,OAAQ4O,cAAc,CAACV,QAASgG,OAAOlU,YAEvF,oBAAC,4BAAD,CACC+N,MAAQ5C,gBAAI,YAAa,4BACzBnL,MAAQ0R,gBACRsC,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAE8C,gBAAiB1R,SACrEmV,IAAM,GACNC,IAAM,GACNC,KAAO,IAER,oBAAC,6BAAD,CACCtH,MAAQ5C,gBAAI,eAAgB,4BAC5B7I,YAAc6I,gBAAI,iDAAkD,4BACpE4I,QAAUd,0BACVjT,MAAQ+R,mBACRiC,SAAW,SAAEhU,OAAF,OAAa,OAAKgO,MAAMY,cAAe,CAAEmD,mBAAoB/R,WAEzE,oBAAC,kCAAD,CACA8T,MAAQ3I,gBAAI,mBAAoB,4BAChCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAO6R,uBACPmC,SAlDyB,SAAAhU,OAAK,OAAI4O,cAAe,CAAEiD,uBAAwB7R,SAmD3E+N,MAAO5C,gBAAI,mBAAoB,gCAIhC,oBAAC,kCAAD,CACA2I,MAAQ3I,gBAAI,aAAc,4BAC1BsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAO8R,iBACPkC,SA3D0B,SAAAhU,OAAK,OAAI4O,cAAe,CAAEkD,iBAAkB9R,SA4DtE+N,MAAO5C,gBAAI,aAAc,gCAI1B,oBAAC,kCAAD,CACA2I,MAAQ3I,gBAAI,8BAA+B,4BAC3CsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOgS,gCACPgC,SApEkC,SAAAhU,OAAK,OAAI4O,cAAe,CAAEoD,gCAAiChS,SAqE7F+N,MAAO5C,gBAAI,wBAAyB,gCAIrC,oBAAC,kCAAD,CACA2I,MAAQ3I,gBAAI,wBAAyB,4BACrCsJ,aAAc,EACdC,cAAgB,CAAE,CACjB1U,MAAOiS,0BACP+B,SA7E4B,SAAAhU,OAAK,OAAI4O,cAAe,CAAEqD,0BAA2BjS,SA8EjF+N,MAAO5C,gBAAI,wBAAyB,gCAKrC,oBAAC,6BAAD,CACC4C,MAAQ5C,gBAAI,YAAa,4BACzBmJ,QAAUnC,SACV6B,SAAW,kBAAM,OAAKhG,MAAMY,cAAe,CAAEuD,UAAYA,cAE1D,oBAAC,6BAAD,CACCpE,MAAQ5C,gBAAI,aAAc,4BAC1BmJ,QAAUpC,UACV8B,SAAW,kBAAM,OAAKhG,MAAMY,cAAe,CAAEsD,WAAaA,eAE3D,oBAAC,6BAAD,CACCnE,MAAQ5C,gBAAI,mBAAoB,4BAChCmJ,QAAUlC,gBACV4B,SAAW,kBAAM,OAAKhG,MAAMY,cAAe,CAAEwD,iBAAmBA,qBAEjE,oBAAC,6BAAD,CACCrE,MAAQ5C,gBAAI,kBAAmB,4BAC/BmJ,QAAUjC,cACV2B,SAAW,kBAAM,OAAKhG,MAAMY,cAAe,CAAEyD,eAAiBA,qBAIjE,oBAAC,6BAAD,CAAepV,IAAI,YAClB,oBAAC,gCAAD,CACC+C,MAAQyR,iBACRuC,SAAW,SAAEhU,OAAF,OAAa4O,cAAe,CAAE6C,iBAAkBzR,YAG7D,2BACCgT,UACC+C,qBACC,mBACA,SACAtE,iBACAM,mBACA,gBAAkBL,gBAClB,qBAGFsE,MAAQ,CACPE,gBAAiBrE,uBACjBsE,MAAOrE,mBAGR,2BAAKkB,UACJ+C,qBACC,+BAID,2BAAK/C,UAAU,6BACd,2BAAKA,UAAU,4BACd,oBAAC,2BAAD,CACCsD,YAAc,CACbtD,UAAW,gBAEZuD,SAAW,SAAEC,KAAW,OAAKC,kBAAmBD,IAAIrV,GAAIqV,IAAIvb,KAAO2T,cAAe,CAAEP,aAAcmI,IAAIrV,GAAIiN,cAAeoI,IAAIvb,OAC7Hyb,KAAK,QACL1W,MAAQqO,aACRsI,OAAS,mBAAIzb,KAAJ,KAAIA,KAAJ,OACR,oBAAC,sBAAD,CAAQ0b,QAAU1b,MACbmT,aAA+D,2BAClEyJ,MAAM,iBACNjB,IAAMzI,cACN0I,IAAI,WAHc,2BAAKD,IAAKzI,cAAe0I,IAAI,sBAWrD,2BAAK9D,UAAU,oBACbb,UACD,oBAAC,wBAAD,CACC4E,QAAQ,KACRC,YAAc7L,gBAAI,WAAY,4BAC9BnL,MAAQsO,YACR0E,UAAU,mBACVgD,MAAQ,CACPG,MAAOrE,kBAERkC,SAAW,SAAEhU,OAAa,OAAKkX,aAAalX,OAAQ4O,cAAe,CAAEN,YAAatO,WAGlFkS,WACD,oBAAC,wBAAD,CACC6E,QAAQ,IACRC,YAAc7L,gBAAI,YAAa,iBAC/BnL,MAAQuO,aACRyE,UAAU,oBACVgD,MAAQ,CACPG,MAAOrE,kBAERkC,SAAW,SAAEhU,OAAY,OAAKmX,cAAcnX,OAAQ4O,cAAe,CAAEL,aAAcvO,WAGnFoS,iBACD,oBAAC,wBAAD,CACC2E,QAAQ,MACR/D,UAAU,mBACVgE,YAAc7L,gBAAI,sBAAuB,4BACzCnL,MAAQwO,eACR4I,mBAAqB,CAAE,OAAQ,SAAU,gBAAiB,QAC1DpD,SAAW,SAAEhU,OAAY,OAAKqX,oBAAoBrX,OAAQ4O,cAAe,CAAEJ,eAAgBxO,aAK7F6O,cAAiBA,WAAWlO,QAC7B,2BAAKqS,UAAU,4BACdX,eACA,2BACCW,UAAU,yBACVgD,MAAQ,CACPE,gBAAiBlE,gCACjBmE,MAAOlE,4BAGR,yBACC3K,KAAMuH,WACNmH,MAAQ,CACPE,gBAAiBlE,gCACjBmE,MAAOlE,4BAEP9G,gBAAG,aAAc,uC,uJAjZAF,0B,IChBpBE,OAAOJ,GAAGK,KAAVD,GACA4M,kBAAsBhN,GAAGiN,OAAzBD,kB,iBAOwBhN,GAAGC,QAAhBE,gB,iBAAXD,U,iBAAWC,UAUlBoB,eACGvB,GAAGmB,OADNI,SAoFDyL,kBAAmB,mBAAoB,CACtCjE,MAAO3I,OAAI,sBAAuB,4BAClC8M,KAAM,2BAAK9W,GAAG,UAAUoS,YAAU,UAAUC,MAAM,6BAA6BG,QAAQ,qBAAoB,wCAAkB,4BAAMlJ,EAAE,wHAAwHmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,0HAA0HmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,8JAA8JmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,iIAAiImJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,sGAAsGmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,uKAAuKmJ,UAAU,yBAAyBC,KAAK,aAClvCqE,SAAU,MAEVjK,WAtFuB,CACvBK,YAAa,CACZoI,KAAM,SACNvX,QAAS,IAEVoP,aAAc,CACbmI,KAAM,SACNvX,QAAS,IAEVqP,eAAgB,CACfkI,KAAM,SACNvX,QAAS,IAEVsS,iBAAkB,CACjBiF,KAAM,UAEPtI,cAAe,CACdsI,KAAM,SACN5W,OAAQ,YACRqY,UAAW,MACXC,SAAU,MACVjZ,QAAS,IAEVkP,aAAc,CACbqI,KAAM,SACNvX,QAAS,IAEV0P,WAAY,CACX6H,KAAM,SACNvX,QAAS,IAEV0S,uBAAwB,CACvB6E,KAAM,SACNvX,QAAS,WAEV2S,iBAAkB,CACjB4E,KAAM,SACNvX,QAAS,WAEV6S,gCAAiC,CAChC0E,KAAM,SACNvX,QAAS,WAEV8S,0BAA2B,CAC1ByE,KAAM,SACNvX,QAAS,WAEVkZ,sBAAuB,CACtB3B,KAAM,SACNvX,QAAS,KAEVuS,gBAAiB,CAChBgF,KAAM,SACNvX,QAAS,IAEV4S,mBAAoB,CACnB2E,KAAM,SACNvX,QAAS,UAEVgT,SAAU,CACTuE,KAAM,UACNvX,SAAS,GAEV+S,UAAW,CACVwE,KAAM,UACNvX,SAAS,GAEViT,gBAAiB,CAChBsE,KAAM,UACNvX,SAAS,GAEVkT,cAAe,CACdqE,KAAM,UACNvX,SAAS,GAEV+O,QAAS,CACRwI,KAAM,SACNvX,QAAS,IAWVmZ,KAAMC,eAENC,KATsC,SAShCxK,OAAQ,sBACsTA,MAAMC,WAAjUK,YADK,kBACLA,YAAaC,aADR,kBACQA,aAAcC,eADtB,kBACsBA,eAAgBiD,iBADtC,kBACsCA,iBAAkBrD,cADxD,kBACwDA,cAA6BsD,iBADrF,kBACuErD,aADvE,kBACqFqD,iBAAiBG,uBADtG,kBACsGA,uBAAwBC,iBAD9H,kBAC8HA,iBAAoCC,oBADlK,kBACgJc,iBADhJ,kBACkKd,oBAAoBC,gCADtL,kBACsLA,gCAAiCC,0BADvN,kBACuNA,0BAA2BpD,WADlP,kBACkPA,WAAYqD,UAD9P,kBAC8PA,UAAWC,SADzQ,kBACyQA,SAAUC,gBADnR,kBACmRA,gBAAiBC,cADpS,kBACoSA,cAEjT,OACC,oBAAC,eAAD,KACC,2BACCW,UACC+C,qBACC,mBACAtE,iBACAM,mBACA,gBAAkBL,gBAClB,qBAGFsE,MAAQ,CACPE,gBAAiBrE,uBACjBsE,MAAOrE,mBAGR,2BAAKkB,UACF+C,qBACC,6BACA,qBAGFC,MAAQ,CACPE,gBAAiBrE,uBACjBsE,MAAOrE,mBAGT,2BAAKkB,UAAU,6BACd,2BAAKA,UAAU,4BACd,2BACCA,UAAU,qBACV6D,IAAKzI,cACL0I,IAAI,aAIP,2BAAK9D,UAAU,oBACZ1E,eAAkBA,YAAY3N,QAAUwR,UACzC,oBAAC,eAASsG,QAAV,CACC1B,QAAQ,KACR/D,UAAU,mBACVgD,MAAQ,CACPG,MAAOrE,kBAER9R,MAAQsO,cAIRC,gBAAmBA,aAAa5N,QAAUuR,WAC3C,oBAAC,eAASuG,QAAV,CACC1B,QAAQ,IACR/D,UAAU,oBACVgD,MAAQ,CACPG,MAAOrE,kBAER9R,MAAQuO,eAIRC,kBAAqBA,eAAe7N,QAAUyR,iBAC/C,oBAAC,eAASqG,QAAV,CACC1B,QAAQ,MACR/D,UAAU,mBACVhT,MAAQwO,mBAKXK,cAAiBA,WAAWlO,QAC7B,2BAAKqS,UAAU,4BACdX,eACA,2BACCW,UAAU,yBACVgD,MAAQ,CACPE,gBAAiBlE,gCACjBmE,MAAOlE,4BAGR,yBACC3K,KAAMuH,WACNmH,MAAQ,CACPE,gBAAiBlE,gCACjBmE,MAAOlE,4BAEP9G,OAAG,aAAc,oCAuB1B4M,kBAAmB,4BAA6B,CAC/CjE,MAAO3I,OAAI,eAAgB,4BAC3B8M,KAAM,2BAAK9W,GAAG,UAAUoS,YAAU,UAAUC,MAAM,6BAA6BG,QAAQ,qBAAoB,wCAAkB,4BAAMlJ,EAAE,wHAAwHmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,0HAA0HmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,8JAA8JmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,iIAAiImJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,sGAAsGmJ,UAAU,yBAAyBC,KAAK,YAAW,4BAAMpJ,EAAE,uKAAuKmJ,UAAU,yBAAyBC,KAAK,aAClvCqE,SAAU,MAEVQ,oBAL+C,SAK1BzK,cAGrBqK,KAAMA,QAENE,KAV+C,WAUvC,OAAO,S", "file": "blocks.build.js", "sourcesContent": ["module.exports = require('./lib/axios');", "'use strict';\n\nvar utils = require('./../utils');\nvar settle = require('./../core/settle');\nvar buildURL = require('./../helpers/buildURL');\nvar buildFullPath = require('../core/buildFullPath');\nvar parseHeaders = require('./../helpers/parseHeaders');\nvar isURLSameOrigin = require('./../helpers/isURLSameOrigin');\nvar createError = require('../core/createError');\n\nmodule.exports = function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    var requestData = config.data;\n    var requestHeaders = config.headers;\n\n    if (utils.isFormData(requestData)) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n\n    var request = new XMLHttpRequest();\n\n    // HTTP basic authentication\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = config.auth.password || '';\n      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);\n    }\n\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);\n\n    // Set the request timeout in MS\n    request.timeout = config.timeout;\n\n    // Listen for ready state\n    request.onreadystatechange = function handleLoad() {\n      if (!request || request.readyState !== 4) {\n        return;\n      }\n\n      // The request errored out and we didn't get a response, this will be\n      // handled by onerror instead\n      // With one exception: request that using file: protocol, most browsers\n      // will return status as 0 even though it's a successful request\n      if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n        return;\n      }\n\n      // Prepare the response\n      var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;\n      var responseData = !config.responseType || config.responseType === 'text' ? request.responseText : request.response;\n      var response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config: config,\n        request: request\n      };\n\n      settle(resolve, reject, response);\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(createError('Request aborted', config, 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(createError('Network Error', config, null, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      var timeoutErrorMessage = 'timeout of ' + config.timeout + 'ms exceeded';\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n      reject(createError(timeoutErrorMessage, config, 'ECONNABORTED',\n        request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (utils.isStandardBrowserEnv()) {\n      var cookies = require('./../helpers/cookies');\n\n      // Add xsrf header\n      var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ?\n        cookies.read(config.xsrfCookieName) :\n        undefined;\n\n      if (xsrfValue) {\n        requestHeaders[config.xsrfHeaderName] = xsrfValue;\n      }\n    }\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders, function setRequestHeader(val, key) {\n        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {\n          // Remove Content-Type if data is undefined\n          delete requestHeaders[key];\n        } else {\n          // Otherwise add header to the request\n          request.setRequestHeader(key, val);\n        }\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (config.responseType) {\n      try {\n        request.responseType = config.responseType;\n      } catch (e) {\n        // Expected DOMException thrown by browsers not compatible XMLHttpRequest Level 2.\n        // But, this can be suppressed for 'json' type as it can be parsed by default 'transformResponse' function.\n        if (config.responseType !== 'json') {\n          throw e;\n        }\n      }\n    }\n\n    // Handle progress if needed\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', config.onDownloadProgress);\n    }\n\n    // Not all browsers support upload events\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', config.onUploadProgress);\n    }\n\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (!request) {\n          return;\n        }\n\n        request.abort();\n        reject(cancel);\n        // Clean up request\n        request = null;\n      });\n    }\n\n    if (requestData === undefined) {\n      requestData = null;\n    }\n\n    // Send the request\n    request.send(requestData);\n  });\n};\n", "'use strict';\n\nvar utils = require('./utils');\nvar bind = require('./helpers/bind');\nvar Axios = require('./core/Axios');\nvar mergeConfig = require('./core/mergeConfig');\nvar defaults = require('./defaults');\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n * @return {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  var context = new Axios(defaultConfig);\n  var instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context);\n\n  // Copy context to instance\n  utils.extend(instance, context);\n\n  return instance;\n}\n\n// Create the default instance to be exported\nvar axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Factory for creating new instances\naxios.create = function create(instanceConfig) {\n  return createInstance(mergeConfig(axios.defaults, instanceConfig));\n};\n\n// Expose Cancel & CancelToken\naxios.Cancel = require('./cancel/Cancel');\naxios.CancelToken = require('./cancel/CancelToken');\naxios.isCancel = require('./cancel/isCancel');\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\naxios.spread = require('./helpers/spread');\n\nmodule.exports = axios;\n\n// Allow use of default import syntax in TypeScript\nmodule.exports.default = axios;\n", "'use strict';\n\n/**\n * A `Cancel` is an object that is thrown when an operation is canceled.\n *\n * @class\n * @param {string=} message The message.\n */\nfunction Cancel(message) {\n  this.message = message;\n}\n\nCancel.prototype.toString = function toString() {\n  return 'Cancel' + (this.message ? ': ' + this.message : '');\n};\n\nCancel.prototype.__CANCEL__ = true;\n\nmodule.exports = Cancel;\n", "'use strict';\n\nvar Cancel = require('./Cancel');\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @class\n * @param {Function} executor The executor function.\n */\nfunction CancelToken(executor) {\n  if (typeof executor !== 'function') {\n    throw new TypeError('executor must be a function.');\n  }\n\n  var resolvePromise;\n  this.promise = new Promise(function promiseExecutor(resolve) {\n    resolvePromise = resolve;\n  });\n\n  var token = this;\n  executor(function cancel(message) {\n    if (token.reason) {\n      // Cancellation has already been requested\n      return;\n    }\n\n    token.reason = new Cancel(message);\n    resolvePromise(token.reason);\n  });\n}\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nCancelToken.prototype.throwIfRequested = function throwIfRequested() {\n  if (this.reason) {\n    throw this.reason;\n  }\n};\n\n/**\n * Returns an object that contains a new `CancelToken` and a function that, when called,\n * cancels the `CancelToken`.\n */\nCancelToken.source = function source() {\n  var cancel;\n  var token = new CancelToken(function executor(c) {\n    cancel = c;\n  });\n  return {\n    token: token,\n    cancel: cancel\n  };\n};\n\nmodule.exports = CancelToken;\n", "'use strict';\n\nmodule.exports = function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n};\n", "'use strict';\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\n\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof config === 'string') {\n    config = arguments[1] || {};\n    config.url = arguments[0];\n  } else {\n    config = config || {};\n  }\n\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n\n  // Hook up interceptors middleware\n  var chain = [dispatchRequest, undefined];\n  var promise = Promise.resolve(config);\n\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    chain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    chain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  while (chain.length) {\n    promise = promise.then(chain.shift(), chain.shift());\n  }\n\n  return promise;\n};\n\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\\?/, '');\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, config) {\n    return this.request(utils.merge(config || {}, {\n      method: method,\n      url: url\n    }));\n  };\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, data, config) {\n    return this.request(utils.merge(config || {}, {\n      method: method,\n      url: url,\n      data: data\n    }));\n  };\n});\n\nmodule.exports = Axios;\n", "'use strict';\n\nvar utils = require('./../utils');\n\nfunction InterceptorManager() {\n  this.handlers = [];\n}\n\n/**\n * Add a new interceptor to the stack\n *\n * @param {Function} fulfilled The function to handle `then` for a `Promise`\n * @param {Function} rejected The function to handle `reject` for a `Promise`\n *\n * @return {Number} An ID used to remove interceptor later\n */\nInterceptorManager.prototype.use = function use(fulfilled, rejected) {\n  this.handlers.push({\n    fulfilled: fulfilled,\n    rejected: rejected\n  });\n  return this.handlers.length - 1;\n};\n\n/**\n * Remove an interceptor from the stack\n *\n * @param {Number} id The ID that was returned by `use`\n */\nInterceptorManager.prototype.eject = function eject(id) {\n  if (this.handlers[id]) {\n    this.handlers[id] = null;\n  }\n};\n\n/**\n * Iterate over all the registered interceptors\n *\n * This method is particularly useful for skipping over any\n * interceptors that may have become `null` calling `eject`.\n *\n * @param {Function} fn The function to call for each interceptor\n */\nInterceptorManager.prototype.forEach = function forEach(fn) {\n  utils.forEach(this.handlers, function forEachHandler(h) {\n    if (h !== null) {\n      fn(h);\n    }\n  });\n};\n\nmodule.exports = InterceptorManager;\n", "'use strict';\n\nvar isAbsoluteURL = require('../helpers/isAbsoluteURL');\nvar combineURLs = require('../helpers/combineURLs');\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n * @returns {string} The combined full path\n */\nmodule.exports = function buildFullPath(baseURL, requestedURL) {\n  if (baseURL && !isAbsoluteURL(requestedURL)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n};\n", "'use strict';\n\nvar enhanceError = require('./enhanceError');\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The created error.\n */\nmodule.exports = function createError(message, config, code, request, response) {\n  var error = new Error(message);\n  return enhanceError(error, config, code, request, response);\n};\n", "'use strict';\n\nvar utils = require('./../utils');\nvar transformData = require('./transformData');\nvar isCancel = require('../cancel/isCancel');\nvar defaults = require('../defaults');\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n * @returns {Promise} The Promise to be fulfilled\n */\nmodule.exports = function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  // Ensure headers exist\n  config.headers = config.headers || {};\n\n  // Transform request data\n  config.data = transformData(\n    config.data,\n    config.headers,\n    config.transformRequest\n  );\n\n  // Flatten headers\n  config.headers = utils.merge(\n    config.headers.common || {},\n    config.headers[config.method] || {},\n    config.headers\n  );\n\n  utils.forEach(\n    ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],\n    function cleanHeaderConfig(method) {\n      delete config.headers[method];\n    }\n  );\n\n  var adapter = config.adapter || defaults.adapter;\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData(\n      response.data,\n      response.headers,\n      config.transformResponse\n    );\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData(\n          reason.response.data,\n          reason.response.headers,\n          config.transformResponse\n        );\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n};\n", "'use strict';\n\n/**\n * Update an Error with the specified config, error code, and response.\n *\n * @param {Error} error The error to update.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The error.\n */\nmodule.exports = function enhanceError(error, config, code, request, response) {\n  error.config = config;\n  if (code) {\n    error.code = code;\n  }\n\n  error.request = request;\n  error.response = response;\n  error.isAxiosError = true;\n\n  error.toJSON = function() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code\n    };\n  };\n  return error;\n};\n", "'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n * @returns {Object} New object resulting from merging config2 to config1\n */\nmodule.exports = function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  var config = {};\n\n  var valueFromConfig2Keys = ['url', 'method', 'params', 'data'];\n  var mergeDeepPropertiesKeys = ['headers', 'auth', 'proxy'];\n  var defaultToConfig2Keys = [\n    'baseURL', 'url', 'transformRequest', 'transformResponse', 'paramsSerializer',\n    'timeout', 'withCredentials', 'adapter', 'responseType', 'xsrfCookieName',\n    'xsrfHeaderName', 'onUploadProgress', 'onDownloadProgress',\n    'maxContentLength', 'validateStatus', 'maxRedirects', 'httpAgent',\n    'httpsAgent', 'cancelToken', 'socketPath'\n  ];\n\n  utils.forEach(valueFromConfig2Keys, function valueFromConfig2(prop) {\n    if (typeof config2[prop] !== 'undefined') {\n      config[prop] = config2[prop];\n    }\n  });\n\n  utils.forEach(mergeDeepPropertiesKeys, function mergeDeepProperties(prop) {\n    if (utils.isObject(config2[prop])) {\n      config[prop] = utils.deepMerge(config1[prop], config2[prop]);\n    } else if (typeof config2[prop] !== 'undefined') {\n      config[prop] = config2[prop];\n    } else if (utils.isObject(config1[prop])) {\n      config[prop] = utils.deepMerge(config1[prop]);\n    } else if (typeof config1[prop] !== 'undefined') {\n      config[prop] = config1[prop];\n    }\n  });\n\n  utils.forEach(defaultToConfig2Keys, function defaultToConfig2(prop) {\n    if (typeof config2[prop] !== 'undefined') {\n      config[prop] = config2[prop];\n    } else if (typeof config1[prop] !== 'undefined') {\n      config[prop] = config1[prop];\n    }\n  });\n\n  var axiosKeys = valueFromConfig2Keys\n    .concat(mergeDeepPropertiesKeys)\n    .concat(defaultToConfig2Keys);\n\n  var otherKeys = Object\n    .keys(config2)\n    .filter(function filterAxiosKeys(key) {\n      return axiosKeys.indexOf(key) === -1;\n    });\n\n  utils.forEach(otherKeys, function otherKeysDefaultToConfig2(prop) {\n    if (typeof config2[prop] !== 'undefined') {\n      config[prop] = config2[prop];\n    } else if (typeof config1[prop] !== 'undefined') {\n      config[prop] = config1[prop];\n    }\n  });\n\n  return config;\n};\n", "'use strict';\n\nvar createError = require('./createError');\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n */\nmodule.exports = function settle(resolve, reject, response) {\n  var validateStatus = response.config.validateStatus;\n  if (!validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(createError(\n      'Request failed with status code ' + response.status,\n      response.config,\n      null,\n      response.request,\n      response\n    ));\n  }\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Object|String} data The data to be transformed\n * @param {Array} headers The headers for the request or response\n * @param {Array|Function} fns A single function or Array of functions\n * @returns {*} The resulting transformed data\n */\nmodule.exports = function transformData(data, headers, fns) {\n  /*eslint no-param-reassign:0*/\n  utils.forEach(fns, function transform(fn) {\n    data = fn(data, headers);\n  });\n\n  return data;\n};\n", "'use strict';\n\nvar utils = require('./utils');\nvar normalizeHeaderName = require('./helpers/normalizeHeaderName');\n\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\n\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\n\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('./adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('./adapters/http');\n  }\n  return adapter;\n}\n\nvar defaults = {\n  adapter: getDefaultAdapter(),\n\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n    if (utils.isFormData(data) ||\n      utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n    if (utils.isObject(data)) {\n      setContentTypeIfUnset(headers, 'application/json;charset=utf-8');\n      return JSON.stringify(data);\n    }\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    /*eslint no-param-reassign:0*/\n    if (typeof data === 'string') {\n      try {\n        data = JSON.parse(data);\n      } catch (e) { /* Ignore */ }\n    }\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  }\n};\n\ndefaults.headers = {\n  common: {\n    'Accept': 'application/json, text/plain, */*'\n  }\n};\n\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\n\nmodule.exports = defaults;\n", "'use strict';\n\nmodule.exports = function bind(fn, thisArg) {\n  return function wrap() {\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n    return fn.apply(thisArg, args);\n  };\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\nfunction encode(val) {\n  return encodeURIComponent(val).\n    replace(/%40/gi, '@').\n    replace(/%3A/gi, ':').\n    replace(/%24/g, '$').\n    replace(/%2C/gi, ',').\n    replace(/%20/g, '+').\n    replace(/%5B/gi, '[').\n    replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @returns {string} The formatted url\n */\nmodule.exports = function buildURL(url, params, paramsSerializer) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n\n  var serializedParams;\n  if (paramsSerializer) {\n    serializedParams = paramsSerializer(params);\n  } else if (utils.isURLSearchParams(params)) {\n    serializedParams = params.toString();\n  } else {\n    var parts = [];\n\n    utils.forEach(params, function serialize(val, key) {\n      if (val === null || typeof val === 'undefined') {\n        return;\n      }\n\n      if (utils.isArray(val)) {\n        key = key + '[]';\n      } else {\n        val = [val];\n      }\n\n      utils.forEach(val, function parseValue(v) {\n        if (utils.isDate(v)) {\n          v = v.toISOString();\n        } else if (utils.isObject(v)) {\n          v = JSON.stringify(v);\n        }\n        parts.push(encode(key) + '=' + encode(v));\n      });\n    });\n\n    serializedParams = parts.join('&');\n  }\n\n  if (serializedParams) {\n    var hashmarkIndex = url.indexOf('#');\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n};\n", "'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n * @returns {string} The combined URL\n */\nmodule.exports = function combineURLs(baseURL, relativeURL) {\n  return relativeURL\n    ? baseURL.replace(/\\/+$/, '') + '/' + relativeURL.replace(/^\\/+/, '')\n    : baseURL;\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\nmodule.exports = (\n  utils.isStandardBrowserEnv() ?\n\n  // Standard browser envs support document.cookie\n    (function standardBrowserEnv() {\n      return {\n        write: function write(name, value, expires, path, domain, secure) {\n          var cookie = [];\n          cookie.push(name + '=' + encodeURIComponent(value));\n\n          if (utils.isNumber(expires)) {\n            cookie.push('expires=' + new Date(expires).toGMTString());\n          }\n\n          if (utils.isString(path)) {\n            cookie.push('path=' + path);\n          }\n\n          if (utils.isString(domain)) {\n            cookie.push('domain=' + domain);\n          }\n\n          if (secure === true) {\n            cookie.push('secure');\n          }\n\n          document.cookie = cookie.join('; ');\n        },\n\n        read: function read(name) {\n          var match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n          return (match ? decodeURIComponent(match[3]) : null);\n        },\n\n        remove: function remove(name) {\n          this.write(name, '', Date.now() - 86400000);\n        }\n      };\n    })() :\n\n  // Non standard browser env (web workers, react-native) lack needed support.\n    (function nonStandardBrowserEnv() {\n      return {\n        write: function write() {},\n        read: function read() { return null; },\n        remove: function remove() {}\n      };\n    })()\n);\n", "'use strict';\n\n/**\n * Determines whether the specified URL is absolute\n *\n * @param {string} url The URL to test\n * @returns {boolean} True if the specified URL is absolute, otherwise false\n */\nmodule.exports = function isAbsoluteURL(url) {\n  // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\n  // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\n  // by any combination of letters, digits, plus, period, or hyphen.\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url);\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\nmodule.exports = (\n  utils.isStandardBrowserEnv() ?\n\n  // Standard browser envs have full support of the APIs needed to test\n  // whether the request URL is of the same origin as current location.\n    (function standardBrowserEnv() {\n      var msie = /(msie|trident)/i.test(navigator.userAgent);\n      var urlParsingNode = document.createElement('a');\n      var originURL;\n\n      /**\n    * Parse a URL to discover it's components\n    *\n    * @param {String} url The URL to be parsed\n    * @returns {Object}\n    */\n      function resolveURL(url) {\n        var href = url;\n\n        if (msie) {\n        // IE needs attribute set twice to normalize properties\n          urlParsingNode.setAttribute('href', href);\n          href = urlParsingNode.href;\n        }\n\n        urlParsingNode.setAttribute('href', href);\n\n        // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils\n        return {\n          href: urlParsingNode.href,\n          protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',\n          host: urlParsingNode.host,\n          search: urlParsingNode.search ? urlParsingNode.search.replace(/^\\?/, '') : '',\n          hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',\n          hostname: urlParsingNode.hostname,\n          port: urlParsingNode.port,\n          pathname: (urlParsingNode.pathname.charAt(0) === '/') ?\n            urlParsingNode.pathname :\n            '/' + urlParsingNode.pathname\n        };\n      }\n\n      originURL = resolveURL(window.location.href);\n\n      /**\n    * Determine if a URL shares the same origin as the current location\n    *\n    * @param {String} requestURL The URL to test\n    * @returns {boolean} True if URL shares the same origin, otherwise false\n    */\n      return function isURLSameOrigin(requestURL) {\n        var parsed = (utils.isString(requestURL)) ? resolveURL(requestURL) : requestURL;\n        return (parsed.protocol === originURL.protocol &&\n            parsed.host === originURL.host);\n      };\n    })() :\n\n  // Non standard browser envs (web workers, react-native) lack needed support.\n    (function nonStandardBrowserEnv() {\n      return function isURLSameOrigin() {\n        return true;\n      };\n    })()\n);\n", "'use strict';\n\nvar utils = require('../utils');\n\nmodule.exports = function normalizeHeaderName(headers, normalizedName) {\n  utils.forEach(headers, function processHeader(value, name) {\n    if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {\n      headers[normalizedName] = value;\n      delete headers[name];\n    }\n  });\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\n// Headers whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nvar ignoreDuplicateOf = [\n  'age', 'authorization', 'content-length', 'content-type', 'etag',\n  'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',\n  'last-modified', 'location', 'max-forwards', 'proxy-authorization',\n  'referer', 'retry-after', 'user-agent'\n];\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} headers Headers needing to be parsed\n * @returns {Object} Headers parsed into an object\n */\nmodule.exports = function parseHeaders(headers) {\n  var parsed = {};\n  var key;\n  var val;\n  var i;\n\n  if (!headers) { return parsed; }\n\n  utils.forEach(headers.split('\\n'), function parser(line) {\n    i = line.indexOf(':');\n    key = utils.trim(line.substr(0, i)).toLowerCase();\n    val = utils.trim(line.substr(i + 1));\n\n    if (key) {\n      if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {\n        return;\n      }\n      if (key === 'set-cookie') {\n        parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);\n      } else {\n        parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n      }\n    }\n  });\n\n  return parsed;\n};\n", "'use strict';\n\n/**\n * Syntactic sugar for invoking a function and expanding an array for arguments.\n *\n * Common use case would be to use `Function.prototype.apply`.\n *\n *  ```js\n *  function f(x, y, z) {}\n *  var args = [1, 2, 3];\n *  f.apply(null, args);\n *  ```\n *\n * With `spread` this example can be re-written.\n *\n *  ```js\n *  spread(function(x, y, z) {})([1, 2, 3]);\n *  ```\n *\n * @param {Function} callback\n * @returns {Function}\n */\nmodule.exports = function spread(callback) {\n  return function wrap(arr) {\n    return callback.apply(null, arr);\n  };\n};\n", "'use strict';\n\nvar bind = require('./helpers/bind');\n\n/*global toString:true*/\n\n// utils is a library of generic helper functions non-specific to axios\n\nvar toString = Object.prototype.toString;\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Array, otherwise false\n */\nfunction isArray(val) {\n  return toString.call(val) === '[object Array]';\n}\n\n/**\n * Determine if a value is undefined\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nfunction isUndefined(val) {\n  return typeof val === 'undefined';\n}\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor)\n    && typeof val.constructor.isBuffer === 'function' && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nfunction isArrayBuffer(val) {\n  return toString.call(val) === '[object ArrayBuffer]';\n}\n\n/**\n * Determine if a value is a FormData\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nfunction isFormData(val) {\n  return (typeof FormData !== 'undefined') && (val instanceof FormData);\n}\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  var result;\n  if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = (val) && (val.buffer) && (val.buffer instanceof ArrayBuffer);\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a String, otherwise false\n */\nfunction isString(val) {\n  return typeof val === 'string';\n}\n\n/**\n * Determine if a value is a Number\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Number, otherwise false\n */\nfunction isNumber(val) {\n  return typeof val === 'number';\n}\n\n/**\n * Determine if a value is an Object\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Object, otherwise false\n */\nfunction isObject(val) {\n  return val !== null && typeof val === 'object';\n}\n\n/**\n * Determine if a value is a Date\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Date, otherwise false\n */\nfunction isDate(val) {\n  return toString.call(val) === '[object Date]';\n}\n\n/**\n * Determine if a value is a File\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a File, otherwise false\n */\nfunction isFile(val) {\n  return toString.call(val) === '[object File]';\n}\n\n/**\n * Determine if a value is a Blob\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nfunction isBlob(val) {\n  return toString.call(val) === '[object Blob]';\n}\n\n/**\n * Determine if a value is a Function\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nfunction isFunction(val) {\n  return toString.call(val) === '[object Function]';\n}\n\n/**\n * Determine if a value is a Stream\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nfunction isStream(val) {\n  return isObject(val) && isFunction(val.pipe);\n}\n\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nfunction isURLSearchParams(val) {\n  return typeof URLSearchParams !== 'undefined' && val instanceof URLSearchParams;\n}\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n * @returns {String} The String freed of excess whitespace\n */\nfunction trim(str) {\n  return str.replace(/^\\s*/, '').replace(/\\s*$/, '');\n}\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n */\nfunction isStandardBrowserEnv() {\n  if (typeof navigator !== 'undefined' && (navigator.product === 'ReactNative' ||\n                                           navigator.product === 'NativeScript' ||\n                                           navigator.product === 'NS')) {\n    return false;\n  }\n  return (\n    typeof window !== 'undefined' &&\n    typeof document !== 'undefined'\n  );\n}\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n */\nfunction forEach(obj, fn) {\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  // Force an array if not already something iterable\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (var i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    for (var key in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, key)) {\n        fn.call(null, obj[key], key, obj);\n      }\n    }\n  }\n}\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n * @returns {Object} Result of all merge properties\n */\nfunction merge(/* obj1, obj2, obj3, ... */) {\n  var result = {};\n  function assignValue(val, key) {\n    if (typeof result[key] === 'object' && typeof val === 'object') {\n      result[key] = merge(result[key], val);\n    } else {\n      result[key] = val;\n    }\n  }\n\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Function equal to merge with the difference being that no reference\n * to original objects is kept.\n *\n * @see merge\n * @param {Object} obj1 Object to merge\n * @returns {Object} Result of all merge properties\n */\nfunction deepMerge(/* obj1, obj2, obj3, ... */) {\n  var result = {};\n  function assignValue(val, key) {\n    if (typeof result[key] === 'object' && typeof val === 'object') {\n      result[key] = deepMerge(result[key], val);\n    } else if (typeof val === 'object') {\n      result[key] = deepMerge({}, val);\n    } else {\n      result[key] = val;\n    }\n  }\n\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n * @return {Object} The resulting value of object a\n */\nfunction extend(a, b, thisArg) {\n  forEach(b, function assignValue(val, key) {\n    if (thisArg && typeof val === 'function') {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  });\n  return a;\n}\n\nmodule.exports = {\n  isArray: isArray,\n  isArrayBuffer: isArrayBuffer,\n  isBuffer: isBuffer,\n  isFormData: isFormData,\n  isArrayBufferView: isArrayBufferView,\n  isString: isString,\n  isNumber: isNumber,\n  isObject: isObject,\n  isUndefined: isUndefined,\n  isDate: isDate,\n  isFile: isFile,\n  isBlob: isBlob,\n  isFunction: isFunction,\n  isStream: isStream,\n  isURLSearchParams: isURLSearchParams,\n  isStandardBrowserEnv: isStandardBrowserEnv,\n  forEach: forEach,\n  merge: merge,\n  deepMerge: deepMerge,\n  extend: extend,\n  trim: trim\n};\n", "/*!\n  Copyright (c) 2017 <PERSON>.\n  Licensed under the MIT License (MIT), see\n  http://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = [];\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (!arg) continue;\n\n\t\t\tvar argType = typeof arg;\n\n\t\t\tif (argType === 'string' || argType === 'number') {\n\t\t\t\tclasses.push(arg);\n\t\t\t} else if (Array.isArray(arg) && arg.length) {\n\t\t\t\tvar inner = classNames.apply(null, arg);\n\t\t\t\tif (inner) {\n\t\t\t\t\tclasses.push(inner);\n\t\t\t\t}\n\t\t\t} else if (argType === 'object') {\n\t\t\t\tfor (var key in arg) {\n\t\t\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\t\t\tclasses.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn classes.join(' ');\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tif(__webpack_module_cache__[moduleId]) {\n\t\treturn __webpack_module_cache__[moduleId].exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => module['default'] :\n\t\t() => module;\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop)", "/**\n * External dependencies\n */\nimport axios from 'axios';\nconst { Component, Fragment } = wp.element;\n\nconst { __ } = wp.i18n;\n\nconst {\n\tPanelBody,\n\tPlaceholder,\n\tQueryControls,\n\tRangeControl,\n\tSelectControl,\n\tSpinner,\n\tTextControl,\n\tToggleControl,\n\tToolbar,\n\twithAPIData,\n\tColorPalette,\n\tButton,\n} = wp.components;\n\nconst {\n\tInspectorControls,\n\tBlockControls,\n\tMediaUpload,\n\tRichText,\n\tAlignmentToolbar,\n\tPanelColorSettings,\n} = wp.editor;\n\n// Import block dependencies and components\nimport classnames from 'classnames';\n\n\nclass MPP_Gutenberg_Enhanced extends Component {\n\tconstructor() {\n\t\tsuper( ...arguments );\n\n\t\tlet theme_list = Array();\n\t\ttheme_list.push( { value: 'regular', label: __( 'Regular', 'metronet-profile-picture' )});\n\t\ttheme_list.push( { value: 'profile', label: __( 'Profile', 'metronet-profile-picture' )});\n\t\ttheme_list.push( { value: 'tabbed', label: __( 'Tabbed', 'metronet-profile-picture' )});\n\t\ttheme_list.push( { value: 'compact', label: __( 'Compact', 'metronet-profile-picture' )});\n\t\tthis.state = {\n\t\t\tloading: true,\n\t\t\tusers: false,\n\t\t\tuser_list: false,\n\t\t\tprofile_picture: this.props.attributes.profileImgURL,\n\t\t\tprofile_picture_id: this.props.attributes.profileImgID,\n\t\t\tactive_user: false,\n\t\t\tprofile_description: '',\n\t\t\tprofile_name: '',\n\t\t\tprofile_name_unfiltered: '',\n\t\t\tprofile_title: '',\n\t\t\tshow_website: this.props.attributes.showWebsite,\n\t\t\tprofileViewPosts: this.props.attributes.profileViewPosts,\n\t\t\tprofileViewWebsite: this.props.attributes.profileViewWebsite,\n\t\t\ttheme: this.props.attributes.theme,\n\t\t\tthemes: theme_list,\n\t\t\tsocialFacebook: this.props.attributes.socialFacebook,\n\t\t\tsocialGitHub: this.props.attributes.socialGitHub,\n\t\t\tsocialLinkedIn: this.props.attributes.socialLinkedIn,\n\t\t\tsocialPinterest: this.props.attributes.socialPinterest,\n\t\t\tsocialTwitter: this.props.attributes.socialTwitter,\n\t\t\tsocialWordPress: this.props.attributes.socialWordPress,\n\t\t\tsocialYouTube: this.props.attributes.socialYouTube,\n\t\t\tsocialInstagram: this.props.attributes.socialInstagram,\n\t\t\twebsite: this.props.attributes.website,\n\t\t\tshowSocialMedia: true,\n\t\t\tsocialMediaOptions: this.props.attributes.socialMediaOptions,\n\t\t\tsocialMediaColors: this.props.attributes.socialMediaColors,\n\t\t\ttabbedAuthorProfile: this.props.attributes.tabbedAuthorProfile,\n\t\t\ttabbedAuthorLatestPosts: this.props.attributes.tabbedAuthorLatestPosts,\n\t\t\ttabbedAuthorSubHeading: this.props.attributes.tabbedAuthorSubHeading,\n\t\t\ttabbedAuthorProfileHeading: this.props.attributes.tabbedAuthorProfileHeading,\n\t\t\tactiveTab: 'profile',\n\t\t\tloadingLatestPosts: true,\n\t\t\tlatestPosts: {},\n\t\t\tprofileTabColor: this.props.attributes.profileTabColor,\n\t\t\tprofileTabHeadlineColor: this.props.attributes.profileTabHeadlineColor,\n\t\t\tprofileTabPostsColor: this.props.attributes.profileTabPostsColor,\n\t\t\tprofileTabHeadlineTextColor: this.props.attributes.profileTabHeadlineTextColor,\n\t\t\tprofileTabTextColor: this.props.attributes.profileTabTextColor,\n\t\t\tprofileTabPostsTextColor: this.props.attributes.profileTabPostsTextColor,\n\t\t\tprofileLatestPostsOptionsValue: this.props.attributes.profileLatestPostsOptionsValue,\n\t\t\tprofileCompactAlignment: this.props.attributes.profileCompactAlignment,\n\t\t};\n\t}\n\tget_users = () => {\n\t\taxios.post(mpp_gutenberg.rest_url + `/get_users`, {}, { 'headers': { 'X-WP-Nonce': mpp_gutenberg.nonce } } ).then( (response) => {\n\t\t\tlet users = Array();\n\t\t\tlet user_list = Array();\n\t\t\tlet active_user = 0;\n\t\t\tlet profile_picture = '';\n\t\t\tlet profile_picture_id = 0;\n\t\t\tlet profile_name = '';\n\t\t\tlet profile_description = '';\n\t\t\tlet profile_title = '';\n\t\t\tlet profile_url = '';\n\t\t\tlet show_website = '';\n\t\t\tjQuery.each( response.data, function( key, value ) {\n\t\t\t\tusers[value.ID] = {\n\t\t\t\t\tprofile_pictures: value.profile_pictures,\n\t\t\t\t\thas_profile_picture: value.has_profile_picture,\n\t\t\t\t\tdisplay_name: value.display_name,\n\t\t\t\t\tdescription: value.description,\n\t\t\t\t\tis_user_logged_in: value.is_user_logged_in,\n\t\t\t\t\tprofile_picture_id: value.profile_picture_id,\n\t\t\t\t\tdefault_image: value.default_image,\n\t\t\t\t\tpermalink: value.permalink,\n\t\t\t\t};\n\t\t\t\tif ( value.is_user_logged_in ) {\n\t\t\t\t\tactive_user = value.ID;\n\t\t\t\t}\n\t\t\t\tuser_list.push( { value: value.ID, label: value.display_name });\n\t\t\t} );\n\t\t\tif( this.props.attributes.user_id !== 0 ) {\n\t\t\t\tactive_user = this.props.attributes.user_id;\n\t\t\t}\n\t\t\tlet active_user_profile = users[active_user];\n\t\t\tif( active_user_profile.has_profile_picture ) {\n\t\t\t\tprofile_picture = this.props.attributes.profileImgURL.length > 0 ? this.props.attributes.profileImgURL : active_user_profile.profile_pictures['thumbnail'];\n\t\t\t\tprofile_picture_id = this.props.attributes.profileImgID.length > 0 ? this.props.attributes.profileImgID : active_user_profile.profile_picture_id;\n\t\t\t\tprofile_name = this.props.attributes.profileName.length > 0 ? this.props.attributes.profileName :  active_user_profile.display_name;\n\t\t\t\tprofile_title = this.props.attributes.profileTitle.length > 0 ? this.props.attributes.profileTitle :  '';\n\t\t\t\tprofile_url = active_user_profile.permalink;\n\t\t\t\tprofile_description = this.props.attributes.profileContent.length > 0 ? this.props.attributes.profileContent : active_user_profile.description;\n\t\t\t\tshow_website = this.props.attributes.showWebsite;\n\t\t\t} else {\n\t\t\t\tprofile_name = this.props.attributes.profileName.length > 0 ? this.props.attributes.profileName :  active_user_profile.display_name;\n\t\t\t\tprofile_title = this.props.attributes.profileTitle.length > 0 ? this.props.attributes.profileTitle :  '';\n\t\t\t\tprofile_description = this.props.attributes.profileContent.length > 0 ? this.props.attributes.profileContent : active_user_profile.description;\n\t\t\t\tprofile_picture = this.props.attributes.profileImgURL.length > 0 ? this.props.attributes.profileImgURL : active_user_profile.default_image;\n\t\t\t\tprofile_picture_id = this.props.attributes.profileImgID.length > 0 ? this.props.attributes.profileImgID : 0;\n\t\t\t\tprofile_url = active_user_profile.permalink;\n\t\t\t\tshow_website = this.props.attributes.showWebsite;\n\t\t\t}\n\t\t\tif( undefined == profile_description ) {\n\t\t\t\tprofile_description = '';\n\t\t\t}\n\t\t\tthis.setState(\n\t\t\t\t{\n\t\t\t\t\tloading: false,\n\t\t\t\t\tusers: users,\n\t\t\t\t\tactive_user: active_user,\n\t\t\t\t\tuser_list: user_list,\n\t\t\t\t\tprofile_picture: profile_picture,\n\t\t\t\t\tprofile_picture_id: profile_picture_id,\n\t\t\t\t\tactive_user: active_user,\n\t\t\t\t\tprofile_name: profile_name,\n\t\t\t\t\tprofile_name_unfiltered: active_user_profile.display_name,\n\t\t\t\t\tprofile_title: profile_title,\n\t\t\t\t\tprofile_description: profile_description,\n\t\t\t\t\tprofile_url: profile_url,\n\t\t\t\t\tshow_website: show_website,\n\t\t\t\t}\n\t\t\t);\n\t\t\tthis.props.setAttributes( {\n\t\t\t\tprofileContent: profile_description,\n\t\t\t\tprofileName: profile_name,\n\t\t\t\tprofileTitle: profile_title,\n\t\t\t\tprofileURL: profile_url,\n\t\t\t\tprofileImgID: profile_picture_id,\n\t\t\t\tprofileImgURL: profile_picture,\n\t\t\t\tshowWebsite: show_website,\n\t\t\t\tshowSocialMedia: true,\n\t\t\t\tprofileName: active_user_profile.display_name\n\t\t\t});\n\t\t});\n\t}\n\ton_user_change = ( user_id ) => {\n\t\tlet user = this.state.users[user_id];\n\t\tlet profile_picture = '';\n\t\tlet profile_picture_id = 0;\n\t\tlet profile_name = '';\n\t\tif( !user.has_profile_picture ) {\n\t\t\tprofile_picture = mpp_gutenberg.mystery_man;\n\t\t\tprofile_picture_id = 0;\n\t\t} else {\n\t\t\tprofile_picture = this.state.users[user_id]['profile_pictures']['thumbnail']\n\t\t\tprofile_picture_id = this.state.users[user_id]['profile_picture_id'];\n\t\t}\n\t\tlet description = this.state.users[user_id].description;\n\t\tif( undefined === description ) {\n\t\t\tdescription = '';\n\t\t}\n\t\tprofile_name = this.state.users[user_id].display_name;\n\t\tthis.props.setAttributes( {\n\t\t\tprofileName: profile_name,\n\t\t\tprofileContent: description,\n\t\t\tprofileTitle: '',\n\t\t\tprofileURL: this.state.users[user_id].permalink,\n\t\t\tprofileImgURL: profile_picture,\n\t\t\ttabbedAuthorSubHeading: '',\n\t\t\ttabbedAuthorProfileTitle: '',\n\t\t\tsocialFacebook: '',\n\t\t\tsocialGitHub: '',\n\t\t\tsocialInstagram: '',\n\t\t\tsocialLinkedIn: '',\n\t\t\tsocialPinterest: '',\n\t\t\tsocialTwitter: '',\n\t\t\tsocialWordPress: '',\n\t\t\tsocialYouTube: '',\n\t\t\tprofileName: this.state.users[user_id].display_name\n\t\t} );\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofile_name_unfiltered: this.state.users[user_id].display_name,\n\t\t\t\tprofile_name: profile_name,\n\t\t\t\tprofile_description: description,\n\t\t\t\tprofile_title: '',\n\t\t\t\tprofile_picture: profile_picture,\n\t\t\t\tprofile_picture_id: profile_picture_id,\n\t\t\t\tactive_user: user_id,\n\t\t\t\tprofile_url: this.state.users[user_id].permalink,\n\t\t\t\tsocialFacebook: '',\n\t\t\t\tsocialGitHub: '',\n\t\t\t\tsocialInstagram: '',\n\t\t\t\tsocialLinkedIn: '',\n\t\t\t\tsocialPinterest: '',\n\t\t\t\tsocialTwitter: '',\n\t\t\t\tsocialWordPress: '',\n\t\t\t\tsocialYouTube: '',\n\t\t\t}\n\t\t);\n\t\tthis.getLatestPosts();\n\t}\n\tgetLatestPosts = () => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tloadingLatestPosts: true\n\t\t\t}\n\t\t);\n\t\tlet classRef = this;\n\t\taxios.post(mpp_gutenberg.rest_url + `/get_posts`, {user_id: this.state.active_user }, { 'headers': { 'X-WP-Nonce': mpp_gutenberg.nonce } } ).then( (response) => {\n\t\t\tconst latestPosts = response.data;\n\t\t\tlet postJSX = latestPosts.map( function(data) {\n\t\t\t\treturn (\n\t\t\t\t\t<li key={data.ID}><a href={data.permalink}>{data.post_title}</a></li>\n\t\t\t\t)\n\t\t\t});\n\n\t\t\tthis.setState( {\n\t\t\t\tloadingLatestPosts: false,\n\t\t\t\tlatestPosts: postJSX\n\t\t\t\t}\n\t\t\t)\n\t\t} );\n\t}\n\tcomponentDidMount = () => {\n\t\tthis.get_users();\n\t}\n\thandleImageChange = ( image_id, image_url ) => {\n\t\tthis.setState( {\n\t\t\tprofile_picture: image_url,\n\t\t\tprofile_picture_id: image_id,\n\t\t} );\n\t}\n\tonChangeName = (value) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofile_name: value\n\t\t\t}\n\t\t);\n\t}\n\tonChangeTitle = (value) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofile_title: value\n\t\t\t}\n\t\t);\n\t}\n\tonChangeProfileText = (value) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofile_description: value\n\t\t\t}\n\t\t);\n\t}\n\tonThemeChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\ttheme: value\n\t\t\t}\n\t\t);\n\t}\n\thandleFacebookChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialFacebook: value\n\t\t\t}\n\t\t);\n\t}\n\thandleYouTubeChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialYouTube: value\n\t\t\t}\n\t\t);\n\t}\n\thandleGitHubChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialGitHub: value\n\t\t\t}\n\t\t);\n\t}\n\thandleLinkedInChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialLinkedIn: value\n\t\t\t}\n\t\t);\n\t}\n\thandleTwitterChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialTwitter: value\n\t\t\t}\n\t\t);\n\t}\n\thandleWordPressChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialWordPress: value\n\t\t\t}\n\t\t);\n\t}\n\thandleWebsiteChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\twebsite: value\n\t\t\t}\n\t\t);\n\t\tif( '' !== value ) {\n\t\t\tthis.props.setAttributes( {\n\t\t\t\tshowWebsite: true\n\t\t\t});\n\t\t}\n\n\t}\n\thandleInstagramChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialInstagram: value\n\t\t\t}\n\t\t);\n\t}\n\thandlePinterestChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialPinterest: value\n\t\t\t}\n\t\t);\n\t}\n\thandleSocialMediaChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tshowSocialMedia: value\n\t\t\t}\n\t\t);\n\t\tthis.props.setAttributes( { showSocialMedia: value } );\n\t}\n\thandleSocialMediaOptionChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tsocialMediaOptions: value\n\t\t\t}\n\t\t);\n\t}\n\tonChangeTabbedProfileText = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\ttabbedAuthorProfile: value\n\t\t\t}\n\t\t);\n\t}\n\tonChangeTabbedSubHeading = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\ttabbedAuthorSubHeading: value\n\t\t\t}\n\t\t);\n\t}\n\tonChangeActiveProfileTab = () => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tactiveTab: 'profile'\n\t\t\t}\n\t\t);\n\t}\n\tonChangeActivePostTab = () => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tactiveTab: 'latest',\n\t\t\t\tloadingLatestPosts: true\n\t\t\t}\n\t\t);\n\t\tthis.getLatestPosts();\n\t}\n\tonChangetabbedAuthorProfile = ( value ) => {\n\t\tthis.setState( {\n\t\t\ttabbedAuthorProfile: value\n\t\t});\n\t}\n\tonChangetabbedAuthorProfileHeading = ( value ) => {\n\t\tthis.setState( {\n\t\t\ttabbedAuthorProfileHeading: value\n\t\t});\n\t}\n\tonChangetabbedAuthorLatestPosts = ( value ) => {\n\t\tthis.setState( {\n\t\t\ttabbedAuthorLatestPosts: value\n\t\t});\n\t}\n\tonChangeProfileTabColor = ( value ) => {\n\t\tthis.setState( {\n\t\t\tprofileTabColor: value\n\t\t});\n\t\tthis.props.setAttributes( { profileTabColor: value } );\n\t}\n\tonChangePostsTabColor = ( value ) => {\n\t\tthis.setState( {\n\t\t\tprofileTabPostsColor: value\n\t\t});\n\t\tthis.props.setAttributes( { profileTabPostsColor: value } );\n\n\t}\n\tonChangePostsTabHeadlineColor = ( value ) => {\n\t\tthis.setState( {\n\t\t\tprofileTabHeadlineColor: value\n\t\t});\n\t\tthis.props.setAttributes( { profileTabHeadlineColor: value } );\n\t}\n\tonChangeProfileTabPostColorText = ( value ) => {\n\t\tthis.setState( {\n\t\t\tprofileTabPostsTextColor: value\n\t\t});\n\t\tthis.props.setAttributes( { profileTabPostsTextColor: value } );\n\t}\n\tonChangeProfileTabHeadlineColorText = ( value ) => {\n\t\tthis.setState( {\n\t\t\tprofileTabHeadlineTextColor: value\n\t\t});\n\t\tthis.props.setAttributes( { profileTabHeadlineTextColor: value } );\n\t}\n\tonChangeProfileTabColorText = ( value ) => {\n\t\tthis.setState( {\n\t\t\tprofileTabTextColor: value\n\t\t});\n\t\tthis.props.setAttributes( { profileTabTextColor: value } );\n\t}\n\tonLatestPostsChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofileLatestPostsOptionsValue: value,\n\t\t\t}\n\t\t);\n\t}\n\tonCompactAlignmentChange = ( value ) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofileCompactAlignment: value,\n\t\t\t}\n\t\t);\n\t}\n\trender() {\n\t\t// Setup the attributes\n\t\tlet {\n\t\t\tattributes: {\n\t\t\t\tprofileName,\n\t\t\t\tprofileTitle,\n\t\t\t\tprofileContent,\n\t\t\t\tprofileAlignment,\n\t\t\t\tprofileImgURL,\n\t\t\t\tprofileImgID,\n\t\t\t\tprofileURL,\n\t\t\t\tprofileFontSize,\n\t\t\t\tbuttonFontSize,\n\t\t\t\theaderFontSize,\n\t\t\t\tprofileBackgroundColor,\n\t\t\t\tprofileTextColor,\n\t\t\t\tprofileAvatarShape,\n\t\t\t\tprofileViewPostsBackgroundColor,\n\t\t\t\tprofileViewPostsTextColor,\n\t\t\t\tprofileViewPosts,\n\t\t\t\tprofileViewWebsite,\n\t\t\t\tshowTitle,\n\t\t\t\tshowName,\n\t\t\t\tshowDescription,\n\t\t\t\tshowViewPosts,\n\t\t\t\tshowPostsWidth,\n\t\t\t\tshowSocialMedia,\n\t\t\t\tshowWebsite,\n\t\t\t\ttheme,\n\t\t\t\ttheme_list,\n\t\t\t\tsocialFacebook,\n\t\t\t\tsocialGitHub,\n\t\t\t\tsocialLinkedIn,\n\t\t\t\tsocialPinterest,\n\t\t\t\tsocialTwitter,\n\t\t\t\tsocialWordPress,\n\t\t\t\tsocialYouTube,\n\t\t\t\tsocialMediaColors,\n\t\t\t\tprofileWebsiteBackgroundColor,\n\t\t\t\tprofileWebsiteTextColor,\n\t\t\t\tpadding,\n\t\t\t\tborder,\n\t\t\t\tborderRounded,\n\t\t\t\tborderColor,\n\t\t\t\tprofileLinkColor,\n\t\t\t\ttabbedAuthorProfile,\n\t\t\t\ttabbedAuthorSubHeading,\n\t\t\t\ttabbedAuthorProfileTitle,\n\t\t\t\tprofileLatestPostsOptionsValue,\n\n\t\t\t},\n\t\t\tattributes,\n\t\t\tisSelected,\n\t\t\teditable,\n\t\t\tclassName,\n\t\t\tsetAttributes\n\t\t} = this.props;\n\t\tlet profile_pictures = this.state.profile_pictures;\n\t\tprofileImgID = this.state.profile_picture_id;\n\t\tprofileImgURL = this.state.profile_picture;\n\t\tprofileName = this.state.profile_name;\n\t\tprofileContent = this.state.profile_description;\n\t\tprofileTitle = this.state.profile_title;\n\t\tprofileURL = this.state.profile_url;\n\t\tshowPostsWidth = this.state.website === '' || !this.props.attributes.showWebsite ? '100%' : '';\n\t\tsetAttributes({showPostsWidth: showPostsWidth });\n\n\t\tconst onChangeBackgroundColor = value => setAttributes( { profileBackgroundColor: value } );\n\t\tconst onChangeProfileTextColor = value => setAttributes( { profileTextColor: value } );\n\t\tconst onChangeViewPostsBackgroundColor = value => setAttributes( { profileViewPostsBackgroundColor: value } );\n\t\tconst onChangeViewPostsTextColor = value => setAttributes( { profileViewPostsTextColor: value } );\n\t\tconst onChangeWebsitesBackgroundColor = value => setAttributes( { profileWebsiteBackgroundColor: value } );\n\t\tconst onChangeWebsiteTextColor = value => setAttributes( { profileWebsiteTextColor: value } );\n\t\tconst onChangeSocialMediaColor = value => setAttributes( { socialMediaColors: value } );\n\t\tconst onChangeBorderColor = value => setAttributes( { borderColor: value } );\n\t\tconst onChangeProfileLinkColor = value => setAttributes( { profileLinkColor: value } );\n\n\t\t// Avatar shape options\n\t\tconst profileAvatarShapeOptions = [\n\t\t\t{ value: 'square', label: __( 'Square', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'round', label: __( 'Round', 'metronet-profile-picture' ) },\n\t\t];\n\n\t\t// Social Media Options\n\t\tconst profileSocialMediaOptions = [\n\t\t\t{ value: 'colors', label: __( 'Brand Colors', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'custom', label: __( 'Custom', 'metronet-profile-picture' ) },\n\t\t];\n\n\t\t// Latest Posts Theme Options\n\t\tconst profileLatestPostsOptions = [\n\t\t\t{ value: 'none', label: __( 'None', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'white', label: __( 'White', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'light', label: __( 'Light', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'black', label: __( 'Black', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'magenta', label: __( 'Magenta', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'blue', label: __( 'Blue', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'green', label: __( 'Green', 'metronet-profile-picture' ) },\n\t\t];\n\n\t\t// Profile Comptact Alignment Options\n\t\tconst profileCompactOptions = [\n\t\t\t{ value: 'left', label: __( 'Left', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'center', label: __( 'Center', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'right', label: __( 'Right', 'metronet-profile-picture' ) },\n\t\t];\n\t\tlet profileFloat = 'none';\n\t\tlet profileMargin = '';\n\t\tif( this.state.profileCompactAlignment === 'center' ) {\n\t\t\tprofileFloat = 'none';\n\t\t\tprofileMargin = '0 auto';\n\t\t}\n\t\tif( this.state.profileCompactAlignment === 'left' ) {\n\t\t\tprofileFloat = 'left';\n\t\t\tprofileMargin = '0';\n\t\t}\n\t\tif( this.state.profileCompactAlignment === 'right' ) {\n\t\t\tprofileFloat = 'right';\n\t\t\tprofileMargin = '0';\n\t\t}\n\t\treturn(\n\t\t\t<Fragment>\n\t\t\t\t{this.state.loading &&\n\t\t\t\t<Fragment>\n\t\t\t\t\t<Placeholder>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" width=\"125px\" height=\"125px\" viewBox=\"0 0 753.53 979.74\"><title>upp</title><path d=\"M806.37,185.9c0,40.27-30.49,72.9-68.11,72.9s-68.17-32.63-68.17-72.9S700.62,113,738.26,113,806.37,145.64,806.37,185.9Z\" transform=\"translate(-123.47 -11)\" fill=\"#4063ad\"/><path d=\"M330.36,183.8c0,40.27-30.49,72.9-68.12,72.9s-68.17-32.63-68.17-72.9,30.52-72.87,68.17-72.87S330.36,143.56,330.36,183.8Z\" transform=\"translate(-123.47 -11)\" fill=\"#a34d9c\"/><path d=\"M331.3,888.13V698.21H329c-31.64,0-57.28-27.45-57.28-61.29V336.5a118.37,118.37,0,0,1,5.43-34.79H179.84c-31.94,0-56.37,31.57-56.37,56.34V601.46h48.32V888.13Z\" transform=\"translate(-123.47 -11)\" fill=\"#a34d9c\"/><path d=\"M388.59,636.92V990.74H611.88V636.92H671.5V336.5c0-30.63-27.64-69.57-69.6-69.57H398.56c-39.44,0-69.61,38.94-69.61,69.57V636.92Z\" transform=\"translate(-123.47 -11)\" fill=\"#f4831f\"/><path d=\"M584.3,101c0,49.69-37.63,90-84,90S416.12,150.67,416.12,101s37.66-90,84.14-90S584.3,51.27,584.3,101Z\" transform=\"translate(-123.47 -11)\" fill=\"#f4831f\"/><path d=\"M820.61,303.79H724.08a121.69,121.69,0,0,1,4.7,32.71V636.92c0,33.84-25.64,61.29-57.28,61.29h-2.33v192H828.7V603.54H877V360.16C877,335.36,854.62,303.79,820.61,303.79Z\" transform=\"translate(-123.47 -11)\" fill=\"#4063ad\"/></svg>\n\t\t\t\t\t\t\t<div className=\"mpp-spinner\"><Spinner /></div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</Placeholder>\n\t\t\t\t</Fragment>\n\t\t\t\t}\n\t\t\t\t{!this.state.loading &&\n\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t<InspectorControls>\n\t\t\t\t\t\t\t<PanelBody title={ __( 'User Profile Settings', 'metronet-profile-picture' ) }>\n\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Select a user', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={this.state.active_user}\n\t\t\t\t\t\t\t\t\t\toptions={ this.state.user_list }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.on_user_change(value); setAttributes({user_id: Number(value)}); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Select a theme', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={this.state.theme}\n\t\t\t\t\t\t\t\t\t\toptions={ this.state.themes }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.onThemeChange(value); setAttributes({theme: value}); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{ this.state.theme === 'compact' &&\n\n\t\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Select an alignment', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={this.state.profileCompactAlignment}\n\t\t\t\t\t\t\t\t\t\toptions={ profileCompactOptions }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.onCompactAlignmentChange(value); setAttributes({profileCompactAlignment: value}); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Avatar Shape', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tdescription={ __( 'Choose between a round or square avatar shape.', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\toptions={ profileAvatarShapeOptions }\n\t\t\t\t\t\t\t\t\tvalue={ profileAvatarShape }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { profileAvatarShape: value } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{ this.state.theme !== 'tabbed' &&\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('Website', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.website}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { website: value }); this.handleWebsiteChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Show Name', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tchecked={ showName }\n\t\t\t\t\t\t\t\t\tonChange={ () => this.props.setAttributes( { showName: ! showName } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Show Title', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tchecked={ showTitle }\n\t\t\t\t\t\t\t\t\tonChange={ () => this.props.setAttributes( { showTitle: ! showTitle } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Show Description', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tchecked={ showDescription }\n\t\t\t\t\t\t\t\t\tonChange={ () => this.props.setAttributes( { showDescription: ! showDescription } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{ this.state.theme !== 'tabbed' &&\n\t\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Show View Posts', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tchecked={ showViewPosts }\n\t\t\t\t\t\t\t\t\t\tonChange={ () => this.props.setAttributes( { showViewPosts: ! showViewPosts } ) }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{ showViewPosts &&\n\t\t\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\t\t\tlabel={__('View Posts Text', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\t\t\tvalue={profileViewPosts}\n\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { profileViewPosts: value }); } }\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Show Website', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tchecked={ this.state.show_website }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { showWebsite: value } ); this.setState({show_website: value}); } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{ this.state.show_website &&\n\t\t\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\t\t\tlabel={__('View Website Text', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\t\t\tvalue={profileViewWebsite}\n\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { profileViewWebsite: value }); } }\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Show Social Media', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tchecked={ this.state.showSocialMedia }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.props.setAttributes( { showSocialMedia: value } ); this.handleSocialMediaChange( value );  } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</PanelBody>\n\t\t\t\t\t\t\t{this.state.theme === 'tabbed' &&\n\t\t\t\t\t\t\t<PanelBody title={ __( 'User Profile Settings', 'metronet-profile-picture' ) }>\n\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Select a theme', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={this.state.latestPostsTheme}\n\t\t\t\t\t\t\t\t\t\toptions={ {\n\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.on_user_change(value); setAttributes({user_id: Number(value)}); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</PanelBody>\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t<PanelBody title={ __( 'Colors', 'metronet-profile-picture' ) } initialOpen={false}>\n\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\ttitle={ __( 'Background Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\tvalue: profileBackgroundColor,\n\t\t\t\t\t\t\t\t\tonChange: onChangeBackgroundColor,\n\t\t\t\t\t\t\t\t\tlabel: __( 'Background Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\ttitle={ __( 'Text Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\tvalue: profileTextColor,\n\t\t\t\t\t\t\t\t\tonChange: onChangeProfileTextColor,\n\t\t\t\t\t\t\t\t\tlabel: __( 'Text Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t{ this.state.theme === 'profile' &&\n\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\ttitle={ __( 'Link Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\tvalue: profileLinkColor,\n\t\t\t\t\t\t\t\t\t\tonChange: onChangeProfileLinkColor,\n\t\t\t\t\t\t\t\t\t\tlabel: __( 'Link Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{this.state.theme === 'tabbed' &&\n\t\t\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\t\ttitle={ __( 'Profile Tab Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\t\tvalue: this.state.profileTabColor,\n\t\t\t\t\t\t\t\t\t\t\tonChange: this.onChangeProfileTabColor,\n\t\t\t\t\t\t\t\t\t\t\tlabel: __( 'Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\t\ttitle={ __( 'Profile Tab Color Text', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\t\tvalue: this.state.profileTabTextColor,\n\t\t\t\t\t\t\t\t\t\t\tonChange: this.onChangeProfileTabColorText,\n\t\t\t\t\t\t\t\t\t\t\tlabel: __( 'Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\t\ttitle={ __( 'Profile Posts Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\t\tvalue: this.state.profileTabPostsColor,\n\t\t\t\t\t\t\t\t\t\t\tonChange: this.onChangePostsTabColor,\n\t\t\t\t\t\t\t\t\t\t\tlabel: __( 'Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\t\ttitle={ __( 'Profile Post Color Text', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\t\tvalue: this.state.profileTabPostsTextColor,\n\t\t\t\t\t\t\t\t\t\t\tonChange: this.onChangeProfileTabPostColorText,\n\t\t\t\t\t\t\t\t\t\t\tlabel: __( 'Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\t\ttitle={ __( 'Profile Headline Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\t\tvalue: this.state.profileTabHeadlineColor,\n\t\t\t\t\t\t\t\t\t\t\tonChange: this.onChangePostsTabHeadlineColor,\n\t\t\t\t\t\t\t\t\t\t\tlabel: __( 'Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t\t></PanelColorSettings>\n\t\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\t\ttitle={ __( 'Profile Headline Color Text', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\t\tvalue: this.state.profileTabHeadlineColorText,\n\t\t\t\t\t\t\t\t\t\t\tonChange: this.onChangeProfileTabHeadlineColorText,\n\t\t\t\t\t\t\t\t\t\t\tlabel: __( 'Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Select a Post Theme', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={this.state.profileLatestPostsOptionsValue}\n\t\t\t\t\t\t\t\t\t\t\t\toptions={profileLatestPostsOptions}\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.onLatestPostsChange(value); setAttributes({profileLatestPostsOptionsValue: value}); } }\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{this.state.theme !== 'tabbed' && this.state.theme !== 'profile' &&\n\t\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\ttitle={ __( 'View Posts Background Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\tvalue: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tonChange: onChangeViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tlabel: __( 'View Posts Background', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\ttitle={ __( 'View Posts Text Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\tvalue: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t\t\tonChange: onChangeViewPostsTextColor,\n\t\t\t\t\t\t\t\t\t\tlabel: __( 'View Posts Text Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\ttitle={ __( 'Website Background Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\tvalue: profileWebsiteBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tonChange: onChangeWebsitesBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tlabel: __( 'View Website Background', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\ttitle={ __( 'View Website Text Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\tvalue: profileWebsiteTextColor,\n\t\t\t\t\t\t\t\t\t\tonChange: onChangeWebsiteTextColor,\n\t\t\t\t\t\t\t\t\t\tlabel: __( 'View Website Text Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t</PanelBody>\n\t\t\t\t\t\t\t<PanelBody title={ __( 'Spacing and Font Settings', 'metronet-profile-picture' ) } initialOpen={false}>\n\t\t\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Header Font Size', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tvalue={ headerFontSize }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { headerFontSize: value } ) }\n\t\t\t\t\t\t\t\t\tmin={ 14 }\n\t\t\t\t\t\t\t\t\tmax={ 32 }\n\t\t\t\t\t\t\t\t\tstep={ 1 }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Font Size', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tvalue={ profileFontSize }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { profileFontSize: value } ) }\n\t\t\t\t\t\t\t\t\tmin={ 14 }\n\t\t\t\t\t\t\t\t\tmax={ 24 }\n\t\t\t\t\t\t\t\t\tstep={ 1 }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{this.state.theme !== 'tabbed' &&\n\t\t\t\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Button Size', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tvalue={ buttonFontSize }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { buttonFontSize: value } ) }\n\t\t\t\t\t\t\t\t\tmin={ 10 }\n\t\t\t\t\t\t\t\t\tmax={ 24 }\n\t\t\t\t\t\t\t\t\tstep={ 1 }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Padding', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tvalue={ padding }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { padding: value } ) }\n\t\t\t\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\t\t\t\tmax={ 60 }\n\t\t\t\t\t\t\t\t\tstep={ 1 }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Border', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tvalue={ border }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { border: value } ) }\n\t\t\t\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\t\t\t\tmax={ 10 }\n\t\t\t\t\t\t\t\t\tstep={ 1 }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Border Rounded', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tvalue={ borderRounded }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { borderRounded: value } ) }\n\t\t\t\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\t\t\t\tmax={ 10 }\n\t\t\t\t\t\t\t\t\tstep={ 1 }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\ttitle={ __( 'Border Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\tvalue: borderColor,\n\t\t\t\t\t\t\t\t\tonChange: onChangeBorderColor,\n\t\t\t\t\t\t\t\t\tlabel: __( 'Border Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t></PanelColorSettings>\n\t\t\t\t\t\t\t</PanelBody>\n\t\t\t\t\t\t\t<PanelBody title={ __( 'Social Media Settings', 'metronet-profile-picture' ) } initialOpen={false}>\n\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Social Media Colors', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={this.state.socialMediaOptions}\n\t\t\t\t\t\t\t\t\t\toptions={ profileSocialMediaOptions }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { setAttributes({socialMediaOptions: value}); this.handleSocialMediaOptionChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{ this.state.socialMediaOptions === 'custom' &&\n\t\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\t\t\ttitle={ __( 'Social Media Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\t\t\tvalue: socialMediaColors,\n\t\t\t\t\t\t\t\t\t\t\tonChange: onChangeSocialMediaColor,\n\t\t\t\t\t\t\t\t\t\t\tlabel: __( 'Social Media Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('Facebook', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.socialFacebook}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { socialFacebook: value }); this.handleFacebookChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('Twitter', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.socialTwitter}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { socialTwitter: value }); this.handleTwitterChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('Instagram', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.socialInstagram}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { socialInstagram: value }); this.handleInstagramChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('LinkedIn', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.socialLinkedIn}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { socialLinkedIn: value }); this.handleLinkedInChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('YouTube', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.socialYouTube}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { socialYouTube: value }); this.handleYouTubeChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('GitHub', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.socialGitHub}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { socialGitHub: value }); this.handleGitHubChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('Pinterest', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.socialPinterest}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { socialPinterest: value }); this.handlePinterestChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<TextControl\n\t\t\t\t\t\t\t\t\tlabel={__('WordPress', 'metronet-profile-picture')}\n\t\t\t\t\t\t\t\t\tvalue={this.state.socialWordPress}\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.props.setAttributes( { socialWordPress: value }); this.handleWordPressChange(value); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</PanelBody>\n\t\t\t\t\t\t</InspectorControls>\n\t\t\t\t\t\t{ this.state.theme !== 'tabbed' &&\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclassName={\n\t\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t\t'mpp-enhanced-profile-wrap',\n\t\t\t\t\t\t\t\t\t\tthis.state.theme,\n\t\t\t\t\t\t\t\t\t\tprofileAlignment,\n\t\t\t\t\t\t\t\t\t\tprofileAvatarShape,\n\t\t\t\t\t\t\t\t\t\t'mpp-block-profile'\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\tpadding: padding + 'px',\n\t\t\t\t\t\t\t\t\tborder: border + 'px solid ' + borderColor,\n\t\t\t\t\t\t\t\t\tborderRadius: borderRounded + 'px',\n\t\t\t\t\t\t\t\t\tbackgroundColor: profileBackgroundColor,\n\t\t\t\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t\t\t\t\tfloat: profileFloat,\n\t\t\t\t\t\t\t\t\tmargin: profileMargin\n\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t{ this.state.theme === 'regular' &&\n\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t<div className={\n\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t'mpp-profile-gutenberg-wrap',\n\t\t\t\t\t\t\t\t\t'mt-font-size-' + profileFontSize,\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-wrapper\">\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-square\">\n\t\t\t\t\t\t\t\t\t\t<MediaUpload\n\t\t\t\t\t\t\t\t\t\t\tbuttonProps={ {\n\t\t\t\t\t\t\t\t\t\t\t\tclassName: 'change-image'\n\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t\tonSelect={ ( img ) => { this.handleImageChange( img.id, img.url ); setAttributes( { profileImgID: img.id, profileImgURL: img.url } ); } }\n\t\t\t\t\t\t\t\t\t\t\ttype=\"image\"\n\t\t\t\t\t\t\t\t\t\t\tvalue={ profileImgID }\n\t\t\t\t\t\t\t\t\t\t\trender={ ( { open } ) => (\n\t\t\t\t\t\t\t\t\t\t\t\t<Button \n\t\t\t\t\t\t\t\t\t\t\t\tonClick={ open }>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{ ! profileImgID ? <img src={profileImgURL} alt=\"placeholder\" /> : <img\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"profile-avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={ profileImgURL }\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>  }\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</MediaUpload>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div className=\"mpp-content-wrap\">\n\t\t\t\t\t\t\t\t\t{showName &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"h2\"\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add name', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileName }\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-name'\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t\t\t\t\t\t\tfontSize: headerFontSize + 'px'\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.onChangeName(value); setAttributes( { profileName: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t{showTitle &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"p\"\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add title', 'atomic-blocks' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileTitle }\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-title'\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangeTitle(value); setAttributes( { profileTitle: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t{showDescription &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-text'\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add profile text...', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileContent }\n\t\t\t\t\t\t\t\t\t\tformattingControls={ [ 'bold', 'italic', 'strikethrough', 'link' ] }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangeProfileText(value); setAttributes( { profileContent: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{profileURL && !! profileURL.length &&\n\t\t\t\t\t\t\t<div className=\"mpp-gutenberg-view-posts\" style={{width: showPostsWidth}}>\n\t\t\t\t\t\t\t{showViewPosts &&\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclassName=\"mpp-profile-view-posts\"\n\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tcolor: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t\t\twidth: showPostsWidth,\n\t\t\t\t\t\t\t\t\t\tfontSize: buttonFontSize + 'px'\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\thref={profileURL}\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t>{profileViewPosts}</a>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t{ this.state.website != '' && showWebsite &&\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclassName=\"mpp-profile-view-website\"\n\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\tbackgroundColor: profileWebsiteBackgroundColor,\n\t\t\t\t\t\t\t\t\tcolor: profileWebsiteTextColor,\n\t\t\t\t\t\t\t\t\tfontSize: buttonFontSize + 'px'\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\thref={this.state.website}\n\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileWebsiteBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tcolor: profileWebsiteTextColor,\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t>{profileViewWebsite}</a>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t}\n\t\t\t\t\t\t{ this.state.theme === 'profile' &&\n\t\t\t\t\t\t\t<div className={\n\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t'mpp-profile-gutenberg-wrap',\n\t\t\t\t\t\t\t\t\t'mt-font-size-' + profileFontSize,\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{showName &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"h2\"\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add name', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileName }\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-name'\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t\t\t\t\t\t\tfontSize: headerFontSize + 'px'\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.onChangeName(value); setAttributes( { profileName: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-wrapper\">\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-square\">\n\t\t\t\t\t\t\t\t\t\t<MediaUpload\n\t\t\t\t\t\t\t\t\t\t\tbuttonProps={ {\n\t\t\t\t\t\t\t\t\t\t\t\tclassName: 'change-image'\n\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t\tonSelect={ ( img ) => { this.handleImageChange( img.id, img.url ); setAttributes( { profileImgID: img.id, profileImgURL: img.url } ); } }\n\t\t\t\t\t\t\t\t\t\t\ttype=\"image\"\n\t\t\t\t\t\t\t\t\t\t\tvalue={ profileImgID }\n\t\t\t\t\t\t\t\t\t\t\trender={ ( { open } ) => (\n\t\t\t\t\t\t\t\t\t\t\t\t<Button onClick={ open }>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{ ! profileImgID ? <img src={profileImgURL} alt=\"placeholder\" /> : <img\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"profile-avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={ profileImgURL }\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>  }\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</MediaUpload>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t{showDescription &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-text'\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add profile text...', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileContent }\n\t\t\t\t\t\t\t\t\t\tformattingControls={ [ 'bold', 'italic', 'strikethrough', 'link' ] }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangeProfileText(value); setAttributes( { profileContent: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<div className=\"mpp-profile-meta\" style={{fontSize: buttonFontSize + 'px'}}>\n\t\t\t\t\t\t\t\t\t{showViewPosts &&\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-profile-link alignleft\">\n\t\t\t\t\t\t\t\t\t\t<a href={this.state.profile_url}\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileLinkColor,\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t>{__( 'View all posts by', 'metronet-profile-picture' )} {this.state.profile_name_unfiltered}</a>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t{this.state.website != '' && showWebsite &&\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-profile-link alignright\">\n\t\t\t\t\t\t\t\t\t\t<a href={this.state.website}\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileLinkColor,\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t>{__( 'Website', 'metronet-profile-picture' )}</a>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\t\t\t\t\t\t{ this.state.theme === 'compact' &&\n\t\t\t\t\t\t\t<div className={\n\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t'mpp-profile-gutenberg-wrap',\n\t\t\t\t\t\t\t\t\t'mt-font-size-' + profileFontSize,\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{showName &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"h2\"\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add name', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileName }\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-name'\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t\t\t\t\t\t\tfontSize: headerFontSize + 'px'\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.onChangeName(value); setAttributes( { profileName: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-wrapper\">\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-square\">\n\t\t\t\t\t\t\t\t\t\t<MediaUpload\n\t\t\t\t\t\t\t\t\t\t\tbuttonProps={ {\n\t\t\t\t\t\t\t\t\t\t\t\tclassName: 'change-image'\n\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t\tonSelect={ ( img ) => { this.handleImageChange( img.id, img.url ); setAttributes( { profileImgID: img.id, profileImgURL: img.url } ); } }\n\t\t\t\t\t\t\t\t\t\t\ttype=\"image\"\n\t\t\t\t\t\t\t\t\t\t\tvalue={ profileImgID }\n\t\t\t\t\t\t\t\t\t\t\trender={ ( { open } ) => (\n\t\t\t\t\t\t\t\t\t\t\t\t<Button onClick={ open }>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{ ! profileImgID ? <img src={profileImgURL} alt=\"placeholder\" /> : <img\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"profile-avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={ profileImgURL }\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>  }\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</MediaUpload>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t{showDescription &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-text'\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add profile text...', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileContent }\n\t\t\t\t\t\t\t\t\t\tformattingControls={ [ 'bold', 'italic', 'strikethrough', 'link' ] }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangeProfileText(value); setAttributes( { profileContent: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t<div className=\"mpp-compact-meta\">\n\t\t\t\t\t\t\t\t\t{showViewPosts &&\n\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\tclassName=\"mpp-profile-view-posts\"\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t\t\t\twidth: '90%',\n\t\t\t\t\t\t\t\t\t\t\tmargin: '0 auto 10px auto',\n\t\t\t\t\t\t\t\t\t\t\tfontSize: buttonFontSize + 'px'\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\t\thref={profileURL}\n\t\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t>{__('View Posts', 'metronet-profile-picture')}</a>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{ this.state.website != '' && showWebsite &&\n\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclassName=\"mpp-profile-view-website\"\n\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileWebsiteBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tcolor: profileWebsiteTextColor,\n\t\t\t\t\t\t\t\t\t\tfontSize: buttonFontSize + 'px',\n\t\t\t\t\t\t\t\t\t\twidth: '90%',\n\t\t\t\t\t\t\t\t\t\tmargin: '0 auto',\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\thref={this.state.website}\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileWebsiteBackgroundColor,\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileWebsiteTextColor,\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t>{__('View Website', 'metronet-profile-picture')}</a>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\t\t\t\t\t\t{ this.state.showSocialMedia == true && ( this.state.theme === 'regular' || this.state.theme === 'compact' || this.state.theme === 'profile' ) &&\n\t\t\t\t\t\t\t<div className=\"mpp-social\">\n\t\t\t\t\t\t\t\t{ this.state.socialFacebook != '' &&\n\t\t\t\t\t\t\t\t\t<a href={this.state.socialFacebook}>\n\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-facebook\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t<use href=\"#facebook\"></use>\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{ this.state.socialTwitter != '' &&\n\t\t\t\t\t\t\t\t\t<a href={this.state.socialTwitter}>\n\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-twitter\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t<use href=\"#twitter\"></use>\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{ this.state.socialInstagram != '' &&\n\t\t\t\t\t\t\t\t\t<a href={this.state.socialInstagram}>\n\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-instagram\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t<use href=\"#instagram\"></use>\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{ this.state.socialPinterest != '' &&\n\t\t\t\t\t\t\t\t\t<a href={this.state.socialPinterest}>\n\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-pinterest\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t<use href=\"#pinterest\"></use>\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{ this.state.socialLinkedIn != '' &&\n\t\t\t\t\t\t\t\t\t<a href={this.state.socialLinkedIn}>\n\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-linkedin\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t<use href=\"#linkedin\"></use>\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{ this.state.socialYouTube != '' &&\n\t\t\t\t\t\t\t\t\t<a href={this.state.socialYouTube}>\n\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-youtube\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t<use href=\"#youtube\"></use>\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{ this.state.socialGitHub != '' &&\n\t\t\t\t\t\t\t\t\t<a href={this.state.socialGitHub}>\n\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-github\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t<use href=\"#github\"></use>\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{ this.state.socialWordPress != '' &&\n\t\t\t\t\t\t\t\t\t<a href={this.state.socialWordPress}>\n\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-wordpress\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t<use href=\"#wordpress\"></use>\n\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\t\t\t\t\t\t{ this.state.theme === 'tabbed' &&\n\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclassName={\n\t\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t\t'mpp-author-tabbed',\n\t\t\t\t\t\t\t\t\t\tthis.state.theme,\n\t\t\t\t\t\t\t\t\t\tprofileAlignment,\n\t\t\t\t\t\t\t\t\t\tprofileAvatarShape,\n\t\t\t\t\t\t\t\t\t\t'mpp-block-profile'\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<ul className=\"mpp-author-tabs\">\n\t\t\t\t\t\t\t\t\t<li className={\n\t\t\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t\t\t'mpp-tab-profile',\n\t\t\t\t\t\t\t\t\t\t\tthis.state.activeTab === 'profile' ? 'active' : ''\n\t\t\t\t\t\t\t\t\t\t)\n\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tonClick={this.onChangeActiveProfileTab}\n\t\t\t\t\t\t\t\t\tstyle={{backgroundColor: this.state.profileTabColor, color: this.state.profileTabTextColor}}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\t\ttagName=\"span\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add tab name.', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\t\tvalue={this.state.tabbedAuthorProfile}\n\t\t\t\t\t\t\t\t\t\t\tformattingControls={[]}\n\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangetabbedAuthorProfile(value); setAttributes( { tabbedAuthorProfile: value } ) } }\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t<li className={\n\t\t\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t\t\t'mpp-tab-posts',\n\t\t\t\t\t\t\t\t\t\t\tthis.state.activeTab === 'latest' ? 'active' : ''\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\tonClick={this.onChangeActivePostTab}\n\t\t\t\t\t\t\t\t\t\tstyle={{backgroundColor: this.state.profileTabPostsColor, color: this.state.profileTabPostsTextColor}}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\t\ttagName=\"span\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add tab name.', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\t\tvalue={this.state.tabbedAuthorLatestPosts}\n\t\t\t\t\t\t\t\t\t\t\tformattingControls={[]}\n\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangetabbedAuthorLatestPosts(value); setAttributes( { tabbedAuthorLatestPosts: value } ) } }\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t<div className=\"mpp-tab-wrapper\"\n\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\tpadding: padding + 'px',\n\t\t\t\t\t\t\t\t\t\tborder: border + 'px solid ' + borderColor,\n\t\t\t\t\t\t\t\t\t\tborderRadius: borderRounded + 'px',\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{ this.state.activeTab === 'profile' &&\n\t\t\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t\t<div className=\"mpp-author-social-wrapper\">\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-author-heading\">\n\t\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"mpp-author-profile-heading\"\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={ this.state.tabbedAuthorProfileHeading }\n\t\t\t\t\t\t\t\t\t\t\t\tformattingControls={[]}\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangetabbedAuthorProfileHeading(value); setAttributes( { profileTabHeadlineTextColor: value } ) } }\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{backgroundColor: this.state.profileTabHeadlineColor, color: this.state.profileTabHeadlineTextColor}}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t{this.state.showSocialMedia &&\n\t\t\t\t\t\t\t\t\t\t<div className=\"mpp-author-social\">\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"mpp-social\">\n\t\t\t\t\t\t\t\t\t\t\t\t{ this.state.socialFacebook != '' &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t<a href={this.state.socialFacebook}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-facebook\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<use href=\"#facebook\"></use>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t{ this.state.socialTwitter != '' &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t<a href={this.state.socialTwitter}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-twitter\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<use href=\"#twitter\"></use>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t{ this.state.socialInstagram != '' &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t<a href={this.state.socialInstagram}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-instagram\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<use href=\"#instagram\"></use>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t{ this.state.socialPinterest != '' &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t<a href={this.state.socialPinterest}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-pinterest\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<use href=\"#pinterest\"></use>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t{ this.state.socialLinkedIn != '' &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t<a href={this.state.socialLinkedIn}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-linkedin\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<use href=\"#linkedin\"></use>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t{ this.state.socialYouTube != '' &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t<a href={this.state.socialYouTube}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-youtube\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<use href=\"#youtube\"></use>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t{ this.state.socialGitHub != '' &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t<a href={this.state.socialGitHub}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-github\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<use href=\"#github\"></use>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t{ this.state.socialWordPress != '' &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t<a href={this.state.socialWordPress}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<svg className=\"icon icon-wordpress\" role=\"img\" style={{fill: this.state.socialMediaOptions === 'custom' ? socialMediaColors : ''}}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<use href=\"#wordpress\"></use>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-wrapper\">\n\t\t\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-square\">\n\t\t\t\t\t\t\t\t\t\t\t<MediaUpload\n\t\t\t\t\t\t\t\t\t\t\t\tbuttonProps={ {\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName: 'change-image'\n\t\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t\t\tonSelect={ ( img ) => { this.handleImageChange( img.id, img.url ); setAttributes( { profileImgID: img.id, profileImgURL: img.url } ); } }\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"image\"\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={ profileImgID }\n\t\t\t\t\t\t\t\t\t\t\t\trender={ ( { open } ) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button onClick={ open }>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{ ! profileImgID ? <img src={profileImgURL} alt=\"placeholder\" /> : <img\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"profile-avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={ profileImgURL }\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>  }\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t</MediaUpload>\n\t\t\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"mpp-author-profile-sub-heading\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add profile description...', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={ this.state.tabbedAuthorSubHeading }\n\t\t\t\t\t\t\t\t\t\t\t\tformattingControls={ [ 'bold', 'italic', 'strikethrough', 'link' ] }\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangeTabbedSubHeading(value); setAttributes( { tabbedAuthorSubHeading: value } ) } }\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-tabbed-profile-information\">\n\t\t\t\t\t\t\t\t\t\t{ showTitle &&\n\t\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"mpp-author-profile-title\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add profile title...', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={ tabbedAuthorProfileTitle }\n\t\t\t\t\t\t\t\t\t\t\t\tformattingControls={ [ 'bold', 'italic', 'strikethrough', 'link' ] }\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { setAttributes( { tabbedAuthorProfileTitle: value } ) } }\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t{ showName &&\n\t\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\t\ttagName=\"h2\"\n\t\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add name', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\t\tvalue={ profileName }\n\t\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-name'\n\t\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: headerFontSize + 'px'\n\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.onChangeName(value); setAttributes( { profileName: value } ) } }\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t{ showDescription &&\n\t\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\t\t\tclassName={\n\t\t\t\t\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t\t\t\t\t'mpp-profile-text',\n\t\t\t\t\t\t\t\t\t\t\t\t\t'mt-font-size-' + profileFontSize,\n\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add profile text...', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\t\tvalue={ profileContent }\n\t\t\t\t\t\t\t\t\t\t\tformattingControls={ [ 'bold', 'italic', 'strikethrough', 'link' ] }\n\t\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangeProfileText(value); setAttributes( { profileContent: value } ) } }\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t{this.state.activeTab === 'latest' &&\n\t\t\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t\t\t\t{this.state.loadingLatestPosts &&\n\t\t\t\t\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"mpp-spinner\"><Spinner /></div>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t{!this.state.loadingLatestPosts &&\n\t\t\t\t\t\t\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t\t\t\t\t\t\t<ul\n\t\t\t\t\t\t\t\t\t\t\t\tclassName={\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t'mpp-author-tab-content',\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.state.profileLatestPostsOptionsValue\n\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t}>\n\t\t\t\t\t\t\t\t\t\t\t\t{this.state.latestPosts}\n\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</Fragment>\n\t\t\t\t\t\t}\n\t\t\t\t\t</Fragment>\n\n\t\t\t\t}\n\t\t\t</Fragment>\n\t\t);\n\t}\n}\n\nexport default MPP_Gutenberg_Enhanced;\n", "/**\n * External dependencies\n */\nimport axios from 'axios';\nconst { Component, Fragment } = wp.element;\n\nconst { __ } = wp.i18n;\n\nconst {\n\tPanelBody,\n\tPlaceholder,\n\tQueryControls,\n\tRangeControl,\n\tSelectControl,\n\tSpinner,\n\tTextControl,\n\tToggleControl,\n\tToolbar,\n\twithAPIData,\n\tColorPalette,\n\tButton,\n} = wp.components;\n\nconst {\n\tInspectorControls,\n\tBlockControls,\n\tMediaUpload,\n\tRichText,\n\tAlignmentToolbar,\n\tPanelColorSettings,\n} = wp.editor;\n\n// Import block dependencies and components\nimport classnames from 'classnames';\n\n\nclass MPP_Gutenberg extends Component {\n\tconstructor() {\n\t\tsuper( ...arguments );\n\n\t\tthis.state = {\n\t\t\tloading: true,\n\t\t\tusers: false,\n\t\t\tuser_list: false,\n\t\t\tprofile_picture: false,\n\t\t\tprofile_picture_id: 0,\n\t\t\tactive_user: false,\n\t\t\tprofile_description: '',\n\t\t\tprofile_name: '',\n\t\t\tprofile_title: '',\n\t\t};\n\t}\n\tget_users = () => {\n\t\taxios.post(mpp_gutenberg.rest_url + `/get_users`, {}, { 'headers': { 'X-WP-Nonce': mpp_gutenberg.nonce } } ).then( (response) => {\n\t\t\tlet users = Array();\n\t\t\tlet user_list = Array();\n\t\t\tlet active_user = 0;\n\t\t\tlet profile_picture = '';\n\t\t\tlet profile_picture_id = 0;\n\t\t\tlet profile_name = '';\n\t\t\tlet profile_description = '';\n\t\t\tlet profile_title = '';\n\t\t\tlet profile_url = '';\n\t\t\t$.each( response.data, function( key, value ) {\n\t\t\t\tusers[value.ID] = {\n\t\t\t\t\tprofile_pictures: value.profile_pictures,\n\t\t\t\t\thas_profile_picture: value.has_profile_picture,\n\t\t\t\t\tdisplay_name: value.display_name,\n\t\t\t\t\tdescription: value.description,\n\t\t\t\t\tis_user_logged_in: value.is_user_logged_in,\n\t\t\t\t\tprofile_picture_id: value.profile_picture_id,\n\t\t\t\t\tdefault_image: value.default_image,\n\t\t\t\t\tpermalink: value.permalink,\n\t\t\t\t};\n\t\t\t\tif ( value.is_user_logged_in ) {\n\t\t\t\t\tactive_user = value.ID;\n\t\t\t\t}\n\t\t\t\tuser_list.push( { value: value.ID, label: value.display_name });\n\t\t\t} );\n\t\t\tif( this.props.attributes.user_id !== 0 ) {\n\t\t\t\tactive_user = this.props.attributes.user_id;\n\t\t\t}\n\t\t\tlet active_user_profile = users[active_user];\n\t\t\tif( active_user_profile.has_profile_picture ) {\n\t\t\t\tprofile_picture = this.props.attributes.profileImgURL.length > 0 ? this.props.attributes.profileImgURL : active_user_profile.profile_pictures['thumbnail'];\n\t\t\t\tprofile_picture_id = this.props.attributes.profileImgID.length > 0 ? this.props.attributes.profileImgID : active_user_profile.profile_picture_id;\n\t\t\t\tprofile_name = this.props.attributes.profileName.length > 0 ? this.props.attributes.profileName :  active_user_profile.display_name;\n\t\t\t\tprofile_title = this.props.attributes.profileTitle.length > 0 ? this.props.attributes.profileTitle :  '';\n\t\t\t\tprofile_url = active_user_profile.permalink;\n\t\t\t\tprofile_description = this.props.attributes.profileContent.length > 0 ? this.props.attributes.profileContent : active_user_profile.description;\n\t\t\t} else {\n\t\t\t\tprofile_name = this.props.attributes.profileName.length > 0 ? this.props.attributes.profileName :  active_user_profile.display_name;\n\t\t\t\tprofile_title = this.props.attributes.profileTitle.length > 0 ? this.props.attributes.profileTitle :  '';\n\t\t\t\tprofile_description = this.props.attributes.profileContent.length > 0 ? this.props.attributes.profileContent : active_user_profile.description;\n\t\t\t\tprofile_picture = this.props.attributes.profileImgURL.length > 0 ? this.props.attributes.profileImgURL : active_user_profile.default_image;\n\t\t\t\tprofile_picture_id = this.props.attributes.profileImgID.length > 0 ? this.props.attributes.profileImgID : 0;\n\t\t\t\tprofile_url = active_user_profile.permalink;\n\t\t\t}\n\t\t\tif( undefined == profile_description ) {\n\t\t\t\tprofile_description = '';\n\t\t\t}\n\t\t\tthis.setState(\n\t\t\t\t{\n\t\t\t\t\tloading: false,\n\t\t\t\t\tusers: users,\n\t\t\t\t\tactive_user: active_user,\n\t\t\t\t\tuser_list: user_list,\n\t\t\t\t\tprofile_picture: profile_picture,\n\t\t\t\t\tprofile_picture_id: profile_picture_id,\n\t\t\t\t\tactive_user: active_user,\n\t\t\t\t\tprofile_name: profile_name,\n\t\t\t\t\tprofile_title: profile_title,\n\t\t\t\t\tprofile_description: profile_description,\n\t\t\t\t\tprofile_url: profile_url,\n\t\t\t\t}\n\t\t\t);\n\t\t\tthis.props.setAttributes( {\n\t\t\t\tprofileContent: profile_description,\n\t\t\t\tprofileName: profile_name,\n\t\t\t\tprofileTitle: profile_title,\n\t\t\t\tprofileURL: profile_url,\n\t\t\t\tprofileImgID: profile_picture_id,\n\t\t\t\tprofileImgURL: profile_picture,\n\t\t\t});\n\t\t});\n\t}\n\ton_user_change = ( user_id ) => {\n\t\tlet user = this.state.users[user_id];\n\t\tlet profile_picture = '';\n\t\tlet profile_picture_id = 0;\n\t\tif( !user.has_profile_picture ) {\n\t\t\tprofile_picture = mpp_gutenberg.mystery_man;\n\t\t\tprofile_picture_id = 0;\n\t\t} else {\n\t\t\tprofile_picture = this.state.users[user_id]['profile_pictures']['thumbnail']\n\t\t\tprofile_picture_id = this.state.users[user_id]['profile_picture_id'];\n\t\t}\n\t\tlet description = this.state.users[user_id].description;\n\t\tif( undefined === description ) {\n\t\t\tdescription = '';\n\t\t}\n\t\tthis.props.setAttributes( {\n\t\t\tprofileName: this.state.users[user_id].display_name,\n\t\t\tprofileContent: description,\n\t\t\tprofileTitle: '',\n\t\t\tprofileURL: this.state.users[user_id].permalink,\n\t\t\tprofileImgURL: profile_picture\n\t\t} );\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofile_name: this.state.users[user_id].display_name,\n\t\t\t\tprofile_description: description,\n\t\t\t\tprofile_title: '',\n\t\t\t\tprofile_picture: profile_picture,\n\t\t\t\tprofile_picture_id: profile_picture_id,\n\t\t\t\tactive_user: user_id,\n\t\t\t\tprofile_url: this.state.users[user_id].permalink\n\t\t\t}\n\t\t);\n\t}\n\tcomponentDidMount = () => {\n\t\tthis.get_users();\n\t}\n\thandleImageChange = ( image_id, image_url ) => {\n\t\tthis.setState( {\n\t\t\tprofile_picture: image_url,\n\t\t\tprofile_picture_id: image_id,\n\t\t} );\n\t}\n\tonChangeName = (value) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofile_name: value\n\t\t\t}\n\t\t);\n\t}\n\tonChangeTitle = (value) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofile_title: value\n\t\t\t}\n\t\t);\n\t}\n\tonChangeProfileText = (value) => {\n\t\tthis.setState(\n\t\t\t{\n\t\t\t\tprofile_description: value\n\t\t\t}\n\t\t);\n\t}\n\trender() {\n\t\t// Setup the attributes\n\t\tlet {\n\t\t\tattributes: {\n\t\t\t\tprofileName,\n\t\t\t\tprofileTitle,\n\t\t\t\tprofileContent,\n\t\t\t\tprofileAlignment,\n\t\t\t\tprofileImgURL,\n\t\t\t\tprofileImgID,\n\t\t\t\tprofileURL,\n\t\t\t\tprofileFontSize,\n\t\t\t\tprofileBackgroundColor,\n\t\t\t\tprofileTextColor,\n\t\t\t\tprofileAvatarShape,\n\t\t\t\tprofileViewPostsBackgroundColor,\n\t\t\t\tprofileViewPostsTextColor,\n\t\t\t\tshowTitle,\n\t\t\t\tshowName,\n\t\t\t\tshowDescription,\n\t\t\t\tshowViewPosts,\n\t\t\t},\n\t\t\tattributes,\n\t\t\tisSelected,\n\t\t\teditable,\n\t\t\tclassName,\n\t\t\tsetAttributes\n\t\t} = this.props;\n\t\tlet profile_pictures = this.state.profile_pictures;\n\t\tprofileImgID = this.state.profile_picture_id;\n\t\tprofileImgURL = this.state.profile_picture;\n\t\tprofileName = this.state.profile_name;\n\t\tprofileContent = this.state.profile_description;\n\t\tprofileTitle = this.state.profile_title;\n\t\tprofileURL = this.state.profile_url;\n\n\t\tconst onChangeBackgroundColor = value => setAttributes( { profileBackgroundColor: value } );\n\t\tconst onChangeProfileTextColor = value => setAttributes( { profileTextColor: value } );\n\t\tconst onChangeViewPostsBackgroundColor = value => setAttributes( { profileViewPostsBackgroundColor: value } );\n\t\tconst onChangeViewPostsTextColor = value => setAttributes( { profileViewPostsTextColor: value } );\n\n\t\t// Avatar shape options\n\t\tconst profileAvatarShapeOptions = [\n\t\t\t{ value: 'square', label: __( 'Square', 'metronet-profile-picture' ) },\n\t\t\t{ value: 'round', label: __( 'Round', 'metronet-profile-picture' ) },\n\t\t];\n\t\treturn(\n\t\t\t<Fragment>\n\t\t\t\t{this.state.loading &&\n\t\t\t\t<Fragment>\n\t\t\t\t\t<Placeholder>\n\t\t\t\t\t\t{__('Loading...', 'metronet-profile-picture')}\n\t\t\t\t\t\t<Spinner />\n\t\t\t\t\t</Placeholder>\n\t\t\t\t</Fragment>\n\t\t\t\t}\n\t\t\t\t{!this.state.loading &&\n\t\t\t\t\t<Fragment>\n\t\t\t\t\t\t<InspectorControls>\n\t\t\t\t\t\t\t<PanelBody title={ __( 'User Profile Settings', 'metronet-profile-picture' ) }>\n\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Select a user', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={this.state.active_user}\n\t\t\t\t\t\t\t\t\t\toptions={ this.state.user_list }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.on_user_change(value); setAttributes({user_id: Number(value)}); } }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Font Size', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tvalue={ profileFontSize }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { profileFontSize: value } ) }\n\t\t\t\t\t\t\t\t\tmin={ 14 }\n\t\t\t\t\t\t\t\t\tmax={ 24 }\n\t\t\t\t\t\t\t\t\tstep={ 1 }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Avatar Shape', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tdescription={ __( 'Choose between a round or square avatar shape.', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\toptions={ profileAvatarShapeOptions }\n\t\t\t\t\t\t\t\t\tvalue={ profileAvatarShape }\n\t\t\t\t\t\t\t\t\tonChange={ ( value ) => this.props.setAttributes( { profileAvatarShape: value } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\ttitle={ __( 'Background Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\tvalue: profileBackgroundColor,\n\t\t\t\t\t\t\t\t\tonChange: onChangeBackgroundColor,\n\t\t\t\t\t\t\t\t\tlabel: __( 'Background Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\ttitle={ __( 'Text Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\tvalue: profileTextColor,\n\t\t\t\t\t\t\t\t\tonChange: onChangeProfileTextColor,\n\t\t\t\t\t\t\t\t\tlabel: __( 'Text Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\ttitle={ __( 'View Posts Background Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\tvalue: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\tonChange: onChangeViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\tlabel: __( 'View Posts Background', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</PanelColorSettings>\n\t\t\t\t\t\t\t\t<PanelColorSettings\n\t\t\t\t\t\t\t\ttitle={ __( 'View Posts Text Color', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\tinitialOpen={ false }\n\t\t\t\t\t\t\t\tcolorSettings={ [ {\n\t\t\t\t\t\t\t\t\tvalue: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t\tonChange: onChangeViewPostsTextColor,\n\t\t\t\t\t\t\t\t\tlabel: __( 'View Posts Text Color', 'metronet-profile-picture' ),\n\t\t\t\t\t\t\t\t} ] }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</PanelColorSettings>\n\n\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Show Name', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tchecked={ showName }\n\t\t\t\t\t\t\t\t\tonChange={ () => this.props.setAttributes( { showName: ! showName } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Show Title', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tchecked={ showTitle }\n\t\t\t\t\t\t\t\t\tonChange={ () => this.props.setAttributes( { showTitle: ! showTitle } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Show Description', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tchecked={ showDescription }\n\t\t\t\t\t\t\t\t\tonChange={ () => this.props.setAttributes( { showDescription: ! showDescription } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<ToggleControl\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Show View Posts', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\tchecked={ showViewPosts }\n\t\t\t\t\t\t\t\t\tonChange={ () => this.props.setAttributes( { showViewPosts: ! showViewPosts } ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</PanelBody>\n\t\t\t\t\t\t</InspectorControls>\n\t\t\t\t\t\t<BlockControls key=\"controls\">\n\t\t\t\t\t\t\t<AlignmentToolbar\n\t\t\t\t\t\t\t\tvalue={ profileAlignment }\n\t\t\t\t\t\t\t\tonChange={ ( value ) => setAttributes( { profileAlignment: value } ) }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</BlockControls>\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tclassName={\n\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t'mpp-profile-wrap',\n\t\t\t\t\t\t\t\t\t'legacy',\n\t\t\t\t\t\t\t\t\tprofileAlignment,\n\t\t\t\t\t\t\t\t\tprofileAvatarShape,\n\t\t\t\t\t\t\t\t\t'mt-font-size-' + profileFontSize,\n\t\t\t\t\t\t\t\t\t'mpp-block-profile'\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\tbackgroundColor: profileBackgroundColor,\n\t\t\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div className={\n\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t'mpp-profile-gutenberg-wrap',\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-wrapper\">\n\t\t\t\t\t\t\t\t\t<div className=\"mpp-profile-image-square\">\n\t\t\t\t\t\t\t\t\t\t<MediaUpload\n\t\t\t\t\t\t\t\t\t\t\tbuttonProps={ {\n\t\t\t\t\t\t\t\t\t\t\t\tclassName: 'change-image'\n\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t\tonSelect={ ( img ) => { this.handleImageChange( img.id, img.url ); setAttributes( { profileImgID: img.id, profileImgURL: img.url } ); } }\n\t\t\t\t\t\t\t\t\t\t\ttype=\"image\"\n\t\t\t\t\t\t\t\t\t\t\tvalue={ profileImgID }\n\t\t\t\t\t\t\t\t\t\t\trender={ ( { open } ) => (\n\t\t\t\t\t\t\t\t\t\t\t\t<Button onClick={ open }>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{ ! profileImgID ? <img src={profileImgURL} alt=\"placeholder\" /> : <img\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"profile-avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={ profileImgURL }\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>  }\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</MediaUpload>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div className=\"mpp-content-wrap\">\n\t\t\t\t\t\t\t\t\t{showName &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"h2\"\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add name', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileName }\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-name'\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => { this.onChangeName(value); setAttributes( { profileName: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t{showTitle &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"p\"\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add title', 'atomic-blocks' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileTitle }\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-title'\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangeTitle(value); setAttributes( { profileTitle: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t{showDescription &&\n\t\t\t\t\t\t\t\t\t<RichText\n\t\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\t\tclassName='mpp-profile-text'\n\t\t\t\t\t\t\t\t\t\tplaceholder={ __( 'Add profile text...', 'metronet-profile-picture' ) }\n\t\t\t\t\t\t\t\t\t\tvalue={ profileContent }\n\t\t\t\t\t\t\t\t\t\tformattingControls={ [ 'bold', 'italic', 'strikethrough', 'link' ] }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( value ) => {this.onChangeProfileText(value); setAttributes( { profileContent: value } ) } }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{profileURL && !! profileURL.length &&\n\t\t\t\t\t\t\t<div className=\"mpp-gutenberg-view-posts\">\n\t\t\t\t\t\t\t{showViewPosts &&\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclassName=\"mpp-profile-view-posts\"\n\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\t\tcolor: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\thref={profileURL}\n\t\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\t\t\tcolor: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t>{__('View Posts', 'metronet-profile-picture')}</a>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</Fragment>\n\t\t\t\t}\n\t\t\t</Fragment>\n\t\t);\n\t}\n}\n\nexport default MPP_Gutenberg;\n", "/**\n * BLOCK: wp-plugin-info-card\n *\n * Registering a basic block with <PERSON><PERSON><PERSON>.\n * Simple block, renders and saves the same content without any interactivity.\n */\n\n//  Import CSS.\nimport './style.scss';\nimport './editor.scss';\n/**\n * BLOCK: Basic with ESNext\n *\n * Registering a basic block with <PERSON><PERSON><PERSON>.\n * Simple block, renders and saves the same content without any interactivity.\n *\n * Using inline styles - no external stylesheet needed.  Not recommended!\n * because all of these styles will appear in `post_content`.\n */\n\nconst { __ } = wp.i18n; // Import __() from wp.i18n\nconst { registerBlockType } = wp.blocks; // Import registerBlockType() from wp.blocks\n\n// Import JS\nimport edit from './profile';\nimport legacyEdit from './profile-legacy';\n\n// Extend component\nconst { Component, Fragment } = wp.element;\n\nexport const legacy_name = 'mpp/user-profile';\nexport const name = 'mpp/user-profile-enhanced';\n\n\n// Import block dependencies and components\nimport classnames from 'classnames';\n\nconst {\n\tRichText,\n} = wp.editor;\n\nconst blockAttributes = {\n\tprofileName: {\n\t\ttype: 'string',\n\t\tdefault: ''\n\t},\n\tprofileTitle: {\n\t\ttype: 'string',\n\t\tdefault: ''\n\t},\n\tprofileContent: {\n\t\ttype: 'string',\n\t\tdefault: ''\n\t},\n\tprofileAlignment: {\n\t\ttype: 'string',\n\t},\n\tprofileImgURL: {\n\t\ttype: 'string',\n\t\tsource: 'attribute',\n\t\tattribute: 'src',\n\t\tselector: 'img',\n\t\tdefault: '',\n\t},\n\tprofileImgID: {\n\t\ttype: 'number',\n\t\tdefault: '',\n\t},\n\tprofileURL: {\n\t\ttype: 'string',\n\t\tdefault: '',\n\t},\n\tprofileBackgroundColor: {\n\t\ttype: 'string',\n\t\tdefault: '#f2f2f2'\n\t},\n\tprofileTextColor: {\n\t\ttype: 'string',\n\t\tdefault: '#32373c'\n\t},\n\tprofileViewPostsBackgroundColor: {\n\t\ttype: 'string',\n\t\tdefault: '#cf6d38'\n\t},\n\tprofileViewPostsTextColor: {\n\t\ttype: 'string',\n\t\tdefault: '#FFFFFF'\n\t},\n\tprofileViewPostsWidth: {\n\t\ttype: 'number',\n\t\tdefault: 100\n\t},\n\tprofileFontSize: {\n\t\ttype: 'number',\n\t\tdefault: 18\n\t},\n\tprofileAvatarShape: {\n\t\ttype: 'string',\n\t\tdefault: 'square',\n\t},\n\tshowName: {\n\t\ttype: 'boolean',\n\t\tdefault: true,\n\t},\n\tshowTitle: {\n\t\ttype: 'boolean',\n\t\tdefault: true,\n\t},\n\tshowDescription: {\n\t\ttype: 'boolean',\n\t\tdefault: true,\n\t},\n\tshowViewPosts: {\n\t\ttype: 'boolean',\n\t\tdefault: true,\n\t},\n\tuser_id: {\n\t\ttype: 'number',\n\t\tdefault: 0\n\t}\n};\n\nregisterBlockType( 'mpp/user-profile', { // Block name. Block names must be string that contains a namespace prefix. Example: my-plugin/my-custom-block.\n\ttitle: __( 'User Profile Legacy', 'metronet-profile-picture' ), // Block title.\n\ticon: <svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 753.53 979.74\"><title>upp</title><path d=\"M806.37,185.9c0,40.27-30.49,72.9-68.11,72.9s-68.17-32.63-68.17-72.9S700.62,113,738.26,113,806.37,145.64,806.37,185.9Z\" transform=\"translate(-123.47 -11)\" fill=\"#4063ad\"/><path d=\"M330.36,183.8c0,40.27-30.49,72.9-68.12,72.9s-68.17-32.63-68.17-72.9,30.52-72.87,68.17-72.87S330.36,143.56,330.36,183.8Z\" transform=\"translate(-123.47 -11)\" fill=\"#a34d9c\"/><path d=\"M331.3,888.13V698.21H329c-31.64,0-57.28-27.45-57.28-61.29V336.5a118.37,118.37,0,0,1,5.43-34.79H179.84c-31.94,0-56.37,31.57-56.37,56.34V601.46h48.32V888.13Z\" transform=\"translate(-123.47 -11)\" fill=\"#a34d9c\"/><path d=\"M388.59,636.92V990.74H611.88V636.92H671.5V336.5c0-30.63-27.64-69.57-69.6-69.57H398.56c-39.44,0-69.61,38.94-69.61,69.57V636.92Z\" transform=\"translate(-123.47 -11)\" fill=\"#f4831f\"/><path d=\"M584.3,101c0,49.69-37.63,90-84,90S416.12,150.67,416.12,101s37.66-90,84.14-90S584.3,51.27,584.3,101Z\" transform=\"translate(-123.47 -11)\" fill=\"#f4831f\"/><path d=\"M820.61,303.79H724.08a121.69,121.69,0,0,1,4.7,32.71V636.92c0,33.84-25.64,61.29-57.28,61.29h-2.33v192H828.7V603.54H877V360.16C877,335.36,854.62,303.79,820.61,303.79Z\" transform=\"translate(-123.47 -11)\" fill=\"#4063ad\"/></svg>,\n\tcategory: 'mpp', // Block category — Group blocks together based on common traits E.g. common, formatting, layout widgets, embed.\n\t// Setup the block attributes\n\tattributes: blockAttributes,\n\n\tedit: legacyEdit,\n\n\tsave( props ) {\n\t\tconst { profileName, profileTitle, profileContent, profileAlignment, profileImgURL, profileImgID, profileFontSize, profileBackgroundColor, profileTextColor, profileLinkColor, profileAvatarShape, profileViewPostsBackgroundColor, profileViewPostsTextColor, profileURL, showTitle, showName, showDescription, showViewPosts } = props.attributes;\n\n\t\treturn(\n\t\t\t<Fragment>\n\t\t\t\t<div\n\t\t\t\t\tclassName={\n\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t'mpp-profile-wrap',\n\t\t\t\t\t\t\tprofileAlignment,\n\t\t\t\t\t\t\tprofileAvatarShape,\n\t\t\t\t\t\t\t'mt-font-size-' + profileFontSize,\n\t\t\t\t\t\t\t'mpp-block-profile'\n\t\t\t\t\t\t)\n\t\t\t\t\t}\n\t\t\t\t\tstyle={ {\n\t\t\t\t\t\tbackgroundColor: profileBackgroundColor,\n\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t} }\n\t\t\t\t>\n\t\t\t\t\t<div className={\n\t\t\t\t\t\t\t\tclassnames(\n\t\t\t\t\t\t\t\t\t'mpp-profile-gutenberg-wrap',\n\t\t\t\t\t\t\t\t\t'mpp-block-profile'\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\tbackgroundColor: profileBackgroundColor,\n\t\t\t\t\t\t\t\tcolor: profileTextColor,\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t<div className=\"mpp-profile-image-wrapper\">\n\t\t\t\t\t\t\t<div className=\"mpp-profile-image-square\">\n\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\tclassName=\"mpp-profile-avatar\"\n\t\t\t\t\t\t\t\t\tsrc={profileImgURL}\n\t\t\t\t\t\t\t\t\talt=\"avatar\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"mpp-content-wrap\">\n\t\t\t\t\t\t\t{ profileName && !! profileName.length && showName && (\n\t\t\t\t\t\t\t\t<RichText.Content\n\t\t\t\t\t\t\t\t\ttagName=\"h2\"\n\t\t\t\t\t\t\t\t\tclassName=\"mpp-profile-name\"\n\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\tvalue={ profileName }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t) }\n\n\t\t\t\t\t\t\t{ profileTitle && !! profileTitle.length && showTitle && (\n\t\t\t\t\t\t\t\t<RichText.Content\n\t\t\t\t\t\t\t\t\ttagName=\"p\"\n\t\t\t\t\t\t\t\t\tclassName=\"mpp-profile-title\"\n\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\tcolor: profileTextColor\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\tvalue={ profileTitle }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t) }\n\n\t\t\t\t\t\t\t{ profileContent && !! profileContent.length && showDescription && (\n\t\t\t\t\t\t\t\t<RichText.Content\n\t\t\t\t\t\t\t\t\ttagName=\"div\"\n\t\t\t\t\t\t\t\t\tclassName=\"mpp-profile-text\"\n\t\t\t\t\t\t\t\t\tvalue={ profileContent }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t{profileURL && !! profileURL.length &&\n\t\t\t\t\t<div className=\"mpp-gutenberg-view-posts\">\n\t\t\t\t\t{showViewPosts &&\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tclassName=\"mpp-profile-view-posts\"\n\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\tbackgroundColor: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\tcolor: profileViewPostsTextColor,\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\thref={profileURL}\n\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\tbackgroundColor: profileViewPostsBackgroundColor,\n\t\t\t\t\t\t\t\t\tcolor: profileViewPostsTextColor,\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t>{__('View Posts', 'metronet-profile-picture')}</a>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t}\n\t\t\t\t\t</div>\n\t\t\t\t\t}\n\t\t\t\t</div>\n\t\t\t</Fragment>\n\t\t)\n\t},\n} );\n\n/**\n * Register Basic Block.\n *\n * Registers a new block provided a unique name and an object defining its\n * behavior. Once registered, the block is made available as an option to any\n * editor interface where blocks are implemented.\n *\n * @param  {string}   name     Block name.\n * @param  {Object}   settings Block settings.\n * @return {?WPBlock}          The block, if it has been successfully\n *                             registered; otherwise `undefined`.\n */\nregisterBlockType( 'mpp/user-profile-enhanced', { // Block name. Block names must be string that contains a namespace prefix. Example: my-plugin/my-custom-block.\n\ttitle: __( 'User Profile', 'metronet-profile-picture' ), // Block title.\n\ticon: <svg id=\"Layer_1\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 753.53 979.74\"><title>upp</title><path d=\"M806.37,185.9c0,40.27-30.49,72.9-68.11,72.9s-68.17-32.63-68.17-72.9S700.62,113,738.26,113,806.37,145.64,806.37,185.9Z\" transform=\"translate(-123.47 -11)\" fill=\"#4063ad\"/><path d=\"M330.36,183.8c0,40.27-30.49,72.9-68.12,72.9s-68.17-32.63-68.17-72.9,30.52-72.87,68.17-72.87S330.36,143.56,330.36,183.8Z\" transform=\"translate(-123.47 -11)\" fill=\"#a34d9c\"/><path d=\"M331.3,888.13V698.21H329c-31.64,0-57.28-27.45-57.28-61.29V336.5a118.37,118.37,0,0,1,5.43-34.79H179.84c-31.94,0-56.37,31.57-56.37,56.34V601.46h48.32V888.13Z\" transform=\"translate(-123.47 -11)\" fill=\"#a34d9c\"/><path d=\"M388.59,636.92V990.74H611.88V636.92H671.5V336.5c0-30.63-27.64-69.57-69.6-69.57H398.56c-39.44,0-69.61,38.94-69.61,69.57V636.92Z\" transform=\"translate(-123.47 -11)\" fill=\"#f4831f\"/><path d=\"M584.3,101c0,49.69-37.63,90-84,90S416.12,150.67,416.12,101s37.66-90,84.14-90S584.3,51.27,584.3,101Z\" transform=\"translate(-123.47 -11)\" fill=\"#f4831f\"/><path d=\"M820.61,303.79H724.08a121.69,121.69,0,0,1,4.7,32.71V636.92c0,33.84-25.64,61.29-57.28,61.29h-2.33v192H828.7V603.54H877V360.16C877,335.36,854.62,303.79,820.61,303.79Z\" transform=\"translate(-123.47 -11)\" fill=\"#4063ad\"/></svg>,\n\tcategory: 'mpp', // Block category — Group blocks together based on common traits E.g. common, formatting, layout widgets, embed.\n\t// Setup the block attributes\n\tgetEditWrapperProps( attributes ) {\n\n    },\n\tedit: edit,\n\n\tsave() {return null }\n} );\n"], "sourceRoot": ""}