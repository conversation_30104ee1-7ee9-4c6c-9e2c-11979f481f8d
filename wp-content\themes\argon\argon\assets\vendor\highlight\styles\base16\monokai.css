/*!
  Theme: Monokai
  Author: <PERSON><PERSON><PERSON> (http://www.monokai.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme monokai
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #272822  Default Background
base01  #383830  Lighter Background (Used for status bars, line number and folding marks)
base02  #49483e  Selection Background
base03  #75715e  Comments, Invisibles, Line Highlighting
base04  #a59f85  Dark Foreground (Used for status bars)
base05  #f8f8f2  Default Foreground, Caret, Delimiters, Operators
base06  #f5f4f1  Light Foreground (Not often used)
base07  #f9f8f5  Light Background (Not often used)
base08  #f92672  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #fd971f  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f4bf75  Classes, Markup Bold, Search Text Background
base0B  #a6e22e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #a1efe4  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #66d9ef  Functions, Methods, Attribute IDs, Headings
base0E  #ae81ff  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #cc6633  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #f8f8f2;
  background: #272822;
}

.hljs::selection,
.hljs ::selection {
  background-color: #49483e;
  color: #f8f8f2;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #75715e -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #75715e;
}

/* base04 - #a59f85 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #a59f85;
}

/* base05 - #f8f8f2 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #f8f8f2;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #f92672;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #fd971f;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #f4bf75;
}

.hljs-strong {
  font-weight:bold;
  color: #f4bf75;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #a6e22e;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #a1efe4;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #66d9ef;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #ae81ff;
}
.hljs-emphasis {
  color: #ae81ff;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #cc6633;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
