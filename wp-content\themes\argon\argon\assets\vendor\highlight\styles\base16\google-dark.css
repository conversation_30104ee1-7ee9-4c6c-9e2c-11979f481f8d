/*!
  Theme: Google Dark
  Author: <PERSON> (http://sethawright.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme google-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #1d1f21  Default Background
base01  #282a2e  Lighter Background (Used for status bars, line number and folding marks)
base02  #373b41  Selection Background
base03  #969896  Comments, Invisibles, Line Highlighting
base04  #b4b7b4  Dark Foreground (Used for status bars)
base05  #c5c8c6  Default Foreground, Caret, Delimiters, Operators
base06  #e0e0e0  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #CC342B  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #F96A38  Integers, <PERSON><PERSON>an, Constants, XML Attributes, Markup Link Url
base0A  #FBA922  Classes, Markup Bold, Search Text Background
base0B  #198844  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #3971ED  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #3971ED  Functions, Methods, Attribute IDs, Headings
base0E  #A36AC7  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #3971ED  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #c5c8c6;
  background: #1d1f21;
}

.hljs::selection,
.hljs ::selection {
  background-color: #373b41;
  color: #c5c8c6;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #969896 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #969896;
}

/* base04 - #b4b7b4 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #b4b7b4;
}

/* base05 - #c5c8c6 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #c5c8c6;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #CC342B;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #F96A38;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #FBA922;
}

.hljs-strong {
  font-weight:bold;
  color: #FBA922;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #198844;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #3971ED;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #3971ED;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #A36AC7;
}
.hljs-emphasis {
  color: #A36AC7;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #3971ED;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
