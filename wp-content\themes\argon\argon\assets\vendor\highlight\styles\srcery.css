/*
Theme: Srcery
Description: Srcery dark color scheme for highlight.js
Author: <PERSON> <<EMAIL>>
Maintainer: @redguardtoo
Website: https://srcery-colors.github.io/
Date: 2021-04-13
*/

.hljs {
  background: #1C1B19; /* Black */
  color: #FCE8C3; /* Bright White */
}

/* Bright White */
.hljs-subst,
.hljs-quote,
.hljs-literal {
  color: #FCE8C3;
}

/* Bright Blue */
.hljs-type,
.hljs-symbol {
  color: #68A8E4;
}

/* Red */
.hljs-keyword,
.hljs-deletion {
  color: #EF2F27;
}

/* Yellow */
.hljs-name,
.hljs-function,
.hljs-attribute,
.hljs-selector-attr,
.hljs-selector-id,
.hljs-selector-class,
.hljs-selector-pseudo,
.hljs-section,
.hljs-title {
  color: #FBB829;
}

/* <PERSON><PERSON> */
.hljs-code,
.hljs-variable,
.hljs-property,
.hljs-template-variable,
.hljs-class {
  color: #0AAEB3;
}

/* Bright Green */
.hljs-string,
.hljs-regexp,
.hljs-bullet,
.hljs-addition {
  color: #98BC37;
}

/* Bright Magenta */
.hljs-built_in,
.hljs-params {
  color: #FF5C8F;
}

/* Blue */
.hljs-template-tag,
.hljs-selector-tag {
  color: #2C78BF;
}

/* Bright Black */
.hljs-link,
.hljs-number,
.hljs-comment,
.hljs-meta {
  color: #918175;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

/* @see https://github.com/srcery-colors/srcery-emacs for reference */
