{"Version": 3, "Meta": {"Duration": 10.133, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 46, "TotalSegmentCount": 922, "TotalPointCount": 2770, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param51", "Segments": [0, -30, 1, 2.467, -30, 4.933, -30, 7.4, -30, 1, 7.456, -30, 7.511, 30, 7.567, 30, 1, 7.778, 30, 7.989, 30, 8.2, 30, 1, 8.256, 30, 8.311, -30, 8.367, -30, 0, 10.133, -30]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 1, 0.4, 0, 0.8, -0.012, 1.2, -0.012, 1, 1.378, -0.012, 1.556, 0.031, 1.733, 0.031, 1, 1.856, 0.031, 1.978, -0.125, 2.1, -0.125, 1, 2.111, -0.125, 2.122, -0.124, 2.133, -0.124, 1, 2.167, -0.124, 2.2, -0.214, 2.233, -0.214, 1, 2.389, -0.214, 2.544, 0.38, 2.7, 0.38, 1, 2.789, 0.38, 2.878, 0.114, 2.967, 0.114, 1, 3.111, 0.114, 3.256, 0.428, 3.4, 0.428, 1, 3.444, 0.428, 3.489, 0.383, 3.533, 0.383, 1, 3.689, 0.383, 3.844, 1.109, 4, 1.109, 1, 4.389, 1.109, 4.778, -0.334, 5.167, -0.334, 1, 5.289, -0.334, 5.411, 0.161, 5.533, 0.161, 1, 5.644, 0.161, 5.756, -0.065, 5.867, -0.065, 1, 5.989, -0.065, 6.111, 0.024, 6.233, 0.024, 1, 6.344, 0.024, 6.456, -0.009, 6.567, -0.009, 1, 6.689, -0.009, 6.811, 0.003, 6.933, 0.003, 1, 7.044, 0.003, 7.156, -0.001, 7.267, -0.001, 1, 7.389, -0.001, 7.511, 0, 7.633, 0, 1, 7.778, 0, 7.922, -0.275, 8.067, -0.275, 1, 8.089, -0.275, 8.111, -0.202, 8.133, -0.202, 1, 8.244, -0.202, 8.356, -4.664, 8.467, -4.664, 1, 8.589, -4.664, 8.711, 2.966, 8.833, 2.966, 1, 8.944, 2.966, 9.056, -1.37, 9.167, -1.37, 1, 9.289, -1.37, 9.411, 0.498, 9.533, 0.498, 1, 9.644, 0.498, 9.756, -0.193, 9.867, -0.193, 1, 9.956, -0.193, 10.044, -0.025, 10.133, 0.043]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, -30, 0, 3.767, -30, 1, 4.3, -30, 4.833, 30, 5.367, 30, 1, 5.678, 30, 5.989, 18, 6.3, 18, 1, 6.6, 18, 6.9, 28, 7.2, 28, 0, 10.133, 28]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, -30, 0, 2.733, -30, 1, 3.044, -30, 3.356, 30, 3.667, 30, 1, 4.844, 30, 6.022, 30, 7.2, 30, 1, 7.233, 30, 7.267, -30, 7.3, -30, 0, 10.133, -30]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, -30, 0, 4.533, -30, 1, 4.933, -30, 5.333, 30, 5.733, 30, 1, 6.111, 30, 6.489, -30, 6.867, -30, 0, 10.133, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 3.367, 0, 1, 3.878, 0, 4.389, -3, 4.9, -3, 0, 10.133, -3]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 3.367, 0, 1, 3.878, 0, 4.389, -6, 4.9, -6, 0, 10.133, -6]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -1, 1, 0.5, -1, 1, -1, 1.5, -1, 1, 1.789, -1, 2.078, 0, 2.367, 0, 1, 2.7, 0, 3.033, 0.332, 3.367, -1, 1, 3.878, -3.042, 4.389, -10, 4.9, -10, 1, 5.467, -10, 6.033, -10, 6.6, -10, 1, 7.067, -10, 7.533, -10, 8, -10, 1, 8.167, -10, 8.333, 1.682, 8.5, 2, 1, 8.967, 2.89, 9.433, 3, 9.9, 3, 0, 10.133, 3]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.5, 0, 1, -8, 1.5, -8, 1, 1.789, -8, 2.078, 0, 2.367, 0, 1, 2.7, 0, 3.033, -8, 3.367, -8, 1, 3.878, -8, 4.389, -4, 4.9, -4, 1, 5.467, -4, 6.033, -11, 6.6, -11, 1, 7.067, -11, 7.533, -11, 8, -11, 1, 8.167, -11, 8.333, -1.318, 8.5, -1, 1, 8.967, -0.111, 9.433, 0, 9.9, 0, 0, 10.133, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.778, 0, 1.556, 1, 2.333, 1, 1, 3.189, 1, 4.044, -30, 4.9, -30, 1, 5.9, -30, 6.9, -30, 7.9, -30, 1, 8.089, -30, 8.278, 2, 8.467, 2, 0, 10.133, 2]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.511, 0, 1.022, 1, 1.533, 1, 1, 2.067, 1, 2.6, 0, 3.133, 0, 1, 3.7, 0, 4.267, 1, 4.833, 1, 1, 5.422, 1, 6.011, 0, 6.6, 0, 1, 7.144, 0, 7.689, 1, 8.233, 1, 1, 8.789, 1, 9.344, 0.3, 9.9, 0.3, 0, 10.133, 0.3]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 5.649, 0.267, 5.649, 1, 0.511, 5.649, 0.756, -4.076, 1, -4.076, 1, 1.078, -4.076, 1.156, 0.275, 1.233, 0.275, 1, 1.322, 0.275, 1.411, -1.697, 1.5, -1.697, 1, 1.667, -1.697, 1.833, 1.394, 2, 1.394, 1, 2.078, 1.394, 2.156, 0.339, 2.233, 0.339, 1, 2.3, 0.339, 2.367, 3.776, 2.433, 3.776, 1, 2.611, 3.776, 2.789, -1.707, 2.967, -1.707, 1, 3.056, -1.707, 3.144, 0.47, 3.233, 0.47, 1, 3.3, 0.47, 3.367, 0.035, 3.433, 0.035, 1, 3.6, 0.035, 3.767, 2.319, 3.933, 2.319, 1, 4.144, 2.319, 4.356, -2.097, 4.567, -2.097, 1, 4.644, -2.097, 4.722, 0.873, 4.8, 0.873, 1, 4.889, 0.873, 4.978, -0.364, 5.067, -0.364, 1, 5.144, -0.364, 5.222, 0.152, 5.3, 0.152, 1, 5.389, 0.152, 5.478, -0.063, 5.567, -0.063, 1, 5.644, -0.063, 5.722, 0.027, 5.8, 0.027, 1, 5.889, 0.027, 5.978, -0.011, 6.067, -0.011, 1, 6.144, -0.011, 6.222, 0.005, 6.3, 0.005, 1, 6.389, 0.005, 6.478, -0.002, 6.567, -0.002, 1, 6.644, -0.002, 6.722, 0.001, 6.8, 0.001, 1, 6.889, 0.001, 6.978, 0, 7.067, 0, 1, 7.144, 0, 7.222, 0, 7.3, 0, 1, 7.367, 0, 7.433, -30, 7.5, -30, 1, 7.522, -30, 7.544, -30, 7.567, -30, 1, 7.611, -30, 7.656, 30, 7.7, 30, 1, 7.711, 30, 7.722, 30, 7.733, 30, 1, 7.8, 30, 7.867, -11.018, 7.933, -11.018, 1, 7.956, -11.018, 7.978, 30, 8, 30, 1, 8.022, 30, 8.044, 30, 8.067, 30, 1, 8.089, 30, 8.111, -30, 8.133, -30, 1, 8.156, -30, 8.178, -30, 8.2, -30, 1, 8.244, -30, 8.289, 30, 8.333, 30, 1, 8.344, 30, 8.356, 30, 8.367, 30, 1, 8.444, 30, 8.522, -12.538, 8.6, -12.538, 1, 8.689, -12.538, 8.778, 5.151, 8.867, 5.151, 1, 8.944, 5.151, 9.022, -2.147, 9.1, -2.147, 1, 9.189, -2.147, 9.278, 0.893, 9.367, 0.893, 1, 9.444, 0.893, 9.522, -0.374, 9.6, -0.374, 1, 9.689, -0.374, 9.778, 0.155, 9.867, 0.155, 1, 9.944, 0.155, 10.022, -0.065, 10.1, -0.065, 1, 10.111, -0.065, 10.122, -0.064, 10.133, -0.061]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 5.649, 0.267, 5.649, 1, 0.511, 5.649, 0.756, -4.076, 1, -4.076, 1, 1.078, -4.076, 1.156, 0.275, 1.233, 0.275, 1, 1.322, 0.275, 1.411, -1.697, 1.5, -1.697, 1, 1.667, -1.697, 1.833, 1.394, 2, 1.394, 1, 2.078, 1.394, 2.156, 0.339, 2.233, 0.339, 1, 2.3, 0.339, 2.367, 3.776, 2.433, 3.776, 1, 2.611, 3.776, 2.789, -1.707, 2.967, -1.707, 1, 3.056, -1.707, 3.144, 0.47, 3.233, 0.47, 1, 3.3, 0.47, 3.367, 0.035, 3.433, 0.035, 1, 3.6, 0.035, 3.767, 2.319, 3.933, 2.319, 1, 4.144, 2.319, 4.356, -2.097, 4.567, -2.097, 1, 4.644, -2.097, 4.722, 0.873, 4.8, 0.873, 1, 4.889, 0.873, 4.978, -0.364, 5.067, -0.364, 1, 5.144, -0.364, 5.222, 0.152, 5.3, 0.152, 1, 5.389, 0.152, 5.478, -0.063, 5.567, -0.063, 1, 5.644, -0.063, 5.722, 0.027, 5.8, 0.027, 1, 5.889, 0.027, 5.978, -0.011, 6.067, -0.011, 1, 6.144, -0.011, 6.222, 0.005, 6.3, 0.005, 1, 6.389, 0.005, 6.478, -0.002, 6.567, -0.002, 1, 6.644, -0.002, 6.722, 0.001, 6.8, 0.001, 1, 6.889, 0.001, 6.978, 0, 7.067, 0, 1, 7.144, 0, 7.222, 0, 7.3, 0, 1, 7.367, 0, 7.433, -30, 7.5, -30, 1, 7.522, -30, 7.544, -30, 7.567, -30, 1, 7.611, -30, 7.656, 30, 7.7, 30, 1, 7.711, 30, 7.722, 30, 7.733, 30, 1, 7.8, 30, 7.867, -11.018, 7.933, -11.018, 1, 7.956, -11.018, 7.978, 30, 8, 30, 1, 8.022, 30, 8.044, 30, 8.067, 30, 1, 8.089, 30, 8.111, -30, 8.133, -30, 1, 8.156, -30, 8.178, -30, 8.2, -30, 1, 8.244, -30, 8.289, 30, 8.333, 30, 1, 8.344, 30, 8.356, 30, 8.367, 30, 1, 8.444, 30, 8.522, -12.538, 8.6, -12.538, 1, 8.689, -12.538, 8.778, 5.151, 8.867, 5.151, 1, 8.944, 5.151, 9.022, -2.147, 9.1, -2.147, 1, 9.189, -2.147, 9.278, 0.893, 9.367, 0.893, 1, 9.444, 0.893, 9.522, -0.374, 9.6, -0.374, 1, 9.689, -0.374, 9.778, 0.155, 9.867, 0.155, 1, 9.944, 0.155, 10.022, -0.065, 10.1, -0.065, 1, 10.111, -0.065, 10.122, -0.064, 10.133, -0.061]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.311, 1, 0.622, 0.4, 0.933, 0.4, 1, 1.189, 0.4, 1.444, 0.481, 1.7, 0.6, 1, 1.867, 0.678, 2.033, 0.7, 2.2, 0.7, 1, 2.433, 0.7, 2.667, 0.448, 2.9, 0.4, 1, 3.167, 0.345, 3.433, 0.357, 3.7, 0.3, 1, 3.967, 0.243, 4.233, 0, 4.5, 0, 1, 5.467, 0, 6.433, 0, 7.4, 0, 1, 7.444, 0, 7.489, 1, 7.533, 1, 1, 7.656, 1, 7.778, 1, 7.9, 1, 1, 7.944, 1, 7.989, 0, 8.033, 0, 1, 8.078, 0, 8.122, 1, 8.167, 1, 0, 10.133, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.311, 1, 0.622, 0.4, 0.933, 0.4, 1, 1.189, 0.4, 1.444, 0.481, 1.7, 0.6, 1, 1.867, 0.678, 2.033, 0.7, 2.2, 0.7, 1, 2.433, 0.7, 2.667, 0.448, 2.9, 0.4, 1, 3.167, 0.345, 3.433, 0.357, 3.7, 0.3, 1, 3.967, 0.243, 4.233, 0, 4.5, 0, 1, 5.467, 0, 6.433, 0, 7.4, 0, 1, 7.444, 0, 7.489, 1, 7.533, 1, 1, 7.656, 1, 7.778, 1, 7.9, 1, 1, 7.944, 1, 7.989, 0, 8.033, 0, 1, 8.078, 0, 8.122, 1, 8.167, 1, 0, 10.133, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.4, 1, 1.278, -0.4, 2.556, -0.3, 3.833, -0.3, 1, 4.167, -0.3, 4.5, -0.4, 4.833, -0.4, 1, 5.133, -0.4, 5.433, -0.4, 5.733, -0.4, 1, 6.167, -0.4, 6.6, -0.4, 7.033, -0.4, 1, 7.344, -0.4, 7.656, -0.4, 7.967, -0.4, 1, 8.044, -0.4, 8.122, -0.2, 8.2, -0.2, 1, 8.5, -0.2, 8.8, -0.2, 9.1, -0.2, 0, 10.133, -0.2]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 1.278, 0, 2.556, 0.136, 3.833, 0.4, 1, 4.167, 0.469, 4.5, 0.5, 4.833, 0.5, 1, 5.133, 0.5, 5.433, 0.3, 5.733, 0.3, 1, 6.167, 0.3, 6.6, 0.7, 7.033, 0.7, 1, 7.344, 0.7, 7.656, 0.7, 7.967, 0.7, 1, 8.044, 0.7, 8.122, 0, 8.2, 0, 1, 8.5, 0, 8.8, 0, 9.1, 0, 0, 10.133, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 10.133, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 1, 1, 1.022, 1, 2.044, -6, 3.067, -6, 1, 4.411, -6, 5.756, 11, 7.1, 11, 0, 10.133, 11]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.167, 0, 0.3, 2.905, 0.433, 2.905, 1, 0.644, 2.905, 0.856, -0.959, 1.067, -0.959, 1, 1.144, -0.959, 1.222, -0.646, 1.3, -0.646, 1, 1.444, -0.646, 1.589, -1.252, 1.733, -1.252, 1, 2.011, -1.252, 2.289, 1.91, 2.567, 1.91, 1, 2.733, 1.91, 2.9, -0.082, 3.067, -0.082, 1, 3.389, -0.082, 3.711, 1.535, 4.033, 1.535, 1, 4.222, 1.535, 4.411, -0.448, 4.6, -0.448, 1, 4.711, -0.448, 4.822, 0.088, 4.933, 0.088, 1, 5.033, 0.088, 5.133, -0.017, 5.233, -0.017, 1, 5.344, -0.017, 5.456, 0.003, 5.567, 0.003, 1, 5.667, 0.003, 5.767, -0.001, 5.867, -0.001, 1, 5.967, -0.001, 6.067, 0, 6.167, 0, 1, 6.278, 0, 6.389, 0, 6.5, 0, 1, 6.511, 0, 6.522, 0, 6.533, 0, 1, 6.622, 0, 6.711, 0, 6.8, 0, 1, 6.811, 0, 6.822, 0, 6.833, 0, 1, 6.933, 0, 7.033, 0, 7.133, 0, 1, 7.233, 0, 7.333, 0, 7.433, 0, 1, 7.478, 0, 7.522, -19.504, 7.567, -19.504, 1, 7.656, -19.504, 7.744, 3.605, 7.833, 3.605, 1, 7.867, 3.605, 7.9, 1.945, 7.933, 1.945, 1, 7.967, 1.945, 8, 19.462, 8.033, 19.462, 1, 8.089, 19.462, 8.144, -16.847, 8.2, -16.847, 1, 8.278, -16.847, 8.356, 3.954, 8.433, 3.954, 1, 8.533, 3.954, 8.633, -0.75, 8.733, -0.75, 1, 8.844, -0.75, 8.956, 0.146, 9.067, 0.146, 1, 9.167, 0.146, 9.267, -0.028, 9.367, -0.028, 1, 9.478, -0.028, 9.589, 0.005, 9.7, 0.005, 1, 9.8, 0.005, 9.9, -0.001, 10, -0.001, 1, 10.044, -0.001, 10.089, -0.001, 10.133, -0.001]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0.053, 1, 0.044, 0.053, 0.089, 0.145, 0.133, 0.145, 1, 0.2, 0.145, 0.267, 0, 0.333, 0, 1, 1.356, 0, 2.378, 0, 3.4, 0, 1, 3.489, 0, 3.578, 0.595, 3.667, 0.595, 1, 4.1, 0.595, 4.533, -5.869, 4.967, -5.869, 1, 5.111, -5.869, 5.256, -5.018, 5.4, -5.018, 1, 5.544, -5.018, 5.689, -5.31, 5.833, -5.31, 1, 5.967, -5.31, 6.1, -5.211, 6.233, -5.211, 1, 6.378, -5.211, 6.522, -5.245, 6.667, -5.245, 1, 6.8, -5.245, 6.933, -5.233, 7.067, -5.233, 1, 7.211, -5.233, 7.356, -5.237, 7.5, -5.237, 1, 7.633, -5.237, 7.767, -5.236, 7.9, -5.236, 1, 8.033, -5.236, 8.167, -5.236, 8.3, -5.236, 1, 8.311, -5.236, 8.322, -5.236, 8.333, -5.236, 1, 8.456, -5.236, 8.578, -5.236, 8.7, -5.236, 1, 8.733, -5.236, 8.767, -5.236, 8.8, -5.236, 1, 8.889, -5.236, 8.978, -5.236, 9.067, -5.236, 1, 9.078, -5.236, 9.089, -5.236, 9.1, -5.236, 1, 9.111, -5.236, 9.122, -5.236, 9.133, -5.236, 1, 9.144, -5.236, 9.156, -5.236, 9.167, -5.236, 1, 9.178, -5.236, 9.189, -5.236, 9.2, -5.236, 1, 9.211, -5.236, 9.222, -5.236, 9.233, -5.236, 1, 9.322, -5.236, 9.411, -5.236, 9.5, -5.236, 1, 9.533, -5.236, 9.567, -5.236, 9.6, -5.236, 1, 9.633, -5.236, 9.667, -5.236, 9.7, -5.236, 1, 9.711, -5.236, 9.722, -5.236, 9.733, -5.236, 1, 9.778, -5.236, 9.822, -5.236, 9.867, -5.236, 1, 9.878, -5.236, 9.889, -5.236, 9.9, -5.236, 1, 9.911, -5.236, 9.922, -5.236, 9.933, -5.236, 1, 9.967, -5.236, 10, -5.236, 10.033, -5.236, 1, 10.044, -5.236, 10.056, -5.236, 10.067, -5.236, 1, 10.078, -5.236, 10.089, -5.236, 10.1, -5.236, 1, 10.111, -5.236, 10.122, -5.236, 10.133, -5.236]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0.053, 1, 0.044, 0.053, 0.089, 0.145, 0.133, 0.145, 1, 0.2, 0.145, 0.267, 0, 0.333, 0, 1, 1.356, 0, 2.378, 0, 3.4, 0, 1, 3.489, 0, 3.578, 0.595, 3.667, 0.595, 1, 4.1, 0.595, 4.533, -5.869, 4.967, -5.869, 1, 5.111, -5.869, 5.256, -5.018, 5.4, -5.018, 1, 5.544, -5.018, 5.689, -5.31, 5.833, -5.31, 1, 5.967, -5.31, 6.1, -5.211, 6.233, -5.211, 1, 6.378, -5.211, 6.522, -5.245, 6.667, -5.245, 1, 6.8, -5.245, 6.933, -5.233, 7.067, -5.233, 1, 7.211, -5.233, 7.356, -5.237, 7.5, -5.237, 1, 7.633, -5.237, 7.767, -5.236, 7.9, -5.236, 1, 8.033, -5.236, 8.167, -5.236, 8.3, -5.236, 1, 8.311, -5.236, 8.322, -5.236, 8.333, -5.236, 1, 8.456, -5.236, 8.578, -5.236, 8.7, -5.236, 1, 8.733, -5.236, 8.767, -5.236, 8.8, -5.236, 1, 8.889, -5.236, 8.978, -5.236, 9.067, -5.236, 1, 9.078, -5.236, 9.089, -5.236, 9.1, -5.236, 1, 9.111, -5.236, 9.122, -5.236, 9.133, -5.236, 1, 9.144, -5.236, 9.156, -5.236, 9.167, -5.236, 1, 9.178, -5.236, 9.189, -5.236, 9.2, -5.236, 1, 9.211, -5.236, 9.222, -5.236, 9.233, -5.236, 1, 9.322, -5.236, 9.411, -5.236, 9.5, -5.236, 1, 9.533, -5.236, 9.567, -5.236, 9.6, -5.236, 1, 9.633, -5.236, 9.667, -5.236, 9.7, -5.236, 1, 9.711, -5.236, 9.722, -5.236, 9.733, -5.236, 1, 9.778, -5.236, 9.822, -5.236, 9.867, -5.236, 1, 9.878, -5.236, 9.889, -5.236, 9.9, -5.236, 1, 9.911, -5.236, 9.922, -5.236, 9.933, -5.236, 1, 9.967, -5.236, 10, -5.236, 10.033, -5.236, 1, 10.044, -5.236, 10.056, -5.236, 10.067, -5.236, 1, 10.078, -5.236, 10.089, -5.236, 10.1, -5.236, 1, 10.111, -5.236, 10.122, -5.236, 10.133, -5.236]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0.053, 1, 0.044, 0.053, 0.089, 0.145, 0.133, 0.145, 1, 0.2, 0.145, 0.267, 0, 0.333, 0, 1, 1.356, 0, 2.378, 0, 3.4, 0, 1, 3.489, 0, 3.578, 0.595, 3.667, 0.595, 1, 4.1, 0.595, 4.533, -5.869, 4.967, -5.869, 1, 5.111, -5.869, 5.256, -5.018, 5.4, -5.018, 1, 5.544, -5.018, 5.689, -5.31, 5.833, -5.31, 1, 5.967, -5.31, 6.1, -5.211, 6.233, -5.211, 1, 6.378, -5.211, 6.522, -5.245, 6.667, -5.245, 1, 6.8, -5.245, 6.933, -5.233, 7.067, -5.233, 1, 7.211, -5.233, 7.356, -5.237, 7.5, -5.237, 1, 7.633, -5.237, 7.767, -5.236, 7.9, -5.236, 1, 8.033, -5.236, 8.167, -5.236, 8.3, -5.236, 1, 8.311, -5.236, 8.322, -5.236, 8.333, -5.236, 1, 8.456, -5.236, 8.578, -5.236, 8.7, -5.236, 1, 8.733, -5.236, 8.767, -5.236, 8.8, -5.236, 1, 8.889, -5.236, 8.978, -5.236, 9.067, -5.236, 1, 9.078, -5.236, 9.089, -5.236, 9.1, -5.236, 1, 9.111, -5.236, 9.122, -5.236, 9.133, -5.236, 1, 9.144, -5.236, 9.156, -5.236, 9.167, -5.236, 1, 9.178, -5.236, 9.189, -5.236, 9.2, -5.236, 1, 9.211, -5.236, 9.222, -5.236, 9.233, -5.236, 1, 9.322, -5.236, 9.411, -5.236, 9.5, -5.236, 1, 9.533, -5.236, 9.567, -5.236, 9.6, -5.236, 1, 9.633, -5.236, 9.667, -5.236, 9.7, -5.236, 1, 9.711, -5.236, 9.722, -5.236, 9.733, -5.236, 1, 9.778, -5.236, 9.822, -5.236, 9.867, -5.236, 1, 9.878, -5.236, 9.889, -5.236, 9.9, -5.236, 1, 9.911, -5.236, 9.922, -5.236, 9.933, -5.236, 1, 9.967, -5.236, 10, -5.236, 10.033, -5.236, 1, 10.044, -5.236, 10.056, -5.236, 10.067, -5.236, 1, 10.078, -5.236, 10.089, -5.236, 10.1, -5.236, 1, 10.111, -5.236, 10.122, -5.236, 10.133, -5.236]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.556, 0, 1.044, 0.128, 1.533, 0.128, 1, 1.644, 0.128, 1.756, 0.038, 1.867, 0.038, 1, 2.022, 0.038, 2.178, 0.273, 2.333, 0.273, 1, 3.211, 0.273, 4.089, -7.72, 4.967, -7.72, 1, 5.089, -7.72, 5.211, -7.194, 5.333, -7.194, 1, 5.444, -7.194, 5.556, -7.378, 5.667, -7.378, 1, 5.789, -7.378, 5.911, -7.314, 6.033, -7.314, 1, 6.144, -7.314, 6.256, -7.336, 6.367, -7.336, 1, 6.489, -7.336, 6.611, -7.328, 6.733, -7.328, 1, 6.856, -7.328, 6.978, -7.331, 7.1, -7.331, 1, 7.211, -7.331, 7.322, -7.33, 7.433, -7.33, 1, 7.444, -7.33, 7.456, -7.33, 7.467, -7.33, 1, 7.578, -7.33, 7.689, -7.33, 7.8, -7.33, 1, 7.889, -7.33, 7.978, -7.051, 8.067, -7.051, 1, 8.1, -7.051, 8.133, -7.247, 8.167, -7.247, 1, 8.311, -7.247, 8.456, 1.232, 8.6, 1.232, 1, 8.722, 1.232, 8.844, -2.834, 8.967, -2.834, 1, 9.089, -2.834, 9.211, -1.375, 9.333, -1.375, 1, 9.444, -1.375, 9.556, -1.874, 9.667, -1.874, 1, 9.789, -1.874, 9.911, -1.696, 10.033, -1.696, 1, 10.067, -1.696, 10.1, -1.702, 10.133, -1.71]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.556, 0, 1.044, 0.128, 1.533, 0.128, 1, 1.644, 0.128, 1.756, 0.038, 1.867, 0.038, 1, 2.022, 0.038, 2.178, 0.273, 2.333, 0.273, 1, 3.211, 0.273, 4.089, -7.72, 4.967, -7.72, 1, 5.089, -7.72, 5.211, -7.194, 5.333, -7.194, 1, 5.444, -7.194, 5.556, -7.378, 5.667, -7.378, 1, 5.789, -7.378, 5.911, -7.314, 6.033, -7.314, 1, 6.144, -7.314, 6.256, -7.336, 6.367, -7.336, 1, 6.489, -7.336, 6.611, -7.328, 6.733, -7.328, 1, 6.856, -7.328, 6.978, -7.331, 7.1, -7.331, 1, 7.211, -7.331, 7.322, -7.33, 7.433, -7.33, 1, 7.444, -7.33, 7.456, -7.33, 7.467, -7.33, 1, 7.578, -7.33, 7.689, -7.33, 7.8, -7.33, 1, 7.889, -7.33, 7.978, -7.051, 8.067, -7.051, 1, 8.1, -7.051, 8.133, -7.247, 8.167, -7.247, 1, 8.311, -7.247, 8.456, 1.232, 8.6, 1.232, 1, 8.722, 1.232, 8.844, -2.834, 8.967, -2.834, 1, 9.089, -2.834, 9.211, -1.375, 9.333, -1.375, 1, 9.444, -1.375, 9.556, -1.874, 9.667, -1.874, 1, 9.789, -1.874, 9.911, -1.696, 10.033, -1.696, 1, 10.067, -1.696, 10.1, -1.702, 10.133, -1.71]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0.001, 1, 0.4, 0.001, 0.8, -0.017, 1.2, -0.017, 1, 1.378, -0.017, 1.556, 0.043, 1.733, 0.043, 1, 1.856, 0.043, 1.978, -0.175, 2.1, -0.175, 1, 2.111, -0.175, 2.122, -0.174, 2.133, -0.174, 1, 2.167, -0.174, 2.2, -0.3, 2.233, -0.3, 1, 2.389, -0.3, 2.544, 0.532, 2.7, 0.532, 1, 2.789, 0.532, 2.878, 0.16, 2.967, 0.16, 1, 3.111, 0.16, 3.256, 0.6, 3.4, 0.6, 1, 3.444, 0.6, 3.489, 0.536, 3.533, 0.536, 1, 3.689, 0.536, 3.844, 1.553, 4, 1.553, 1, 4.389, 1.553, 4.778, -0.468, 5.167, -0.468, 1, 5.289, -0.468, 5.411, 0.225, 5.533, 0.225, 1, 5.644, 0.225, 5.756, -0.09, 5.867, -0.09, 1, 5.989, -0.09, 6.111, 0.033, 6.233, 0.033, 1, 6.344, 0.033, 6.456, -0.012, 6.567, -0.012, 1, 6.689, -0.012, 6.811, 0.004, 6.933, 0.004, 1, 7.044, 0.004, 7.156, -0.002, 7.267, -0.002, 1, 7.389, -0.002, 7.511, 0.001, 7.633, 0.001, 1, 7.778, 0.001, 7.922, -0.385, 8.067, -0.385, 1, 8.089, -0.385, 8.111, -0.283, 8.133, -0.283, 1, 8.244, -0.283, 8.356, -6.53, 8.467, -6.53, 1, 8.589, -6.53, 8.711, 4.152, 8.833, 4.152, 1, 8.944, 4.152, 9.056, -1.918, 9.167, -1.918, 1, 9.289, -1.918, 9.411, 0.697, 9.533, 0.697, 1, 9.644, 0.697, 9.756, -0.27, 9.867, -0.27, 1, 9.956, -0.27, 10.044, -0.034, 10.133, 0.06]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.556, 0, 1.044, 0.128, 1.533, 0.128, 1, 1.644, 0.128, 1.756, 0.038, 1.867, 0.038, 1, 2.022, 0.038, 2.178, 0.273, 2.333, 0.273, 1, 3.211, 0.273, 4.089, -7.72, 4.967, -7.72, 1, 5.089, -7.72, 5.211, -7.194, 5.333, -7.194, 1, 5.444, -7.194, 5.556, -7.378, 5.667, -7.378, 1, 5.789, -7.378, 5.911, -7.314, 6.033, -7.314, 1, 6.144, -7.314, 6.256, -7.336, 6.367, -7.336, 1, 6.489, -7.336, 6.611, -7.328, 6.733, -7.328, 1, 6.856, -7.328, 6.978, -7.331, 7.1, -7.331, 1, 7.211, -7.331, 7.322, -7.33, 7.433, -7.33, 1, 7.444, -7.33, 7.456, -7.33, 7.467, -7.33, 1, 7.578, -7.33, 7.689, -7.33, 7.8, -7.33, 1, 7.889, -7.33, 7.978, -7.051, 8.067, -7.051, 1, 8.1, -7.051, 8.133, -7.247, 8.167, -7.247, 1, 8.311, -7.247, 8.456, 1.232, 8.6, 1.232, 1, 8.722, 1.232, 8.844, -2.834, 8.967, -2.834, 1, 9.089, -2.834, 9.211, -1.375, 9.333, -1.375, 1, 9.444, -1.375, 9.556, -1.874, 9.667, -1.874, 1, 9.789, -1.874, 9.911, -1.696, 10.033, -1.696, 1, 10.067, -1.696, 10.1, -1.702, 10.133, -1.71]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.4, 0, 0.8, -0.012, 1.2, -0.012, 1, 1.378, -0.012, 1.556, 0.031, 1.733, 0.031, 1, 1.856, 0.031, 1.978, -0.125, 2.1, -0.125, 1, 2.111, -0.125, 2.122, -0.124, 2.133, -0.124, 1, 2.167, -0.124, 2.2, -0.214, 2.233, -0.214, 1, 2.389, -0.214, 2.544, 0.38, 2.7, 0.38, 1, 2.789, 0.38, 2.878, 0.114, 2.967, 0.114, 1, 3.111, 0.114, 3.256, 0.428, 3.4, 0.428, 1, 3.444, 0.428, 3.489, 0.383, 3.533, 0.383, 1, 3.689, 0.383, 3.844, 1.109, 4, 1.109, 1, 4.389, 1.109, 4.778, -0.334, 5.167, -0.334, 1, 5.289, -0.334, 5.411, 0.161, 5.533, 0.161, 1, 5.644, 0.161, 5.756, -0.065, 5.867, -0.065, 1, 5.989, -0.065, 6.111, 0.024, 6.233, 0.024, 1, 6.344, 0.024, 6.456, -0.009, 6.567, -0.009, 1, 6.689, -0.009, 6.811, 0.003, 6.933, 0.003, 1, 7.044, 0.003, 7.156, -0.001, 7.267, -0.001, 1, 7.389, -0.001, 7.511, 0, 7.633, 0, 1, 7.778, 0, 7.922, -0.275, 8.067, -0.275, 1, 8.089, -0.275, 8.111, -0.202, 8.133, -0.202, 1, 8.244, -0.202, 8.356, -4.664, 8.467, -4.664, 1, 8.589, -4.664, 8.711, 2.966, 8.833, 2.966, 1, 8.944, 2.966, 9.056, -1.37, 9.167, -1.37, 1, 9.289, -1.37, 9.411, 0.498, 9.533, 0.498, 1, 9.644, 0.498, 9.756, -0.193, 9.867, -0.193, 1, 9.956, -0.193, 10.044, -0.025, 10.133, 0.043]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.556, 0, 1.044, 0.128, 1.533, 0.128, 1, 1.644, 0.128, 1.756, 0.038, 1.867, 0.038, 1, 2.022, 0.038, 2.178, 0.273, 2.333, 0.273, 1, 3.211, 0.273, 4.089, -7.72, 4.967, -7.72, 1, 5.089, -7.72, 5.211, -7.194, 5.333, -7.194, 1, 5.444, -7.194, 5.556, -7.378, 5.667, -7.378, 1, 5.789, -7.378, 5.911, -7.314, 6.033, -7.314, 1, 6.144, -7.314, 6.256, -7.336, 6.367, -7.336, 1, 6.489, -7.336, 6.611, -7.328, 6.733, -7.328, 1, 6.856, -7.328, 6.978, -7.331, 7.1, -7.331, 1, 7.211, -7.331, 7.322, -7.33, 7.433, -7.33, 1, 7.444, -7.33, 7.456, -7.33, 7.467, -7.33, 1, 7.578, -7.33, 7.689, -7.33, 7.8, -7.33, 1, 7.889, -7.33, 7.978, -7.051, 8.067, -7.051, 1, 8.1, -7.051, 8.133, -7.247, 8.167, -7.247, 1, 8.311, -7.247, 8.456, 1.232, 8.6, 1.232, 1, 8.722, 1.232, 8.844, -2.834, 8.967, -2.834, 1, 9.089, -2.834, 9.211, -1.375, 9.333, -1.375, 1, 9.444, -1.375, 9.556, -1.874, 9.667, -1.874, 1, 9.789, -1.874, 9.911, -1.696, 10.033, -1.696, 1, 10.067, -1.696, 10.1, -1.702, 10.133, -1.71]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.4, 0, 0.8, -0.012, 1.2, -0.012, 1, 1.378, -0.012, 1.556, 0.031, 1.733, 0.031, 1, 1.856, 0.031, 1.978, -0.125, 2.1, -0.125, 1, 2.111, -0.125, 2.122, -0.124, 2.133, -0.124, 1, 2.167, -0.124, 2.2, -0.214, 2.233, -0.214, 1, 2.389, -0.214, 2.544, 0.38, 2.7, 0.38, 1, 2.789, 0.38, 2.878, 0.114, 2.967, 0.114, 1, 3.111, 0.114, 3.256, 0.428, 3.4, 0.428, 1, 3.444, 0.428, 3.489, 0.383, 3.533, 0.383, 1, 3.689, 0.383, 3.844, 1.109, 4, 1.109, 1, 4.389, 1.109, 4.778, -0.334, 5.167, -0.334, 1, 5.289, -0.334, 5.411, 0.161, 5.533, 0.161, 1, 5.644, 0.161, 5.756, -0.065, 5.867, -0.065, 1, 5.989, -0.065, 6.111, 0.024, 6.233, 0.024, 1, 6.344, 0.024, 6.456, -0.009, 6.567, -0.009, 1, 6.689, -0.009, 6.811, 0.003, 6.933, 0.003, 1, 7.044, 0.003, 7.156, -0.001, 7.267, -0.001, 1, 7.389, -0.001, 7.511, 0, 7.633, 0, 1, 7.778, 0, 7.922, -0.275, 8.067, -0.275, 1, 8.089, -0.275, 8.111, -0.202, 8.133, -0.202, 1, 8.244, -0.202, 8.356, -4.664, 8.467, -4.664, 1, 8.589, -4.664, 8.711, 2.966, 8.833, 2.966, 1, 8.944, 2.966, 9.056, -1.37, 9.167, -1.37, 1, 9.289, -1.37, 9.411, 0.498, 9.533, 0.498, 1, 9.644, 0.498, 9.756, -0.193, 9.867, -0.193, 1, 9.956, -0.193, 10.044, -0.025, 10.133, 0.043]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.556, 0, 1.044, 0.153, 1.533, 0.153, 1, 1.644, 0.153, 1.756, 0.045, 1.867, 0.045, 1, 2.022, 0.045, 2.178, 0.328, 2.333, 0.328, 1, 3.211, 0.328, 4.089, -9.264, 4.967, -9.264, 1, 5.089, -9.264, 5.211, -8.633, 5.333, -8.633, 1, 5.444, -8.633, 5.556, -8.854, 5.667, -8.854, 1, 5.789, -8.854, 5.911, -8.776, 6.033, -8.776, 1, 6.144, -8.776, 6.256, -8.804, 6.367, -8.804, 1, 6.489, -8.804, 6.611, -8.794, 6.733, -8.794, 1, 6.856, -8.794, 6.978, -8.797, 7.1, -8.797, 1, 7.211, -8.797, 7.322, -8.796, 7.433, -8.796, 1, 7.444, -8.796, 7.456, -8.796, 7.467, -8.796, 1, 7.578, -8.796, 7.689, -8.797, 7.8, -8.797, 1, 7.889, -8.797, 7.978, -8.462, 8.067, -8.462, 1, 8.1, -8.462, 8.133, -8.697, 8.167, -8.697, 1, 8.311, -8.697, 8.456, 1.479, 8.6, 1.479, 1, 8.722, 1.479, 8.844, -3.401, 8.967, -3.401, 1, 9.089, -3.401, 9.211, -1.65, 9.333, -1.65, 1, 9.444, -1.65, 9.556, -2.249, 9.667, -2.249, 1, 9.789, -2.249, 9.911, -2.035, 10.033, -2.035, 1, 10.067, -2.035, 10.1, -2.042, 10.133, -2.053]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0.001, 1, 0.4, 0.001, 0.8, -0.015, 1.2, -0.015, 1, 1.378, -0.015, 1.556, 0.037, 1.733, 0.037, 1, 1.856, 0.037, 1.978, -0.15, 2.1, -0.15, 1, 2.111, -0.15, 2.122, -0.149, 2.133, -0.149, 1, 2.167, -0.149, 2.2, -0.257, 2.233, -0.257, 1, 2.389, -0.257, 2.544, 0.456, 2.7, 0.456, 1, 2.789, 0.456, 2.878, 0.137, 2.967, 0.137, 1, 3.111, 0.137, 3.256, 0.514, 3.4, 0.514, 1, 3.444, 0.514, 3.489, 0.459, 3.533, 0.459, 1, 3.689, 0.459, 3.844, 1.331, 4, 1.331, 1, 4.389, 1.331, 4.778, -0.401, 5.167, -0.401, 1, 5.289, -0.401, 5.411, 0.193, 5.533, 0.193, 1, 5.644, 0.193, 5.756, -0.077, 5.867, -0.077, 1, 5.989, -0.077, 6.111, 0.029, 6.233, 0.029, 1, 6.344, 0.029, 6.456, -0.01, 6.567, -0.01, 1, 6.689, -0.01, 6.811, 0.004, 6.933, 0.004, 1, 7.044, 0.004, 7.156, -0.001, 7.267, -0.001, 1, 7.389, -0.001, 7.511, 0, 7.633, 0, 1, 7.778, 0, 7.922, -0.33, 8.067, -0.33, 1, 8.089, -0.33, 8.111, -0.243, 8.133, -0.243, 1, 8.244, -0.243, 8.356, -5.597, 8.467, -5.597, 1, 8.589, -5.597, 8.711, 3.559, 8.833, 3.559, 1, 8.944, 3.559, 9.056, -1.644, 9.167, -1.644, 1, 9.289, -1.644, 9.411, 0.597, 9.533, 0.597, 1, 9.644, 0.597, 9.756, -0.231, 9.867, -0.231, 1, 9.956, -0.231, 10.044, -0.029, 10.133, 0.051]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.556, 0, 1.044, 0.153, 1.533, 0.153, 1, 1.644, 0.153, 1.756, 0.045, 1.867, 0.045, 1, 2.022, 0.045, 2.178, 0.328, 2.333, 0.328, 1, 3.211, 0.328, 4.089, -9.264, 4.967, -9.264, 1, 5.089, -9.264, 5.211, -8.633, 5.333, -8.633, 1, 5.444, -8.633, 5.556, -8.854, 5.667, -8.854, 1, 5.789, -8.854, 5.911, -8.776, 6.033, -8.776, 1, 6.144, -8.776, 6.256, -8.804, 6.367, -8.804, 1, 6.489, -8.804, 6.611, -8.794, 6.733, -8.794, 1, 6.856, -8.794, 6.978, -8.797, 7.1, -8.797, 1, 7.211, -8.797, 7.322, -8.796, 7.433, -8.796, 1, 7.444, -8.796, 7.456, -8.796, 7.467, -8.796, 1, 7.578, -8.796, 7.689, -8.797, 7.8, -8.797, 1, 7.889, -8.797, 7.978, -8.462, 8.067, -8.462, 1, 8.1, -8.462, 8.133, -8.697, 8.167, -8.697, 1, 8.311, -8.697, 8.456, 1.479, 8.6, 1.479, 1, 8.722, 1.479, 8.844, -3.401, 8.967, -3.401, 1, 9.089, -3.401, 9.211, -1.65, 9.333, -1.65, 1, 9.444, -1.65, 9.556, -2.249, 9.667, -2.249, 1, 9.789, -2.249, 9.911, -2.035, 10.033, -2.035, 1, 10.067, -2.035, 10.1, -2.042, 10.133, -2.053]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0.001, 1, 0.4, 0.001, 0.8, -0.015, 1.2, -0.015, 1, 1.378, -0.015, 1.556, 0.037, 1.733, 0.037, 1, 1.856, 0.037, 1.978, -0.15, 2.1, -0.15, 1, 2.111, -0.15, 2.122, -0.149, 2.133, -0.149, 1, 2.167, -0.149, 2.2, -0.257, 2.233, -0.257, 1, 2.389, -0.257, 2.544, 0.456, 2.7, 0.456, 1, 2.789, 0.456, 2.878, 0.137, 2.967, 0.137, 1, 3.111, 0.137, 3.256, 0.514, 3.4, 0.514, 1, 3.444, 0.514, 3.489, 0.459, 3.533, 0.459, 1, 3.689, 0.459, 3.844, 1.331, 4, 1.331, 1, 4.389, 1.331, 4.778, -0.401, 5.167, -0.401, 1, 5.289, -0.401, 5.411, 0.193, 5.533, 0.193, 1, 5.644, 0.193, 5.756, -0.077, 5.867, -0.077, 1, 5.989, -0.077, 6.111, 0.029, 6.233, 0.029, 1, 6.344, 0.029, 6.456, -0.01, 6.567, -0.01, 1, 6.689, -0.01, 6.811, 0.004, 6.933, 0.004, 1, 7.044, 0.004, 7.156, -0.001, 7.267, -0.001, 1, 7.389, -0.001, 7.511, 0, 7.633, 0, 1, 7.778, 0, 7.922, -0.33, 8.067, -0.33, 1, 8.089, -0.33, 8.111, -0.243, 8.133, -0.243, 1, 8.244, -0.243, 8.356, -5.597, 8.467, -5.597, 1, 8.589, -5.597, 8.711, 3.559, 8.833, 3.559, 1, 8.944, 3.559, 9.056, -1.644, 9.167, -1.644, 1, 9.289, -1.644, 9.411, 0.597, 9.533, 0.597, 1, 9.644, 0.597, 9.756, -0.231, 9.867, -0.231, 1, 9.956, -0.231, 10.044, -0.029, 10.133, 0.051]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.167, 0, 0.3, 0.968, 0.433, 0.968, 1, 0.644, 0.968, 0.856, -0.32, 1.067, -0.32, 1, 1.144, -0.32, 1.222, -0.215, 1.3, -0.215, 1, 1.444, -0.215, 1.589, -0.417, 1.733, -0.417, 1, 2.011, -0.417, 2.289, 0.637, 2.567, 0.637, 1, 2.733, 0.637, 2.9, -0.027, 3.067, -0.027, 1, 3.389, -0.027, 3.711, 0.512, 4.033, 0.512, 1, 4.222, 0.512, 4.411, -0.149, 4.6, -0.149, 1, 4.711, -0.149, 4.822, 0.029, 4.933, 0.029, 1, 5.033, 0.029, 5.133, -0.006, 5.233, -0.006, 1, 5.344, -0.006, 5.456, 0.001, 5.567, 0.001, 1, 5.667, 0.001, 5.767, 0, 5.867, 0, 1, 5.967, 0, 6.067, 0, 6.167, 0, 1, 6.278, 0, 6.389, 0, 6.5, 0, 1, 6.511, 0, 6.522, 0, 6.533, 0, 1, 6.622, 0, 6.711, 0, 6.8, 0, 1, 6.811, 0, 6.822, 0, 6.833, 0, 1, 6.933, 0, 7.033, 0, 7.133, 0, 1, 7.233, 0, 7.333, 0, 7.433, 0, 1, 7.478, 0, 7.522, -6.501, 7.567, -6.501, 1, 7.656, -6.501, 7.744, 1.202, 7.833, 1.202, 1, 7.867, 1.202, 7.9, 0.648, 7.933, 0.648, 1, 7.967, 0.648, 8, 6.487, 8.033, 6.487, 1, 8.089, 6.487, 8.144, -5.616, 8.2, -5.616, 1, 8.278, -5.616, 8.356, 1.318, 8.433, 1.318, 1, 8.533, 1.318, 8.633, -0.25, 8.733, -0.25, 1, 8.844, -0.25, 8.956, 0.049, 9.067, 0.049, 1, 9.167, 0.049, 9.267, -0.009, 9.367, -0.009, 1, 9.478, -0.009, 9.589, 0.002, 9.7, 0.002, 1, 9.8, 0.002, 9.9, 0, 10, 0, 1, 10.044, 0, 10.089, 0, 10.133, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.556, 0, 1.044, 0.102, 1.533, 0.102, 1, 1.644, 0.102, 1.756, 0.03, 1.867, 0.03, 1, 2.022, 0.03, 2.178, 0.219, 2.333, 0.219, 1, 3.211, 0.219, 4.089, -6.176, 4.967, -6.176, 1, 5.089, -6.176, 5.211, -5.756, 5.333, -5.756, 1, 5.444, -5.756, 5.556, -5.903, 5.667, -5.903, 1, 5.789, -5.903, 5.911, -5.851, 6.033, -5.851, 1, 6.144, -5.851, 6.256, -5.869, 6.367, -5.869, 1, 6.489, -5.869, 6.611, -5.863, 6.733, -5.863, 1, 6.856, -5.863, 6.978, -5.865, 7.1, -5.865, 1, 7.211, -5.865, 7.322, -5.864, 7.433, -5.864, 1, 7.444, -5.864, 7.456, -5.864, 7.467, -5.864, 1, 7.578, -5.864, 7.689, -5.864, 7.8, -5.864, 1, 7.889, -5.864, 7.978, -5.641, 8.067, -5.641, 1, 8.1, -5.641, 8.133, -5.798, 8.167, -5.798, 1, 8.311, -5.798, 8.456, 0.986, 8.6, 0.986, 1, 8.722, 0.986, 8.844, -2.267, 8.967, -2.267, 1, 9.089, -2.267, 9.211, -1.1, 9.333, -1.1, 1, 9.444, -1.1, 9.556, -1.499, 9.667, -1.499, 1, 9.789, -1.499, 9.911, -1.357, 10.033, -1.357, 1, 10.067, -1.357, 10.1, -1.362, 10.133, -1.368]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.4, 0, 0.8, -0.01, 1.2, -0.01, 1, 1.378, -0.01, 1.556, 0.025, 1.733, 0.025, 1, 1.856, 0.025, 1.978, -0.1, 2.1, -0.1, 1, 2.111, -0.1, 2.122, -0.1, 2.133, -0.1, 1, 2.167, -0.1, 2.2, -0.171, 2.233, -0.171, 1, 2.389, -0.171, 2.544, 0.304, 2.7, 0.304, 1, 2.789, 0.304, 2.878, 0.091, 2.967, 0.091, 1, 3.111, 0.091, 3.256, 0.343, 3.4, 0.343, 1, 3.444, 0.343, 3.489, 0.306, 3.533, 0.306, 1, 3.689, 0.306, 3.844, 0.887, 4, 0.887, 1, 4.389, 0.887, 4.778, -0.267, 5.167, -0.267, 1, 5.289, -0.267, 5.411, 0.129, 5.533, 0.129, 1, 5.644, 0.129, 5.756, -0.052, 5.867, -0.052, 1, 5.989, -0.052, 6.111, 0.019, 6.233, 0.019, 1, 6.344, 0.019, 6.456, -0.007, 6.567, -0.007, 1, 6.689, -0.007, 6.811, 0.002, 6.933, 0.002, 1, 7.044, 0.002, 7.156, -0.001, 7.267, -0.001, 1, 7.389, -0.001, 7.511, 0, 7.633, 0, 1, 7.778, 0, 7.922, -0.22, 8.067, -0.22, 1, 8.089, -0.22, 8.111, -0.162, 8.133, -0.162, 1, 8.244, -0.162, 8.356, -3.731, 8.467, -3.731, 1, 8.589, -3.731, 8.711, 2.373, 8.833, 2.373, 1, 8.944, 2.373, 9.056, -1.096, 9.167, -1.096, 1, 9.289, -1.096, 9.411, 0.398, 9.533, 0.398, 1, 9.644, 0.398, 9.756, -0.154, 9.867, -0.154, 1, 9.956, -0.154, 10.044, -0.02, 10.133, 0.034]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.556, 0, 1.044, 0.205, 1.533, 0.205, 1, 1.644, 0.205, 1.756, 0.06, 1.867, 0.06, 1, 2.022, 0.06, 2.178, 0.437, 2.333, 0.437, 1, 3.211, 0.437, 4.089, -12.352, 4.967, -12.352, 1, 5.089, -12.352, 5.211, -11.511, 5.333, -11.511, 1, 5.444, -11.511, 5.556, -11.805, 5.667, -11.805, 1, 5.789, -11.805, 5.911, -11.702, 6.033, -11.702, 1, 6.144, -11.702, 6.256, -11.738, 6.367, -11.738, 1, 6.489, -11.738, 6.611, -11.725, 6.733, -11.725, 1, 6.856, -11.725, 6.978, -11.73, 7.1, -11.73, 1, 7.211, -11.73, 7.322, -11.728, 7.433, -11.728, 1, 7.444, -11.728, 7.456, -11.728, 7.467, -11.728, 1, 7.578, -11.728, 7.689, -11.729, 7.8, -11.729, 1, 7.889, -11.729, 7.978, -11.282, 8.067, -11.282, 1, 8.1, -11.282, 8.133, -11.596, 8.167, -11.596, 1, 8.311, -11.596, 8.456, 1.971, 8.6, 1.971, 1, 8.722, 1.971, 8.844, -4.534, 8.967, -4.534, 1, 9.089, -4.534, 9.211, -2.2, 9.333, -2.2, 1, 9.444, -2.2, 9.556, -2.999, 9.667, -2.999, 1, 9.789, -2.999, 9.911, -2.714, 10.033, -2.714, 1, 10.067, -2.714, 10.1, -2.723, 10.133, -2.737]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.511, 0, 1.022, 0.038, 1.533, 0.038, 1, 1.667, 0.038, 1.8, 0.001, 1.933, 0.001, 1, 2.089, 0.001, 2.244, 0.082, 2.4, 0.082, 1, 3.267, 0.082, 4.133, -2.309, 5, -2.309, 1, 5.133, -2.309, 5.267, -2.173, 5.4, -2.173, 1, 5.533, -2.173, 5.667, -2.205, 5.8, -2.205, 1, 5.933, -2.205, 6.067, -2.198, 6.2, -2.198, 1, 6.322, -2.198, 6.444, -2.199, 6.567, -2.199, 1, 6.7, -2.199, 6.833, -2.199, 6.967, -2.199, 1, 7.089, -2.199, 7.211, -2.199, 7.333, -2.199, 1, 7.344, -2.199, 7.356, -2.199, 7.367, -2.199, 1, 7.478, -2.199, 7.589, -2.199, 7.7, -2.199, 1, 7.733, -2.199, 7.767, -2.199, 7.8, -2.199, 1, 7.844, -2.199, 7.889, -2.199, 7.933, -2.199, 1, 7.978, -2.199, 8.022, -2.125, 8.067, -2.125, 1, 8.111, -2.125, 8.156, -2.266, 8.2, -2.266, 1, 8.356, -2.266, 8.511, 0.143, 8.667, 0.143, 1, 8.8, 0.143, 8.933, -0.701, 9.067, -0.701, 1, 9.2, -0.701, 9.333, -0.487, 9.467, -0.487, 1, 9.589, -0.487, 9.711, -0.532, 9.833, -0.532, 1, 9.933, -0.532, 10.033, -0.525, 10.133, -0.522]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.267, 0, 0.4, -0.002, 0.533, -0.002, 1, 0.667, -0.002, 0.8, -0.001, 0.933, -0.001, 1, 1.022, -0.001, 1.111, -0.001, 1.2, -0.001, 1, 1.389, -0.001, 1.578, 0.019, 1.767, 0.019, 1, 1.889, 0.019, 2.011, -0.008, 2.133, -0.008, 1, 2.144, -0.008, 2.156, 0.004, 2.167, 0.004, 1, 2.2, 0.004, 2.233, -0.015, 2.267, -0.015, 1, 2.422, -0.015, 2.578, 0.079, 2.733, 0.079, 1, 2.833, 0.079, 2.933, -0.02, 3.033, -0.02, 1, 3.144, -0.02, 3.256, 0.019, 3.367, 0.019, 1, 3.456, 0.019, 3.544, -0.045, 3.633, -0.045, 1, 3.733, -0.045, 3.833, 0.006, 3.933, 0.006, 1, 4.067, 0.006, 4.2, -0.076, 4.333, -0.076, 1, 4.533, -0.076, 4.733, -0.03, 4.933, -0.03, 1, 5.011, -0.03, 5.089, -0.085, 5.167, -0.085, 1, 5.289, -0.085, 5.411, 0.064, 5.533, 0.064, 1, 5.656, 0.064, 5.778, -0.031, 5.9, -0.031, 1, 6.033, -0.031, 6.167, 0.013, 6.3, 0.013, 1, 6.422, 0.013, 6.544, -0.005, 6.667, -0.005, 1, 6.8, -0.005, 6.933, 0.002, 7.067, 0.002, 1, 7.2, 0.002, 7.333, -0.001, 7.467, -0.001, 1, 7.589, -0.001, 7.711, 0, 7.833, 0, 1, 7.911, 0, 7.989, -0.054, 8.067, -0.054, 1, 8.111, -0.054, 8.156, 0.14, 8.2, 0.14, 1, 8.311, 0.14, 8.422, -0.46, 8.533, -0.46, 1, 8.633, -0.46, 8.733, 0.877, 8.833, 0.877, 1, 8.956, 0.877, 9.078, -0.513, 9.2, -0.513, 1, 9.322, -0.513, 9.444, 0.235, 9.567, 0.235, 1, 9.7, 0.235, 9.833, -0.094, 9.967, -0.094, 1, 10.022, -0.094, 10.078, -0.067, 10.133, -0.038]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.007, 1, 0.111, -0.007, 0.222, 0.001, 0.333, 0.001, 1, 0.467, 0.001, 0.6, -0.004, 0.733, -0.004, 1, 0.856, -0.004, 0.978, -0.001, 1.1, -0.001, 1, 1.211, -0.001, 1.322, -0.002, 1.433, -0.002, 1, 1.578, -0.002, 1.722, 0.011, 1.867, 0.011, 1, 2, 0.011, 2.133, -0.091, 2.267, -0.091, 1, 2.444, -0.091, 2.622, 0.119, 2.8, 0.119, 1, 2.922, 0.119, 3.044, -0.01, 3.167, -0.01, 1, 3.289, -0.01, 3.411, 0.122, 3.533, 0.122, 1, 3.6, 0.122, 3.667, 0.1, 3.733, 0.1, 1, 3.856, 0.1, 3.978, 0.233, 4.1, 0.233, 1, 4.256, 0.233, 4.411, 0.04, 4.567, 0.04, 1, 4.622, 0.04, 4.678, 0.054, 4.733, 0.054, 1, 4.911, 0.054, 5.089, -0.156, 5.267, -0.156, 1, 5.4, -0.156, 5.533, 0.146, 5.667, 0.146, 1, 5.8, 0.146, 5.933, -0.093, 6.067, -0.093, 1, 6.189, -0.093, 6.311, 0.05, 6.433, 0.05, 1, 6.567, 0.05, 6.7, -0.024, 6.833, -0.024, 1, 6.956, -0.024, 7.078, 0.011, 7.2, 0.011, 1, 7.333, 0.011, 7.467, -0.005, 7.6, -0.005, 1, 7.711, -0.005, 7.822, 0.002, 7.933, 0.002, 1, 8.144, 0.002, 8.356, -1.066, 8.567, -1.066, 1, 8.7, -1.066, 8.833, 1.663, 8.967, 1.663, 1, 9.089, 1.663, 9.211, -1.299, 9.333, -1.299, 1, 9.467, -1.299, 9.6, 0.767, 9.733, 0.767, 1, 9.856, 0.767, 9.978, -0.39, 10.1, -0.39, 1, 10.111, -0.39, 10.122, -0.386, 10.133, -0.378]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0.001, 1, 0.056, 0.001, 0.111, -0.028, 0.167, -0.028, 1, 0.289, -0.028, 0.411, 0.011, 0.533, 0.011, 1, 0.656, 0.011, 0.778, -0.008, 0.9, -0.008, 1, 1.022, -0.008, 1.144, 0.002, 1.267, 0.002, 1, 1.378, 0.002, 1.489, -0.003, 1.6, -0.003, 1, 1.733, -0.003, 1.867, 0.021, 2, 0.021, 1, 2.044, 0.021, 2.089, 0.007, 2.133, 0.007, 1, 2.156, 0.007, 2.178, 0.021, 2.2, 0.021, 1, 2.289, 0.021, 2.378, -0.098, 2.467, -0.098, 1, 2.611, -0.098, 2.756, 0.149, 2.9, 0.149, 1, 3.044, 0.149, 3.189, -0.081, 3.333, -0.081, 1, 3.444, -0.081, 3.556, 0.103, 3.667, 0.103, 1, 3.756, 0.103, 3.844, 0.026, 3.933, 0.026, 1, 4.044, 0.026, 4.156, 0.17, 4.267, 0.17, 1, 4.4, 0.17, 4.533, -0.042, 4.667, -0.042, 1, 4.778, -0.042, 4.889, 0.07, 5, 0.07, 1, 5.133, 0.07, 5.267, -0.187, 5.4, -0.187, 1, 5.533, -0.187, 5.667, 0.215, 5.8, 0.215, 1, 5.933, 0.215, 6.067, -0.171, 6.2, -0.171, 1, 6.333, -0.171, 6.467, 0.11, 6.6, 0.11, 1, 6.722, 0.11, 6.844, -0.063, 6.967, -0.063, 1, 7.1, -0.063, 7.233, 0.033, 7.367, 0.033, 1, 7.5, 0.033, 7.633, -0.016, 7.767, -0.016, 1, 7.933, -0.016, 8.1, 0.089, 8.267, 0.089, 1, 8.422, 0.089, 8.578, -1.236, 8.733, -1.236, 1, 8.856, -1.236, 8.978, 2.15, 9.1, 2.15, 1, 9.233, 2.15, 9.367, -2.085, 9.5, -2.085, 1, 9.622, -2.085, 9.744, 1.498, 9.867, 1.498, 1, 9.956, 1.498, 10.044, 0.42, 10.133, -0.299]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.511, 0, 1.022, 0.038, 1.533, 0.038, 1, 1.667, 0.038, 1.8, 0.001, 1.933, 0.001, 1, 2.089, 0.001, 2.244, 0.082, 2.4, 0.082, 1, 3.267, 0.082, 4.133, -2.309, 5, -2.309, 1, 5.133, -2.309, 5.267, -2.173, 5.4, -2.173, 1, 5.533, -2.173, 5.667, -2.205, 5.8, -2.205, 1, 5.933, -2.205, 6.067, -2.198, 6.2, -2.198, 1, 6.322, -2.198, 6.444, -2.199, 6.567, -2.199, 1, 6.7, -2.199, 6.833, -2.199, 6.967, -2.199, 1, 7.089, -2.199, 7.211, -2.199, 7.333, -2.199, 1, 7.344, -2.199, 7.356, -2.199, 7.367, -2.199, 1, 7.478, -2.199, 7.589, -2.199, 7.7, -2.199, 1, 7.733, -2.199, 7.767, -2.199, 7.8, -2.199, 1, 7.844, -2.199, 7.889, -2.199, 7.933, -2.199, 1, 7.978, -2.199, 8.022, -2.125, 8.067, -2.125, 1, 8.111, -2.125, 8.156, -2.266, 8.2, -2.266, 1, 8.356, -2.266, 8.511, 0.143, 8.667, 0.143, 1, 8.8, 0.143, 8.933, -0.701, 9.067, -0.701, 1, 9.2, -0.701, 9.333, -0.487, 9.467, -0.487, 1, 9.589, -0.487, 9.711, -0.532, 9.833, -0.532, 1, 9.933, -0.532, 10.033, -0.525, 10.133, -0.522]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.267, 0, 0.4, -0.002, 0.533, -0.002, 1, 0.667, -0.002, 0.8, -0.001, 0.933, -0.001, 1, 1.022, -0.001, 1.111, -0.001, 1.2, -0.001, 1, 1.389, -0.001, 1.578, 0.019, 1.767, 0.019, 1, 1.889, 0.019, 2.011, -0.008, 2.133, -0.008, 1, 2.144, -0.008, 2.156, 0.004, 2.167, 0.004, 1, 2.2, 0.004, 2.233, -0.015, 2.267, -0.015, 1, 2.422, -0.015, 2.578, 0.079, 2.733, 0.079, 1, 2.833, 0.079, 2.933, -0.02, 3.033, -0.02, 1, 3.144, -0.02, 3.256, 0.019, 3.367, 0.019, 1, 3.456, 0.019, 3.544, -0.045, 3.633, -0.045, 1, 3.733, -0.045, 3.833, 0.006, 3.933, 0.006, 1, 4.067, 0.006, 4.2, -0.076, 4.333, -0.076, 1, 4.533, -0.076, 4.733, -0.03, 4.933, -0.03, 1, 5.011, -0.03, 5.089, -0.085, 5.167, -0.085, 1, 5.289, -0.085, 5.411, 0.064, 5.533, 0.064, 1, 5.656, 0.064, 5.778, -0.031, 5.9, -0.031, 1, 6.033, -0.031, 6.167, 0.013, 6.3, 0.013, 1, 6.422, 0.013, 6.544, -0.005, 6.667, -0.005, 1, 6.8, -0.005, 6.933, 0.002, 7.067, 0.002, 1, 7.2, 0.002, 7.333, -0.001, 7.467, -0.001, 1, 7.589, -0.001, 7.711, 0, 7.833, 0, 1, 7.911, 0, 7.989, -0.054, 8.067, -0.054, 1, 8.111, -0.054, 8.156, 0.14, 8.2, 0.14, 1, 8.311, 0.14, 8.422, -0.46, 8.533, -0.46, 1, 8.633, -0.46, 8.733, 0.877, 8.833, 0.877, 1, 8.956, 0.877, 9.078, -0.513, 9.2, -0.513, 1, 9.322, -0.513, 9.444, 0.235, 9.567, 0.235, 1, 9.7, 0.235, 9.833, -0.094, 9.967, -0.094, 1, 10.022, -0.094, 10.078, -0.067, 10.133, -0.038]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.007, 1, 0.111, -0.007, 0.222, 0.001, 0.333, 0.001, 1, 0.467, 0.001, 0.6, -0.004, 0.733, -0.004, 1, 0.856, -0.004, 0.978, -0.001, 1.1, -0.001, 1, 1.211, -0.001, 1.322, -0.002, 1.433, -0.002, 1, 1.578, -0.002, 1.722, 0.011, 1.867, 0.011, 1, 2, 0.011, 2.133, -0.091, 2.267, -0.091, 1, 2.444, -0.091, 2.622, 0.119, 2.8, 0.119, 1, 2.922, 0.119, 3.044, -0.01, 3.167, -0.01, 1, 3.289, -0.01, 3.411, 0.122, 3.533, 0.122, 1, 3.6, 0.122, 3.667, 0.1, 3.733, 0.1, 1, 3.856, 0.1, 3.978, 0.233, 4.1, 0.233, 1, 4.256, 0.233, 4.411, 0.04, 4.567, 0.04, 1, 4.622, 0.04, 4.678, 0.054, 4.733, 0.054, 1, 4.911, 0.054, 5.089, -0.156, 5.267, -0.156, 1, 5.4, -0.156, 5.533, 0.146, 5.667, 0.146, 1, 5.8, 0.146, 5.933, -0.093, 6.067, -0.093, 1, 6.189, -0.093, 6.311, 0.05, 6.433, 0.05, 1, 6.567, 0.05, 6.7, -0.024, 6.833, -0.024, 1, 6.956, -0.024, 7.078, 0.011, 7.2, 0.011, 1, 7.333, 0.011, 7.467, -0.005, 7.6, -0.005, 1, 7.711, -0.005, 7.822, 0.002, 7.933, 0.002, 1, 8.144, 0.002, 8.356, -1.066, 8.567, -1.066, 1, 8.7, -1.066, 8.833, 1.663, 8.967, 1.663, 1, 9.089, 1.663, 9.211, -1.299, 9.333, -1.299, 1, 9.467, -1.299, 9.6, 0.767, 9.733, 0.767, 1, 9.856, 0.767, 9.978, -0.39, 10.1, -0.39, 1, 10.111, -0.39, 10.122, -0.386, 10.133, -0.378]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0.001, 1, 0.056, 0.001, 0.111, -0.028, 0.167, -0.028, 1, 0.289, -0.028, 0.411, 0.011, 0.533, 0.011, 1, 0.656, 0.011, 0.778, -0.008, 0.9, -0.008, 1, 1.022, -0.008, 1.144, 0.002, 1.267, 0.002, 1, 1.378, 0.002, 1.489, -0.003, 1.6, -0.003, 1, 1.733, -0.003, 1.867, 0.021, 2, 0.021, 1, 2.044, 0.021, 2.089, 0.007, 2.133, 0.007, 1, 2.156, 0.007, 2.178, 0.021, 2.2, 0.021, 1, 2.289, 0.021, 2.378, -0.098, 2.467, -0.098, 1, 2.611, -0.098, 2.756, 0.149, 2.9, 0.149, 1, 3.044, 0.149, 3.189, -0.081, 3.333, -0.081, 1, 3.444, -0.081, 3.556, 0.103, 3.667, 0.103, 1, 3.756, 0.103, 3.844, 0.026, 3.933, 0.026, 1, 4.044, 0.026, 4.156, 0.17, 4.267, 0.17, 1, 4.4, 0.17, 4.533, -0.042, 4.667, -0.042, 1, 4.778, -0.042, 4.889, 0.07, 5, 0.07, 1, 5.133, 0.07, 5.267, -0.187, 5.4, -0.187, 1, 5.533, -0.187, 5.667, 0.215, 5.8, 0.215, 1, 5.933, 0.215, 6.067, -0.171, 6.2, -0.171, 1, 6.333, -0.171, 6.467, 0.11, 6.6, 0.11, 1, 6.722, 0.11, 6.844, -0.063, 6.967, -0.063, 1, 7.1, -0.063, 7.233, 0.033, 7.367, 0.033, 1, 7.5, 0.033, 7.633, -0.016, 7.767, -0.016, 1, 7.933, -0.016, 8.1, 0.089, 8.267, 0.089, 1, 8.422, 0.089, 8.578, -1.236, 8.733, -1.236, 1, 8.856, -1.236, 8.978, 2.15, 9.1, 2.15, 1, 9.233, 2.15, 9.367, -2.085, 9.5, -2.085, 1, 9.622, -2.085, 9.744, 1.498, 9.867, 1.498, 1, 9.956, 1.498, 10.044, 0.42, 10.133, -0.299]}]}