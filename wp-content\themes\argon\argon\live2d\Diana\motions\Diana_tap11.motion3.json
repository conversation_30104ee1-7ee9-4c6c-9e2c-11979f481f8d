{"Version": 3, "Meta": {"Duration": 4.867, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 48, "TotalSegmentCount": 496, "TotalPointCount": 1488, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param43", "Segments": [0, -0.001, 1, 0.078, -0.001, 0.156, 1.261, 0.233, 1.261, 1, 0.367, 1.261, 0.5, -3.232, 0.633, -3.232, 1, 0.867, -3.232, 1.1, 0.406, 1.333, 0.406, 1, 1.422, 0.406, 1.511, -0.065, 1.6, -0.065, 1, 1.7, -0.065, 1.8, 0.338, 1.9, 0.338, 1, 2.078, 0.338, 2.256, -0.6, 2.433, -0.6, 1, 2.522, -0.6, 2.611, -0.5, 2.7, -0.5, 1, 2.756, -0.5, 2.811, -0.545, 2.867, -0.545, 1, 3.144, -0.545, 3.422, 1.064, 3.7, 1.064, 1, 3.756, 1.064, 3.811, 0.967, 3.867, 0.967, 1, 3.989, 0.967, 4.111, 1.617, 4.233, 1.617, 1, 4.389, 1.617, 4.544, 0.028, 4.7, 0.028, 1, 4.744, 0.028, 4.789, 0.155, 4.833, 0.155, 1, 4.844, 0.155, 4.856, 0.148, 4.867, 0.135]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, -30, 0, 0.4, -30, 1, 0.556, -30, 0.711, 30, 0.867, 30, 1, 1.644, 30, 2.422, 30, 3.2, 30, 1, 3.456, 30, 3.711, -30, 3.967, -30, 0, 4.867, -30]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -30, 0, 0.867, -30, 1, 1.022, -30, 1.178, 30, 1.333, 30, 1, 1.511, 30, 1.689, -30, 1.867, -30, 1, 2.033, -30, 2.2, 30, 2.367, 30, 1, 2.533, 30, 2.7, -30, 2.867, -30, 1, 3.056, -30, 3.244, 30, 3.433, 30, 0, 4.867, 30]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, -30, 0, 0.5, -30, 1, 0.644, -30, 0.789, 30, 0.933, 30, 1, 1.767, 30, 2.6, 30, 3.433, 30, 1, 3.544, 30, 3.656, -30, 3.767, -30, 0, 4.867, -30]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -30, 0, 0.267, -30, 1, 0.367, -30, 0.467, 30, 0.567, 30, 1, 0.733, 30, 0.9, 30, 1.067, 30, 1, 1.156, 30, 1.244, 6, 1.333, 6, 1, 1.433, 6, 1.533, 30, 1.633, 30, 1, 1.711, 30, 1.789, 6, 1.867, 6, 1, 1.967, 6, 2.067, 30, 2.167, 30, 1, 2.278, 30, 2.389, 6, 2.5, 6, 1, 2.6, 6, 2.7, 30, 2.8, 30, 1, 3.244, 30, 3.689, 30, 4.133, 30, 1, 4.322, 30, 4.511, -30, 4.7, -30, 0, 4.867, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.311, 0, 0.622, 10, 0.933, 10, 1, 1.278, 10, 1.622, 5, 1.967, 5, 1, 2.311, 5, 2.656, 10, 3, 10, 1, 3.456, 10, 3.911, 0, 4.367, 0, 0, 4.867, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.178, 0, 0.356, -10, 0.533, -10, 1, 0.678, -10, 0.822, 7, 0.967, 7, 1, 1.222, 7, 1.478, 0, 1.733, 0, 0, 4.867, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -1, 1, 0.311, -1, 0.622, 13.153, 0.933, 18, 1, 1.211, 22.328, 1.489, 22, 1.767, 22, 1, 2.4, 22, 3.033, 21.439, 3.667, 16, 1, 4.022, 12.947, 4.378, 2, 4.733, 2, 0, 4.867, 2]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -11, 1, 0.311, -11, 0.622, 1, 0.933, 1, 1, 1.211, 1, 1.489, -5, 1.767, -5, 1, 2.4, -5, 3.033, 12, 3.667, 12, 1, 4.022, 12, 4.378, -5, 4.733, -5, 0, 4.867, -5]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 1, 1, 0.311, 1, 0.622, -9, 0.933, -9, 1, 1.411, -9, 1.889, 11, 2.367, 11, 1, 3.156, 11, 3.944, -6, 4.733, -6, 0, 4.867, -6]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.022, 0, 1.044, 30, 1.067, 30, 1, 1.1, 30, 1.133, 30, 1.167, 30, 1, 1.189, 30, 1.211, -30, 1.233, -30, 1, 1.267, -30, 1.3, -30, 1.333, -30, 1, 1.389, -30, 1.444, 30, 1.5, 30, 1, 1.578, 30, 1.656, -11.795, 1.733, -11.795, 1, 1.822, -11.795, 1.911, 4.885, 2, 4.885, 1, 2.078, 4.885, 2.156, -2.024, 2.233, -2.024, 1, 2.322, -2.024, 2.411, 0.847, 2.5, 0.847, 1, 2.522, 0.847, 2.544, 0.538, 2.567, 0.538, 1, 2.589, 0.538, 2.611, 30, 2.633, 30, 1, 2.667, 30, 2.7, 30, 2.733, 30, 1, 2.756, 30, 2.778, -30, 2.8, -30, 1, 2.844, -30, 2.889, -30, 2.933, -30, 1, 2.956, -30, 2.978, 30, 3, 30, 1, 3.033, 30, 3.067, 30, 3.1, 30, 1, 3.122, 30, 3.144, -30, 3.167, -30, 1, 3.2, -30, 3.233, -30, 3.267, -30, 1, 3.311, -30, 3.356, 30, 3.4, 30, 1, 3.411, 30, 3.422, 30, 3.433, 30, 1, 3.511, 30, 3.589, -12.232, 3.667, -12.232, 1, 3.756, -12.232, 3.844, 4.99, 3.933, 4.99, 1, 4.011, 4.99, 4.089, -2.096, 4.167, -2.096, 1, 4.256, -2.096, 4.344, 0.865, 4.433, 0.865, 1, 4.511, 0.865, 4.589, -0.365, 4.667, -0.365, 1, 4.733, -0.365, 4.8, -0.075, 4.867, 0.07]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.022, 0, 1.044, 30, 1.067, 30, 1, 1.1, 30, 1.133, 30, 1.167, 30, 1, 1.189, 30, 1.211, -30, 1.233, -30, 1, 1.267, -30, 1.3, -30, 1.333, -30, 1, 1.389, -30, 1.444, 30, 1.5, 30, 1, 1.578, 30, 1.656, -11.795, 1.733, -11.795, 1, 1.822, -11.795, 1.911, 4.885, 2, 4.885, 1, 2.078, 4.885, 2.156, -2.024, 2.233, -2.024, 1, 2.322, -2.024, 2.411, 0.847, 2.5, 0.847, 1, 2.522, 0.847, 2.544, 0.538, 2.567, 0.538, 1, 2.589, 0.538, 2.611, 30, 2.633, 30, 1, 2.667, 30, 2.7, 30, 2.733, 30, 1, 2.756, 30, 2.778, -30, 2.8, -30, 1, 2.844, -30, 2.889, -30, 2.933, -30, 1, 2.956, -30, 2.978, 30, 3, 30, 1, 3.033, 30, 3.067, 30, 3.1, 30, 1, 3.122, 30, 3.144, -30, 3.167, -30, 1, 3.2, -30, 3.233, -30, 3.267, -30, 1, 3.311, -30, 3.356, 30, 3.4, 30, 1, 3.411, 30, 3.422, 30, 3.433, 30, 1, 3.511, 30, 3.589, -12.232, 3.667, -12.232, 1, 3.756, -12.232, 3.844, 4.99, 3.933, 4.99, 1, 4.011, 4.99, 4.089, -2.096, 4.167, -2.096, 1, 4.256, -2.096, 4.344, 0.865, 4.433, 0.865, 1, 4.511, 0.865, 4.589, -0.365, 4.667, -0.365, 1, 4.733, -0.365, 4.8, -0.075, 4.867, 0.07]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.322, 1, 0.644, 1, 0.967, 1, 1, 1.022, 1, 1.078, 0, 1.133, 0, 1, 1.189, 0, 1.244, 1, 1.3, 1, 1, 1.711, 1, 2.122, 1, 2.533, 1, 1, 2.589, 1, 2.644, 0, 2.7, 0, 1, 2.767, 0, 2.833, 1, 2.9, 1, 1, 2.956, 1, 3.011, 0, 3.067, 0, 1, 3.122, 0, 3.178, 1, 3.233, 1, 0, 4.867, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.322, 1, 0.644, 1, 0.967, 1, 1, 1.022, 1, 1.078, 0, 1.133, 0, 1, 1.189, 0, 1.244, 1, 1.3, 1, 1, 1.711, 1, 2.122, 1, 2.533, 1, 1, 2.589, 1, 2.644, 0, 2.7, 0, 1, 2.767, 0, 2.833, 1, 2.9, 1, 1, 2.956, 1, 3.011, 0, 3.067, 0, 1, 3.122, 0, 3.178, 1, 3.233, 1, 0, 4.867, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 4.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.1, 0, 4.867, -0.1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0.1, 0, 0.267, 0.1, 1, 0.467, 0.1, 0.667, 1, 0.867, 1, 1, 1.744, 1, 2.622, 1, 3.5, 1, 1, 3.744, 1, 3.989, 1, 4.233, 1, 0, 4.867, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.267, 0, 1, 0.467, 0, 0.667, 1, 0.867, 1, 1, 1.744, 1, 2.622, 1, 3.5, 1, 1, 3.744, 1, 3.989, 0, 4.233, 0, 0, 4.867, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -30, 1, 0.378, -30, 0.756, 30, 1.133, 30, 0, 4.867, 30]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, -30, 1, 0.378, -30, 0.756, 30, 1.133, 30, 0, 4.867, 30]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -30, 1, 0.011, -30, 0.022, -30, 0.033, -30, 1, 0.422, -30, 0.811, 30, 1.2, 30, 0, 4.867, 30]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.044, 0, 1.089, 19.274, 1.133, 19.274, 1, 1.189, 19.274, 1.244, -17.159, 1.3, -17.159, 1, 1.389, -17.159, 1.478, 3.757, 1.567, 3.757, 1, 1.667, 3.757, 1.767, -0.711, 1.867, -0.711, 1, 1.978, -0.711, 2.089, 0.139, 2.2, 0.139, 1, 2.3, 0.139, 2.4, -0.027, 2.5, -0.027, 1, 2.567, -0.027, 2.633, 19.274, 2.7, 19.274, 1, 2.767, 19.274, 2.833, -17.257, 2.9, -17.257, 1, 2.956, -17.257, 3.011, 17.73, 3.067, 17.73, 1, 3.122, 17.73, 3.178, -17.179, 3.233, -17.179, 1, 3.322, -17.179, 3.411, 3.756, 3.5, 3.756, 1, 3.6, 3.756, 3.7, -0.711, 3.8, -0.711, 1, 3.911, -0.711, 4.022, 0.139, 4.133, 0.139, 1, 4.233, 0.139, 4.333, -0.027, 4.433, -0.027, 1, 4.544, -0.027, 4.656, 0.005, 4.767, 0.005, 1, 4.8, 0.005, 4.833, 0.004, 4.867, 0.004]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.167, 0, 0.3, -7.352, 0.433, -7.352, 1, 0.656, -7.352, 0.878, 4.601, 1.1, 4.601, 1, 1.5, 4.601, 1.9, -3.113, 2.3, -3.113, 1, 2.678, -3.113, 3.056, 3.748, 3.433, 3.748, 1, 3.778, 3.748, 4.122, -1.813, 4.467, -1.813, 1, 4.6, -1.813, 4.733, 0.262, 4.867, 0.581]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.167, 0, 0.3, -7.352, 0.433, -7.352, 1, 0.656, -7.352, 0.878, 4.601, 1.1, 4.601, 1, 1.5, 4.601, 1.9, -3.113, 2.3, -3.113, 1, 2.678, -3.113, 3.056, 3.748, 3.433, 3.748, 1, 3.778, 3.748, 4.122, -1.813, 4.467, -1.813, 1, 4.6, -1.813, 4.733, 0.262, 4.867, 0.581]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.167, 0, 0.3, -7.352, 0.433, -7.352, 1, 0.656, -7.352, 0.878, 4.601, 1.1, 4.601, 1, 1.5, 4.601, 1.9, -3.113, 2.3, -3.113, 1, 2.678, -3.113, 3.056, 3.748, 3.433, 3.748, 1, 3.778, 3.748, 4.122, -1.813, 4.467, -1.813, 1, 4.6, -1.813, 4.733, 0.262, 4.867, 0.581]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0.174, 1, 0.011, 0.174, 0.022, 0.175, 0.033, 0.175, 1, 0.167, 0.175, 0.3, -4.204, 0.433, -4.204, 1, 0.667, -4.204, 0.9, -0.221, 1.133, -0.221, 1, 1.189, -0.221, 1.244, -0.337, 1.3, -0.337, 1, 1.511, -0.337, 1.722, 1.014, 1.933, 1.014, 1, 1.978, 1.014, 2.022, 1.001, 2.067, 1.001, 1, 2.5, 1.001, 2.933, 3.233, 3.367, 3.233, 1, 3.478, 3.233, 3.589, 3.118, 3.7, 3.118, 1, 3.778, 3.118, 3.856, 3.361, 3.933, 3.361, 1, 4.122, 3.361, 4.311, 1.605, 4.5, 1.605, 1, 4.567, 1.605, 4.633, 1.78, 4.7, 1.78, 1, 4.744, 1.78, 4.789, 1.637, 4.833, 1.637, 1, 4.844, 1.637, 4.856, 1.64, 4.867, 1.646]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0.174, 1, 0.011, 0.174, 0.022, 0.175, 0.033, 0.175, 1, 0.167, 0.175, 0.3, -4.204, 0.433, -4.204, 1, 0.667, -4.204, 0.9, -0.221, 1.133, -0.221, 1, 1.189, -0.221, 1.244, -0.337, 1.3, -0.337, 1, 1.511, -0.337, 1.722, 1.014, 1.933, 1.014, 1, 1.978, 1.014, 2.022, 1.001, 2.067, 1.001, 1, 2.5, 1.001, 2.933, 3.233, 3.367, 3.233, 1, 3.478, 3.233, 3.589, 3.118, 3.7, 3.118, 1, 3.778, 3.118, 3.856, 3.361, 3.933, 3.361, 1, 4.122, 3.361, 4.311, 1.605, 4.5, 1.605, 1, 4.567, 1.605, 4.633, 1.78, 4.7, 1.78, 1, 4.744, 1.78, 4.789, 1.637, 4.833, 1.637, 1, 4.844, 1.637, 4.856, 1.64, 4.867, 1.646]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, -0.001, 1, 0.078, -0.001, 0.156, 1.765, 0.233, 1.765, 1, 0.367, 1.765, 0.5, -4.525, 0.633, -4.525, 1, 0.867, -4.525, 1.1, 0.568, 1.333, 0.568, 1, 1.422, 0.568, 1.511, -0.091, 1.6, -0.091, 1, 1.7, -0.091, 1.8, 0.473, 1.9, 0.473, 1, 2.078, 0.473, 2.256, -0.84, 2.433, -0.84, 1, 2.522, -0.84, 2.611, -0.7, 2.7, -0.7, 1, 2.756, -0.7, 2.811, -0.762, 2.867, -0.762, 1, 3.144, -0.762, 3.422, 1.49, 3.7, 1.49, 1, 3.756, 1.49, 3.811, 1.354, 3.867, 1.354, 1, 3.989, 1.354, 4.111, 2.264, 4.233, 2.264, 1, 4.389, 2.264, 4.544, 0.039, 4.7, 0.039, 1, 4.744, 0.039, 4.789, 0.217, 4.833, 0.217, 1, 4.844, 0.217, 4.856, 0.207, 4.867, 0.189]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0.174, 1, 0.011, 0.174, 0.022, 0.175, 0.033, 0.175, 1, 0.167, 0.175, 0.3, -4.204, 0.433, -4.204, 1, 0.667, -4.204, 0.9, -0.221, 1.133, -0.221, 1, 1.189, -0.221, 1.244, -0.337, 1.3, -0.337, 1, 1.511, -0.337, 1.722, 1.014, 1.933, 1.014, 1, 1.978, 1.014, 2.022, 1.001, 2.067, 1.001, 1, 2.5, 1.001, 2.933, 3.233, 3.367, 3.233, 1, 3.478, 3.233, 3.589, 3.118, 3.7, 3.118, 1, 3.778, 3.118, 3.856, 3.361, 3.933, 3.361, 1, 4.122, 3.361, 4.311, 1.605, 4.5, 1.605, 1, 4.567, 1.605, 4.633, 1.78, 4.7, 1.78, 1, 4.744, 1.78, 4.789, 1.637, 4.833, 1.637, 1, 4.844, 1.637, 4.856, 1.64, 4.867, 1.646]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, -0.001, 1, 0.078, -0.001, 0.156, 1.261, 0.233, 1.261, 1, 0.367, 1.261, 0.5, -3.232, 0.633, -3.232, 1, 0.867, -3.232, 1.1, 0.406, 1.333, 0.406, 1, 1.422, 0.406, 1.511, -0.065, 1.6, -0.065, 1, 1.7, -0.065, 1.8, 0.338, 1.9, 0.338, 1, 2.078, 0.338, 2.256, -0.6, 2.433, -0.6, 1, 2.522, -0.6, 2.611, -0.5, 2.7, -0.5, 1, 2.756, -0.5, 2.811, -0.545, 2.867, -0.545, 1, 3.144, -0.545, 3.422, 1.064, 3.7, 1.064, 1, 3.756, 1.064, 3.811, 0.967, 3.867, 0.967, 1, 3.989, 0.967, 4.111, 1.617, 4.233, 1.617, 1, 4.389, 1.617, 4.544, 0.028, 4.7, 0.028, 1, 4.744, 0.028, 4.789, 0.155, 4.833, 0.155, 1, 4.844, 0.155, 4.856, 0.148, 4.867, 0.135]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0.174, 1, 0.011, 0.174, 0.022, 0.175, 0.033, 0.175, 1, 0.167, 0.175, 0.3, -4.204, 0.433, -4.204, 1, 0.667, -4.204, 0.9, -0.221, 1.133, -0.221, 1, 1.189, -0.221, 1.244, -0.337, 1.3, -0.337, 1, 1.511, -0.337, 1.722, 1.014, 1.933, 1.014, 1, 1.978, 1.014, 2.022, 1.001, 2.067, 1.001, 1, 2.5, 1.001, 2.933, 3.233, 3.367, 3.233, 1, 3.478, 3.233, 3.589, 3.118, 3.7, 3.118, 1, 3.778, 3.118, 3.856, 3.361, 3.933, 3.361, 1, 4.122, 3.361, 4.311, 1.605, 4.5, 1.605, 1, 4.567, 1.605, 4.633, 1.78, 4.7, 1.78, 1, 4.744, 1.78, 4.789, 1.637, 4.833, 1.637, 1, 4.844, 1.637, 4.856, 1.64, 4.867, 1.646]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, -0.001, 1, 0.078, -0.001, 0.156, 1.261, 0.233, 1.261, 1, 0.367, 1.261, 0.5, -3.232, 0.633, -3.232, 1, 0.867, -3.232, 1.1, 0.406, 1.333, 0.406, 1, 1.422, 0.406, 1.511, -0.065, 1.6, -0.065, 1, 1.7, -0.065, 1.8, 0.338, 1.9, 0.338, 1, 2.078, 0.338, 2.256, -0.6, 2.433, -0.6, 1, 2.522, -0.6, 2.611, -0.5, 2.7, -0.5, 1, 2.756, -0.5, 2.811, -0.545, 2.867, -0.545, 1, 3.144, -0.545, 3.422, 1.064, 3.7, 1.064, 1, 3.756, 1.064, 3.811, 0.967, 3.867, 0.967, 1, 3.989, 0.967, 4.111, 1.617, 4.233, 1.617, 1, 4.389, 1.617, 4.544, 0.028, 4.7, 0.028, 1, 4.744, 0.028, 4.789, 0.155, 4.833, 0.155, 1, 4.844, 0.155, 4.856, 0.148, 4.867, 0.135]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0.209, 1, 0.011, 0.209, 0.022, 0.209, 0.033, 0.209, 1, 0.167, 0.209, 0.3, -5.045, 0.433, -5.045, 1, 0.667, -5.045, 0.9, -0.265, 1.133, -0.265, 1, 1.189, -0.265, 1.244, -0.404, 1.3, -0.404, 1, 1.511, -0.404, 1.722, 1.217, 1.933, 1.217, 1, 1.978, 1.217, 2.022, 1.201, 2.067, 1.201, 1, 2.5, 1.201, 2.933, 3.879, 3.367, 3.879, 1, 3.478, 3.879, 3.589, 3.742, 3.7, 3.742, 1, 3.778, 3.742, 3.856, 4.034, 3.933, 4.034, 1, 4.122, 4.034, 4.311, 1.926, 4.5, 1.926, 1, 4.567, 1.926, 4.633, 2.136, 4.7, 2.136, 1, 4.744, 2.136, 4.789, 1.965, 4.833, 1.965, 1, 4.844, 1.965, 4.856, 1.968, 4.867, 1.975]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, -0.001, 1, 0.078, -0.001, 0.156, 1.513, 0.233, 1.513, 1, 0.367, 1.513, 0.5, -3.879, 0.633, -3.879, 1, 0.867, -3.879, 1.1, 0.487, 1.333, 0.487, 1, 1.422, 0.487, 1.511, -0.078, 1.6, -0.078, 1, 1.7, -0.078, 1.8, 0.406, 1.9, 0.406, 1, 2.078, 0.406, 2.256, -0.72, 2.433, -0.72, 1, 2.522, -0.72, 2.611, -0.6, 2.7, -0.6, 1, 2.756, -0.6, 2.811, -0.653, 2.867, -0.653, 1, 3.144, -0.653, 3.422, 1.277, 3.7, 1.277, 1, 3.756, 1.277, 3.811, 1.16, 3.867, 1.16, 1, 3.989, 1.16, 4.111, 1.941, 4.233, 1.941, 1, 4.389, 1.941, 4.544, 0.033, 4.7, 0.033, 1, 4.744, 0.033, 4.789, 0.186, 4.833, 0.186, 1, 4.844, 0.186, 4.856, 0.177, 4.867, 0.162]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0.209, 1, 0.011, 0.209, 0.022, 0.209, 0.033, 0.209, 1, 0.167, 0.209, 0.3, -5.045, 0.433, -5.045, 1, 0.667, -5.045, 0.9, -0.265, 1.133, -0.265, 1, 1.189, -0.265, 1.244, -0.404, 1.3, -0.404, 1, 1.511, -0.404, 1.722, 1.217, 1.933, 1.217, 1, 1.978, 1.217, 2.022, 1.201, 2.067, 1.201, 1, 2.5, 1.201, 2.933, 3.879, 3.367, 3.879, 1, 3.478, 3.879, 3.589, 3.742, 3.7, 3.742, 1, 3.778, 3.742, 3.856, 4.034, 3.933, 4.034, 1, 4.122, 4.034, 4.311, 1.926, 4.5, 1.926, 1, 4.567, 1.926, 4.633, 2.136, 4.7, 2.136, 1, 4.744, 2.136, 4.789, 1.965, 4.833, 1.965, 1, 4.844, 1.965, 4.856, 1.968, 4.867, 1.975]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, -0.001, 1, 0.078, -0.001, 0.156, 1.513, 0.233, 1.513, 1, 0.367, 1.513, 0.5, -3.879, 0.633, -3.879, 1, 0.867, -3.879, 1.1, 0.487, 1.333, 0.487, 1, 1.422, 0.487, 1.511, -0.078, 1.6, -0.078, 1, 1.7, -0.078, 1.8, 0.406, 1.9, 0.406, 1, 2.078, 0.406, 2.256, -0.72, 2.433, -0.72, 1, 2.522, -0.72, 2.611, -0.6, 2.7, -0.6, 1, 2.756, -0.6, 2.811, -0.653, 2.867, -0.653, 1, 3.144, -0.653, 3.422, 1.277, 3.7, 1.277, 1, 3.756, 1.277, 3.811, 1.16, 3.867, 1.16, 1, 3.989, 1.16, 4.111, 1.941, 4.233, 1.941, 1, 4.389, 1.941, 4.544, 0.033, 4.7, 0.033, 1, 4.744, 0.033, 4.789, 0.186, 4.833, 0.186, 1, 4.844, 0.186, 4.856, 0.177, 4.867, 0.162]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.044, 0, 1.089, 6.425, 1.133, 6.425, 1, 1.189, 6.425, 1.244, -5.72, 1.3, -5.72, 1, 1.389, -5.72, 1.478, 1.252, 1.567, 1.252, 1, 1.667, 1.252, 1.767, -0.237, 1.867, -0.237, 1, 1.978, -0.237, 2.089, 0.046, 2.2, 0.046, 1, 2.3, 0.046, 2.4, -0.009, 2.5, -0.009, 1, 2.567, -0.009, 2.633, 6.425, 2.7, 6.425, 1, 2.767, 6.425, 2.833, -5.752, 2.9, -5.752, 1, 2.956, -5.752, 3.011, 5.91, 3.067, 5.91, 1, 3.122, 5.91, 3.178, -5.726, 3.233, -5.726, 1, 3.322, -5.726, 3.411, 1.252, 3.5, 1.252, 1, 3.6, 1.252, 3.7, -0.237, 3.8, -0.237, 1, 3.911, -0.237, 4.022, 0.046, 4.133, 0.046, 1, 4.233, 0.046, 4.333, -0.009, 4.433, -0.009, 1, 4.544, -0.009, 4.656, 0.002, 4.767, 0.002, 1, 4.8, 0.002, 4.833, 0.001, 4.867, 0.001]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0.14, 1, 0.011, 0.14, 0.022, 0.14, 0.033, 0.14, 1, 0.167, 0.14, 0.3, -3.363, 0.433, -3.363, 1, 0.667, -3.363, 0.9, -0.177, 1.133, -0.177, 1, 1.189, -0.177, 1.244, -0.27, 1.3, -0.27, 1, 1.511, -0.27, 1.722, 0.811, 1.933, 0.811, 1, 1.978, 0.811, 2.022, 0.801, 2.067, 0.801, 1, 2.5, 0.801, 2.933, 2.586, 3.367, 2.586, 1, 3.478, 2.586, 3.589, 2.495, 3.7, 2.495, 1, 3.778, 2.495, 3.856, 2.689, 3.933, 2.689, 1, 4.122, 2.689, 4.311, 1.284, 4.5, 1.284, 1, 4.567, 1.284, 4.633, 1.424, 4.7, 1.424, 1, 4.744, 1.424, 4.789, 1.31, 4.833, 1.31, 1, 4.844, 1.31, 4.856, 1.312, 4.867, 1.317]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.078, 0, 0.156, 1.009, 0.233, 1.009, 1, 0.367, 1.009, 0.5, -2.586, 0.633, -2.586, 1, 0.867, -2.586, 1.1, 0.325, 1.333, 0.325, 1, 1.422, 0.325, 1.511, -0.052, 1.6, -0.052, 1, 1.7, -0.052, 1.8, 0.27, 1.9, 0.27, 1, 2.078, 0.27, 2.256, -0.48, 2.433, -0.48, 1, 2.522, -0.48, 2.611, -0.4, 2.7, -0.4, 1, 2.756, -0.4, 2.811, -0.436, 2.867, -0.436, 1, 3.144, -0.436, 3.422, 0.851, 3.7, 0.851, 1, 3.756, 0.851, 3.811, 0.774, 3.867, 0.774, 1, 3.989, 0.774, 4.111, 1.294, 4.233, 1.294, 1, 4.389, 1.294, 4.544, 0.022, 4.7, 0.022, 1, 4.744, 0.022, 4.789, 0.124, 4.833, 0.124, 1, 4.844, 0.124, 4.856, 0.118, 4.867, 0.108]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0.279, 1, 0.011, 0.279, 0.022, 0.279, 0.033, 0.279, 1, 0.167, 0.279, 0.3, -6.726, 0.433, -6.726, 1, 0.667, -6.726, 0.9, -0.353, 1.133, -0.353, 1, 1.189, -0.353, 1.244, -0.539, 1.3, -0.539, 1, 1.511, -0.539, 1.722, 1.623, 1.933, 1.623, 1, 1.978, 1.623, 2.022, 1.601, 2.067, 1.601, 1, 2.5, 1.601, 2.933, 5.172, 3.367, 5.172, 1, 3.478, 5.172, 3.589, 4.989, 3.7, 4.989, 1, 3.778, 4.989, 3.856, 5.378, 3.933, 5.378, 1, 4.122, 5.378, 4.311, 2.568, 4.5, 2.568, 1, 4.567, 2.568, 4.633, 2.849, 4.7, 2.849, 1, 4.744, 2.849, 4.789, 2.619, 4.833, 2.619, 1, 4.844, 2.619, 4.856, 2.625, 4.867, 2.634]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0.052, 1, 0.167, 0.052, 0.333, -1.718, 0.5, -1.718, 1, 0.956, -1.718, 1.411, 0.376, 1.867, 0.376, 1, 2, 0.376, 2.133, 0.272, 2.267, 0.272, 1, 2.733, 0.272, 3.2, 1.198, 3.667, 1.198, 1, 3.678, 1.198, 3.689, 1.197, 3.7, 1.197, 1, 3.789, 1.197, 3.878, 1.286, 3.967, 1.286, 1, 4.267, 1.286, 4.567, 0.479, 4.867, 0.479]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.625, 0.3, 0.625, 1, 0.411, 0.625, 0.522, -0.11, 0.633, -0.11, 1, 0.722, -0.11, 0.811, 0.192, 0.9, 0.192, 1, 0.944, 0.192, 0.989, 0.142, 1.033, 0.142, 1, 1.089, 0.142, 1.144, 0.189, 1.2, 0.189, 1, 1.333, 0.189, 1.467, -0.208, 1.6, -0.208, 1, 1.756, -0.208, 1.911, 0.075, 2.067, 0.075, 1, 2.156, 0.075, 2.244, 0.007, 2.333, 0.007, 1, 2.444, 0.007, 2.556, 0.072, 2.667, 0.072, 1, 2.856, 0.072, 3.044, -0.102, 3.233, -0.102, 1, 3.3, -0.102, 3.367, -0.075, 3.433, -0.075, 1, 3.589, -0.075, 3.744, -0.268, 3.9, -0.268, 1, 4.067, -0.268, 4.233, -0.03, 4.4, -0.03, 1, 4.478, -0.03, 4.556, -0.184, 4.633, -0.184, 1, 4.711, -0.184, 4.789, -0.074, 4.867, -0.025]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0.008, 1, 0.122, 0.008, 0.244, 0.445, 0.367, 0.445, 1, 0.5, 0.445, 0.633, -1.116, 0.767, -1.116, 1, 0.933, -1.116, 1.1, 0.519, 1.267, 0.519, 1, 1.422, 0.519, 1.578, -0.304, 1.733, -0.304, 1, 1.867, -0.304, 2, 0.18, 2.133, 0.18, 1, 2.256, 0.18, 2.378, -0.225, 2.5, -0.225, 1, 2.633, -0.225, 2.767, 0.017, 2.9, 0.017, 1, 2.989, 0.017, 3.078, -0.049, 3.167, -0.049, 1, 3.322, -0.049, 3.478, 0.254, 3.633, 0.254, 1, 3.767, 0.254, 3.9, 0.004, 4.033, 0.004, 1, 4.156, 0.004, 4.278, 0.298, 4.4, 0.298, 1, 4.533, 0.298, 4.667, -0.279, 4.8, -0.279, 1, 4.822, -0.279, 4.844, -0.271, 4.867, -0.257]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, -0.02, 1, 0.178, -0.02, 0.356, 0.709, 0.533, 0.709, 1, 0.667, 0.709, 0.8, -1.295, 0.933, -1.295, 1, 1.078, -1.295, 1.222, 0.864, 1.367, 0.864, 1, 1.522, 0.864, 1.678, -0.567, 1.833, -0.567, 1, 1.978, -0.567, 2.122, 0.424, 2.267, 0.424, 1, 2.4, 0.424, 2.533, -0.332, 2.667, -0.332, 1, 2.789, -0.332, 2.911, 0.118, 3.033, 0.118, 1, 3.144, 0.118, 3.256, -0.177, 3.367, -0.177, 1, 3.5, -0.177, 3.633, 0.209, 3.767, 0.209, 1, 3.9, 0.209, 4.033, -0.184, 4.167, -0.184, 1, 4.3, -0.184, 4.433, 0.367, 4.567, 0.367, 1, 4.667, 0.367, 4.767, -0.057, 4.867, -0.269]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0.052, 1, 0.167, 0.052, 0.333, -1.718, 0.5, -1.718, 1, 0.956, -1.718, 1.411, 0.376, 1.867, 0.376, 1, 2, 0.376, 2.133, 0.272, 2.267, 0.272, 1, 2.733, 0.272, 3.2, 1.198, 3.667, 1.198, 1, 3.678, 1.198, 3.689, 1.197, 3.7, 1.197, 1, 3.789, 1.197, 3.878, 1.286, 3.967, 1.286, 1, 4.267, 1.286, 4.567, 0.479, 4.867, 0.479]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.625, 0.3, 0.625, 1, 0.411, 0.625, 0.522, -0.11, 0.633, -0.11, 1, 0.722, -0.11, 0.811, 0.192, 0.9, 0.192, 1, 0.944, 0.192, 0.989, 0.142, 1.033, 0.142, 1, 1.089, 0.142, 1.144, 0.189, 1.2, 0.189, 1, 1.333, 0.189, 1.467, -0.208, 1.6, -0.208, 1, 1.756, -0.208, 1.911, 0.075, 2.067, 0.075, 1, 2.156, 0.075, 2.244, 0.007, 2.333, 0.007, 1, 2.444, 0.007, 2.556, 0.072, 2.667, 0.072, 1, 2.856, 0.072, 3.044, -0.102, 3.233, -0.102, 1, 3.3, -0.102, 3.367, -0.075, 3.433, -0.075, 1, 3.589, -0.075, 3.744, -0.268, 3.9, -0.268, 1, 4.067, -0.268, 4.233, -0.03, 4.4, -0.03, 1, 4.478, -0.03, 4.556, -0.184, 4.633, -0.184, 1, 4.711, -0.184, 4.789, -0.074, 4.867, -0.025]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0.008, 1, 0.122, 0.008, 0.244, 0.445, 0.367, 0.445, 1, 0.5, 0.445, 0.633, -1.116, 0.767, -1.116, 1, 0.933, -1.116, 1.1, 0.519, 1.267, 0.519, 1, 1.422, 0.519, 1.578, -0.304, 1.733, -0.304, 1, 1.867, -0.304, 2, 0.18, 2.133, 0.18, 1, 2.256, 0.18, 2.378, -0.225, 2.5, -0.225, 1, 2.633, -0.225, 2.767, 0.017, 2.9, 0.017, 1, 2.989, 0.017, 3.078, -0.049, 3.167, -0.049, 1, 3.322, -0.049, 3.478, 0.254, 3.633, 0.254, 1, 3.767, 0.254, 3.9, 0.004, 4.033, 0.004, 1, 4.156, 0.004, 4.278, 0.298, 4.4, 0.298, 1, 4.533, 0.298, 4.667, -0.279, 4.8, -0.279, 1, 4.822, -0.279, 4.844, -0.271, 4.867, -0.257]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, -0.02, 1, 0.178, -0.02, 0.356, 0.709, 0.533, 0.709, 1, 0.667, 0.709, 0.8, -1.295, 0.933, -1.295, 1, 1.078, -1.295, 1.222, 0.864, 1.367, 0.864, 1, 1.522, 0.864, 1.678, -0.567, 1.833, -0.567, 1, 1.978, -0.567, 2.122, 0.424, 2.267, 0.424, 1, 2.4, 0.424, 2.533, -0.332, 2.667, -0.332, 1, 2.789, -0.332, 2.911, 0.118, 3.033, 0.118, 1, 3.144, 0.118, 3.256, -0.177, 3.367, -0.177, 1, 3.5, -0.177, 3.633, 0.209, 3.767, 0.209, 1, 3.9, 0.209, 4.033, -0.184, 4.167, -0.184, 1, 4.3, -0.184, 4.433, 0.367, 4.567, 0.367, 1, 4.667, 0.367, 4.767, -0.057, 4.867, -0.269]}]}