/*
    Name:     <PERSON><PERSON> (light)
    Author:   Jan <PERSON>
    License:  Creative Commons Attribution-ShareAlike 4.0 Unported License
    URL:      https://github.com/idleberg/<PERSON><PERSON>-highlight.js
*/

.hljs {
  background: #fbebd4;
  color: #84613d;
}

/* <PERSON><PERSON> Comment */
.hljs-comment,
.hljs-quote {
  color: #a57a4c;
}

/* <PERSON>bie Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-meta {
  color: #dc3958;
}

/* <PERSON><PERSON> Orange */
.hljs-number,
.hljs-built_in,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-deletion,
.hljs-link {
  color: #f79a32;
}

/* <PERSON><PERSON> Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
  color: #889b4a;
}

/* <PERSON><PERSON> Purple */
.hljs-keyword,
.hljs-selector-tag,
.hljs-function {
  color: #98676a;
}

/* <PERSON><PERSON> Yellow */
.hljs-title,
.hljs-section,
.hljs-attribute {
  color: #f06431;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
