{"Version": 3, "Meta": {"Duration": 6.533, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 50, "TotalSegmentCount": 641, "TotalPointCount": 1935, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -0.716, 0.233, -0.716, 1, 0.344, -0.716, 0.456, 0.895, 0.567, 0.895, 1, 0.844, 0.895, 1.122, -0.554, 1.4, -0.554, 1, 1.522, -0.554, 1.644, -0.192, 1.767, -0.192, 1, 1.822, -0.192, 1.878, -1.658, 1.933, -1.658, 1, 2.056, -1.658, 2.178, 8.091, 2.3, 8.091, 1, 2.444, 8.091, 2.589, -4.547, 2.733, -4.547, 1, 2.844, -4.547, 2.956, 1.259, 3.067, 1.259, 1, 3.189, 1.259, 3.311, -1.586, 3.433, -1.586, 1, 3.8, -1.586, 4.167, 0.398, 4.533, 0.398, 1, 4.622, 0.398, 4.711, 0.265, 4.8, 0.265, 1, 4.811, 0.265, 4.822, 0.268, 4.833, 0.268, 1, 4.856, 0.268, 4.878, 0.263, 4.9, 0.263, 1, 4.956, 0.263, 5.011, 0.309, 5.067, 0.309, 1, 5.233, 0.309, 5.4, -0.024, 5.567, -0.024, 1, 5.6, -0.024, 5.633, 0.067, 5.667, 0.067, 1, 5.811, 0.067, 5.956, -2.945, 6.1, -2.945, 1, 6.244, -2.945, 6.389, -1.202, 6.533, 0.018]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 0, 1.7, -30, 1, 1.989, -30, 2.278, 30, 2.567, 30, 1, 2.911, 30, 3.256, 30, 3.6, 30, 1, 3.8, 30, 4, -2, 4.2, -2, 1, 4.422, -2, 4.644, 30, 4.867, 30, 1, 5.111, 30, 5.356, 30, 5.6, 30, 1, 5.911, 30, 6.222, -30, 6.533, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.278, 0, 0.556, -2, 0.833, -2, 1, 1.133, -2, 1.433, -1, 1.733, -1, 1, 1.967, -1, 2.2, -10, 2.433, -10, 1, 2.933, -10, 3.433, -2, 3.933, -2, 1, 4.467, -2, 5, -10, 5.533, -10, 1, 5.844, -10, 6.156, -1, 6.467, -1, 0, 6.533, -1]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.278, 0, 0.556, 2, 0.833, 2, 1, 1.133, 2, 1.433, 1.281, 1.733, 1, 1, 3.311, -0.48, 4.889, -1, 6.467, -1, 0, 6.533, -1]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.278, 0, 0.556, -1, 0.833, -1, 1, 1.133, -1, 1.433, -1, 1.733, -1, 1, 1.967, -1, 2.2, -10, 2.433, -10, 1, 3.467, -10, 4.5, -10, 5.533, -10, 1, 5.844, -10, 6.156, -1, 6.467, -1, 0, 6.533, -1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.278, 0, 0.556, -2, 0.833, -2, 1, 1.133, -2, 1.433, 3, 1.733, 3, 1, 1.967, 3, 2.2, -20, 2.433, -20, 1, 2.8, -20, 3.167, -17.496, 3.533, -16, 1, 3.978, -14.187, 4.422, -14.31, 4.867, -12, 1, 5.4, -9.228, 5.933, -1, 6.467, -1, 0, 6.533, -1]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.278, -1, 0.556, 1, 0.833, 1, 1, 1.133, 1, 1.433, -1.656, 1.733, -8, 1, 1.967, -12.935, 2.2, -16, 2.433, -16, 1, 2.8, -16, 3.167, 10, 3.533, 10, 1, 3.978, 10, 4.422, -14, 4.867, -14, 1, 5.4, -14, 5.933, 1, 6.467, 1, 0, 6.533, 1]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.278, 0, 0.556, 4, 0.833, 4, 1, 1.133, 4, 1.433, 3.605, 1.733, 0, 1, 1.967, -2.804, 2.2, -8, 2.433, -8, 1, 3.467, -8, 4.5, -8, 5.533, -8, 1, 5.844, -8, 6.156, -1, 6.467, -1, 0, 6.533, -1]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 0.956, 0, 0.978, 30, 1, 30, 1, 1.033, 30, 1.067, 30, 1.1, 30, 1, 1.133, 30, 1.167, -30, 1.2, -30, 1, 1.256, -30, 1.311, -30, 1.367, -30, 1, 1.4, -30, 1.433, 30, 1.467, 30, 1, 1.5, 30, 1.533, 30, 1.567, 30, 1, 1.633, 30, 1.7, -15.299, 1.767, -15.299, 1, 1.9, -15.299, 2.033, 30, 2.167, 30, 1, 2.189, 30, 2.211, 30, 2.233, 30, 1, 2.289, 30, 2.344, -30, 2.4, -30, 1, 2.433, -30, 2.467, -30, 2.5, -30, 1, 2.567, -30, 2.633, 14.649, 2.7, 14.649, 1, 2.778, 14.649, 2.856, -5.931, 2.933, -5.931, 1, 3.022, -5.931, 3.111, 2.493, 3.2, 2.493, 1, 3.278, 2.493, 3.356, -1.03, 3.433, -1.03, 1, 3.489, -1.03, 3.544, 0.073, 3.6, 0.073, 1, 3.667, 0.073, 3.733, -22.067, 3.8, -22.067, 1, 3.922, -22.067, 4.044, 20.903, 4.167, 20.903, 1, 4.244, 20.903, 4.322, -8.407, 4.4, -8.407, 1, 4.489, -8.407, 4.578, 3.481, 4.667, 3.481, 1, 4.744, 3.481, 4.822, -1.454, 4.9, -1.454, 1, 4.989, -1.454, 5.078, 0.604, 5.167, 0.604, 1, 5.244, 0.604, 5.322, -0.253, 5.4, -0.253, 1, 5.489, -0.253, 5.578, 0.105, 5.667, 0.105, 1, 5.744, 0.105, 5.822, -0.044, 5.9, -0.044, 1, 5.989, -0.044, 6.078, 0.018, 6.167, 0.018, 1, 6.244, 0.018, 6.322, -0.008, 6.4, -0.008, 1, 6.444, -0.008, 6.489, -0.005, 6.533, -0.002]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 0.956, 0, 0.978, 30, 1, 30, 1, 1.033, 30, 1.067, 30, 1.1, 30, 1, 1.133, 30, 1.167, -30, 1.2, -30, 1, 1.256, -30, 1.311, -30, 1.367, -30, 1, 1.4, -30, 1.433, 30, 1.467, 30, 1, 1.5, 30, 1.533, 30, 1.567, 30, 1, 1.633, 30, 1.7, -15.299, 1.767, -15.299, 1, 1.9, -15.299, 2.033, 30, 2.167, 30, 1, 2.189, 30, 2.211, 30, 2.233, 30, 1, 2.289, 30, 2.344, -30, 2.4, -30, 1, 2.433, -30, 2.467, -30, 2.5, -30, 1, 2.567, -30, 2.633, 14.649, 2.7, 14.649, 1, 2.778, 14.649, 2.856, -5.931, 2.933, -5.931, 1, 3.022, -5.931, 3.111, 2.493, 3.2, 2.493, 1, 3.278, 2.493, 3.356, -1.03, 3.433, -1.03, 1, 3.489, -1.03, 3.544, 0.073, 3.6, 0.073, 1, 3.667, 0.073, 3.733, -22.067, 3.8, -22.067, 1, 3.922, -22.067, 4.044, 20.903, 4.167, 20.903, 1, 4.244, 20.903, 4.322, -8.407, 4.4, -8.407, 1, 4.489, -8.407, 4.578, 3.481, 4.667, 3.481, 1, 4.744, 3.481, 4.822, -1.454, 4.9, -1.454, 1, 4.989, -1.454, 5.078, 0.604, 5.167, 0.604, 1, 5.244, 0.604, 5.322, -0.253, 5.4, -0.253, 1, 5.489, -0.253, 5.578, 0.105, 5.667, 0.105, 1, 5.744, 0.105, 5.822, -0.044, 5.9, -0.044, 1, 5.989, -0.044, 6.078, 0.018, 6.167, 0.018, 1, 6.244, 0.018, 6.322, -0.008, 6.4, -0.008, 1, 6.444, -0.008, 6.489, -0.005, 6.533, -0.002]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.3, 1, 0.6, 1, 0.9, 1, 1, 0.967, 1, 1.033, 0, 1.1, 0, 1, 1.2, 0, 1.3, 1, 1.4, 1, 1, 1.6, 1, 1.8, 1, 2, 1, 1, 2.111, 1, 2.222, 0, 2.333, 0, 1, 2.744, 0, 3.156, 0, 3.567, 0, 1, 3.744, 0, 3.922, 1, 4.1, 1, 0, 6.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 2, 0, 1, 2.111, 0, 2.222, 0.5, 2.333, 0.5, 1, 2.744, 0.5, 3.156, 0.5, 3.567, 0.5, 1, 3.744, 0.5, 3.922, 0, 4.1, 0, 0, 6.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.3, 1, 0.6, 1, 0.9, 1, 1, 0.967, 1, 1.033, 0, 1.1, 0, 1, 1.2, 0, 1.3, 1, 1.4, 1, 1, 1.6, 1, 1.8, 1, 2, 1, 1, 2.111, 1, 2.222, 0, 2.333, 0, 1, 2.744, 0, 3.156, 0, 3.567, 0, 1, 3.744, 0, 3.922, 1, 4.1, 1, 0, 6.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 2, 0, 1, 2.111, 0, 2.222, 0.5, 2.333, 0.5, 1, 2.744, 0.5, 3.156, 0.5, 3.567, 0.5, 1, 3.744, 0.5, 3.922, 0, 4.1, 0, 0, 6.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.567, 0, 1.133, 0, 1.7, 0, 1, 1.956, 0, 2.211, -0.6, 2.467, -0.6, 0, 6.533, -0.6]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.567, 0, 1.133, 0, 1.7, 0, 1, 1.956, 0, 2.211, -0.6, 2.467, -0.6, 0, 6.533, -0.6]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.567, 0, 1.133, 0, 1.7, 0, 1, 1.956, 0, 2.211, -1, 2.467, -1, 0, 6.533, -1]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.567, 0, 1.133, 0, 1.7, 0, 1, 1.956, 0, 2.211, -1, 2.467, -1, 0, 6.533, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.567, 0, 1.133, 0, 1.7, 0, 1, 1.956, 0, 2.211, -0.6, 2.467, -0.6, 0, 6.533, -0.6]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.567, 0, 1.133, 0, 1.7, 0, 1, 1.956, 0, 2.211, -0.6, 2.467, -0.6, 0, 6.533, -0.6]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.4, 1, 0.556, -0.4, 1.111, -0.4, 1.667, -0.4, 1, 1.844, -0.4, 2.022, -1, 2.2, -1, 1, 2.333, -1, 2.467, -1, 2.6, -1, 1, 2.7, -1, 2.8, -1, 2.9, -1, 1, 2.978, -1, 3.056, -0.4, 3.133, -0.4, 1, 3.2, -0.4, 3.267, -0.9, 3.333, -0.9, 1, 3.389, -0.9, 3.444, -0.5, 3.5, -0.5, 1, 3.522, -0.5, 3.544, -0.6, 3.567, -0.6, 1, 3.644, -0.6, 3.722, -0.4, 3.8, -0.4, 1, 3.867, -0.4, 3.933, -0.4, 4, -0.4, 1, 4.044, -0.4, 4.089, -0.6, 4.133, -0.6, 1, 4.178, -0.6, 4.222, -0.4, 4.267, -0.4, 1, 4.344, -0.4, 4.422, -0.7, 4.5, -0.7, 1, 5.178, -0.7, 5.856, -0.4, 6.533, -0.4]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.556, 0, 1.111, 0, 1.667, 0, 1, 1.844, 0, 2.022, 0, 2.2, 0, 1, 2.333, 0, 2.467, 0, 2.6, 0, 1, 2.7, 0, 2.8, 1, 2.9, 1, 1, 2.978, 1, 3.056, 0.6, 3.133, 0.6, 1, 3.2, 0.6, 3.267, 0.9, 3.333, 0.9, 1, 3.389, 0.9, 3.444, 0, 3.5, 0, 1, 3.522, 0, 3.544, 0.31, 3.567, 0.4, 1, 3.644, 0.716, 3.722, 0.8, 3.8, 0.8, 1, 3.867, 0.8, 3.933, 0, 4, 0, 1, 4.044, 0, 4.089, 0.222, 4.133, 0.4, 1, 4.178, 0.578, 4.222, 0.6, 4.267, 0.6, 1, 4.344, 0.6, 4.422, 0, 4.5, 0, 1, 5.178, 0, 5.856, 0, 6.533, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -1, 1, 0.544, -1, 1.089, -9, 1.633, -9, 1, 1.9, -9, 2.167, 24, 2.433, 24, 1, 3.344, 24, 4.256, 23.088, 5.167, 16, 1, 5.622, 12.456, 6.078, -1, 6.533, -1]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 0.989, 0, 1.044, 17.962, 1.1, 17.962, 1, 1.178, 17.962, 1.256, -16.352, 1.333, -16.352, 1, 1.422, -16.352, 1.511, 4.802, 1.6, 4.802, 1, 1.711, 4.802, 1.822, -0.9, 1.933, -0.9, 1, 2.033, -0.9, 2.133, 14.001, 2.233, 14.001, 1, 2.322, 14.001, 2.411, -5.127, 2.5, -5.127, 1, 2.611, -5.127, 2.722, 0.978, 2.833, 0.978, 1, 2.933, 0.978, 3.033, -0.19, 3.133, -0.19, 1, 3.244, -0.19, 3.356, 0.036, 3.467, 0.036, 1, 3.6, 0.036, 3.733, -9.3, 3.867, -9.3, 1, 3.989, -9.3, 4.111, 3.692, 4.233, 3.692, 1, 4.333, 3.692, 4.433, -0.7, 4.533, -0.7, 1, 4.644, -0.7, 4.756, 0.137, 4.867, 0.137, 1, 4.967, 0.137, 5.067, -0.027, 5.167, -0.027, 1, 5.278, -0.027, 5.389, 0.005, 5.5, 0.005, 1, 5.6, 0.005, 5.7, -0.001, 5.8, -0.001, 1, 5.9, -0.001, 6, 0, 6.1, 0, 1, 6.2, 0, 6.3, 0, 6.4, 0, 1, 6.411, 0, 6.422, 0, 6.433, 0, 1, 6.467, 0, 6.5, 0, 6.533, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 1.481, 0.367, 1.481, 1, 0.578, 1.481, 0.789, -2.179, 1, -2.179, 1, 1.344, -2.179, 1.689, 5.655, 2.033, 5.655, 1, 2.211, 5.655, 2.389, -17.288, 2.567, -17.288, 1, 2.711, -17.288, 2.856, -8.793, 3, -8.793, 1, 3.122, -8.793, 3.244, -11.345, 3.367, -11.345, 1, 3.711, -11.345, 4.056, -6.552, 4.4, -6.552, 1, 4.411, -6.552, 4.422, -6.57, 4.433, -6.57, 1, 4.444, -6.57, 4.456, -6.567, 4.467, -6.567, 1, 4.933, -6.567, 5.4, -13.164, 5.867, -13.164, 1, 6.089, -13.164, 6.311, 1.862, 6.533, 3.293]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 1.481, 0.367, 1.481, 1, 0.578, 1.481, 0.789, -2.179, 1, -2.179, 1, 1.344, -2.179, 1.689, 5.655, 2.033, 5.655, 1, 2.211, 5.655, 2.389, -17.288, 2.567, -17.288, 1, 2.711, -17.288, 2.856, -8.793, 3, -8.793, 1, 3.122, -8.793, 3.244, -11.345, 3.367, -11.345, 1, 3.711, -11.345, 4.056, -6.552, 4.4, -6.552, 1, 4.411, -6.552, 4.422, -6.57, 4.433, -6.57, 1, 4.444, -6.57, 4.456, -6.567, 4.467, -6.567, 1, 4.933, -6.567, 5.4, -13.164, 5.867, -13.164, 1, 6.089, -13.164, 6.311, 1.862, 6.533, 3.293]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 1.481, 0.367, 1.481, 1, 0.578, 1.481, 0.789, -2.179, 1, -2.179, 1, 1.344, -2.179, 1.689, 5.655, 2.033, 5.655, 1, 2.211, 5.655, 2.389, -17.288, 2.567, -17.288, 1, 2.711, -17.288, 2.856, -8.793, 3, -8.793, 1, 3.122, -8.793, 3.244, -11.345, 3.367, -11.345, 1, 3.711, -11.345, 4.056, -6.552, 4.4, -6.552, 1, 4.411, -6.552, 4.422, -6.57, 4.433, -6.57, 1, 4.444, -6.57, 4.456, -6.567, 4.467, -6.567, 1, 4.933, -6.567, 5.4, -13.164, 5.867, -13.164, 1, 6.089, -13.164, 6.311, 1.862, 6.533, 3.293]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.066, 0.333, 1.066, 1, 0.6, 1.066, 0.867, -0.407, 1.133, -0.407, 1, 1.433, -0.407, 1.733, 4.396, 2.033, 4.396, 1, 2.2, 4.396, 2.367, -10.034, 2.533, -10.034, 1, 2.644, -10.034, 2.756, -4.174, 2.867, -4.174, 1, 2.989, -4.174, 3.111, -6.243, 3.233, -6.243, 1, 3.556, -6.243, 3.878, -4.39, 4.2, -4.39, 1, 4.356, -4.39, 4.511, -4.648, 4.667, -4.648, 1, 4.678, -4.648, 4.689, -4.648, 4.7, -4.648, 1, 4.711, -4.648, 4.722, -4.66, 4.733, -4.66, 1, 4.756, -4.66, 4.778, -4.655, 4.8, -4.655, 1, 5.122, -4.655, 5.444, -6.187, 5.767, -6.187, 1, 6.022, -6.187, 6.278, 0.835, 6.533, 0.835]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.066, 0.333, 1.066, 1, 0.6, 1.066, 0.867, -0.407, 1.133, -0.407, 1, 1.433, -0.407, 1.733, 4.396, 2.033, 4.396, 1, 2.2, 4.396, 2.367, -10.034, 2.533, -10.034, 1, 2.644, -10.034, 2.756, -4.174, 2.867, -4.174, 1, 2.989, -4.174, 3.111, -6.243, 3.233, -6.243, 1, 3.556, -6.243, 3.878, -4.39, 4.2, -4.39, 1, 4.356, -4.39, 4.511, -4.648, 4.667, -4.648, 1, 4.678, -4.648, 4.689, -4.648, 4.7, -4.648, 1, 4.711, -4.648, 4.722, -4.66, 4.733, -4.66, 1, 4.756, -4.66, 4.778, -4.655, 4.8, -4.655, 1, 5.122, -4.655, 5.444, -6.187, 5.767, -6.187, 1, 6.022, -6.187, 6.278, 0.835, 6.533, 0.835]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -1.003, 0.233, -1.003, 1, 0.344, -1.003, 0.456, 1.254, 0.567, 1.254, 1, 0.844, 1.254, 1.122, -0.775, 1.4, -0.775, 1, 1.522, -0.775, 1.644, -0.268, 1.767, -0.268, 1, 1.822, -0.268, 1.878, -2.321, 1.933, -2.321, 1, 2.056, -2.321, 2.178, 11.327, 2.3, 11.327, 1, 2.444, 11.327, 2.589, -6.366, 2.733, -6.366, 1, 2.844, -6.366, 2.956, 1.762, 3.067, 1.762, 1, 3.189, 1.762, 3.311, -2.22, 3.433, -2.22, 1, 3.8, -2.22, 4.167, 0.557, 4.533, 0.557, 1, 4.622, 0.557, 4.711, 0.371, 4.8, 0.371, 1, 4.811, 0.371, 4.822, 0.375, 4.833, 0.375, 1, 4.856, 0.375, 4.878, 0.369, 4.9, 0.369, 1, 4.956, 0.369, 5.011, 0.432, 5.067, 0.432, 1, 5.233, 0.432, 5.4, -0.034, 5.567, -0.034, 1, 5.6, -0.034, 5.633, 0.094, 5.667, 0.094, 1, 5.811, 0.094, 5.956, -4.123, 6.1, -4.123, 1, 6.244, -4.123, 6.389, -1.683, 6.533, 0.026]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.066, 0.333, 1.066, 1, 0.6, 1.066, 0.867, -0.407, 1.133, -0.407, 1, 1.433, -0.407, 1.733, 4.396, 2.033, 4.396, 1, 2.2, 4.396, 2.367, -10.034, 2.533, -10.034, 1, 2.644, -10.034, 2.756, -4.174, 2.867, -4.174, 1, 2.989, -4.174, 3.111, -6.243, 3.233, -6.243, 1, 3.556, -6.243, 3.878, -4.39, 4.2, -4.39, 1, 4.356, -4.39, 4.511, -4.648, 4.667, -4.648, 1, 4.678, -4.648, 4.689, -4.648, 4.7, -4.648, 1, 4.711, -4.648, 4.722, -4.66, 4.733, -4.66, 1, 4.756, -4.66, 4.778, -4.655, 4.8, -4.655, 1, 5.122, -4.655, 5.444, -6.187, 5.767, -6.187, 1, 6.022, -6.187, 6.278, 0.835, 6.533, 0.835]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -0.716, 0.233, -0.716, 1, 0.344, -0.716, 0.456, 0.895, 0.567, 0.895, 1, 0.844, 0.895, 1.122, -0.554, 1.4, -0.554, 1, 1.522, -0.554, 1.644, -0.192, 1.767, -0.192, 1, 1.822, -0.192, 1.878, -1.658, 1.933, -1.658, 1, 2.056, -1.658, 2.178, 8.091, 2.3, 8.091, 1, 2.444, 8.091, 2.589, -4.547, 2.733, -4.547, 1, 2.844, -4.547, 2.956, 1.259, 3.067, 1.259, 1, 3.189, 1.259, 3.311, -1.586, 3.433, -1.586, 1, 3.8, -1.586, 4.167, 0.398, 4.533, 0.398, 1, 4.622, 0.398, 4.711, 0.265, 4.8, 0.265, 1, 4.811, 0.265, 4.822, 0.268, 4.833, 0.268, 1, 4.856, 0.268, 4.878, 0.263, 4.9, 0.263, 1, 4.956, 0.263, 5.011, 0.309, 5.067, 0.309, 1, 5.233, 0.309, 5.4, -0.024, 5.567, -0.024, 1, 5.6, -0.024, 5.633, 0.067, 5.667, 0.067, 1, 5.811, 0.067, 5.956, -2.945, 6.1, -2.945, 1, 6.244, -2.945, 6.389, -1.202, 6.533, 0.018]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.066, 0.333, 1.066, 1, 0.6, 1.066, 0.867, -0.407, 1.133, -0.407, 1, 1.433, -0.407, 1.733, 4.396, 2.033, 4.396, 1, 2.2, 4.396, 2.367, -10.034, 2.533, -10.034, 1, 2.644, -10.034, 2.756, -4.174, 2.867, -4.174, 1, 2.989, -4.174, 3.111, -6.243, 3.233, -6.243, 1, 3.556, -6.243, 3.878, -4.39, 4.2, -4.39, 1, 4.356, -4.39, 4.511, -4.648, 4.667, -4.648, 1, 4.678, -4.648, 4.689, -4.648, 4.7, -4.648, 1, 4.711, -4.648, 4.722, -4.66, 4.733, -4.66, 1, 4.756, -4.66, 4.778, -4.655, 4.8, -4.655, 1, 5.122, -4.655, 5.444, -6.187, 5.767, -6.187, 1, 6.022, -6.187, 6.278, 0.835, 6.533, 0.835]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -0.716, 0.233, -0.716, 1, 0.344, -0.716, 0.456, 0.895, 0.567, 0.895, 1, 0.844, 0.895, 1.122, -0.554, 1.4, -0.554, 1, 1.522, -0.554, 1.644, -0.192, 1.767, -0.192, 1, 1.822, -0.192, 1.878, -1.658, 1.933, -1.658, 1, 2.056, -1.658, 2.178, 8.091, 2.3, 8.091, 1, 2.444, 8.091, 2.589, -4.547, 2.733, -4.547, 1, 2.844, -4.547, 2.956, 1.259, 3.067, 1.259, 1, 3.189, 1.259, 3.311, -1.586, 3.433, -1.586, 1, 3.8, -1.586, 4.167, 0.398, 4.533, 0.398, 1, 4.622, 0.398, 4.711, 0.265, 4.8, 0.265, 1, 4.811, 0.265, 4.822, 0.268, 4.833, 0.268, 1, 4.856, 0.268, 4.878, 0.263, 4.9, 0.263, 1, 4.956, 0.263, 5.011, 0.309, 5.067, 0.309, 1, 5.233, 0.309, 5.4, -0.024, 5.567, -0.024, 1, 5.6, -0.024, 5.633, 0.067, 5.667, 0.067, 1, 5.811, 0.067, 5.956, -2.945, 6.1, -2.945, 1, 6.244, -2.945, 6.389, -1.202, 6.533, 0.018]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.279, 0.333, 1.279, 1, 0.6, 1.279, 0.867, -0.488, 1.133, -0.488, 1, 1.433, -0.488, 1.733, 5.275, 2.033, 5.275, 1, 2.2, 5.275, 2.367, -12.041, 2.533, -12.041, 1, 2.644, -12.041, 2.756, -5.008, 2.867, -5.008, 1, 2.989, -5.008, 3.111, -7.491, 3.233, -7.491, 1, 3.556, -7.491, 3.878, -5.268, 4.2, -5.268, 1, 4.356, -5.268, 4.511, -5.578, 4.667, -5.578, 1, 4.678, -5.578, 4.689, -5.578, 4.7, -5.578, 1, 4.711, -5.578, 4.722, -5.592, 4.733, -5.592, 1, 4.756, -5.592, 4.778, -5.586, 4.8, -5.586, 1, 5.122, -5.586, 5.444, -7.424, 5.767, -7.424, 1, 6.022, -7.424, 6.278, 1.002, 6.533, 1.002]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -0.86, 0.233, -0.86, 1, 0.344, -0.86, 0.456, 1.074, 0.567, 1.074, 1, 0.844, 1.074, 1.122, -0.664, 1.4, -0.664, 1, 1.522, -0.664, 1.644, -0.23, 1.767, -0.23, 1, 1.822, -0.23, 1.878, -1.989, 1.933, -1.989, 1, 2.056, -1.989, 2.178, 9.709, 2.3, 9.709, 1, 2.444, 9.709, 2.589, -5.456, 2.733, -5.456, 1, 2.844, -5.456, 2.956, 1.51, 3.067, 1.51, 1, 3.189, 1.51, 3.311, -1.903, 3.433, -1.903, 1, 3.8, -1.903, 4.167, 0.478, 4.533, 0.478, 1, 4.622, 0.478, 4.711, 0.318, 4.8, 0.318, 1, 4.811, 0.318, 4.822, 0.322, 4.833, 0.322, 1, 4.856, 0.322, 4.878, 0.316, 4.9, 0.316, 1, 4.956, 0.316, 5.011, 0.37, 5.067, 0.37, 1, 5.233, 0.37, 5.4, -0.029, 5.567, -0.029, 1, 5.6, -0.029, 5.633, 0.081, 5.667, 0.081, 1, 5.811, 0.081, 5.956, -3.534, 6.1, -3.534, 1, 6.244, -3.534, 6.389, -1.442, 6.533, 0.022]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.279, 0.333, 1.279, 1, 0.6, 1.279, 0.867, -0.488, 1.133, -0.488, 1, 1.433, -0.488, 1.733, 5.275, 2.033, 5.275, 1, 2.2, 5.275, 2.367, -12.041, 2.533, -12.041, 1, 2.644, -12.041, 2.756, -5.008, 2.867, -5.008, 1, 2.989, -5.008, 3.111, -7.491, 3.233, -7.491, 1, 3.556, -7.491, 3.878, -5.268, 4.2, -5.268, 1, 4.356, -5.268, 4.511, -5.578, 4.667, -5.578, 1, 4.678, -5.578, 4.689, -5.578, 4.7, -5.578, 1, 4.711, -5.578, 4.722, -5.592, 4.733, -5.592, 1, 4.756, -5.592, 4.778, -5.586, 4.8, -5.586, 1, 5.122, -5.586, 5.444, -7.424, 5.767, -7.424, 1, 6.022, -7.424, 6.278, 1.002, 6.533, 1.002]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -0.86, 0.233, -0.86, 1, 0.344, -0.86, 0.456, 1.074, 0.567, 1.074, 1, 0.844, 1.074, 1.122, -0.664, 1.4, -0.664, 1, 1.522, -0.664, 1.644, -0.23, 1.767, -0.23, 1, 1.822, -0.23, 1.878, -1.989, 1.933, -1.989, 1, 2.056, -1.989, 2.178, 9.709, 2.3, 9.709, 1, 2.444, 9.709, 2.589, -5.456, 2.733, -5.456, 1, 2.844, -5.456, 2.956, 1.51, 3.067, 1.51, 1, 3.189, 1.51, 3.311, -1.903, 3.433, -1.903, 1, 3.8, -1.903, 4.167, 0.478, 4.533, 0.478, 1, 4.622, 0.478, 4.711, 0.318, 4.8, 0.318, 1, 4.811, 0.318, 4.822, 0.322, 4.833, 0.322, 1, 4.856, 0.322, 4.878, 0.316, 4.9, 0.316, 1, 4.956, 0.316, 5.011, 0.37, 5.067, 0.37, 1, 5.233, 0.37, 5.4, -0.029, 5.567, -0.029, 1, 5.6, -0.029, 5.633, 0.081, 5.667, 0.081, 1, 5.811, 0.081, 5.956, -3.534, 6.1, -3.534, 1, 6.244, -3.534, 6.389, -1.442, 6.533, 0.022]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.311, 0, 0.622, 0, 0.933, 0, 1, 0.989, 0, 1.044, 5.987, 1.1, 5.987, 1, 1.178, 5.987, 1.256, -5.451, 1.333, -5.451, 1, 1.422, -5.451, 1.511, 1.601, 1.6, 1.601, 1, 1.711, 1.601, 1.822, -0.3, 1.933, -0.3, 1, 2.033, -0.3, 2.133, 4.667, 2.233, 4.667, 1, 2.322, 4.667, 2.411, -1.709, 2.5, -1.709, 1, 2.611, -1.709, 2.722, 0.326, 2.833, 0.326, 1, 2.933, 0.326, 3.033, -0.063, 3.133, -0.063, 1, 3.244, -0.063, 3.356, 0.012, 3.467, 0.012, 1, 3.6, 0.012, 3.733, -3.1, 3.867, -3.1, 1, 3.989, -3.1, 4.111, 1.231, 4.233, 1.231, 1, 4.333, 1.231, 4.433, -0.233, 4.533, -0.233, 1, 4.644, -0.233, 4.756, 0.046, 4.867, 0.046, 1, 4.967, 0.046, 5.067, -0.009, 5.167, -0.009, 1, 5.278, -0.009, 5.389, 0.002, 5.5, 0.002, 1, 5.6, 0.002, 5.7, 0, 5.8, 0, 1, 5.9, 0, 6, 0, 6.1, 0, 1, 6.2, 0, 6.3, 0, 6.4, 0, 1, 6.411, 0, 6.422, 0, 6.433, 0, 1, 6.467, 0, 6.5, 0, 6.533, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.706, 0.333, 1.706, 1, 0.6, 1.706, 0.867, -0.651, 1.133, -0.651, 1, 1.433, -0.651, 1.733, 7.034, 2.033, 7.034, 1, 2.2, 7.034, 2.367, -16.055, 2.533, -16.055, 1, 2.644, -16.055, 2.756, -6.678, 2.867, -6.678, 1, 2.989, -6.678, 3.111, -9.988, 3.233, -9.988, 1, 3.556, -9.988, 3.878, -7.024, 4.2, -7.024, 1, 4.356, -7.024, 4.511, -7.437, 4.667, -7.437, 1, 4.678, -7.437, 4.689, -7.437, 4.7, -7.437, 1, 4.711, -7.437, 4.722, -7.456, 4.733, -7.456, 1, 4.756, -7.456, 4.778, -7.448, 4.8, -7.448, 1, 5.122, -7.448, 5.444, -9.899, 5.767, -9.899, 1, 6.022, -9.899, 6.278, 1.336, 6.533, 1.336]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -1.146, 0.233, -1.146, 1, 0.344, -1.146, 0.456, 1.433, 0.567, 1.433, 1, 0.844, 1.433, 1.122, -0.886, 1.4, -0.886, 1, 1.522, -0.886, 1.644, -0.307, 1.767, -0.307, 1, 1.822, -0.307, 1.878, -2.652, 1.933, -2.652, 1, 2.056, -2.652, 2.178, 12.945, 2.3, 12.945, 1, 2.444, 12.945, 2.589, -7.275, 2.733, -7.275, 1, 2.844, -7.275, 2.956, 2.014, 3.067, 2.014, 1, 3.189, 2.014, 3.311, -2.537, 3.433, -2.537, 1, 3.8, -2.537, 4.167, 0.637, 4.533, 0.637, 1, 4.622, 0.637, 4.711, 0.424, 4.8, 0.424, 1, 4.811, 0.424, 4.822, 0.429, 4.833, 0.429, 1, 4.856, 0.429, 4.878, 0.421, 4.9, 0.421, 1, 4.956, 0.421, 5.011, 0.494, 5.067, 0.494, 1, 5.233, 0.494, 5.4, -0.039, 5.567, -0.039, 1, 5.6, -0.039, 5.633, 0.108, 5.667, 0.108, 1, 5.811, 0.108, 5.956, -4.712, 6.1, -4.712, 1, 6.244, -4.712, 6.389, -1.923, 6.533, 0.029]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.706, 0.333, 1.706, 1, 0.6, 1.706, 0.867, -0.651, 1.133, -0.651, 1, 1.433, -0.651, 1.733, 7.034, 2.033, 7.034, 1, 2.2, 7.034, 2.367, -16.055, 2.533, -16.055, 1, 2.644, -16.055, 2.756, -6.678, 2.867, -6.678, 1, 2.989, -6.678, 3.111, -9.988, 3.233, -9.988, 1, 3.556, -9.988, 3.878, -7.024, 4.2, -7.024, 1, 4.356, -7.024, 4.511, -7.437, 4.667, -7.437, 1, 4.678, -7.437, 4.689, -7.437, 4.7, -7.437, 1, 4.711, -7.437, 4.722, -7.456, 4.733, -7.456, 1, 4.756, -7.456, 4.778, -7.448, 4.8, -7.448, 1, 5.122, -7.448, 5.444, -9.899, 5.767, -9.899, 1, 6.022, -9.899, 6.278, 1.336, 6.533, 1.336]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, 0.375, 0.4, 0.375, 1, 0.678, 0.375, 0.956, -0.216, 1.233, -0.216, 1, 1.511, -0.216, 1.789, 1.763, 2.067, 1.763, 1, 2.244, 1.763, 2.422, -2.823, 2.6, -2.823, 1, 2.722, -2.823, 2.844, -1.606, 2.967, -1.606, 1, 3.078, -1.606, 3.189, -1.924, 3.3, -1.924, 1, 3.633, -1.924, 3.967, -1.268, 4.3, -1.268, 1, 4.311, -1.268, 4.322, -1.268, 4.333, -1.268, 1, 4.344, -1.268, 4.356, -1.267, 4.367, -1.267, 1, 4.856, -1.267, 5.344, -2.019, 5.833, -2.019, 1, 6.067, -2.019, 6.3, 0.032, 6.533, 0.219]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -0.238, 0.267, -0.238, 1, 0.367, -0.238, 0.467, 0.114, 0.567, 0.114, 1, 0.678, 0.114, 0.789, -0.03, 0.9, -0.03, 1, 1.122, -0.03, 1.344, 0.058, 1.567, 0.058, 1, 1.7, 0.058, 1.833, -0.848, 1.967, -0.848, 1, 2.089, -0.848, 2.211, 0.774, 2.333, 0.774, 1, 2.467, 0.774, 2.6, -1.388, 2.733, -1.388, 1, 2.856, -1.388, 2.978, 0.966, 3.1, 0.966, 1, 3.233, 0.966, 3.367, -0.282, 3.5, -0.282, 1, 3.622, -0.282, 3.744, 0.186, 3.867, 0.186, 1, 4.011, 0.186, 4.156, -0.053, 4.3, -0.053, 1, 4.389, -0.053, 4.478, -0.024, 4.567, -0.024, 1, 4.644, -0.024, 4.722, -0.057, 4.8, -0.057, 1, 4.811, -0.057, 4.822, -0.057, 4.833, -0.057, 1, 4.856, -0.057, 4.878, -0.059, 4.9, -0.059, 1, 5.189, -0.059, 5.478, 0.193, 5.767, 0.193, 1, 5.867, 0.193, 5.967, -0.134, 6.067, -0.134, 1, 6.189, -0.134, 6.311, 0.18, 6.433, 0.18, 1, 6.467, 0.18, 6.5, 0.162, 6.533, 0.162]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.037, 1, 0.022, -0.037, 0.044, 0, 0.067, 0, 1, 0.111, 0, 0.156, 0, 0.2, 0, 1, 0.267, 0, 0.333, -0.249, 0.4, -0.249, 1, 0.511, -0.249, 0.622, 0.392, 0.733, 0.392, 1, 0.878, 0.392, 1.022, -0.218, 1.167, -0.218, 1, 1.367, -0.218, 1.567, 0.079, 1.767, 0.079, 1, 1.856, 0.079, 1.944, -0.338, 2.033, -0.338, 1, 2.167, -0.338, 2.3, 2.445, 2.433, 2.445, 1, 2.578, 2.445, 2.722, -3.051, 2.867, -3.051, 1, 2.989, -3.051, 3.111, 2.116, 3.233, 2.116, 1, 3.367, 2.116, 3.5, -1.37, 3.633, -1.37, 1, 3.767, -1.37, 3.9, 0.637, 4.033, 0.637, 1, 4.156, 0.637, 4.278, -0.192, 4.4, -0.192, 1, 4.522, -0.192, 4.644, 0.137, 4.767, 0.137, 1, 4.878, 0.137, 4.989, -0.024, 5.1, -0.024, 1, 5.211, -0.024, 5.322, 0.04, 5.433, 0.04, 1, 5.689, 0.04, 5.944, -0.796, 6.2, -0.796, 1, 6.311, -0.796, 6.422, -0.303, 6.533, 0.102]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, -0.077, 1, 0.022, -0.077, 0.044, -0.153, 0.067, -0.153, 1, 0.156, -0.153, 0.244, 0.106, 0.333, 0.106, 1, 0.422, 0.106, 0.511, -0.287, 0.6, -0.287, 1, 0.711, -0.287, 0.822, 0.493, 0.933, 0.493, 1, 1.056, 0.493, 1.178, -0.339, 1.3, -0.339, 1, 1.467, -0.339, 1.633, 0.046, 1.8, 0.046, 1, 1.933, 0.046, 2.067, -0.856, 2.2, -0.856, 1, 2.333, -0.856, 2.467, 2.952, 2.6, 2.952, 1, 2.733, 2.952, 2.867, -4.081, 3, -4.081, 1, 3.133, -4.081, 3.267, 3.631, 3.4, 3.631, 1, 3.533, 3.631, 3.667, -2.609, 3.8, -2.609, 1, 3.922, -2.609, 4.044, 1.519, 4.167, 1.519, 1, 4.3, 1.519, 4.433, -0.764, 4.567, -0.764, 1, 4.689, -0.764, 4.811, 0.382, 4.933, 0.382, 1, 5.056, 0.382, 5.178, -0.153, 5.3, -0.153, 1, 5.444, -0.153, 5.589, 0.133, 5.733, 0.133, 1, 5.756, 0.133, 5.778, 0.132, 5.8, 0.132, 1, 5.811, 0.132, 5.822, 0.133, 5.833, 0.133, 1, 6.011, 0.133, 6.189, -0.777, 6.367, -0.777, 1, 6.422, -0.777, 6.478, -0.606, 6.533, -0.379]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, 0.375, 0.4, 0.375, 1, 0.678, 0.375, 0.956, -0.216, 1.233, -0.216, 1, 1.511, -0.216, 1.789, 1.763, 2.067, 1.763, 1, 2.244, 1.763, 2.422, -2.823, 2.6, -2.823, 1, 2.722, -2.823, 2.844, -1.606, 2.967, -1.606, 1, 3.078, -1.606, 3.189, -1.924, 3.3, -1.924, 1, 3.633, -1.924, 3.967, -1.268, 4.3, -1.268, 1, 4.311, -1.268, 4.322, -1.268, 4.333, -1.268, 1, 4.344, -1.268, 4.356, -1.267, 4.367, -1.267, 1, 4.856, -1.267, 5.344, -2.019, 5.833, -2.019, 1, 6.067, -2.019, 6.3, 0.032, 6.533, 0.219]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -0.238, 0.267, -0.238, 1, 0.367, -0.238, 0.467, 0.114, 0.567, 0.114, 1, 0.678, 0.114, 0.789, -0.03, 0.9, -0.03, 1, 1.122, -0.03, 1.344, 0.058, 1.567, 0.058, 1, 1.7, 0.058, 1.833, -0.848, 1.967, -0.848, 1, 2.089, -0.848, 2.211, 0.774, 2.333, 0.774, 1, 2.467, 0.774, 2.6, -1.388, 2.733, -1.388, 1, 2.856, -1.388, 2.978, 0.966, 3.1, 0.966, 1, 3.233, 0.966, 3.367, -0.282, 3.5, -0.282, 1, 3.622, -0.282, 3.744, 0.186, 3.867, 0.186, 1, 4.011, 0.186, 4.156, -0.053, 4.3, -0.053, 1, 4.389, -0.053, 4.478, -0.024, 4.567, -0.024, 1, 4.644, -0.024, 4.722, -0.057, 4.8, -0.057, 1, 4.811, -0.057, 4.822, -0.057, 4.833, -0.057, 1, 4.856, -0.057, 4.878, -0.059, 4.9, -0.059, 1, 5.189, -0.059, 5.478, 0.193, 5.767, 0.193, 1, 5.867, 0.193, 5.967, -0.134, 6.067, -0.134, 1, 6.189, -0.134, 6.311, 0.18, 6.433, 0.18, 1, 6.467, 0.18, 6.5, 0.162, 6.533, 0.162]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.037, 1, 0.022, -0.037, 0.044, 0, 0.067, 0, 1, 0.111, 0, 0.156, 0, 0.2, 0, 1, 0.267, 0, 0.333, -0.249, 0.4, -0.249, 1, 0.511, -0.249, 0.622, 0.392, 0.733, 0.392, 1, 0.878, 0.392, 1.022, -0.218, 1.167, -0.218, 1, 1.367, -0.218, 1.567, 0.079, 1.767, 0.079, 1, 1.856, 0.079, 1.944, -0.338, 2.033, -0.338, 1, 2.167, -0.338, 2.3, 2.445, 2.433, 2.445, 1, 2.578, 2.445, 2.722, -3.051, 2.867, -3.051, 1, 2.989, -3.051, 3.111, 2.116, 3.233, 2.116, 1, 3.367, 2.116, 3.5, -1.37, 3.633, -1.37, 1, 3.767, -1.37, 3.9, 0.637, 4.033, 0.637, 1, 4.156, 0.637, 4.278, -0.192, 4.4, -0.192, 1, 4.522, -0.192, 4.644, 0.137, 4.767, 0.137, 1, 4.878, 0.137, 4.989, -0.024, 5.1, -0.024, 1, 5.211, -0.024, 5.322, 0.04, 5.433, 0.04, 1, 5.689, 0.04, 5.944, -0.796, 6.2, -0.796, 1, 6.311, -0.796, 6.422, -0.303, 6.533, 0.102]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, -0.077, 1, 0.022, -0.077, 0.044, -0.153, 0.067, -0.153, 1, 0.156, -0.153, 0.244, 0.106, 0.333, 0.106, 1, 0.422, 0.106, 0.511, -0.287, 0.6, -0.287, 1, 0.711, -0.287, 0.822, 0.493, 0.933, 0.493, 1, 1.056, 0.493, 1.178, -0.339, 1.3, -0.339, 1, 1.467, -0.339, 1.633, 0.046, 1.8, 0.046, 1, 1.933, 0.046, 2.067, -0.856, 2.2, -0.856, 1, 2.333, -0.856, 2.467, 2.952, 2.6, 2.952, 1, 2.733, 2.952, 2.867, -4.081, 3, -4.081, 1, 3.133, -4.081, 3.267, 3.631, 3.4, 3.631, 1, 3.533, 3.631, 3.667, -2.609, 3.8, -2.609, 1, 3.922, -2.609, 4.044, 1.519, 4.167, 1.519, 1, 4.3, 1.519, 4.433, -0.764, 4.567, -0.764, 1, 4.689, -0.764, 4.811, 0.382, 4.933, 0.382, 1, 5.056, 0.382, 5.178, -0.153, 5.3, -0.153, 1, 5.444, -0.153, 5.589, 0.133, 5.733, 0.133, 1, 5.756, 0.133, 5.778, 0.132, 5.8, 0.132, 1, 5.811, 0.132, 5.822, 0.133, 5.833, 0.133, 1, 6.011, 0.133, 6.189, -0.777, 6.367, -0.777, 1, 6.422, -0.777, 6.478, -0.606, 6.533, -0.379]}]}