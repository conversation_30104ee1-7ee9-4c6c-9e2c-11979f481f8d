<!doctype html>

<head>
    <meta charset="utf-8"/>
    <title>加载动画预览</title>
    <style>
        html {
            height: 100%;
            background: #3023ae;
            background: -moz-linear-gradient(-45deg,  #3023ae 0%, #c86dd7 100%);
            background: -webkit-gradient(linear, left top, right bottom, color-stop(0%,#3023ae), color-stop(100%,#c86dd7));
            background: -webkit-linear-gradient(-45deg,  #3023ae 0%,#c86dd7 100%);
            background: -o-linear-gradient(-45deg,  #3023ae 0%,#c86dd7 100%);
            background: -ms-linear-gradient(-45deg,  #3023ae 0%,#c86dd7 100%);
            background: linear-gradient(135deg,  #3023ae 0%,#c86dd7 100%);
            background-attachment: fixed;
        }
        body {
            font-weight: 300;
        }
        a {
            color: #fff;
        }

        ul {
            background: #fff;
            width: 720px;
            margin: auto;
            margin-top: 60px;
            list-style: none;
            padding: 0;
            -moz-box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.37);
            box-shadow:      0px 0px 4px 0px rgba(0,0,0,0.37);
        }
        ul li {
            width: 180px;
            height: 180px;
            line-height: 176px;
            text-align: center;
            float: left;
            background-repeat: no-repeat;
            background-position: center;
            position: relative;
        }
        ul li img {
            vertical-align: middle;
        }
        ul li div {
            position: absolute;
            left: 10px;
            top: 12px;
            font-size: 16px;
            line-height: 16px;
            color: #fff;
        }
        ul li div:before {
            content: "加载动画 ";
        }
        #close_this_page{
            cursor: pointer;
            font-size: 20px;
            color: #fff;
        }
        li:nth-child(1) {
            background-color: #1ABC9C;
        }
        li:nth-child(2) {
            background-color: #34495E;
        }
        li:nth-child(3) {
            background-color: #F39C12;
        }
        li:nth-child(4) {
            background-color: #9B59B6;
        }
        li:nth-child(5) {
            background-color: #3498DB;
        }
        li:nth-child(6) {
            background-color: #E74C3C;
        }
        li:nth-child(7) {
            background-color: #2ECC71;
        }
        li:nth-child(8) {
            background-color: #2C3E50;
        }
        li:nth-child(9) {
            background-color: #F1C40F;
        }
        li:nth-child(10) {
            background-color: #8E44AD;
        }
        li:nth-child(11) {
            background-color: #2980B9;
        }
        li:nth-child(12) {
            background-color: #E74C3C;
        }
        .actions {
            margin: auto;
            margin-top: 45px;
            width: 325px;
        }
        .actions a {
            width: 150px;
            height: 46px;
            background-repeat: no-repeat;
            background-size: 150px 46px;
            display: block;
            text-indent: -9999px;
            float: left;
        }


        .attr {
            color: #fff;
            text-align: center;
            font-size: 0.875rem;
            margin-top: 20px;
        }

    </style>
</head>

<body>
    <ul>
        <li>
            <img src="svg-loaders/tail-spin.svg" width="50" alt="">
            <div>#1</div>
        </li>
        <li>
            <img src="svg-loaders/puff.svg" width="50" alt="">
            <div>#2</div>
        </li>
        <li>
            <img src="svg-loaders/bars.svg" width="40" alt="">
            <div>#3</div>
        </li>
        <li>
            <img src="svg-loaders/oval.svg" width="50" alt="">
            <div>#4</div>
        </li>
        <li>
            <img src="svg-loaders/rings.svg" width="60" alt="">
            <div>#5</div>
        </li>
        <li>
            <img src="svg-loaders/three-dots.svg" width="60" alt="">
            <div>#6</div>
        </li>
        <li>
            <img src="svg-loaders/audio.svg" width="40" alt="">
            <div>#7</div>
        </li>
        <li>
            <img src="svg-loaders/grid.svg" width="40" alt="">
            <div>#8</div>
        </li>
        <li>
            <img src="svg-loaders/spinning-circles.svg" width="50" alt="">
            <div>#9</div>
        </li>
        <li>
            <img src="svg-loaders/circles.svg" width="50" alt="">
            <div>#10</div>
        </li>
        <li>
            <img src="svg-loaders/ball-triangle.svg" width="50" alt="">
            <div>#11</div>
        </li>
        <li>
            <span id="close_this_page" onclick="window.close();">关闭当前页面</span>
        </li>
        <div style="clear: both"></div>
    </ul>

    <div class="attr">
        图标加载动画来自 <a href="https://github.com/SamHerbert/SVG-Loaders">SVG-Loaders</a>
    </div>

</body>

</html>
