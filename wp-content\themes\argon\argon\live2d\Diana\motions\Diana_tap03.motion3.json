{"Version": 3, "Meta": {"Duration": 6.2, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 45, "TotalSegmentCount": 605, "TotalPointCount": 1828, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.107, 0.2, -1.107, 1, 0.322, -1.107, 0.444, 5.225, 0.567, 5.225, 1, 0.744, 5.225, 0.922, -3.275, 1.1, -3.275, 1, 1.2, -3.275, 1.3, -2.086, 1.4, -2.086, 1, 1.456, -2.086, 1.511, -2.236, 1.567, -2.236, 1, 1.756, -2.236, 1.944, 2.381, 2.133, 2.381, 1, 2.289, 2.381, 2.444, -1.447, 2.6, -1.447, 1, 2.711, -1.447, 2.822, 0.3, 2.933, 0.3, 1, 3.056, 0.3, 3.178, -0.313, 3.3, -0.313, 1, 3.433, -0.313, 3.567, -0.005, 3.7, -0.005, 1, 3.744, -0.005, 3.789, -0.014, 3.833, -0.014, 1, 3.911, -0.014, 3.989, 0.144, 4.067, 0.144, 1, 4.189, 0.144, 4.311, -0.21, 4.433, -0.21, 1, 4.611, -0.21, 4.789, -0.01, 4.967, -0.01, 1, 5, -0.01, 5.033, -0.015, 5.067, -0.015, 1, 5.078, -0.015, 5.089, 0.035, 5.1, 0.035, 1, 5.111, 0.035, 5.122, -0.079, 5.133, -0.079, 1, 5.256, -0.079, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, -30, 0, 1.667, -30, 1, 1.9, -30, 2.133, 30, 2.367, 30, 1, 2.556, 30, 2.744, -4, 2.933, -4, 1, 3.2, -4, 3.467, 30, 3.733, 30, 1, 3.989, 30, 4.244, -4, 4.5, -4, 1, 4.767, -4, 5.033, 30, 5.3, 30, 0, 6.2, 30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.233, 0, 0.467, -10, 0.7, -10, 1, 0.989, -10, 1.278, 0, 1.567, 0, 1, 1.789, 0, 2.011, -7, 2.233, -7, 1, 2.756, -7, 3.278, -2.058, 3.8, -1, 1, 4.356, 0.125, 4.911, 0, 5.467, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.989, 0, 1.278, 0, 1.567, 0, 1, 1.789, 0, 2.011, -2, 2.233, -2, 1, 2.756, -2, 3.278, -2, 3.8, -2, 1, 4.356, -2, 4.911, 0, 5.467, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.233, 0, 0.467, -10, 0.7, -10, 1, 0.989, -10, 1.278, -4.722, 1.567, 0, 1, 1.789, 3.632, 2.011, 4, 2.233, 4, 1, 2.756, 4, 3.278, 1.939, 3.8, 1, 1, 4.356, 0.001, 4.911, 0, 5.467, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.233, 0, 0.467, -4, 0.7, -4, 1, 0.989, -4, 1.278, 0, 1.567, 0, 1, 1.789, 0, 2.011, -3, 2.233, -3, 1, 2.422, -3, 2.611, -2, 2.8, -2, 1, 3.133, -2, 3.467, -3, 3.8, -3, 1, 4.044, -3, 4.289, -1.714, 4.533, -1, 1, 4.844, -0.091, 5.156, 0, 5.467, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.233, 0, 0.467, -6, 0.7, -6, 1, 0.989, -6, 1.278, 0, 1.567, 0, 1, 1.789, 0, 2.011, -21, 2.233, -21, 1, 2.422, -21, 2.611, -21.792, 2.8, -19, 1, 3.133, -14.074, 3.467, 6, 3.8, 6, 1, 4.044, 6, 4.289, -16, 4.533, -16, 1, 4.844, -16, 5.156, 0, 5.467, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.522, 0, 1.044, 0, 1.567, 0, 1, 1.789, 0, 2.011, 13, 2.233, 13, 1, 2.756, 13, 3.278, 10.15, 3.8, 6, 1, 4.356, 1.585, 4.911, 0, 5.467, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 1, 0.522, 0, 1.044, -17, 1.567, -17, 1, 1.789, -17, 2.011, 30, 2.233, 30, 1, 2.756, 30, 3.278, 30, 3.8, 30, 1, 4.356, 30, 4.911, 0, 5.467, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.767, 0, 0.8, 20.852, 0.833, 20.852, 1, 0.867, 20.852, 0.9, -20.253, 0.933, -20.253, 1, 0.978, -20.253, 1.022, 18.032, 1.067, 18.032, 1, 1.1, 18.032, 1.133, -20.033, 1.167, -20.033, 1, 1.233, -20.033, 1.3, 5.493, 1.367, 5.493, 1, 1.444, 5.493, 1.522, -0.119, 1.6, -0.119, 1, 1.7, -0.119, 1.8, 6.926, 1.9, 6.926, 1, 2.056, 6.926, 2.211, -2.313, 2.367, -2.313, 1, 2.467, -2.313, 2.567, 0.452, 2.667, 0.452, 1, 2.767, 0.452, 2.867, -0.087, 2.967, -0.087, 1, 3.078, -0.087, 3.189, 0.017, 3.3, 0.017, 1, 3.4, 0.017, 3.5, -0.003, 3.6, -0.003, 1, 3.7, -0.003, 3.8, 0.001, 3.9, 0.001, 1, 3.911, 0.001, 3.922, 0.001, 3.933, 0.001, 1, 4.033, 0.001, 4.133, 0, 4.233, 0, 1, 4.333, 0, 4.433, 0, 4.533, 0, 1, 4.544, 0, 4.556, 0, 4.567, 0, 1, 4.767, 0, 4.967, -5.238, 5.167, -5.238, 1, 5.367, -5.238, 5.567, 1.524, 5.767, 1.524, 1, 5.878, 1.524, 5.989, -0.3, 6.1, -0.3, 1, 6.133, -0.3, 6.167, -0.26, 6.2, -0.207]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 7.187, 0.333, 7.187, 1, 0.522, 7.187, 0.711, -19.882, 0.9, -19.882, 1, 1.233, -19.882, 1.567, 12.152, 1.9, 12.152, 1, 2.067, 12.152, 2.233, -2.528, 2.4, -2.528, 1, 2.522, -2.528, 2.644, 3.171, 2.767, 3.171, 1, 2.922, 3.171, 3.078, 0.081, 3.233, 0.081, 1, 3.444, 0.081, 3.656, 0.744, 3.867, 0.744, 1, 4.033, 0.744, 4.2, 0.023, 4.367, 0.023, 1, 4.478, 0.023, 4.589, 0.095, 4.7, 0.095, 1, 4.744, 0.095, 4.789, 0.073, 4.833, 0.073, 1, 4.878, 0.073, 4.922, 0.147, 4.967, 0.147, 1, 5.1, 0.147, 5.233, -0.137, 5.367, -0.137, 1, 5.444, -0.137, 5.522, 0, 5.6, 0, 1, 5.8, 0, 6, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 7.187, 0.333, 7.187, 1, 0.522, 7.187, 0.711, -19.882, 0.9, -19.882, 1, 1.233, -19.882, 1.567, 12.152, 1.9, 12.152, 1, 2.067, 12.152, 2.233, -2.528, 2.4, -2.528, 1, 2.522, -2.528, 2.644, 3.171, 2.767, 3.171, 1, 2.922, 3.171, 3.078, 0.081, 3.233, 0.081, 1, 3.444, 0.081, 3.656, 0.744, 3.867, 0.744, 1, 4.033, 0.744, 4.2, 0.023, 4.367, 0.023, 1, 4.478, 0.023, 4.589, 0.095, 4.7, 0.095, 1, 4.744, 0.095, 4.789, 0.073, 4.833, 0.073, 1, 4.878, 0.073, 4.922, 0.147, 4.967, 0.147, 1, 5.1, 0.147, 5.233, -0.137, 5.367, -0.137, 1, 5.444, -0.137, 5.522, 0, 5.6, 0, 1, 5.8, 0, 6, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 7.187, 0.333, 7.187, 1, 0.522, 7.187, 0.711, -19.882, 0.9, -19.882, 1, 1.233, -19.882, 1.567, 12.152, 1.9, 12.152, 1, 2.067, 12.152, 2.233, -2.528, 2.4, -2.528, 1, 2.522, -2.528, 2.644, 3.171, 2.767, 3.171, 1, 2.922, 3.171, 3.078, 0.081, 3.233, 0.081, 1, 3.444, 0.081, 3.656, 0.744, 3.867, 0.744, 1, 4.033, 0.744, 4.2, 0.023, 4.367, 0.023, 1, 4.478, 0.023, 4.589, 0.095, 4.7, 0.095, 1, 4.744, 0.095, 4.789, 0.073, 4.833, 0.073, 1, 4.878, 0.073, 4.922, 0.147, 4.967, 0.147, 1, 5.1, 0.147, 5.233, -0.137, 5.367, -0.137, 1, 5.444, -0.137, 5.522, 0, 5.6, 0, 1, 5.8, 0, 6, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 2.783, 0.3, 2.783, 1, 0.478, 2.783, 0.656, -7.101, 0.833, -7.101, 1, 1.2, -7.101, 1.567, 5.08, 1.933, 5.08, 1, 2.078, 5.08, 2.222, 1.869, 2.367, 1.869, 1, 2.478, 1.869, 2.589, 3.4, 2.7, 3.4, 1, 3.5, 3.4, 4.3, 0.041, 5.1, 0.041, 1, 5.111, 0.041, 5.122, 0.079, 5.133, 0.079, 1, 5.256, 0.079, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 2.783, 0.3, 2.783, 1, 0.478, 2.783, 0.656, -7.101, 0.833, -7.101, 1, 1.2, -7.101, 1.567, 5.08, 1.933, 5.08, 1, 2.078, 5.08, 2.222, 1.869, 2.367, 1.869, 1, 2.478, 1.869, 2.589, 3.4, 2.7, 3.4, 1, 3.5, 3.4, 4.3, 0.041, 5.1, 0.041, 1, 5.111, 0.041, 5.122, 0.079, 5.133, 0.079, 1, 5.256, 0.079, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.55, 0.2, -1.55, 1, 0.322, -1.55, 0.444, 7.315, 0.567, 7.315, 1, 0.744, 7.315, 0.922, -4.586, 1.1, -4.586, 1, 1.2, -4.586, 1.3, -2.92, 1.4, -2.92, 1, 1.456, -2.92, 1.511, -3.131, 1.567, -3.131, 1, 1.756, -3.131, 1.944, 3.334, 2.133, 3.334, 1, 2.289, 3.334, 2.444, -2.025, 2.6, -2.025, 1, 2.711, -2.025, 2.822, 0.421, 2.933, 0.421, 1, 3.056, 0.421, 3.178, -0.438, 3.3, -0.438, 1, 3.433, -0.438, 3.567, -0.006, 3.7, -0.006, 1, 3.744, -0.006, 3.789, -0.02, 3.833, -0.02, 1, 3.911, -0.02, 3.989, 0.202, 4.067, 0.202, 1, 4.189, 0.202, 4.311, -0.294, 4.433, -0.294, 1, 4.611, -0.294, 4.789, -0.014, 4.967, -0.014, 1, 5, -0.014, 5.033, -0.021, 5.067, -0.021, 1, 5.078, -0.021, 5.089, 0.049, 5.1, 0.049, 1, 5.111, 0.049, 5.122, -0.111, 5.133, -0.111, 1, 5.256, -0.111, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 2.783, 0.3, 2.783, 1, 0.478, 2.783, 0.656, -7.101, 0.833, -7.101, 1, 1.2, -7.101, 1.567, 5.08, 1.933, 5.08, 1, 2.078, 5.08, 2.222, 1.869, 2.367, 1.869, 1, 2.478, 1.869, 2.589, 3.4, 2.7, 3.4, 1, 3.5, 3.4, 4.3, 0.041, 5.1, 0.041, 1, 5.111, 0.041, 5.122, 0.079, 5.133, 0.079, 1, 5.256, 0.079, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.107, 0.2, -1.107, 1, 0.322, -1.107, 0.444, 5.225, 0.567, 5.225, 1, 0.744, 5.225, 0.922, -3.275, 1.1, -3.275, 1, 1.2, -3.275, 1.3, -2.086, 1.4, -2.086, 1, 1.456, -2.086, 1.511, -2.236, 1.567, -2.236, 1, 1.756, -2.236, 1.944, 2.381, 2.133, 2.381, 1, 2.289, 2.381, 2.444, -1.447, 2.6, -1.447, 1, 2.711, -1.447, 2.822, 0.3, 2.933, 0.3, 1, 3.056, 0.3, 3.178, -0.313, 3.3, -0.313, 1, 3.433, -0.313, 3.567, -0.005, 3.7, -0.005, 1, 3.744, -0.005, 3.789, -0.014, 3.833, -0.014, 1, 3.911, -0.014, 3.989, 0.144, 4.067, 0.144, 1, 4.189, 0.144, 4.311, -0.21, 4.433, -0.21, 1, 4.611, -0.21, 4.789, -0.01, 4.967, -0.01, 1, 5, -0.01, 5.033, -0.015, 5.067, -0.015, 1, 5.078, -0.015, 5.089, 0.035, 5.1, 0.035, 1, 5.111, 0.035, 5.122, -0.079, 5.133, -0.079, 1, 5.256, -0.079, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 2.783, 0.3, 2.783, 1, 0.478, 2.783, 0.656, -7.101, 0.833, -7.101, 1, 1.2, -7.101, 1.567, 5.08, 1.933, 5.08, 1, 2.078, 5.08, 2.222, 1.869, 2.367, 1.869, 1, 2.478, 1.869, 2.589, 3.4, 2.7, 3.4, 1, 3.5, 3.4, 4.3, 0.041, 5.1, 0.041, 1, 5.111, 0.041, 5.122, 0.079, 5.133, 0.079, 1, 5.256, 0.079, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.107, 0.2, -1.107, 1, 0.322, -1.107, 0.444, 5.225, 0.567, 5.225, 1, 0.744, 5.225, 0.922, -3.275, 1.1, -3.275, 1, 1.2, -3.275, 1.3, -2.086, 1.4, -2.086, 1, 1.456, -2.086, 1.511, -2.236, 1.567, -2.236, 1, 1.756, -2.236, 1.944, 2.381, 2.133, 2.381, 1, 2.289, 2.381, 2.444, -1.447, 2.6, -1.447, 1, 2.711, -1.447, 2.822, 0.3, 2.933, 0.3, 1, 3.056, 0.3, 3.178, -0.313, 3.3, -0.313, 1, 3.433, -0.313, 3.567, -0.005, 3.7, -0.005, 1, 3.744, -0.005, 3.789, -0.014, 3.833, -0.014, 1, 3.911, -0.014, 3.989, 0.144, 4.067, 0.144, 1, 4.189, 0.144, 4.311, -0.21, 4.433, -0.21, 1, 4.611, -0.21, 4.789, -0.01, 4.967, -0.01, 1, 5, -0.01, 5.033, -0.015, 5.067, -0.015, 1, 5.078, -0.015, 5.089, 0.035, 5.1, 0.035, 1, 5.111, 0.035, 5.122, -0.079, 5.133, -0.079, 1, 5.256, -0.079, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 3.339, 0.3, 3.339, 1, 0.478, 3.339, 0.656, -8.522, 0.833, -8.522, 1, 1.2, -8.522, 1.567, 6.095, 1.933, 6.095, 1, 2.078, 6.095, 2.222, 2.243, 2.367, 2.243, 1, 2.478, 2.243, 2.589, 4.08, 2.7, 4.08, 1, 3.5, 4.08, 4.3, 0.049, 5.1, 0.049, 1, 5.111, 0.049, 5.122, 0.095, 5.133, 0.095, 1, 5.256, 0.095, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.328, 0.2, -1.328, 1, 0.322, -1.328, 0.444, 6.27, 0.567, 6.27, 1, 0.744, 6.27, 0.922, -3.931, 1.1, -3.931, 1, 1.2, -3.931, 1.3, -2.503, 1.4, -2.503, 1, 1.456, -2.503, 1.511, -2.684, 1.567, -2.684, 1, 1.756, -2.684, 1.944, 2.857, 2.133, 2.857, 1, 2.289, 2.857, 2.444, -1.736, 2.6, -1.736, 1, 2.711, -1.736, 2.822, 0.361, 2.933, 0.361, 1, 3.056, 0.361, 3.178, -0.376, 3.3, -0.376, 1, 3.433, -0.376, 3.567, -0.006, 3.7, -0.006, 1, 3.744, -0.006, 3.789, -0.017, 3.833, -0.017, 1, 3.911, -0.017, 3.989, 0.173, 4.067, 0.173, 1, 4.189, 0.173, 4.311, -0.252, 4.433, -0.252, 1, 4.611, -0.252, 4.789, -0.012, 4.967, -0.012, 1, 5, -0.012, 5.033, -0.018, 5.067, -0.018, 1, 5.078, -0.018, 5.089, 0.042, 5.1, 0.042, 1, 5.111, 0.042, 5.122, -0.095, 5.133, -0.095, 1, 5.256, -0.095, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 3.339, 0.3, 3.339, 1, 0.478, 3.339, 0.656, -8.522, 0.833, -8.522, 1, 1.2, -8.522, 1.567, 6.095, 1.933, 6.095, 1, 2.078, 6.095, 2.222, 2.243, 2.367, 2.243, 1, 2.478, 2.243, 2.589, 4.08, 2.7, 4.08, 1, 3.5, 4.08, 4.3, 0.049, 5.1, 0.049, 1, 5.111, 0.049, 5.122, 0.095, 5.133, 0.095, 1, 5.256, 0.095, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.328, 0.2, -1.328, 1, 0.322, -1.328, 0.444, 6.27, 0.567, 6.27, 1, 0.744, 6.27, 0.922, -3.931, 1.1, -3.931, 1, 1.2, -3.931, 1.3, -2.503, 1.4, -2.503, 1, 1.456, -2.503, 1.511, -2.684, 1.567, -2.684, 1, 1.756, -2.684, 1.944, 2.857, 2.133, 2.857, 1, 2.289, 2.857, 2.444, -1.736, 2.6, -1.736, 1, 2.711, -1.736, 2.822, 0.361, 2.933, 0.361, 1, 3.056, 0.361, 3.178, -0.376, 3.3, -0.376, 1, 3.433, -0.376, 3.567, -0.006, 3.7, -0.006, 1, 3.744, -0.006, 3.789, -0.017, 3.833, -0.017, 1, 3.911, -0.017, 3.989, 0.173, 4.067, 0.173, 1, 4.189, 0.173, 4.311, -0.252, 4.433, -0.252, 1, 4.611, -0.252, 4.789, -0.012, 4.967, -0.012, 1, 5, -0.012, 5.033, -0.018, 5.067, -0.018, 1, 5.078, -0.018, 5.089, 0.042, 5.1, 0.042, 1, 5.111, 0.042, 5.122, -0.095, 5.133, -0.095, 1, 5.256, -0.095, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.744, 0, 0.756, 30, 0.767, 30, 1, 0.789, 30, 0.811, 30, 0.833, 30, 1, 0.844, 30, 0.856, -30, 0.867, -30, 1, 0.9, -30, 0.933, -30, 0.967, -30, 1, 0.978, -30, 0.989, 30, 1, 30, 1, 1.022, 30, 1.044, 30, 1.067, 30, 1, 1.078, 30, 1.089, -30, 1.1, -30, 1, 1.122, -30, 1.144, -30, 1.167, -30, 1, 1.189, -30, 1.211, 30, 1.233, 30, 1, 1.278, 30, 1.322, 30, 1.367, 30, 1, 1.422, 30, 1.478, -16.85, 1.533, -16.85, 1, 1.622, -16.85, 1.711, 21.053, 1.8, 21.053, 1, 1.967, 21.053, 2.133, -9.195, 2.3, -9.195, 1, 2.378, -9.195, 2.456, 3.811, 2.533, 3.811, 1, 2.622, 3.811, 2.711, -1.585, 2.8, -1.585, 1, 2.878, -1.585, 2.956, 0.663, 3.033, 0.663, 1, 3.122, 0.663, 3.211, -0.275, 3.3, -0.275, 1, 3.378, -0.275, 3.456, 0.116, 3.533, 0.116, 1, 3.622, 0.116, 3.711, -0.048, 3.8, -0.048, 1, 3.878, -0.048, 3.956, 0.02, 4.033, 0.02, 1, 4.122, 0.02, 4.211, -0.008, 4.3, -0.008, 1, 4.378, -0.008, 4.456, 0.004, 4.533, 0.004, 1, 4.622, 0.004, 4.711, -0.001, 4.8, -0.001, 1, 4.811, -0.001, 4.822, -0.001, 4.833, -0.001, 1, 4.911, -0.001, 4.989, -10.663, 5.067, -10.663, 1, 5.289, -10.663, 5.511, 7.493, 5.733, 7.493, 1, 5.811, 7.493, 5.889, -3.089, 5.967, -3.089, 1, 6.044, -3.089, 6.122, 0.268, 6.2, 1.107]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.744, 0, 0.756, 30, 0.767, 30, 1, 0.789, 30, 0.811, 30, 0.833, 30, 1, 0.844, 30, 0.856, -30, 0.867, -30, 1, 0.9, -30, 0.933, -30, 0.967, -30, 1, 0.978, -30, 0.989, 30, 1, 30, 1, 1.022, 30, 1.044, 30, 1.067, 30, 1, 1.078, 30, 1.089, -30, 1.1, -30, 1, 1.122, -30, 1.144, -30, 1.167, -30, 1, 1.189, -30, 1.211, 30, 1.233, 30, 1, 1.278, 30, 1.322, 30, 1.367, 30, 1, 1.422, 30, 1.478, -16.85, 1.533, -16.85, 1, 1.622, -16.85, 1.711, 21.053, 1.8, 21.053, 1, 1.967, 21.053, 2.133, -9.195, 2.3, -9.195, 1, 2.378, -9.195, 2.456, 3.811, 2.533, 3.811, 1, 2.622, 3.811, 2.711, -1.585, 2.8, -1.585, 1, 2.878, -1.585, 2.956, 0.663, 3.033, 0.663, 1, 3.122, 0.663, 3.211, -0.275, 3.3, -0.275, 1, 3.378, -0.275, 3.456, 0.116, 3.533, 0.116, 1, 3.622, 0.116, 3.711, -0.048, 3.8, -0.048, 1, 3.878, -0.048, 3.956, 0.02, 4.033, 0.02, 1, 4.122, 0.02, 4.211, -0.008, 4.3, -0.008, 1, 4.378, -0.008, 4.456, 0.004, 4.533, 0.004, 1, 4.622, 0.004, 4.711, -0.001, 4.8, -0.001, 1, 4.811, -0.001, 4.822, -0.001, 4.833, -0.001, 1, 4.911, -0.001, 4.989, -10.663, 5.067, -10.663, 1, 5.289, -10.663, 5.511, 7.493, 5.733, 7.493, 1, 5.811, 7.493, 5.889, -3.089, 5.967, -3.089, 1, 6.044, -3.089, 6.122, 0.268, 6.2, 1.107]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.233, 1, 0.467, 1, 0.7, 1, 1, 0.733, 1, 0.767, 0, 0.8, 0, 1, 0.844, 0, 0.889, 1, 0.933, 1, 1, 0.967, 1, 1, 0, 1.033, 0, 1, 1.067, 0, 1.1, 1, 1.133, 1, 1, 1.267, 1, 1.4, 1, 1.533, 1, 1, 1.767, 1, 2, 0, 2.233, 0, 1, 3.089, 0, 3.944, 0, 4.8, 0, 1, 5.089, 0, 5.378, 1, 5.667, 1, 0, 6.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.511, 0, 1.022, 0, 1.533, 0, 1, 1.767, 0, 2, 1, 2.233, 1, 1, 3.089, 1, 3.944, 1, 4.8, 1, 1, 5.089, 1, 5.378, 0, 5.667, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.233, 1, 0.467, 1, 0.7, 1, 1, 0.733, 1, 0.767, 0, 0.8, 0, 1, 0.844, 0, 0.889, 1, 0.933, 1, 1, 0.967, 1, 1, 0, 1.033, 0, 1, 1.067, 0, 1.1, 1, 1.133, 1, 1, 1.267, 1, 1.4, 1, 1.533, 1, 1, 1.767, 1, 2, 0, 2.233, 0, 1, 3.089, 0, 3.944, 0, 4.8, 0, 1, 5.089, 0, 5.378, 1, 5.667, 1, 0, 6.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.511, 0, 1.022, 0, 1.533, 0, 1, 1.767, 0, 2, 1, 2.233, 1, 1, 3.089, 1, 3.944, 1, 4.8, 1, 1, 5.089, 1, 5.378, 0, 5.667, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0.1, 1, 0.522, 0.1, 1.044, -0.4, 1.567, -0.4, 1, 1.811, -0.4, 2.056, 0.2, 2.3, 0.2, 1, 2.467, 0.2, 2.633, 0.2, 2.8, 0.2, 1, 3.522, 0.2, 4.244, 0.2, 4.967, 0.2, 1, 5.144, 0.2, 5.322, 0.1, 5.5, 0.1, 0, 6.2, 0.1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.522, 0, 1.044, 0, 1.567, 0, 1, 1.811, 0, 2.056, 1, 2.3, 1, 1, 2.467, 1, 2.633, 1, 2.8, 1, 1, 3.522, 1, 4.244, 1, 4.967, 1, 1, 5.144, 1, 5.322, 0, 5.5, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 0.467, 0, 0.933, 0, 1.4, 0, 1, 1.744, 0, 2.089, 1, 2.433, 1, 1, 2.956, 1, 3.478, 1, 4, 1, 1, 4.211, 1, 4.422, 0, 4.633, 0, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.767, 0, 0.8, 6.951, 0.833, 6.951, 1, 0.867, 6.951, 0.9, -6.751, 0.933, -6.751, 1, 0.978, -6.751, 1.022, 6.011, 1.067, 6.011, 1, 1.1, 6.011, 1.133, -6.678, 1.167, -6.678, 1, 1.233, -6.678, 1.3, 1.831, 1.367, 1.831, 1, 1.444, 1.831, 1.522, -0.04, 1.6, -0.04, 1, 1.7, -0.04, 1.8, 2.309, 1.9, 2.309, 1, 2.056, 2.309, 2.211, -0.771, 2.367, -0.771, 1, 2.467, -0.771, 2.567, 0.151, 2.667, 0.151, 1, 2.767, 0.151, 2.867, -0.029, 2.967, -0.029, 1, 3.078, -0.029, 3.189, 0.006, 3.3, 0.006, 1, 3.4, 0.006, 3.5, -0.001, 3.6, -0.001, 1, 3.7, -0.001, 3.8, 0, 3.9, 0, 1, 3.911, 0, 3.922, 0, 3.933, 0, 1, 4.033, 0, 4.133, 0, 4.233, 0, 1, 4.333, 0, 4.433, 0, 4.533, 0, 1, 4.544, 0, 4.556, 0, 4.567, 0, 1, 4.767, 0, 4.967, -1.746, 5.167, -1.746, 1, 5.367, -1.746, 5.567, 0.508, 5.767, 0.508, 1, 5.878, 0.508, 5.989, -0.1, 6.1, -0.1, 1, 6.133, -0.1, 6.167, -0.087, 6.2, -0.069]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 4.452, 0.3, 4.452, 1, 0.478, 4.452, 0.656, -11.362, 0.833, -11.362, 1, 1.2, -11.362, 1.567, 8.127, 1.933, 8.127, 1, 2.078, 8.127, 2.222, 2.99, 2.367, 2.99, 1, 2.478, 2.99, 2.589, 5.439, 2.7, 5.439, 1, 3.5, 5.439, 4.3, 0.066, 5.1, 0.066, 1, 5.111, 0.066, 5.122, 0.126, 5.133, 0.126, 1, 5.256, 0.126, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.771, 0.2, -1.771, 1, 0.322, -1.771, 0.444, 8.36, 0.567, 8.36, 1, 0.744, 8.36, 0.922, -5.241, 1.1, -5.241, 1, 1.2, -5.241, 1.3, -3.337, 1.4, -3.337, 1, 1.456, -3.337, 1.511, -3.578, 1.567, -3.578, 1, 1.756, -3.578, 1.944, 3.81, 2.133, 3.81, 1, 2.289, 3.81, 2.444, -2.315, 2.6, -2.315, 1, 2.711, -2.315, 2.822, 0.481, 2.933, 0.481, 1, 3.056, 0.481, 3.178, -0.501, 3.3, -0.501, 1, 3.433, -0.501, 3.567, -0.007, 3.7, -0.007, 1, 3.744, -0.007, 3.789, -0.023, 3.833, -0.023, 1, 3.911, -0.023, 3.989, 0.23, 4.067, 0.23, 1, 4.189, 0.23, 4.311, -0.336, 4.433, -0.336, 1, 4.611, -0.336, 4.789, -0.016, 4.967, -0.016, 1, 5, -0.016, 5.033, -0.023, 5.067, -0.023, 1, 5.078, -0.023, 5.089, 0.056, 5.1, 0.056, 1, 5.111, 0.056, 5.122, -0.126, 5.133, -0.126, 1, 5.256, -0.126, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 4.452, 0.3, 4.452, 1, 0.478, 4.452, 0.656, -11.362, 0.833, -11.362, 1, 1.2, -11.362, 1.567, 8.127, 1.933, 8.127, 1, 2.078, 8.127, 2.222, 2.99, 2.367, 2.99, 1, 2.478, 2.99, 2.589, 5.439, 2.7, 5.439, 1, 3.5, 5.439, 4.3, 0.066, 5.1, 0.066, 1, 5.111, 0.066, 5.122, 0.126, 5.133, 0.126, 1, 5.256, 0.126, 5.378, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.07, 0.333, 1.07, 1, 0.533, 1.07, 0.733, -2.243, 0.933, -2.243, 1, 1.278, -2.243, 1.622, 1.9, 1.967, 1.9, 1, 2.122, 1.9, 2.278, 0.555, 2.433, 0.555, 1, 2.556, 0.555, 2.678, 0.867, 2.8, 0.867, 1, 3.389, 0.867, 3.978, 0.033, 4.567, 0.033, 1, 4.589, 0.033, 4.611, 0.034, 4.633, 0.034, 1, 4.8, 0.034, 4.967, 0.007, 5.133, 0.007, 1, 5.144, 0.007, 5.156, 0.018, 5.167, 0.018, 1, 5.278, 0.018, 5.389, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -0.522, 0.233, -0.522, 1, 0.344, -0.522, 0.456, 0.523, 0.567, 0.523, 1, 0.711, 0.523, 0.856, -0.584, 1, -0.584, 1, 1.133, -0.584, 1.267, 0.367, 1.4, 0.367, 1, 1.522, 0.367, 1.644, -0.365, 1.767, -0.365, 1, 1.933, -0.365, 2.1, 0.287, 2.267, 0.287, 1, 2.367, 0.287, 2.467, -0.442, 2.567, -0.442, 1, 2.7, -0.442, 2.833, 0.342, 2.967, 0.342, 1, 3.1, 0.342, 3.233, -0.053, 3.367, -0.053, 1, 3.478, -0.053, 3.589, 0.091, 3.7, 0.091, 1, 3.778, 0.091, 3.856, 0.059, 3.933, 0.059, 1, 3.956, 0.059, 3.978, 0.061, 4, 0.061, 1, 4.133, 0.061, 4.267, -0.004, 4.4, -0.004, 1, 4.533, -0.004, 4.667, 0.034, 4.8, 0.034, 1, 4.856, 0.034, 4.911, 0.022, 4.967, 0.022, 1, 4.978, 0.022, 4.989, 0.031, 5, 0.031, 1, 5.056, 0.031, 5.111, -0.018, 5.167, -0.018, 1, 5.278, -0.018, 5.389, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -0.214, 0.3, -0.214, 1, 0.433, -0.214, 0.567, 1.589, 0.7, 1.589, 1, 0.844, 1.589, 0.989, -1.955, 1.133, -1.955, 1, 1.278, -1.955, 1.422, 0.583, 1.567, 0.583, 1, 1.667, 0.583, 1.767, -0.433, 1.867, -0.433, 1, 2, -0.433, 2.133, 0.988, 2.267, 0.988, 1, 2.411, 0.988, 2.556, -1.092, 2.7, -1.092, 1, 2.833, -1.092, 2.967, 0.764, 3.1, 0.764, 1, 3.233, 0.764, 3.367, -0.412, 3.5, -0.412, 1, 3.633, -0.412, 3.767, 0.227, 3.9, 0.227, 1, 4.078, 0.227, 4.256, -0.087, 4.433, -0.087, 1, 4.611, -0.087, 4.789, 0.036, 4.967, 0.036, 1, 4.978, 0.036, 4.989, 0.005, 5, 0.005, 1, 5.022, 0.005, 5.044, 0.009, 5.067, 0.009, 1, 5.1, 0.009, 5.133, 0, 5.167, 0, 1, 5.511, 0, 5.856, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.222, 0, 0.344, -0.514, 0.467, -0.514, 1, 0.6, -0.514, 0.733, 1.923, 0.867, 1.923, 1, 1, 1.923, 1.133, -2.429, 1.267, -2.429, 1, 1.411, -2.429, 1.556, 1.363, 1.7, 1.363, 1, 1.811, 1.363, 1.922, -1.096, 2.033, -1.096, 1, 2.167, -1.096, 2.3, 1.489, 2.433, 1.489, 1, 2.567, 1.489, 2.7, -1.597, 2.833, -1.597, 1, 2.967, -1.597, 3.1, 1.357, 3.233, 1.357, 1, 3.367, 1.357, 3.5, -0.88, 3.633, -0.88, 1, 3.767, -0.88, 3.9, 0.544, 4.033, 0.544, 1, 4.178, 0.544, 4.322, -0.234, 4.467, -0.234, 1, 4.611, -0.234, 4.756, 0.121, 4.9, 0.121, 1, 4.922, 0.121, 4.944, 0.112, 4.967, 0.112, 1, 4.978, 0.112, 4.989, 0.114, 5, 0.114, 1, 5.044, 0.114, 5.089, 0.024, 5.133, 0.024, 1, 5.144, 0.024, 5.156, 0.032, 5.167, 0.032, 1, 5.233, 0.032, 5.3, -0.066, 5.367, -0.066, 1, 5.478, -0.066, 5.589, 0.063, 5.7, 0.063, 1, 5.811, 0.063, 5.922, -0.06, 6.033, -0.06, 1, 6.089, -0.06, 6.144, -0.031, 6.2, -0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 1.07, 0.333, 1.07, 1, 0.533, 1.07, 0.733, -2.243, 0.933, -2.243, 1, 1.278, -2.243, 1.622, 1.9, 1.967, 1.9, 1, 2.122, 1.9, 2.278, 0.555, 2.433, 0.555, 1, 2.556, 0.555, 2.678, 0.867, 2.8, 0.867, 1, 3.389, 0.867, 3.978, 0.033, 4.567, 0.033, 1, 4.589, 0.033, 4.611, 0.034, 4.633, 0.034, 1, 4.8, 0.034, 4.967, 0.007, 5.133, 0.007, 1, 5.144, 0.007, 5.156, 0.018, 5.167, 0.018, 1, 5.278, 0.018, 5.389, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -0.522, 0.233, -0.522, 1, 0.344, -0.522, 0.456, 0.523, 0.567, 0.523, 1, 0.711, 0.523, 0.856, -0.584, 1, -0.584, 1, 1.133, -0.584, 1.267, 0.367, 1.4, 0.367, 1, 1.522, 0.367, 1.644, -0.365, 1.767, -0.365, 1, 1.933, -0.365, 2.1, 0.287, 2.267, 0.287, 1, 2.367, 0.287, 2.467, -0.442, 2.567, -0.442, 1, 2.7, -0.442, 2.833, 0.342, 2.967, 0.342, 1, 3.1, 0.342, 3.233, -0.053, 3.367, -0.053, 1, 3.478, -0.053, 3.589, 0.091, 3.7, 0.091, 1, 3.778, 0.091, 3.856, 0.059, 3.933, 0.059, 1, 3.956, 0.059, 3.978, 0.061, 4, 0.061, 1, 4.133, 0.061, 4.267, -0.004, 4.4, -0.004, 1, 4.533, -0.004, 4.667, 0.034, 4.8, 0.034, 1, 4.856, 0.034, 4.911, 0.022, 4.967, 0.022, 1, 4.978, 0.022, 4.989, 0.031, 5, 0.031, 1, 5.056, 0.031, 5.111, -0.018, 5.167, -0.018, 1, 5.278, -0.018, 5.389, 0, 5.5, 0, 1, 5.733, 0, 5.967, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -0.214, 0.3, -0.214, 1, 0.433, -0.214, 0.567, 1.589, 0.7, 1.589, 1, 0.844, 1.589, 0.989, -1.955, 1.133, -1.955, 1, 1.278, -1.955, 1.422, 0.583, 1.567, 0.583, 1, 1.667, 0.583, 1.767, -0.433, 1.867, -0.433, 1, 2, -0.433, 2.133, 0.988, 2.267, 0.988, 1, 2.411, 0.988, 2.556, -1.092, 2.7, -1.092, 1, 2.833, -1.092, 2.967, 0.764, 3.1, 0.764, 1, 3.233, 0.764, 3.367, -0.412, 3.5, -0.412, 1, 3.633, -0.412, 3.767, 0.227, 3.9, 0.227, 1, 4.078, 0.227, 4.256, -0.087, 4.433, -0.087, 1, 4.611, -0.087, 4.789, 0.036, 4.967, 0.036, 1, 4.978, 0.036, 4.989, 0.005, 5, 0.005, 1, 5.022, 0.005, 5.044, 0.009, 5.067, 0.009, 1, 5.1, 0.009, 5.133, 0, 5.167, 0, 1, 5.511, 0, 5.856, 0, 6.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.222, 0, 0.344, -0.514, 0.467, -0.514, 1, 0.6, -0.514, 0.733, 1.923, 0.867, 1.923, 1, 1, 1.923, 1.133, -2.429, 1.267, -2.429, 1, 1.411, -2.429, 1.556, 1.363, 1.7, 1.363, 1, 1.811, 1.363, 1.922, -1.096, 2.033, -1.096, 1, 2.167, -1.096, 2.3, 1.489, 2.433, 1.489, 1, 2.567, 1.489, 2.7, -1.597, 2.833, -1.597, 1, 2.967, -1.597, 3.1, 1.357, 3.233, 1.357, 1, 3.367, 1.357, 3.5, -0.88, 3.633, -0.88, 1, 3.767, -0.88, 3.9, 0.544, 4.033, 0.544, 1, 4.178, 0.544, 4.322, -0.234, 4.467, -0.234, 1, 4.611, -0.234, 4.756, 0.121, 4.9, 0.121, 1, 4.922, 0.121, 4.944, 0.112, 4.967, 0.112, 1, 4.978, 0.112, 4.989, 0.114, 5, 0.114, 1, 5.044, 0.114, 5.089, 0.024, 5.133, 0.024, 1, 5.144, 0.024, 5.156, 0.032, 5.167, 0.032, 1, 5.233, 0.032, 5.3, -0.066, 5.367, -0.066, 1, 5.478, -0.066, 5.589, 0.063, 5.7, 0.063, 1, 5.811, 0.063, 5.922, -0.06, 6.033, -0.06, 1, 6.089, -0.06, 6.144, -0.031, 6.2, -0.002]}]}