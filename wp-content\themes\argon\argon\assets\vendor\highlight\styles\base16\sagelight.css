/*!
  Theme: Sagelight
  Author: <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme sagelight
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #f8f8f8  Default Background
base01  #e8e8e8  Lighter Background (Used for status bars, line number and folding marks)
base02  #d8d8d8  Selection Background
base03  #b8b8b8  Comments, Invisibles, Line Highlighting
base04  #585858  Dark Foreground (Used for status bars)
base05  #383838  Default Foreground, Caret, Delimiters, Operators
base06  #282828  Light Foreground (Not often used)
base07  #181818  Light Background (Not often used)
base08  #fa8480  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ffaa61  Integers, <PERSON><PERSON><PERSON>, Constants, XML Attributes, Markup Link Url
base0A  #ffdc61  Classes, Markup Bold, Search Text Background
base0B  #a0d2c8  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #a2d6f5  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #a0a7d2  Functions, Methods, Attribute IDs, Headings
base0E  #c8a0d2  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #d2b2a0  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #383838;
  background: #f8f8f8;
}

.hljs::selection,
.hljs ::selection {
  background-color: #d8d8d8;
  color: #383838;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #b8b8b8 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #b8b8b8;
}

/* base04 - #585858 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #585858;
}

/* base05 - #383838 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #383838;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #fa8480;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ffaa61;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #ffdc61;
}

.hljs-strong {
  font-weight:bold;
  color: #ffdc61;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #a0d2c8;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #a2d6f5;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #a0a7d2;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #c8a0d2;
}
.hljs-emphasis {
  color: #c8a0d2;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #d2b2a0;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
