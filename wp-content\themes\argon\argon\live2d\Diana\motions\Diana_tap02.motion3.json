{"Version": 3, "Meta": {"Duration": 4.567, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 74, "TotalSegmentCount": 567, "TotalPointCount": 1687, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param2", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, -0.028, 1, 0.011, -0.028, 0.022, -0.081, 0.033, -0.081, 1, 0.089, -0.081, 0.144, 3.417, 0.2, 3.417, 1, 0.289, 3.417, 0.378, -9.845, 0.467, -9.845, 1, 0.567, -9.845, 0.667, 5.352, 0.767, 5.352, 1, 0.889, 5.352, 1.011, -2.326, 1.133, -2.326, 1, 1.244, -2.326, 1.356, 0.891, 1.467, 0.891, 1, 1.578, 0.891, 1.689, -0.28, 1.8, -0.28, 1, 1.922, -0.28, 2.044, 0.194, 2.167, 0.194, 1, 2.311, 0.194, 2.456, -5.446, 2.6, -5.446, 1, 2.689, -5.446, 2.778, 17.741, 2.867, 17.741, 1, 2.978, 17.741, 3.089, -10.227, 3.2, -10.227, 1, 3.311, -10.227, 3.422, 4.379, 3.533, 4.379, 1, 3.644, 4.379, 3.756, -1.662, 3.867, -1.662, 1, 3.989, -1.662, 4.111, 0.614, 4.233, 0.614, 1, 4.344, 0.614, 4.456, -0.073, 4.567, -0.197]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 30, 0, 4.567, 30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.122, 0, 0.244, 7, 0.367, 7, 1, 1.044, 7, 1.722, 6.793, 2.4, 6, 1, 2.533, 5.844, 2.667, -5, 2.8, -5, 0, 4.567, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.122, 0, 0.244, 5, 0.367, 5, 1, 1.044, 5, 1.722, 4.792, 2.4, 4, 1, 2.533, 3.844, 2.667, -3, 2.8, -3, 0, 4.567, -3]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.122, 0, 0.244, 6.72, 0.367, 7, 1, 1.044, 8.554, 1.722, 9, 2.4, 9, 1, 2.533, 9, 2.667, -10, 2.8, -10, 0, 4.567, -10]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.122, 0, 0.244, 5.313, 0.367, 6, 1, 1.044, 9.81, 1.722, 11, 2.4, 11, 1, 2.533, 11, 2.667, -8, 2.8, -8, 0, 4.567, -8]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.122, 0, 0.244, -13, 0.367, -13, 1, 1.044, -13, 1.722, -13, 2.4, -13, 1, 2.533, -13, 2.667, 0, 2.8, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.722, 0, 0.744, 30, 0.767, 30, 1, 0.8, 30, 0.833, 30, 0.867, 30, 1, 0.9, 30, 0.933, -30, 0.967, -30, 1, 1.011, -30, 1.056, -30, 1.1, -30, 1, 1.133, -30, 1.167, 30, 1.2, 30, 1, 1.222, 30, 1.244, 30, 1.267, 30, 1, 1.344, 30, 1.422, -13.007, 1.5, -13.007, 1, 1.578, -13.007, 1.656, 5.334, 1.733, 5.334, 1, 1.822, 5.334, 1.911, -2.223, 2, -2.223, 1, 2.078, -2.223, 2.156, 0.927, 2.233, 0.927, 1, 2.322, 0.927, 2.411, -0.386, 2.5, -0.386, 1, 2.578, -0.386, 2.656, 0.162, 2.733, 0.162, 1, 2.822, 0.162, 2.911, -0.067, 3, -0.067, 1, 3.067, -0.067, 3.133, 30, 3.2, 30, 1, 3.233, 30, 3.267, 30, 3.3, 30, 1, 3.333, 30, 3.367, -30, 3.4, -30, 1, 3.444, -30, 3.489, -30, 3.533, -30, 1, 3.567, -30, 3.6, 30, 3.633, 30, 1, 3.656, 30, 3.678, 30, 3.7, 30, 1, 3.778, 30, 3.856, -13.007, 3.933, -13.007, 1, 4.011, -13.007, 4.089, 5.333, 4.167, 5.333, 1, 4.256, 5.333, 4.344, -2.223, 4.433, -2.223, 1, 4.478, -2.223, 4.522, -1.194, 4.567, -0.313]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.722, 0, 0.744, 30, 0.767, 30, 1, 0.8, 30, 0.833, 30, 0.867, 30, 1, 0.9, 30, 0.933, -30, 0.967, -30, 1, 1.011, -30, 1.056, -30, 1.1, -30, 1, 1.133, -30, 1.167, 30, 1.2, 30, 1, 1.222, 30, 1.244, 30, 1.267, 30, 1, 1.344, 30, 1.422, -13.007, 1.5, -13.007, 1, 1.578, -13.007, 1.656, 5.334, 1.733, 5.334, 1, 1.822, 5.334, 1.911, -2.223, 2, -2.223, 1, 2.078, -2.223, 2.156, 0.927, 2.233, 0.927, 1, 2.322, 0.927, 2.411, -0.386, 2.5, -0.386, 1, 2.578, -0.386, 2.656, 0.162, 2.733, 0.162, 1, 2.822, 0.162, 2.911, -0.067, 3, -0.067, 1, 3.067, -0.067, 3.133, 30, 3.2, 30, 1, 3.233, 30, 3.267, 30, 3.3, 30, 1, 3.333, 30, 3.367, -30, 3.4, -30, 1, 3.444, -30, 3.489, -30, 3.533, -30, 1, 3.567, -30, 3.6, 30, 3.633, 30, 1, 3.656, 30, 3.678, 30, 3.7, 30, 1, 3.778, 30, 3.856, -13.007, 3.933, -13.007, 1, 4.011, -13.007, 4.089, 5.333, 4.167, 5.333, 1, 4.256, 5.333, 4.344, -2.223, 4.433, -2.223, 1, 4.478, -2.223, 4.522, -1.194, 4.567, -0.313]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.733, 1, 0.8, 0, 0.867, 0, 1, 0.933, 0, 1, 1, 1.067, 1, 1, 1.744, 1, 2.422, 1, 3.1, 1, 1, 3.167, 1, 3.233, 0, 3.3, 0, 1, 3.367, 0, 3.433, 1, 3.5, 1, 0, 4.567, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.733, 1, 0.8, 0, 0.867, 0, 1, 0.933, 0, 1, 1, 1.067, 1, 1, 1.744, 1, 2.422, 1, 3.1, 1, 1, 3.167, 1, 3.233, 0, 3.3, 0, 1, 3.367, 0, 3.433, 1, 3.5, 1, 0, 4.567, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.2, 1, 0.222, -0.2, 0.444, 0.1, 0.667, 0.1, 1, 0.756, 0.1, 0.844, 0.1, 0.933, 0.1, 1, 1.022, 0.1, 1.111, 0.2, 1.2, 0.2, 1, 1.267, 0.2, 1.333, -0.2, 1.4, -0.2, 1, 1.456, -0.2, 1.511, 0.2, 1.567, 0.2, 1, 1.667, 0.2, 1.767, -0.1, 1.867, -0.1, 1, 2.178, -0.1, 2.489, 0.2, 2.8, 0.2, 1, 2.867, 0.2, 2.933, -0.4, 3, -0.4, 1, 3.078, -0.4, 3.156, 0.1, 3.233, 0.1, 1, 3.278, 0.1, 3.322, -0.4, 3.367, -0.4, 1, 3.433, -0.4, 3.5, -0.4, 3.567, -0.4, 1, 3.644, -0.4, 3.722, -0.324, 3.8, -0.3, 1, 3.944, -0.255, 4.089, -0.243, 4.233, -0.2, 1, 4.278, -0.187, 4.322, 0.5, 4.367, 0.5, 0, 4.567, 0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0.2, 1, 0.222, 0.2, 0.444, 0, 0.667, 0, 1, 0.756, 0, 0.844, 0.6, 0.933, 0.6, 1, 1.022, 0.6, 1.111, 0.4, 1.2, 0.4, 1, 1.267, 0.4, 1.333, 0.5, 1.4, 0.5, 1, 1.456, 0.5, 1.511, 0.503, 1.567, 0.4, 1, 1.667, 0.214, 1.767, 0, 1.867, 0, 1, 2.178, 0, 2.489, 0.103, 2.8, 0.4, 1, 2.867, 0.464, 2.933, 0.6, 3, 0.6, 1, 3.078, 0.6, 3.156, 0, 3.233, 0, 1, 3.278, 0, 3.322, 0.5, 3.367, 0.5, 1, 3.433, 0.5, 3.5, 0.4, 3.567, 0.4, 1, 3.644, 0.4, 3.722, 0.8, 3.8, 0.8, 1, 3.944, 0.8, 4.089, 0.607, 4.233, 0.2, 1, 4.278, 0.075, 4.322, 0, 4.367, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, -1, 0, 4.567, -1]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -1, 1, 0.167, -1, 0.333, -7, 0.5, -7, 1, 0.622, -7, 0.744, -2, 0.867, -2, 1, 1, -2, 1.133, -8, 1.267, -8, 1, 1.378, -8, 1.489, -7.87, 1.6, -4, 1, 1.789, 2.579, 1.978, 10, 2.167, 10, 1, 2.4, 10, 2.633, -13, 2.867, -13, 1, 3.022, -13, 3.178, 4, 3.333, 4, 1, 3.456, 4, 3.578, -3, 3.7, -3, 1, 3.822, -3, 3.944, 0, 4.067, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -30, 0, 4.567, -30]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 4.567, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.756, 0, 0.811, 17.962, 0.867, 17.962, 1, 0.933, 17.962, 1, -18.119, 1.067, -18.119, 1, 1.156, -18.119, 1.244, 3.863, 1.333, 3.863, 1, 1.433, 3.863, 1.533, -0.739, 1.633, -0.739, 1, 1.744, -0.739, 1.856, 0.142, 1.967, 0.142, 1, 2.067, 0.142, 2.167, -0.028, 2.267, -0.028, 1, 2.367, -0.028, 2.467, 0.005, 2.567, 0.005, 1, 2.678, 0.005, 2.789, -0.001, 2.9, -0.001, 1, 3.033, -0.001, 3.167, 17.962, 3.3, 17.962, 1, 3.367, 17.962, 3.433, -18.119, 3.5, -18.119, 1, 3.589, -18.119, 3.678, 3.863, 3.767, 3.863, 1, 3.867, 3.863, 3.967, -0.739, 4.067, -0.739, 1, 4.178, -0.739, 4.289, 0.142, 4.4, 0.142, 1, 4.456, 0.142, 4.511, 0.09, 4.567, 0.043]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -12.108, 0.267, -12.108, 1, 0.378, -12.108, 0.489, 15.055, 0.6, 15.055, 1, 0.744, 15.055, 0.889, 0.775, 1.033, 0.775, 1, 1.167, 0.775, 1.3, 5.54, 1.433, 5.54, 1, 1.578, 5.54, 1.722, 3.731, 1.867, 3.731, 1, 1.978, 3.731, 2.089, 4.073, 2.2, 4.073, 1, 2.278, 4.073, 2.356, 3.853, 2.433, 3.853, 1, 2.511, 3.853, 2.589, 20.483, 2.667, 20.483, 1, 2.789, 20.483, 2.911, -17.947, 3.033, -17.947, 1, 3.167, -17.947, 3.3, 2.524, 3.433, 2.524, 1, 3.578, 2.524, 3.722, -4.375, 3.867, -4.375, 1, 4, -4.375, 4.133, -2.022, 4.267, -2.022, 1, 4.367, -2.022, 4.467, -2.405, 4.567, -2.642]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -12.108, 0.267, -12.108, 1, 0.378, -12.108, 0.489, 15.055, 0.6, 15.055, 1, 0.744, 15.055, 0.889, 0.775, 1.033, 0.775, 1, 1.167, 0.775, 1.3, 5.54, 1.433, 5.54, 1, 1.578, 5.54, 1.722, 3.731, 1.867, 3.731, 1, 1.978, 3.731, 2.089, 4.073, 2.2, 4.073, 1, 2.278, 4.073, 2.356, 3.853, 2.433, 3.853, 1, 2.511, 3.853, 2.589, 20.483, 2.667, 20.483, 1, 2.789, 20.483, 2.911, -17.947, 3.033, -17.947, 1, 3.167, -17.947, 3.3, 2.524, 3.433, 2.524, 1, 3.578, 2.524, 3.722, -4.375, 3.867, -4.375, 1, 4, -4.375, 4.133, -2.022, 4.267, -2.022, 1, 4.367, -2.022, 4.467, -2.405, 4.567, -2.642]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -12.108, 0.267, -12.108, 1, 0.378, -12.108, 0.489, 15.055, 0.6, 15.055, 1, 0.744, 15.055, 0.889, 0.775, 1.033, 0.775, 1, 1.167, 0.775, 1.3, 5.54, 1.433, 5.54, 1, 1.578, 5.54, 1.722, 3.731, 1.867, 3.731, 1, 1.978, 3.731, 2.089, 4.073, 2.2, 4.073, 1, 2.278, 4.073, 2.356, 3.853, 2.433, 3.853, 1, 2.511, 3.853, 2.589, 20.483, 2.667, 20.483, 1, 2.789, 20.483, 2.911, -17.947, 3.033, -17.947, 1, 3.167, -17.947, 3.3, 2.524, 3.433, 2.524, 1, 3.578, 2.524, 3.722, -4.375, 3.867, -4.375, 1, 4, -4.375, 4.133, -2.022, 4.267, -2.022, 1, 4.367, -2.022, 4.467, -2.405, 4.567, -2.642]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, -0.084, 1, 0.011, -0.084, 0.022, -0.061, 0.033, -0.061, 1, 0.111, -0.061, 0.189, -7.59, 0.267, -7.59, 1, 0.367, -7.59, 0.467, 4.559, 0.567, 4.559, 1, 0.678, 4.559, 0.789, -2.342, 0.9, -2.342, 1, 1.022, -2.342, 1.144, 0.064, 1.267, 0.064, 1, 1.389, 0.064, 1.511, -0.822, 1.633, -0.822, 1, 1.733, -0.822, 1.833, -0.578, 1.933, -0.578, 1, 2.1, -0.578, 2.267, -0.8, 2.433, -0.8, 1, 2.511, -0.8, 2.589, 11.865, 2.667, 11.865, 1, 2.767, 11.865, 2.867, -10.833, 2.967, -10.833, 1, 3.089, -10.833, 3.211, 2.392, 3.333, 2.392, 1, 3.456, 2.392, 3.578, -2.247, 3.7, -2.247, 1, 3.811, -2.247, 3.922, -0.624, 4.033, -0.624, 1, 4.156, -0.624, 4.278, -1.196, 4.4, -1.196, 1, 4.456, -1.196, 4.511, -1.146, 4.567, -1.096]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, -0.084, 1, 0.011, -0.084, 0.022, -0.061, 0.033, -0.061, 1, 0.111, -0.061, 0.189, -7.59, 0.267, -7.59, 1, 0.367, -7.59, 0.467, 4.559, 0.567, 4.559, 1, 0.678, 4.559, 0.789, -2.342, 0.9, -2.342, 1, 1.022, -2.342, 1.144, 0.064, 1.267, 0.064, 1, 1.389, 0.064, 1.511, -0.822, 1.633, -0.822, 1, 1.733, -0.822, 1.833, -0.578, 1.933, -0.578, 1, 2.1, -0.578, 2.267, -0.8, 2.433, -0.8, 1, 2.511, -0.8, 2.589, 11.865, 2.667, 11.865, 1, 2.767, 11.865, 2.867, -10.833, 2.967, -10.833, 1, 3.089, -10.833, 3.211, 2.392, 3.333, 2.392, 1, 3.456, 2.392, 3.578, -2.247, 3.7, -2.247, 1, 3.811, -2.247, 3.922, -0.624, 4.033, -0.624, 1, 4.156, -0.624, 4.278, -1.196, 4.4, -1.196, 1, 4.456, -1.196, 4.511, -1.146, 4.567, -1.096]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, -0.039, 1, 0.011, -0.039, 0.022, -0.114, 0.033, -0.114, 1, 0.089, -0.114, 0.144, 4.783, 0.2, 4.783, 1, 0.289, 4.783, 0.378, -13.783, 0.467, -13.783, 1, 0.567, -13.783, 0.667, 7.493, 0.767, 7.493, 1, 0.889, 7.493, 1.011, -3.257, 1.133, -3.257, 1, 1.244, -3.257, 1.356, 1.248, 1.467, 1.248, 1, 1.578, 1.248, 1.689, -0.392, 1.8, -0.392, 1, 1.922, -0.392, 2.044, 0.271, 2.167, 0.271, 1, 2.311, 0.271, 2.456, -7.625, 2.6, -7.625, 1, 2.689, -7.625, 2.778, 24.838, 2.867, 24.838, 1, 2.978, 24.838, 3.089, -14.318, 3.2, -14.318, 1, 3.311, -14.318, 3.422, 6.131, 3.533, 6.131, 1, 3.644, 6.131, 3.756, -2.327, 3.867, -2.327, 1, 3.989, -2.327, 4.111, 0.859, 4.233, 0.859, 1, 4.344, 0.859, 4.456, -0.102, 4.567, -0.276]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, -0.084, 1, 0.011, -0.084, 0.022, -0.061, 0.033, -0.061, 1, 0.111, -0.061, 0.189, -7.59, 0.267, -7.59, 1, 0.367, -7.59, 0.467, 4.559, 0.567, 4.559, 1, 0.678, 4.559, 0.789, -2.342, 0.9, -2.342, 1, 1.022, -2.342, 1.144, 0.064, 1.267, 0.064, 1, 1.389, 0.064, 1.511, -0.822, 1.633, -0.822, 1, 1.733, -0.822, 1.833, -0.578, 1.933, -0.578, 1, 2.1, -0.578, 2.267, -0.8, 2.433, -0.8, 1, 2.511, -0.8, 2.589, 11.865, 2.667, 11.865, 1, 2.767, 11.865, 2.867, -10.833, 2.967, -10.833, 1, 3.089, -10.833, 3.211, 2.392, 3.333, 2.392, 1, 3.456, 2.392, 3.578, -2.247, 3.7, -2.247, 1, 3.811, -2.247, 3.922, -0.624, 4.033, -0.624, 1, 4.156, -0.624, 4.278, -1.196, 4.4, -1.196, 1, 4.456, -1.196, 4.511, -1.146, 4.567, -1.096]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, -0.028, 1, 0.011, -0.028, 0.022, -0.081, 0.033, -0.081, 1, 0.089, -0.081, 0.144, 3.417, 0.2, 3.417, 1, 0.289, 3.417, 0.378, -9.845, 0.467, -9.845, 1, 0.567, -9.845, 0.667, 5.352, 0.767, 5.352, 1, 0.889, 5.352, 1.011, -2.326, 1.133, -2.326, 1, 1.244, -2.326, 1.356, 0.891, 1.467, 0.891, 1, 1.578, 0.891, 1.689, -0.28, 1.8, -0.28, 1, 1.922, -0.28, 2.044, 0.194, 2.167, 0.194, 1, 2.311, 0.194, 2.456, -5.446, 2.6, -5.446, 1, 2.689, -5.446, 2.778, 17.741, 2.867, 17.741, 1, 2.978, 17.741, 3.089, -10.227, 3.2, -10.227, 1, 3.311, -10.227, 3.422, 4.379, 3.533, 4.379, 1, 3.644, 4.379, 3.756, -1.662, 3.867, -1.662, 1, 3.989, -1.662, 4.111, 0.614, 4.233, 0.614, 1, 4.344, 0.614, 4.456, -0.073, 4.567, -0.197]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, -0.084, 1, 0.011, -0.084, 0.022, -0.061, 0.033, -0.061, 1, 0.111, -0.061, 0.189, -7.59, 0.267, -7.59, 1, 0.367, -7.59, 0.467, 4.559, 0.567, 4.559, 1, 0.678, 4.559, 0.789, -2.342, 0.9, -2.342, 1, 1.022, -2.342, 1.144, 0.064, 1.267, 0.064, 1, 1.389, 0.064, 1.511, -0.822, 1.633, -0.822, 1, 1.733, -0.822, 1.833, -0.578, 1.933, -0.578, 1, 2.1, -0.578, 2.267, -0.8, 2.433, -0.8, 1, 2.511, -0.8, 2.589, 11.865, 2.667, 11.865, 1, 2.767, 11.865, 2.867, -10.833, 2.967, -10.833, 1, 3.089, -10.833, 3.211, 2.392, 3.333, 2.392, 1, 3.456, 2.392, 3.578, -2.247, 3.7, -2.247, 1, 3.811, -2.247, 3.922, -0.624, 4.033, -0.624, 1, 4.156, -0.624, 4.278, -1.196, 4.4, -1.196, 1, 4.456, -1.196, 4.511, -1.146, 4.567, -1.096]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, -0.028, 1, 0.011, -0.028, 0.022, -0.081, 0.033, -0.081, 1, 0.089, -0.081, 0.144, 3.417, 0.2, 3.417, 1, 0.289, 3.417, 0.378, -9.845, 0.467, -9.845, 1, 0.567, -9.845, 0.667, 5.352, 0.767, 5.352, 1, 0.889, 5.352, 1.011, -2.326, 1.133, -2.326, 1, 1.244, -2.326, 1.356, 0.891, 1.467, 0.891, 1, 1.578, 0.891, 1.689, -0.28, 1.8, -0.28, 1, 1.922, -0.28, 2.044, 0.194, 2.167, 0.194, 1, 2.311, 0.194, 2.456, -5.446, 2.6, -5.446, 1, 2.689, -5.446, 2.778, 17.741, 2.867, 17.741, 1, 2.978, 17.741, 3.089, -10.227, 3.2, -10.227, 1, 3.311, -10.227, 3.422, 4.379, 3.533, 4.379, 1, 3.644, 4.379, 3.756, -1.662, 3.867, -1.662, 1, 3.989, -1.662, 4.111, 0.614, 4.233, 0.614, 1, 4.344, 0.614, 4.456, -0.073, 4.567, -0.197]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, -0.101, 1, 0.011, -0.101, 0.022, -0.073, 0.033, -0.073, 1, 0.111, -0.073, 0.189, -9.108, 0.267, -9.108, 1, 0.367, -9.108, 0.467, 5.471, 0.567, 5.471, 1, 0.678, 5.471, 0.789, -2.81, 0.9, -2.81, 1, 1.022, -2.81, 1.144, 0.077, 1.267, 0.077, 1, 1.389, 0.077, 1.511, -0.987, 1.633, -0.987, 1, 1.733, -0.987, 1.833, -0.694, 1.933, -0.694, 1, 2.1, -0.694, 2.267, -0.96, 2.433, -0.96, 1, 2.511, -0.96, 2.589, 14.238, 2.667, 14.238, 1, 2.767, 14.238, 2.867, -13, 2.967, -13, 1, 3.089, -13, 3.211, 2.871, 3.333, 2.871, 1, 3.456, 2.871, 3.578, -2.696, 3.7, -2.696, 1, 3.811, -2.696, 3.922, -0.749, 4.033, -0.749, 1, 4.156, -0.749, 4.278, -1.435, 4.4, -1.435, 1, 4.456, -1.435, 4.511, -1.375, 4.567, -1.315]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, -0.033, 1, 0.011, -0.033, 0.022, -0.098, 0.033, -0.098, 1, 0.089, -0.098, 0.144, 4.1, 0.2, 4.1, 1, 0.289, 4.1, 0.378, -11.814, 0.467, -11.814, 1, 0.567, -11.814, 0.667, 6.423, 0.767, 6.423, 1, 0.889, 6.423, 1.011, -2.791, 1.133, -2.791, 1, 1.244, -2.791, 1.356, 1.069, 1.467, 1.069, 1, 1.578, 1.069, 1.689, -0.336, 1.8, -0.336, 1, 1.922, -0.336, 2.044, 0.232, 2.167, 0.232, 1, 2.311, 0.232, 2.456, -6.536, 2.6, -6.536, 1, 2.689, -6.536, 2.778, 21.289, 2.867, 21.289, 1, 2.978, 21.289, 3.089, -12.273, 3.2, -12.273, 1, 3.311, -12.273, 3.422, 5.255, 3.533, 5.255, 1, 3.644, 5.255, 3.756, -1.994, 3.867, -1.994, 1, 3.989, -1.994, 4.111, 0.736, 4.233, 0.736, 1, 4.344, 0.736, 4.456, -0.087, 4.567, -0.237]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, -0.101, 1, 0.011, -0.101, 0.022, -0.073, 0.033, -0.073, 1, 0.111, -0.073, 0.189, -9.108, 0.267, -9.108, 1, 0.367, -9.108, 0.467, 5.471, 0.567, 5.471, 1, 0.678, 5.471, 0.789, -2.81, 0.9, -2.81, 1, 1.022, -2.81, 1.144, 0.077, 1.267, 0.077, 1, 1.389, 0.077, 1.511, -0.987, 1.633, -0.987, 1, 1.733, -0.987, 1.833, -0.694, 1.933, -0.694, 1, 2.1, -0.694, 2.267, -0.96, 2.433, -0.96, 1, 2.511, -0.96, 2.589, 14.238, 2.667, 14.238, 1, 2.767, 14.238, 2.867, -13, 2.967, -13, 1, 3.089, -13, 3.211, 2.871, 3.333, 2.871, 1, 3.456, 2.871, 3.578, -2.696, 3.7, -2.696, 1, 3.811, -2.696, 3.922, -0.749, 4.033, -0.749, 1, 4.156, -0.749, 4.278, -1.435, 4.4, -1.435, 1, 4.456, -1.435, 4.511, -1.375, 4.567, -1.315]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, -0.033, 1, 0.011, -0.033, 0.022, -0.098, 0.033, -0.098, 1, 0.089, -0.098, 0.144, 4.1, 0.2, 4.1, 1, 0.289, 4.1, 0.378, -11.814, 0.467, -11.814, 1, 0.567, -11.814, 0.667, 6.423, 0.767, 6.423, 1, 0.889, 6.423, 1.011, -2.791, 1.133, -2.791, 1, 1.244, -2.791, 1.356, 1.069, 1.467, 1.069, 1, 1.578, 1.069, 1.689, -0.336, 1.8, -0.336, 1, 1.922, -0.336, 2.044, 0.232, 2.167, 0.232, 1, 2.311, 0.232, 2.456, -6.536, 2.6, -6.536, 1, 2.689, -6.536, 2.778, 21.289, 2.867, 21.289, 1, 2.978, 21.289, 3.089, -12.273, 3.2, -12.273, 1, 3.311, -12.273, 3.422, 5.255, 3.533, 5.255, 1, 3.644, 5.255, 3.756, -1.994, 3.867, -1.994, 1, 3.989, -1.994, 4.111, 0.736, 4.233, 0.736, 1, 4.344, 0.736, 4.456, -0.087, 4.567, -0.237]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.756, 0, 0.811, 5.987, 0.867, 5.987, 1, 0.933, 5.987, 1, -6.04, 1.067, -6.04, 1, 1.156, -6.04, 1.244, 1.288, 1.333, 1.288, 1, 1.433, 1.288, 1.533, -0.246, 1.633, -0.246, 1, 1.744, -0.246, 1.856, 0.047, 1.967, 0.047, 1, 2.067, 0.047, 2.167, -0.009, 2.267, -0.009, 1, 2.367, -0.009, 2.467, 0.002, 2.567, 0.002, 1, 2.678, 0.002, 2.789, 0, 2.9, 0, 1, 3.033, 0, 3.167, 5.987, 3.3, 5.987, 1, 3.367, 5.987, 3.433, -6.04, 3.5, -6.04, 1, 3.589, -6.04, 3.678, 1.288, 3.767, 1.288, 1, 3.867, 1.288, 3.967, -0.246, 4.067, -0.246, 1, 4.178, -0.246, 4.289, 0.047, 4.4, 0.047, 1, 4.456, 0.047, 4.511, 0.03, 4.567, 0.014]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.067, 1, 0.011, -0.067, 0.022, -0.049, 0.033, -0.049, 1, 0.111, -0.049, 0.189, -6.072, 0.267, -6.072, 1, 0.367, -6.072, 0.467, 3.647, 0.567, 3.647, 1, 0.678, 3.647, 0.789, -1.873, 0.9, -1.873, 1, 1.022, -1.873, 1.144, 0.052, 1.267, 0.052, 1, 1.389, 0.052, 1.511, -0.658, 1.633, -0.658, 1, 1.733, -0.658, 1.833, -0.463, 1.933, -0.463, 1, 2.1, -0.463, 2.267, -0.64, 2.433, -0.64, 1, 2.511, -0.64, 2.589, 9.492, 2.667, 9.492, 1, 2.767, 9.492, 2.867, -8.666, 2.967, -8.666, 1, 3.089, -8.666, 3.211, 1.914, 3.333, 1.914, 1, 3.456, 1.914, 3.578, -1.798, 3.7, -1.798, 1, 3.811, -1.798, 3.922, -0.499, 4.033, -0.499, 1, 4.156, -0.499, 4.278, -0.957, 4.4, -0.957, 1, 4.456, -0.957, 4.511, -0.917, 4.567, -0.877]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -0.022, 1, 0.011, -0.022, 0.022, -0.065, 0.033, -0.065, 1, 0.089, -0.065, 0.144, 2.733, 0.2, 2.733, 1, 0.289, 2.733, 0.378, -7.876, 0.467, -7.876, 1, 0.567, -7.876, 0.667, 4.282, 0.767, 4.282, 1, 0.889, 4.282, 1.011, -1.861, 1.133, -1.861, 1, 1.244, -1.861, 1.356, 0.713, 1.467, 0.713, 1, 1.578, 0.713, 1.689, -0.224, 1.8, -0.224, 1, 1.922, -0.224, 2.044, 0.155, 2.167, 0.155, 1, 2.311, 0.155, 2.456, -4.357, 2.6, -4.357, 1, 2.689, -4.357, 2.778, 14.193, 2.867, 14.193, 1, 2.978, 14.193, 3.089, -8.182, 3.2, -8.182, 1, 3.311, -8.182, 3.422, 3.503, 3.533, 3.503, 1, 3.644, 3.503, 3.756, -1.33, 3.867, -1.33, 1, 3.989, -1.33, 4.111, 0.491, 4.233, 0.491, 1, 4.344, 0.491, 4.456, -0.058, 4.567, -0.158]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -0.135, 1, 0.011, -0.135, 0.022, -0.097, 0.033, -0.097, 1, 0.111, -0.097, 0.189, -12.144, 0.267, -12.144, 1, 0.367, -12.144, 0.467, 7.294, 0.567, 7.294, 1, 0.678, 7.294, 0.789, -3.747, 0.9, -3.747, 1, 1.022, -3.747, 1.144, 0.103, 1.267, 0.103, 1, 1.389, 0.103, 1.511, -1.316, 1.633, -1.316, 1, 1.733, -1.316, 1.833, -0.926, 1.933, -0.926, 1, 2.1, -0.926, 2.267, -1.279, 2.433, -1.279, 1, 2.511, -1.279, 2.589, 18.984, 2.667, 18.984, 1, 2.767, 18.984, 2.867, -17.333, 2.967, -17.333, 1, 3.089, -17.333, 3.211, 3.828, 3.333, 3.828, 1, 3.456, 3.828, 3.578, -3.595, 3.7, -3.595, 1, 3.811, -3.595, 3.922, -0.999, 4.033, -0.999, 1, 4.156, -0.999, 4.278, -1.914, 4.4, -1.914, 1, 4.456, -1.914, 4.511, -1.834, 4.567, -1.753]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -2.656, 0.3, -2.656, 1, 0.4, -2.656, 0.5, 0.912, 0.6, 0.912, 1, 0.733, 0.912, 0.867, -0.442, 1, -0.442, 1, 1.133, -0.442, 1.267, -0.11, 1.4, -0.11, 1, 1.544, -0.11, 1.689, -0.196, 1.833, -0.196, 1, 1.911, -0.196, 1.989, -0.19, 2.067, -0.19, 1, 2.189, -0.19, 2.311, -0.215, 2.433, -0.215, 1, 2.522, -0.215, 2.611, 4.228, 2.7, 4.228, 1, 2.811, 4.228, 2.922, -2.426, 3.033, -2.426, 1, 3.167, -2.426, 3.3, 0.191, 3.433, 0.191, 1, 3.556, 0.191, 3.678, -0.436, 3.8, -0.436, 1, 3.933, -0.436, 4.067, -0.285, 4.2, -0.285, 1, 4.322, -0.285, 4.444, -0.315, 4.567, -0.321]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0.028, 1, 0.078, 0.028, 0.156, 1.458, 0.233, 1.458, 1, 0.322, 1.458, 0.411, -2.012, 0.5, -2.012, 1, 0.6, -2.012, 0.7, 1.677, 0.8, 1.677, 1, 0.922, 1.677, 1.044, -0.887, 1.167, -0.887, 1, 1.289, -0.887, 1.411, 0.391, 1.533, 0.391, 1, 1.667, 0.391, 1.8, -0.16, 1.933, -0.16, 1, 2.056, -0.16, 2.178, 0.05, 2.3, 0.05, 1, 2.411, 0.05, 2.522, -2.359, 2.633, -2.359, 1, 2.722, -2.359, 2.811, 3.56, 2.9, 3.56, 1, 3, 3.56, 3.1, -3.075, 3.2, -3.075, 1, 3.322, -3.075, 3.444, 1.668, 3.567, 1.668, 1, 3.7, 1.668, 3.833, -0.737, 3.967, -0.737, 1, 4.089, -0.737, 4.211, 0.296, 4.333, 0.296, 1, 4.411, 0.296, 4.489, 0.157, 4.567, 0.041]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.118, 1, 0.011, -0.118, 0.022, -0.123, 0.033, -0.123, 1, 0.122, -0.123, 0.211, 0.999, 0.3, 0.999, 1, 0.411, 0.999, 0.522, -2.925, 0.633, -2.925, 1, 0.744, -2.925, 0.856, 3.248, 0.967, 3.248, 1, 1.089, 3.248, 1.211, -2.304, 1.333, -2.304, 1, 1.456, -2.304, 1.578, 1.32, 1.7, 1.32, 1, 1.833, 1.32, 1.967, -0.65, 2.1, -0.65, 1, 2.222, -0.65, 2.344, 0.309, 2.467, 0.309, 1, 2.544, 0.309, 2.622, -1.445, 2.7, -1.445, 1, 2.811, -1.445, 2.922, 5.105, 3.033, 5.105, 1, 3.144, 5.105, 3.256, -5.901, 3.367, -5.901, 1, 3.489, -5.901, 3.611, 4.191, 3.733, 4.191, 1, 3.867, 4.191, 4, -2.409, 4.133, -2.409, 1, 4.256, -2.409, 4.378, 1.226, 4.5, 1.226, 1, 4.522, 1.226, 4.544, 1.176, 4.567, 1.093]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0.104, 1, 0.056, 0.104, 0.111, -0.185, 0.167, -0.185, 1, 0.256, -0.185, 0.344, 1.388, 0.433, 1.388, 1, 0.544, 1.388, 0.656, -3.535, 0.767, -3.535, 1, 0.889, -3.535, 1.011, 4.482, 1.133, 4.482, 1, 1.256, 4.482, 1.378, -3.857, 1.5, -3.857, 1, 1.622, -3.857, 1.744, 2.654, 1.867, 2.654, 1, 2, 2.654, 2.133, -1.588, 2.267, -1.588, 1, 2.367, -1.588, 2.467, 0.608, 2.567, 0.608, 1, 2.667, 0.608, 2.767, -2.159, 2.867, -2.159, 1, 2.967, -2.159, 3.067, 5.972, 3.167, 5.972, 1, 3.289, 5.972, 3.411, -7.881, 3.533, -7.881, 1, 3.656, -7.881, 3.778, 6.754, 3.9, 6.754, 1, 4.033, 6.754, 4.167, -4.672, 4.3, -4.672, 1, 4.389, -4.672, 4.478, -0.697, 4.567, 1.471]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -2.656, 0.3, -2.656, 1, 0.4, -2.656, 0.5, 0.912, 0.6, 0.912, 1, 0.733, 0.912, 0.867, -0.442, 1, -0.442, 1, 1.133, -0.442, 1.267, -0.11, 1.4, -0.11, 1, 1.544, -0.11, 1.689, -0.196, 1.833, -0.196, 1, 1.911, -0.196, 1.989, -0.19, 2.067, -0.19, 1, 2.189, -0.19, 2.311, -0.215, 2.433, -0.215, 1, 2.522, -0.215, 2.611, 4.228, 2.7, 4.228, 1, 2.811, 4.228, 2.922, -2.426, 3.033, -2.426, 1, 3.167, -2.426, 3.3, 0.191, 3.433, 0.191, 1, 3.556, 0.191, 3.678, -0.436, 3.8, -0.436, 1, 3.933, -0.436, 4.067, -0.285, 4.2, -0.285, 1, 4.322, -0.285, 4.444, -0.315, 4.567, -0.321]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0.028, 1, 0.078, 0.028, 0.156, 1.458, 0.233, 1.458, 1, 0.322, 1.458, 0.411, -2.012, 0.5, -2.012, 1, 0.6, -2.012, 0.7, 1.677, 0.8, 1.677, 1, 0.922, 1.677, 1.044, -0.887, 1.167, -0.887, 1, 1.289, -0.887, 1.411, 0.391, 1.533, 0.391, 1, 1.667, 0.391, 1.8, -0.16, 1.933, -0.16, 1, 2.056, -0.16, 2.178, 0.05, 2.3, 0.05, 1, 2.411, 0.05, 2.522, -2.359, 2.633, -2.359, 1, 2.722, -2.359, 2.811, 3.56, 2.9, 3.56, 1, 3, 3.56, 3.1, -3.075, 3.2, -3.075, 1, 3.322, -3.075, 3.444, 1.668, 3.567, 1.668, 1, 3.7, 1.668, 3.833, -0.737, 3.967, -0.737, 1, 4.089, -0.737, 4.211, 0.296, 4.333, 0.296, 1, 4.411, 0.296, 4.489, 0.157, 4.567, 0.041]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.118, 1, 0.011, -0.118, 0.022, -0.123, 0.033, -0.123, 1, 0.122, -0.123, 0.211, 0.999, 0.3, 0.999, 1, 0.411, 0.999, 0.522, -2.925, 0.633, -2.925, 1, 0.744, -2.925, 0.856, 3.248, 0.967, 3.248, 1, 1.089, 3.248, 1.211, -2.304, 1.333, -2.304, 1, 1.456, -2.304, 1.578, 1.32, 1.7, 1.32, 1, 1.833, 1.32, 1.967, -0.65, 2.1, -0.65, 1, 2.222, -0.65, 2.344, 0.309, 2.467, 0.309, 1, 2.544, 0.309, 2.622, -1.445, 2.7, -1.445, 1, 2.811, -1.445, 2.922, 5.105, 3.033, 5.105, 1, 3.144, 5.105, 3.256, -5.901, 3.367, -5.901, 1, 3.489, -5.901, 3.611, 4.191, 3.733, 4.191, 1, 3.867, 4.191, 4, -2.409, 4.133, -2.409, 1, 4.256, -2.409, 4.378, 1.226, 4.5, 1.226, 1, 4.522, 1.226, 4.544, 1.176, 4.567, 1.093]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0.104, 1, 0.056, 0.104, 0.111, -0.185, 0.167, -0.185, 1, 0.256, -0.185, 0.344, 1.388, 0.433, 1.388, 1, 0.544, 1.388, 0.656, -3.535, 0.767, -3.535, 1, 0.889, -3.535, 1.011, 4.482, 1.133, 4.482, 1, 1.256, 4.482, 1.378, -3.857, 1.5, -3.857, 1, 1.622, -3.857, 1.744, 2.654, 1.867, 2.654, 1, 2, 2.654, 2.133, -1.588, 2.267, -1.588, 1, 2.367, -1.588, 2.467, 0.608, 2.567, 0.608, 1, 2.667, 0.608, 2.767, -2.159, 2.867, -2.159, 1, 2.967, -2.159, 3.067, 5.972, 3.167, 5.972, 1, 3.289, 5.972, 3.411, -7.881, 3.533, -7.881, 1, 3.656, -7.881, 3.778, 6.754, 3.9, 6.754, 1, 4.033, 6.754, 4.167, -4.672, 4.3, -4.672, 1, 4.389, -4.672, 4.478, -0.697, 4.567, 1.471]}]}