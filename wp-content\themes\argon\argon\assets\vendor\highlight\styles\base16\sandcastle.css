/*!
  Theme: Sandcastle
  Author: <PERSON> (https://github.com/gessig)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme sandcastle
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #282c34  Default Background
base01  #2c323b  Lighter Background (Used for status bars, line number and folding marks)
base02  #3e4451  Selection Background
base03  #665c54  Comments, Invisibles, Line Highlighting
base04  #928374  Dark Foreground (Used for status bars)
base05  #a89984  Default Foreground, Caret, Delimiters, Operators
base06  #d5c4a1  Light Foreground (Not often used)
base07  #fdf4c1  Light Background (Not often used)
base08  #83a598  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #a07e3b  Integers, Bo<PERSON>an, Constants, XML Attributes, Markup Link Url
base0A  #a07e3b  Classes, Markup Bold, Search Text Background
base0B  #528b8b  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #83a598  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #83a598  Functions, Methods, Attribute IDs, Headings
base0E  #d75f5f  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #a87322  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #a89984;
  background: #282c34;
}

.hljs::selection,
.hljs ::selection {
  background-color: #3e4451;
  color: #a89984;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #665c54 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #665c54;
}

/* base04 - #928374 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #928374;
}

/* base05 - #a89984 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a89984;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #83a598;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #a07e3b;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #a07e3b;
}

.hljs-strong {
  font-weight:bold;
  color: #a07e3b;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #528b8b;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #83a598;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #83a598;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #d75f5f;
}
.hljs-emphasis {
  color: #d75f5f;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #a87322;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
