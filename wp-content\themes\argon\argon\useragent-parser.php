<?php

/**
 * Parses a user agent string into its important parts
 *
 * @param string|null $u_agent User agent string to parse or null. Uses $_SERVER['HTTP_USER_AGENT'] on NULL
 * @return string[] an array with browser, version and platform keys
 * @throws \InvalidArgumentException on not having a proper user agent to parse.
 *
 * <AUTHOR> <PERSON>at <<EMAIL>>
 *
 * @link https://donatstudios.com/PHP-Parser-HTTP_USER_AGENT
 * @link https://github.com/donatj/PhpUserAgent
 *
 * @license MIT
 */
function argon_parse_user_agent( $u_agent = null ) {
	if( $u_agent === null && isset($_SERVER['HTTP_USER_AGENT']) ) {
		$u_agent = $_SERVER['HTTP_USER_AGENT'];
	}

	if( $u_agent === null ) {
		throw new \InvalidArgumentException('parse_user_agent requires a user agent');
	}

	$platform = null;
	$browser  = null;
	$version  = null;

	$empty = array( 'platform' => $platform, 'browser' => $browser, 'version' => $version );

	if( !$u_agent ) {
		return $empty;
	}

	if( preg_match('/\((.*?)\)/m', $u_agent, $parent_matches) ) {
		preg_match_all('/(?P<platform>BB\d+;|Android|CrOS|Tizen|iPhone|iPad|iPod|Linux|(Open|Net|Free)BSD|Macintosh|Windows(\ Phone)?|Silk|linux-gnu|BlackBerry|PlayBook|X11|(New\ )?Nintendo\ (WiiU?|3?DS|Switch)|Xbox(\ One)?)
				(?:\ [^;]*)?
				(?:;|$)/imx', $parent_matches[1], $result);

		$priority = array( 'Xbox One', 'Xbox', 'Windows Phone', 'Tizen', 'Android', 'FreeBSD', 'NetBSD', 'OpenBSD', 'CrOS', 'X11' );

		$result['platform'] = array_unique($result['platform']);
		if( count($result['platform']) > 1 ) {
			if( $keys = array_intersect($priority, $result['platform']) ) {
				$platform = reset($keys);
			} else {
				$platform = $result['platform'][0];
			}
		} elseif( isset($result['platform'][0]) ) {
			$platform = $result['platform'][0];
		}
	}

	if( $platform == 'linux-gnu' || $platform == 'X11' ) {
		$platform = 'Linux';
	} elseif( $platform == 'CrOS' ) {
		$platform = 'Chrome OS';
	}

	preg_match_all('%(?P<browser>Camino|Kindle(\ Fire)?|Firefox|Iceweasel|IceCat|Safari|MSIE|Trident|AppleWebKit|
				TizenBrowser|(?:Headless)?Chrome|YaBrowser|Vivaldi|IEMobile|Opera|OPR|Silk|Midori|Edge|Edg|CriOS|UCBrowser|Puffin|OculusBrowser|SamsungBrowser|
				Baiduspider|Googlebot|YandexBot|bingbot|Lynx|Version|Wget|curl|
				Valve\ Steam\ Tenfoot|
				NintendoBrowser|PLAYSTATION\ (\d|Vita)+)
				(?:\)?;?)
				(?:(?:[:/ ])(?P<version>[0-9A-Z.]+)|/(?:[A-Z]*))%ix',
		$u_agent, $result);

	// If nothing matched, return null (to avoid undefined index errors)
	if( !isset($result['browser'][0]) || !isset($result['version'][0]) ) {
		if( preg_match('%^(?!Mozilla)(?P<browser>[A-Z0-9\-]+)(/(?P<version>[0-9A-Z.]+))?%ix', $u_agent, $result) ) {
			return array( 'platform' => $platform ?: null, 'browser' => $result['browser'], 'version' => isset($result['version']) ? $result['version'] ?: null : null );
		}

		return $empty;
	}

	if( preg_match('/rv:(?P<version>[0-9A-Z.]+)/i', $u_agent, $rv_result) ) {
		$rv_result = $rv_result['version'];
	}

	$browser = $result['browser'][0];
	$version = $result['version'][0];

	$lowerBrowser = array_map('strtolower', $result['browser']);

	$find = function ( $search, &$key = null, &$value = null ) use ( $lowerBrowser ) {
		$search = (array)$search;

		foreach( $search as $val ) {
			$xkey = array_search(strtolower($val), $lowerBrowser);
			if( $xkey !== false ) {
				$value = $val;
				$key   = $xkey;

				return true;
			}
		}

		return false;
	};

	$findT = function ( array $search, &$key = null, &$value = null ) use ( $find ) {
		$value2 = null;
		if( $find(array_keys($search), $key, $value2) ) {
			$value = $search[$value2];

			return true;
		}

		return false;
	};

	$key = 0;
	$val = '';
	if( $findT(array( 'OPR' => 'Opera', 'UCBrowser' => 'UC Browser', 'YaBrowser' => 'Yandex', 'Iceweasel' => 'Firefox', 'Icecat' => 'Firefox', 'CriOS' => 'Chrome', 'Edg' => 'Edge' ), $key, $browser) ) {
		$version = $result['version'][$key];
	}elseif( $find('Playstation Vita', $key, $platform) ) {
		$platform = 'PlayStation Vita';
		$browser  = 'Browser';
	} elseif( $find(array( 'Kindle Fire', 'Silk' ), $key, $val) ) {
		$browser  = $val == 'Silk' ? 'Silk' : 'Kindle';
		$platform = 'Kindle Fire';
		if( !($version = $result['version'][$key]) || !is_numeric($version[0]) ) {
			$version = $result['version'][array_search('Version', $result['browser'])];
		}
	} elseif( $find('NintendoBrowser', $key) || $platform == 'Nintendo 3DS' ) {
		$browser = 'NintendoBrowser';
		$version = $result['version'][$key];
	} elseif( $find('Kindle', $key, $platform) ) {
		$browser = $result['browser'][$key];
		$version = $result['version'][$key];
	} elseif( $find('Opera', $key, $browser) ) {
		$find('Version', $key);
		$version = $result['version'][$key];
	} elseif( $find('Puffin', $key, $browser) ) {
		$version = $result['version'][$key];
		if( strlen($version) > 3 ) {
			$part = substr($version, -2);
			if( ctype_upper($part) ) {
				$version = substr($version, 0, -2);

				$flags = array( 'IP' => 'iPhone', 'IT' => 'iPad', 'AP' => 'Android', 'AT' => 'Android', 'WP' => 'Windows Phone', 'WT' => 'Windows' );
				if( isset($flags[$part]) ) {
					$platform = $flags[$part];
				}
			}
		}
	} elseif( $find(array( 'IEMobile', 'Edge', 'Midori', 'Vivaldi', 'OculusBrowser', 'SamsungBrowser', 'Valve Steam Tenfoot', 'Chrome', 'HeadlessChrome' ), $key, $browser) ) {
		$version = $result['version'][$key];
	} elseif( $rv_result && $find('Trident') ) {
		$browser = 'MSIE';
		$version = $rv_result;
	} elseif( $browser == 'AppleWebKit' ) {
		if( $platform == 'Android' ) {
			$browser = 'Android Browser';
		} elseif( strpos($platform, 'BB') === 0 ) {
			$browser  = 'BlackBerry Browser';
			$platform = 'BlackBerry';
		} elseif( $platform == 'BlackBerry' || $platform == 'PlayBook' ) {
			$browser = 'BlackBerry Browser';
		} else {
			$find('Safari', $key, $browser) || $find('TizenBrowser', $key, $browser);
		}

		$find('Version', $key);
		$version = $result['version'][$key];
	} elseif( $pKey = preg_grep('/playstation \d/i', $result['browser']) ) {
		$pKey = reset($pKey);

		$platform = 'PlayStation ' . preg_replace('/\D/', '', $pKey);
		$browser  = 'NetFront';
	}

	return array( 'platform' => $platform ?: null, 'browser' => $browser ?: null, 'version' => $version ?: null );
}

//图标
$GLOBALS['UA_ICON']['Chrome'] = $GLOBALS['UA_ICON']['Chrome OS'] = '<svg id="a6e1ba9b-be51-4044-90ec-60a20b2245fb" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 52.36 52.36"><defs><clipPath id="fabb6727-68e3-4dd9-9a76-6979fae57f3f" transform="translate(2.18 2.18)"><circle cx="24" cy="24" r="24" style="fill:none"/></clipPath><linearGradient id="e4eac09d-c1d6-4471-9c77-684844c2292d" x1="2493.34" y1="-1369.02" x2="2545.84" y2="-1338.35" gradientTransform="matrix(0.27, 0, 0, -0.27, -674.18, -355.09)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#a52714" stop-opacity="0.6"/><stop offset="0.66" stop-color="#a52714" stop-opacity="0"/></linearGradient><linearGradient id="af7b1a24-88a6-4858-89c9-11b63ac37cf8" x1="2574.87" y1="-1458.49" x2="2516.54" y2="-1424.33" gradientTransform="matrix(0.27, 0, 0, -0.27, -674.18, -355.09)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#055524" stop-opacity="0.4"/><stop offset="0.33" stop-color="#055524" stop-opacity="0"/></linearGradient><clipPath id="f9af7cc4-0c0b-4e42-a0e8-48956619ecf8" transform="translate(2.18 2.18)"><polygon points="0 48 22.85 48 33.45 37.4 33.45 29.45 14.55 29.45 0 4.5 0 48" style="fill:none"/></clipPath><linearGradient id="a12530e7-37c0-42e0-b52b-8c0998fc3cd4" x1="2585.86" y1="-1343.8" x2="2600.54" y2="-1408.13" gradientTransform="matrix(0.27, 0, 0, -0.27, -674.18, -355.09)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ea6100" stop-opacity="0.3"/><stop offset="0.66" stop-color="#ea6100" stop-opacity="0"/></linearGradient><clipPath id="a719bad8-5e06-463d-8214-3a288be013e5" transform="translate(2.18 2.18)"><polygon points="24 13.09 33.45 29.45 22.85 48 48 48 48 13.09 24 13.09" style="fill:none"/></clipPath><radialGradient id="bdda3c9a-58b1-485e-978c-8235775c263c" cx="3132.18" cy="-1349.95" r="84.08" gradientTransform="matrix(0.27, 0, 0, -0.27, -831.27, -355.09)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#3e2723" stop-opacity="0.2"/><stop offset="1" stop-color="#3e2723" stop-opacity="0"/></radialGradient><clipPath id="f2f490b3-e317-4789-89b5-61c9c4ee4cc3" transform="translate(2.18 2.18)"><polygon points="3.81 0 3.81 11 14.55 29.45 24 13.09 48 13.09 48 0 3.81 0" style="fill:none"/></clipPath><radialGradient id="ea33284c-0fb8-4e7f-ae76-336ef2238d29" cx="3061.87" cy="-1342.52" r="78.04" xlink:href="#bdda3c9a-58b1-485e-978c-8235775c263c"/><radialGradient id="b34460d9-cf2e-4728-9685-20afb38f4188" cx="3135.84" cy="-1390.14" r="87.87" gradientTransform="matrix(0.27, 0, 0, -0.27, -831.27, -355.09)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#263238" stop-opacity="0.2"/><stop offset="1" stop-color="#263238" stop-opacity="0"/></radialGradient><radialGradient id="b6a15be6-3a6e-4f9a-a92a-dfdc462bf5ab" cx="2498.29" cy="-1326.01" r="176.75" gradientTransform="matrix(0.27, 0, 0, -0.27, -672, -352.91)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff" stop-opacity="0.1"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></radialGradient></defs><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M3.81,0V29.45H14.55L24,13.09H48V0Z" transform="translate(2.18 2.18)" style="fill:#db4437"/><path d="M3.81,0V29.45H14.55L24,13.09H48V0Z" transform="translate(2.18 2.18)" style="fill:url(#e4eac09d-c1d6-4471-9c77-684844c2292d)"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M14.81,29.36,4,10.73,3.79,11,14.57,29.49Z" transform="translate(2.18 2.18)" style="fill:#3e2723;fill-opacity:0.15000000596046448"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M0,48H22.85l10.6-10.6V29.45H14.55L0,4.49Z" transform="translate(2.18 2.18)" style="fill:#0f9d58"/><path d="M0,48H22.85l10.6-10.6V29.45H14.55L0,4.49Z" transform="translate(2.18 2.18)" style="fill:url(#af7b1a24-88a6-4858-89c9-11b63ac37cf8)"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M33.23,29.82,33,29.69,22.53,48h.32L33.24,29.83Z" transform="translate(2.18 2.18)" style="fill:#263238;fill-opacity:0.15000000596046448"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><g style="clip-path:url(#f9af7cc4-0c0b-4e42-a0e8-48956619ecf8)"><path d="M24,13.09l9.45,16.36L22.85,48H48V13.09Z" transform="translate(2.18 2.18)" style="fill:#ffcd40"/><path d="M24,13.09l9.45,16.36L22.85,48H48V13.09Z" transform="translate(2.18 2.18)" style="fill:url(#a12530e7-37c0-42e0-b52b-8c0998fc3cd4)"/></g></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M24,13.09l9.45,16.36L22.85,48H48V13.09Z" transform="translate(2.18 2.18)" style="fill:#ffcd40"/><path d="M24,13.09l9.45,16.36L22.85,48H48V13.09Z" transform="translate(2.18 2.18)" style="fill:url(#a12530e7-37c0-42e0-b52b-8c0998fc3cd4)"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><g style="clip-path:url(#a719bad8-5e06-463d-8214-3a288be013e5)"><path d="M3.81,0V29.45H14.55L24,13.09H48V0Z" transform="translate(2.18 2.18)" style="fill:#db4437"/><path d="M3.81,0V29.45H14.55L24,13.09H48V0Z" transform="translate(2.18 2.18)" style="fill:url(#e4eac09d-c1d6-4471-9c77-684844c2292d)"/></g></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M24,13.09V18.8l21.38-5.71Z" transform="translate(2.18 2.18)" style="fill:url(#bdda3c9a-58b1-485e-978c-8235775c263c)"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><g style="clip-path:url(#f2f490b3-e317-4789-89b5-61c9c4ee4cc3)"><path d="M0,48H22.85l10.6-10.6V29.45H14.55L0,4.49Z" transform="translate(2.18 2.18)" style="fill:#0f9d58"/><path d="M0,48H22.85l10.6-10.6V29.45H14.55L0,4.49Z" transform="translate(2.18 2.18)" style="fill:url(#af7b1a24-88a6-4858-89c9-11b63ac37cf8)"/></g></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M3.81,11,19.42,26.64l-4.87,2.81Z" transform="translate(2.18 2.18)" style="fill:url(#ea33284c-0fb8-4e7f-ae76-336ef2238d29)"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M22.86,48l5.72-21.33,4.87,2.81Z" transform="translate(2.18 2.18)" style="fill:url(#b34460d9-cf2e-4728-9685-20afb38f4188)"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><circle cx="26.18" cy="26.18" r="10.91" style="fill:#f1f1f1"/><circle cx="26.18" cy="26.18" r="8.73" style="fill:#4285f4"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M24,12.82A10.91,10.91,0,0,0,13.09,23.73V24A10.92,10.92,0,0,1,24,13.09H48v-.27Z" transform="translate(2.18 2.18)" style="fill:#3e2723;fill-opacity:0.20000000298023224"/><path d="M33.44,29.45a10.89,10.89,0,0,1-18.88,0h0L0,4.49v.28l14.55,25h0a10.9,10.9,0,0,0,18.88,0h0v-.28Z" transform="translate(2.18 2.18)" style="fill:#fff;fill-opacity:0.10000000149011612"/><path d="M24.27,13.09h-.13a10.9,10.9,0,0,1,0,21.8h.13a10.91,10.91,0,1,0,0-21.82Z" transform="translate(2.18 2.18)" style="fill:#3e2723;opacity:0.10000000149011612;isolation:isolate"/><path d="M33.55,29.82a10.89,10.89,0,0,0,1-8.74,10.76,10.76,0,0,1-1.06,8.37h0L22.85,48h.31L33.55,29.83Z" transform="translate(2.18 2.18)" style="fill:#fff;fill-opacity:0.20000000298023224"/></g><g style="clip-path:url(#fabb6727-68e3-4dd9-9a76-6979fae57f3f)"><path d="M24,.27A24,24,0,0,1,48,24.14V24A24,24,0,0,0,0,24v.14A24,24,0,0,1,24,.27Z" transform="translate(2.18 2.18)" style="fill:#fff;fill-opacity:0.20000000298023224"/><path d="M24,47.73A24,24,0,0,0,48,23.86V24A24,24,0,0,1,0,24v-.14A24,24,0,0,0,24,47.73Z" transform="translate(2.18 2.18)" style="fill:#3e2723;fill-opacity:0.15000000596046448"/></g><circle cx="26.18" cy="26.18" r="24" style="fill:url(#b6a15be6-3a6e-4f9a-a92a-dfdc462bf5ab)"/><path d="M-2.18-2.18H50.18V50.18H-2.18Z" transform="translate(2.18 2.18)" style="fill:none"/></svg>';
$GLOBALS['UA_ICON']['Firefox'] = '<svg id="e0e9189a-3478-4282-a976-68efc0f16b9d" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 77.42 79.97" style="transform: scale(1.15) translateY(-1px);><defs><linearGradient id="a6716ad7-bde7-479a-871a-55aeacf92b90" x1="70.78" y1="71.61" x2="6.44" y2="9.54" gradientTransform="matrix(1, 0, 0, -1, 3.7, 84)" gradientUnits="userSpaceOnUse"><stop offset="0.05" stop-color="#fff44f"/><stop offset="0.11" stop-color="#ffe847"/><stop offset="0.23" stop-color="#ffc830"/><stop offset="0.37" stop-color="#ff980e"/><stop offset="0.4" stop-color="#ff8b16"/><stop offset="0.46" stop-color="#ff672a"/><stop offset="0.53" stop-color="#ff3647"/><stop offset="0.7" stop-color="#e31587"/></linearGradient><radialGradient id="fbac4196-d99a-4f62-a085-471f7ae4a58b" cx="-7907.19" cy="8599.12" r="80.8" gradientTransform="matrix(1, 0, 0, -1, 7978.7, 8608)" gradientUnits="userSpaceOnUse"><stop offset="0.13" stop-color="#ffbd4f"/><stop offset="0.19" stop-color="#ffac31"/><stop offset="0.25" stop-color="#ff9d17"/><stop offset="0.28" stop-color="#ff980e"/><stop offset="0.4" stop-color="#ff563b"/><stop offset="0.47" stop-color="#ff3750"/><stop offset="0.71" stop-color="#f5156c"/><stop offset="0.78" stop-color="#eb0878"/><stop offset="0.86" stop-color="#e50080"/></radialGradient><radialGradient id="f5c44653-6815-4f93-9326-b2ee91399c37" cx="-7936.71" cy="8566.09" r="80.8" gradientTransform="matrix(1, 0, 0, -1, 7978.7, 8608)" gradientUnits="userSpaceOnUse"><stop offset="0.3" stop-color="#960e18"/><stop offset="0.35" stop-color="#b11927" stop-opacity="0.74"/><stop offset="0.43" stop-color="#db293d" stop-opacity="0.34"/><stop offset="0.5" stop-color="#f5334b" stop-opacity="0.09"/><stop offset="0.53" stop-color="#ff3750" stop-opacity="0"/></radialGradient><radialGradient id="eac60cb4-5aa5-41b6-99ec-a3e95cfd58d6" cx="-7926.97" cy="8617.46" r="58.53" gradientTransform="matrix(1, 0, 0, -1, 7978.7, 8608)" gradientUnits="userSpaceOnUse"><stop offset="0.13" stop-color="#fff44f"/><stop offset="0.25" stop-color="#ffdc3e"/><stop offset="0.51" stop-color="#ff9d12"/><stop offset="0.53" stop-color="#ff980e"/></radialGradient><radialGradient id="a8bfab76-869c-4b27-99b0-cba9a7516a1b" cx="-7945.65" cy="8544.98" r="38.47" gradientTransform="matrix(1, 0, 0, -1, 7978.7, 8608)" gradientUnits="userSpaceOnUse"><stop offset="0.35" stop-color="#3a8ee6"/><stop offset="0.47" stop-color="#5c79f0"/><stop offset="0.67" stop-color="#9059ff"/><stop offset="1" stop-color="#c139e6"/></radialGradient><radialGradient id="f0ec94f2-0662-47de-8b17-0d29ffc9a2f6" cx="-8078.2" cy="8476.43" r="20.4" gradientTransform="matrix(0.97, -0.23, -0.28, -1.14, 10229.43, 7783.08)" gradientUnits="userSpaceOnUse"><stop offset="0.21" stop-color="#9059ff" stop-opacity="0"/><stop offset="0.28" stop-color="#8c4ff3" stop-opacity="0.06"/><stop offset="0.75" stop-color="#7716a8" stop-opacity="0.45"/><stop offset="0.97" stop-color="#6e008b" stop-opacity="0.6"/></radialGradient><radialGradient id="f452786f-e7d2-43b3-b102-fda40b127bbd" cx="-7937.73" cy="8602.43" r="27.68" gradientTransform="matrix(1, 0, 0, -1, 7978.7, 8608)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffe226"/><stop offset="0.12" stop-color="#ffdb27"/><stop offset="0.29" stop-color="#ffc82a"/><stop offset="0.5" stop-color="#ffa930"/><stop offset="0.73" stop-color="#ff7e37"/><stop offset="0.79" stop-color="#ff7139"/></radialGradient><radialGradient id="a2748335-837e-4b7a-a4da-edeb8bf8419e" cx="-7915.98" cy="8619.98" r="118.08" gradientTransform="matrix(1, 0, 0, -1, 7978.7, 8608)" gradientUnits="userSpaceOnUse"><stop offset="0.11" stop-color="#fff44f"/><stop offset="0.46" stop-color="#ff980e"/><stop offset="0.62" stop-color="#ff5634"/><stop offset="0.72" stop-color="#ff3647"/><stop offset="0.9" stop-color="#e31587"/></radialGradient><radialGradient id="ab60b320-dda3-4742-b689-08fe852a77a7" cx="-8251.08" cy="10411.04" r="86.5" gradientTransform="matrix(0.1, 1, 0.65, -0.07, -5879.28, 8922.77)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff44f"/><stop offset="0.06" stop-color="#ffe847"/><stop offset="0.17" stop-color="#ffc830"/><stop offset="0.3" stop-color="#ff980e"/><stop offset="0.36" stop-color="#ff8b16"/><stop offset="0.46" stop-color="#ff672a"/><stop offset="0.57" stop-color="#ff3647"/><stop offset="0.74" stop-color="#e31587"/></radialGradient><radialGradient id="a6639a63-3ba9-4b04-8f61-77703eb8371d" cx="-7938.38" cy="8592.18" r="73.72" gradientTransform="matrix(1, 0, 0, -1, 7978.7, 8608)" gradientUnits="userSpaceOnUse"><stop offset="0.14" stop-color="#fff44f"/><stop offset="0.48" stop-color="#ff980e"/><stop offset="0.59" stop-color="#ff5634"/><stop offset="0.66" stop-color="#ff3647"/><stop offset="0.9" stop-color="#e31587"/></radialGradient><radialGradient id="ba5f1830-09d8-4408-9f92-c51615d6dd83" cx="-7918.92" cy="8587.86" r="80.69" gradientTransform="matrix(1, 0, 0, -1, 7978.7, 8608)" gradientUnits="userSpaceOnUse"><stop offset="0.09" stop-color="#fff44f"/><stop offset="0.23" stop-color="#ffe141"/><stop offset="0.51" stop-color="#ffaf1e"/><stop offset="0.63" stop-color="#ff980e"/></radialGradient><linearGradient id="e691cfeb-54b2-4ba4-bdea-72904ea15669" x1="70.01" y1="71.94" x2="15.26" y2="17.2" gradientTransform="matrix(1, 0, 0, -1, 3.7, 84)" gradientUnits="userSpaceOnUse"><stop offset="0.17" stop-color="#fff44f" stop-opacity="0.8"/><stop offset="0.27" stop-color="#fff44f" stop-opacity="0.63"/><stop offset="0.49" stop-color="#fff44f" stop-opacity="0.22"/><stop offset="0.6" stop-color="#fff44f" stop-opacity="0"/></linearGradient></defs><path id="bd365425-9039-4e1f-9b1b-4ae40ae65872" data-name="path980" d="M79.62,26.83c-1.69-4.06-5.1-8.43-7.78-9.81a40.35,40.35,0,0,1,3.93,11.76v.07C71.39,17.92,64,13.52,57.89,3.92c-.31-.48-.61-1-.91-1.48-.17-.29-.31-.56-.43-.8A7.51,7.51,0,0,1,56,.1a.1.1,0,0,0-.08-.1h-.08l0,0h0V0c-9.73,5.7-13,16.26-13.34,21.53a19.35,19.35,0,0,0-10.67,4.12,10.53,10.53,0,0,0-1-.76,18,18,0,0,1-.11-9.48,28.94,28.94,0,0,0-9.33,7.21h0c-1.54-1.94-1.43-8.36-1.34-9.7a7,7,0,0,0-1.3.68,29.29,29.29,0,0,0-3.79,3.25,34.37,34.37,0,0,0-3.62,4.35h0a32.83,32.83,0,0,0-5.2,11.75L6,33.19c-.07.34-.34,2.05-.38,2.42,0,0,0,.06,0,.09A36.87,36.87,0,0,0,5,41v.2A38.76,38.76,0,0,0,82,47.8c.07-.5.12-1,.18-1.5A39.93,39.93,0,0,0,79.62,26.83ZM35,57.16c.18.09.35.19.53.27l0,0ZM75.78,28.85v0Z" transform="translate(-5 0)" style="fill:url(#a6716ad7-bde7-479a-871a-55aeacf92b90)"/><path id="a19649d0-56d7-48e2-a9d7-ac86cc0e7c28" data-name="path982" d="M79.62,26.83c-1.69-4.06-5.1-8.43-7.78-9.81a40.35,40.35,0,0,1,3.93,11.76v.08A35.1,35.1,0,0,1,74.57,55c-4.44,9.53-15.2,19.3-32,18.82-18.18-.51-34.2-14-37.19-31.68-.55-2.79,0-4.2.27-6.47A29.15,29.15,0,0,0,5,41v.2A38.76,38.76,0,0,0,82,47.8c.07-.5.12-1,.18-1.5A39.93,39.93,0,0,0,79.62,26.83Z" transform="translate(-5 0)" style="fill:url(#fbac4196-d99a-4f62-a085-471f7ae4a58b)"/><path id="b24e9f0d-5ca2-4232-85ad-ca59a0cca661" data-name="path984" d="M79.62,26.83c-1.69-4.06-5.1-8.43-7.78-9.81a40.35,40.35,0,0,1,3.93,11.76v.08A35.1,35.1,0,0,1,74.57,55c-4.44,9.53-15.2,19.3-32,18.82-18.18-.51-34.2-14-37.19-31.68-.55-2.79,0-4.2.27-6.47A29.15,29.15,0,0,0,5,41v.2A38.76,38.76,0,0,0,82,47.8c.07-.5.12-1,.18-1.5A39.93,39.93,0,0,0,79.62,26.83Z" transform="translate(-5 0)" style="fill:url(#f5c44653-6815-4f93-9326-b2ee91399c37)"/><path id="a06a5f17-2c86-4842-a3e9-6b45aa315912" data-name="path986" d="M60.78,31.38l.24.18a20.82,20.82,0,0,0-3.6-4.7C45.38,14.82,54.27.74,55.76,0l0,0C46,5.7,42.74,16.26,42.44,21.53c.45,0,.9-.06,1.36-.06A19.55,19.55,0,0,1,60.78,31.38Z" transform="translate(-5 0)" style="fill:url(#eac60cb4-5aa5-41b6-99ec-a3e95cfd58d6)"/><path id="aa176faa-53b1-4004-9f18-57d8845af648" data-name="path988" d="M43.83,33.79c-.07,1-3.48,4.29-4.67,4.29-11,0-12.81,6.66-12.81,6.66.49,5.62,4.4,10.24,9.13,12.69l.66.31c.38.17.76.32,1.14.47a17.32,17.32,0,0,0,5,1c19.32.91,23.06-23.1,9.12-30.07a13.39,13.39,0,0,1,9.34,2.27,19.55,19.55,0,0,0-17-9.91c-.46,0-.91,0-1.36.06a19.35,19.35,0,0,0-10.67,4.12c.59.5,1.26,1.16,2.67,2.55C37.07,30.79,43.81,33.47,43.83,33.79Z" transform="translate(-5 0)" style="fill:url(#a8bfab76-869c-4b27-99b0-cba9a7516a1b)"/><path id="b6914df8-08fc-4036-9d76-bf4a4d656265" data-name="path990" d="M43.83,33.79c-.07,1-3.48,4.29-4.67,4.29-11,0-12.81,6.66-12.81,6.66.49,5.62,4.4,10.24,9.13,12.69l.66.31c.38.17.76.32,1.14.47a17.32,17.32,0,0,0,5,1c19.32.91,23.06-23.1,9.12-30.07a13.39,13.39,0,0,1,9.34,2.27,19.55,19.55,0,0,0-17-9.91c-.46,0-.91,0-1.36.06a19.35,19.35,0,0,0-10.67,4.12c.59.5,1.26,1.16,2.67,2.55C37.07,30.79,43.81,33.47,43.83,33.79Z" transform="translate(-5 0)" style="fill:url(#f0ec94f2-0662-47de-8b17-0d29ffc9a2f6)"/><path id="ba93e260-cb3d-4cb8-96e3-13f4e27a7729" data-name="path992" d="M30,24.36c.31.2.57.37.79.53a17.94,17.94,0,0,1-.1-9.48,28.73,28.73,0,0,0-9.33,7.21C21.52,22.62,27.14,22.52,30,24.36Z" transform="translate(-5 0)" style="fill:url(#f452786f-e7d2-43b3-b102-fda40b127bbd)"/><path id="adf847b4-5d57-4a59-9035-17268a752d96" data-name="path994" d="M5.35,42.16c3,17.67,19,31.17,37.2,31.68,16.83.48,27.58-9.29,32-18.82a35.1,35.1,0,0,0,1.21-26.16v0s0,0,0,0v.07c1.37,9-3.2,17.67-10.33,23.55l0,.05c-13.9,11.33-27.21,6.84-29.91,5L35,57.17C26.85,53.3,23.5,45.91,24.22,39.57A9.94,9.94,0,0,1,15,33.8a14.59,14.59,0,0,1,14.24-.57,19.32,19.32,0,0,0,14.56.57c0-.32-6.76-3-9.39-5.59-1.41-1.39-2.07-2.05-2.67-2.55a10.53,10.53,0,0,0-1-.76l-.8-.53c-2.82-1.84-8.45-1.74-8.63-1.74h0c-1.53-1.94-1.43-8.36-1.34-9.7a6.91,6.91,0,0,0-1.29.68,28,28,0,0,0-3.79,3.25,33.6,33.6,0,0,0-3.64,4.34h0a32.83,32.83,0,0,0-5.2,11.75C6.05,33,4.67,39,5.35,42.16Z" transform="translate(-5 0)" style="fill:url(#a2748335-837e-4b7a-a4da-edeb8bf8419e)"/><path id="ff265533-9c2e-4809-979c-ce98909e624a" data-name="path996" d="M57.42,26.86a21,21,0,0,1,3.6,4.7c.22.17.42.33.59.48,8.78,8.1,4.18,19.55,3.84,20.37,7.13-5.89,11.7-14.58,10.33-23.56C71.39,17.92,64,13.52,57.89,3.92c-.31-.48-.61-1-.91-1.48-.17-.29-.31-.56-.43-.8A7.51,7.51,0,0,1,56,.1a.1.1,0,0,0-.08-.1h-.08l0,0h0C54.27.74,45.38,14.82,57.42,26.86Z" transform="translate(-5 0)" style="fill:url(#ab60b320-dda3-4742-b689-08fe852a77a7)"/><path id="afc1732d-a3b4-4034-a0b1-2aaad563731f" data-name="path998" d="M61.6,32a7.41,7.41,0,0,0-.58-.48l-.24-.18a13.43,13.43,0,0,0-9.35-2.27c13.94,7,10.2,31-9.12,30.07a17.32,17.32,0,0,1-5-1c-.38-.15-.76-.3-1.14-.47l-.65-.31,0,0c2.69,1.83,16,6.33,29.91-5l0-.05C65.79,51.59,70.39,40.13,61.6,32Z" transform="translate(-5 0)" style="fill:url(#a6639a63-3ba9-4b04-8f61-77703eb8371d)"/><path id="a024dba7-8939-4f18-809e-416982e10ead" data-name="path1000" d="M26.35,44.74s1.79-6.66,12.81-6.66c1.2,0,4.6-3.33,4.67-4.29a19.32,19.32,0,0,1-14.56-.57A14.61,14.61,0,0,0,15,33.79a10,10,0,0,0,9.19,5.77C23.49,45.9,26.84,53.29,35,57.16l.53.27C30.75,55,26.84,50.36,26.35,44.74Z" transform="translate(-5 0)" style="fill:url(#ba5f1830-09d8-4408-9f92-c51615d6dd83)"/><path id="ff19d047-cf87-4a45-a492-cf0f8de54c73" data-name="path1002" d="M79.62,26.83c-1.69-4.06-5.1-8.43-7.78-9.81a40.35,40.35,0,0,1,3.93,11.76v.07C71.39,17.92,64,13.52,57.89,3.92c-.31-.48-.61-1-.91-1.48-.17-.29-.31-.56-.43-.8A7.51,7.51,0,0,1,56,.1a.1.1,0,0,0-.08-.1h-.08l0,0h0V0c-9.73,5.7-13,16.26-13.34,21.53.45,0,.9-.06,1.36-.06a19.55,19.55,0,0,1,17,9.91,13.39,13.39,0,0,0-9.34-2.27c13.94,7,10.2,31-9.12,30.07a17.32,17.32,0,0,1-5-1c-.38-.15-.76-.3-1.14-.47l-.66-.31,0,0L35,57.16c.18.09.35.19.53.27-4.73-2.45-8.64-7.07-9.13-12.69,0,0,1.79-6.66,12.81-6.66,1.2,0,4.6-3.33,4.67-4.29,0-.32-6.76-3-9.39-5.59-1.41-1.39-2.08-2.05-2.67-2.55a10.53,10.53,0,0,0-1-.76,18,18,0,0,1-.11-9.48,28.94,28.94,0,0,0-9.33,7.21h0c-1.54-1.94-1.43-8.36-1.34-9.7a7,7,0,0,0-1.3.68,29.29,29.29,0,0,0-3.79,3.25,34.37,34.37,0,0,0-3.62,4.35h0a32.83,32.83,0,0,0-5.2,11.75L6,33.19c-.07.34-.4,2.08-.45,2.45h0A44.54,44.54,0,0,0,5,41v.2A38.76,38.76,0,0,0,82,47.8c.07-.5.12-1,.18-1.5A39.93,39.93,0,0,0,79.62,26.83Zm-3.85,2v0Z" transform="translate(-5 0)" style="fill:url(#e691cfeb-54b2-4ba4-bdea-72904ea15669)"/></svg>';
$GLOBALS['UA_ICON']['Safari'] = '<svg id="a4e92f88-6871-4900-94c1-275c64397f6e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 177.68 178.48" style="transform: scale(1.05) translateY(-1px);"><defs><filter id="a05a6cdb-870f-41fc-9968-9bfc71b00e41" x="-4.98" y="-0.16" width="1.1" height="1.1" name="filter2248"><feGaussianBlur result="feGaussianBlur2250" stdDeviation="3.56"/></filter><linearGradient id="b391e05f-04e8-477d-831d-5f38dc037c5d" x1="416.01" y1="-51.17" x2="416.01" y2="127.04" gradientTransform="matrix(1, 0, 0, -1, -320.78, 126.76)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#bdbdbd"/><stop offset="1" stop-color="#fff"/></linearGradient><radialGradient id="a8d29e90-c8e1-4024-809c-5fad76cc7f62" cx="348.08" cy="84.69" r="82.13" gradientTransform="matrix(1.08, 0, 0, -1.08, -282.2, 168.59)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#06c2e7"/><stop offset="0.25" stop-color="#0db8ec"/><stop offset="0.5" stop-color="#12aef1"/><stop offset="0.75" stop-color="#1f86f9"/><stop offset="1" stop-color="#107ddd"/></radialGradient><filter id="a02ea592-7884-4e73-abdb-077b4ef35cba" x="-4.96" y="-0.14" width="1.04" height="1.04" name="filter2222"><feGaussianBlur result="feGaussianBlur2224" stdDeviation="0.96"/></filter></defs><g id="f3b58452-405d-4528-a24d-ad5f359b3429" data-name="layer1"><g id="a5f87dbf-8edf-4e55-9cf3-0e39f3989dbe" data-name="g2858"><g id="a4ea5995-d48b-4a95-915b-bc306a00709f" data-name="path2226" style="opacity:0.5299999713897705;isolation:isolate;filter:url(#a05a6cdb-870f-41fc-9968-9bfc71b00e41)"><ellipse cx="88.84" cy="96.11" rx="85.54" ry="82.37"/></g><path id="abfd8a47-9913-472a-b88b-500895a25895" data-name="path826" d="M182.57,89a88.79,88.79,0,0,1-88.79,88.79h0A88.8,88.8,0,1,1,93.78.16h0A88.8,88.8,0,0,1,182.57,89Z" transform="translate(-4.94 -0.11)" style="stroke:#cdcdcd;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.09301234781742096px;fill:url(#b391e05f-04e8-477d-831d-5f38dc037c5d)"/><path id="a2cf3d28-e9a8-4054-9ece-194c3b43b069" data-name="circle828" d="M175.62,89a81.84,81.84,0,0,1-81.84,81.83h0A81.84,81.84,0,0,1,11.94,89h0A81.84,81.84,0,0,1,93.78,7.12h0A81.84,81.84,0,0,1,175.62,89Z" transform="translate(-4.94 -0.11)" style="fill:url(#a8d29e90-c8e1-4024-809c-5fad76cc7f62)"/><path id="be410b77-c0c6-410a-ae96-6efc91e42d07" data-name="rect830" d="M93.78,11.39a1.19,1.19,0,0,0-1.19,1.19V26.34a1.19,1.19,0,1,0,2.38,0V12.58A1.19,1.19,0,0,0,93.78,11.39ZM86,11.88a1,1,0,0,0-.24,0,1.18,1.18,0,0,0-1.06,1.31l.6,5.76a1.19,1.19,0,1,0,2.37-.25L87,12.94A1.18,1.18,0,0,0,86,11.88Zm15.67,0A1.2,1.2,0,0,0,100.57,13L100,18.71a1.2,1.2,0,0,0,2.38.25l.6-5.76a1.18,1.18,0,0,0-1.06-1.31h-.24ZM78,13l-.24,0a1.2,1.2,0,0,0-.92,1.41L79.64,28a1.2,1.2,0,1,0,2.34-.5L79.13,14A1.2,1.2,0,0,0,78,13Zm31.71,0a1.2,1.2,0,0,0-1.18.95l-2.86,13.46A1.2,1.2,0,1,0,108,28l2.86-13.47a1.2,1.2,0,0,0-.92-1.41l-.24,0ZM70.3,15.2a1.28,1.28,0,0,0-.47.05,1.2,1.2,0,0,0-.77,1.51l1.79,5.5a1.2,1.2,0,0,0,1.51.77,1.18,1.18,0,0,0,.76-1.5L71.33,16a1.18,1.18,0,0,0-1-.82Zm47,0a1.2,1.2,0,0,0-1,.82l-1.79,5.51a1.19,1.19,0,0,0,.77,1.5,1.18,1.18,0,0,0,1.5-.76l1.79-5.51a1.18,1.18,0,0,0-.76-1.5,1.29,1.29,0,0,0-.47-.06ZM62.73,18a1.27,1.27,0,0,0-.46.1,1.19,1.19,0,0,0-.6,1.58l5.59,12.57a1.18,1.18,0,0,0,1.57.61,1.2,1.2,0,0,0,.61-1.58L63.85,18.68A1.2,1.2,0,0,0,62.73,18Zm62.19,0a1.19,1.19,0,0,0-1.11.71L118.2,31.29a1.19,1.19,0,0,0,2.18,1L126,19.69a1.19,1.19,0,0,0-.6-1.57A1.11,1.11,0,0,0,124.92,18ZM55.71,21.69a1.19,1.19,0,0,0-1.12,1.78l2.9,5a1.2,1.2,0,0,0,2.07-1.2l-2.9-5a1.17,1.17,0,0,0-.95-.59Zm76.14,0a1.14,1.14,0,0,0-.95.59l-2.9,5a1.2,1.2,0,0,0,2.07,1.2l2.89-5a1.18,1.18,0,0,0-1.11-1.78Zm-83,4.25a1.17,1.17,0,0,0-.66.23A1.19,1.19,0,0,0,48,27.83L56.05,39A1.19,1.19,0,1,0,58,37.57L49.9,26.43a1.23,1.23,0,0,0-1-.49Zm89.86.06a1.2,1.2,0,0,0-1,.49l-8.1,11.13a1.19,1.19,0,1,0,1.93,1.4l8.09-11.12a1.19,1.19,0,0,0-.26-1.67,1.17,1.17,0,0,0-.66-.23Zm-96,5.05a1.24,1.24,0,0,0-.86.31A1.19,1.19,0,0,0,41.84,33l3.88,4.31a1.19,1.19,0,0,0,1.77-1.6l-3.87-4.3a1.19,1.19,0,0,0-.83-.4Zm102,0a1.19,1.19,0,0,0-.82.4l-3.88,4.3a1.19,1.19,0,0,0,1.78,1.59l3.87-4.3a1.19,1.19,0,0,0-.09-1.68,1.16,1.16,0,0,0-.86-.31ZM37,36.66a1.19,1.19,0,0,0-.82.4,1.17,1.17,0,0,0,.09,1.68L46.44,48a1.2,1.2,0,0,0,1.69-.09A1.19,1.19,0,0,0,48,46.18L37.81,37a1.17,1.17,0,0,0-.86-.3Zm113.69.05a1.18,1.18,0,0,0-.86.3l-10.23,9.2A1.19,1.19,0,0,0,141.14,48l10.24-9.2a1.2,1.2,0,0,0,.09-1.69A1.14,1.14,0,0,0,150.64,36.71ZM32.09,42.91a1.21,1.21,0,0,0-1,.5,1.19,1.19,0,0,0,.27,1.66L36,48.47a1.2,1.2,0,0,0,1.41-1.93l-4.69-3.4a1.25,1.25,0,0,0-.66-.23ZM155.5,43a1.16,1.16,0,0,0-.66.22l-4.69,3.4a1.2,1.2,0,0,0,1.4,1.94l4.69-3.4a1.2,1.2,0,0,0,.26-1.67,1.15,1.15,0,0,0-1-.49ZM27.55,49.58a1.17,1.17,0,0,0-.95.59A1.19,1.19,0,0,0,27,51.8L39,58.68a1.19,1.19,0,1,0,1.19-2.06L28.23,49.74a1.11,1.11,0,0,0-.68-.16Zm132.46,0a1.11,1.11,0,0,0-.68.16l-11.92,6.88a1.19,1.19,0,1,0,1.19,2.06l11.92-6.88a1.19,1.19,0,0,0,.44-1.63,1.17,1.17,0,0,0-1-.59ZM24.12,56.68a1.21,1.21,0,0,0-1.12.71A1.19,1.19,0,0,0,23.61,59l5.29,2.36a1.19,1.19,0,0,0,1-2.18l-5.29-2.35A1.11,1.11,0,0,0,24.12,56.68Zm139.34,0a1.27,1.27,0,0,0-.46.1l-5.29,2.36a1.19,1.19,0,0,0,1,2.18L164,59a1.19,1.19,0,0,0-.51-2.28ZM21.06,64.11a1.16,1.16,0,0,0-1,.82,1.18,1.18,0,0,0,.76,1.5l13.09,4.26a1.19,1.19,0,1,0,.73-2.26L21.53,64.16a1.1,1.1,0,0,0-.47,0Zm145.46,0a1.07,1.07,0,0,0-.47,0L153,68.47a1.19,1.19,0,0,0-.77,1.5,1.2,1.2,0,0,0,1.51.77l13.08-4.26a1.19,1.19,0,0,0-.26-2.32ZM19.15,71.9a1.19,1.19,0,0,0-.25,2.36l5.66,1.2A1.18,1.18,0,0,0,26,74.55a1.19,1.19,0,0,0-.92-1.42l-5.66-1.2a1,1,0,0,0-.24,0Zm149.26,0-.24,0-5.67,1.2a1.19,1.19,0,0,0,.5,2.33l5.66-1.2a1.19,1.19,0,0,0-.25-2.36ZM17.71,79.74a1.19,1.19,0,0,0,0,2.37l13.69,1.45a1.19,1.19,0,0,0,.25-2.37L18,79.74Zm152.15.1a1,1,0,0,0-.24,0l-13.69,1.43a1.2,1.2,0,0,0,.25,2.38l13.69-1.43a1.2,1.2,0,0,0,0-2.38ZM17.48,87.76a1.2,1.2,0,1,0,0,2.39h5.79a1.2,1.2,0,0,0,0-2.39Zm146.81,0a1.2,1.2,0,0,0,0,2.39h5.79a1.2,1.2,0,0,0,0-2.39ZM31.62,94.27h-.24L17.69,95.7a1.19,1.19,0,0,0,.25,2.37l13.69-1.43a1.19,1.19,0,0,0,0-2.37Zm124.31.08a1.2,1.2,0,0,0-1.07,1.07,1.19,1.19,0,0,0,1.06,1.31l13.69,1.45a1.2,1.2,0,0,0,.25-2.38l-13.69-1.45ZM24.8,102.41l-.24,0-5.67,1.2a1.2,1.2,0,0,0,.5,2.34l5.66-1.21a1.18,1.18,0,0,0,.92-1.41,1.19,1.19,0,0,0-1.17-1Zm138,0a1.19,1.19,0,0,0-1.18.94,1.21,1.21,0,0,0,.92,1.42l5.67,1.2a1.19,1.19,0,1,0,.49-2.33L163,102.45a1,1,0,0,0-.24,0Zm-128.43,4.7a1.1,1.1,0,0,0-.47.05l-13.09,4.25a1.2,1.2,0,0,0-.76,1.51,1.18,1.18,0,0,0,1.5.76l13.09-4.25a1.19,1.19,0,0,0,.77-1.5,1.17,1.17,0,0,0-1-.82Zm118.88,0a1.19,1.19,0,0,0-1,.82,1.18,1.18,0,0,0,.76,1.5L166,113.76a1.19,1.19,0,0,0,1.5-.77,1.18,1.18,0,0,0-.76-1.5l-13.09-4.27a1.08,1.08,0,0,0-.47,0ZM29.34,116.44a1.24,1.24,0,0,0-.46.11l-5.29,2.35a1.19,1.19,0,1,0,1,2.18l5.29-2.35a1.2,1.2,0,0,0-.51-2.29Zm128.86,0a1.17,1.17,0,0,0-1.11.71,1.19,1.19,0,0,0,.6,1.57l5.29,2.36a1.19,1.19,0,0,0,1-2.18l-5.29-2.36a1.27,1.27,0,0,0-.46-.1ZM39.64,119.07a1.26,1.26,0,0,0-.68.16L27,126.11a1.19,1.19,0,0,0,1.19,2.07l11.92-6.88a1.19,1.19,0,0,0,.44-1.63,1.21,1.21,0,0,0-1-.6Zm108.28,0a1.21,1.21,0,0,0-.95.6,1.19,1.19,0,0,0,.44,1.63l11.92,6.88a1.19,1.19,0,0,0,1.19-2.07l-11.92-6.88a1.26,1.26,0,0,0-.68-.16ZM36.66,129.17a1.25,1.25,0,0,0-.66.23l-4.68,3.4a1.19,1.19,0,1,0,1.4,1.93l4.69-3.4a1.2,1.2,0,0,0-.75-2.16Zm114.2,0a1.2,1.2,0,0,0-.74,2.16l4.69,3.41a1.2,1.2,0,0,0,1.4-1.94l-4.68-3.4a1.32,1.32,0,0,0-.67-.23Zm-103.58.42a1.19,1.19,0,0,0-.87.3l-10.23,9.2a1.2,1.2,0,0,0,1.6,1.78L48,131.71A1.2,1.2,0,0,0,48.1,130,1.14,1.14,0,0,0,47.28,129.63Zm93,0a1.22,1.22,0,0,0-.82.39,1.2,1.2,0,0,0,.09,1.69L149.75,141a1.19,1.19,0,0,0,1.68-.09,1.2,1.2,0,0,0-.09-1.69L141.11,130a1.15,1.15,0,0,0-.86-.3ZM57,138.4a1.18,1.18,0,0,0-1,.49L47.88,150a1.19,1.19,0,1,0,1.93,1.4l8.1-11.13a1.19,1.19,0,0,0-.26-1.66A1.17,1.17,0,0,0,57,138.4Zm73.51,0a1.23,1.23,0,0,0-.66.23,1.18,1.18,0,0,0-.26,1.66l8.08,11.14a1.19,1.19,0,1,0,1.93-1.4l-8.08-11.14a1.2,1.2,0,0,0-1-.49Zm-84,1.72a1.18,1.18,0,0,0-.82.39l-3.87,4.3a1.19,1.19,0,1,0,1.77,1.6l3.87-4.3a1.19,1.19,0,0,0-.95-2Zm94.49,0a1.24,1.24,0,0,0-.87.31,1.18,1.18,0,0,0-.08,1.68l3.87,4.31a1.19,1.19,0,1,0,1.77-1.6l-3.87-4.3a1.19,1.19,0,0,0-.82-.4ZM68.29,145a1.17,1.17,0,0,0-1.11.7l-5.61,12.57a1.19,1.19,0,1,0,2.18,1l5.61-12.56a1.19,1.19,0,0,0-.6-1.58,1.14,1.14,0,0,0-.47-.1Zm50.9,0a1.23,1.23,0,0,0-.46.1,1.19,1.19,0,0,0-.61,1.58l5.59,12.58a1.19,1.19,0,0,0,2.18-1l-5.59-12.58a1.2,1.2,0,0,0-1.11-.71Zm-60.75,3.85a1.21,1.21,0,0,0-.95.6l-2.9,5a1.19,1.19,0,0,0,2.07,1.19l2.9-5a1.19,1.19,0,0,0-.44-1.63,1.29,1.29,0,0,0-.68-.16Zm70.68,0a1.18,1.18,0,0,0-.68.16,1.19,1.19,0,0,0-.44,1.63l2.9,5a1.19,1.19,0,1,0,2.06-1.19l-2.89-5a1.21,1.21,0,0,0-.95-.6ZM80.77,149a1.19,1.19,0,0,0-1.17,1l-2.86,13.46a1.19,1.19,0,0,0,2.33.5l2.86-13.47A1.19,1.19,0,0,0,81,149l-.25,0Zm26,0a1,1,0,0,0-.24,0,1.18,1.18,0,0,0-.92,1.41l2.85,13.46a1.19,1.19,0,1,0,2.33-.49L107.91,150a1.19,1.19,0,0,0-1.17-.95Zm-13,1.36a1.19,1.19,0,0,0-1.19,1.19v13.76a1.19,1.19,0,1,0,2.38,0V151.57A1.19,1.19,0,0,0,93.78,150.38Zm-21.9,4.45a1.17,1.17,0,0,0-1,.82l-1.79,5.5a1.2,1.2,0,0,0,.77,1.51,1.19,1.19,0,0,0,1.5-.77l1.79-5.51a1.18,1.18,0,0,0-.76-1.5,1.1,1.1,0,0,0-.47,0Zm43.79,0a1.1,1.1,0,0,0-.47,0,1.2,1.2,0,0,0-.77,1.51l1.79,5.5a1.2,1.2,0,0,0,1.51.77,1.18,1.18,0,0,0,.76-1.5l-1.79-5.51a1.19,1.19,0,0,0-1-.82Zm-29.38,3.06A1.2,1.2,0,0,0,85.22,159l-.61,5.76A1.2,1.2,0,0,0,87,165l.6-5.75a1.18,1.18,0,0,0-1.06-1.31Zm14.93,0a1,1,0,0,0-.24,0,1.2,1.2,0,0,0-1.07,1.31l.61,5.76a1.18,1.18,0,0,0,1.31,1.06,1.19,1.19,0,0,0,1.06-1.31l-.6-5.76a1.19,1.19,0,0,0-1.07-1.06Z" transform="translate(-4.94 -0.11)" style="fill:#f4f2f3"/><g id="ad563743-e86a-43b9-9ea7-5679774c8f16" data-name="path2150" style="opacity:0.409000039100647;isolation:isolate;filter:url(#a02ea592-7884-4e73-abdb-077b4ef35cba)"><polygon points="144.77 41.12 79.49 79.05 38.21 144.02 98.59 99.3 144.77 41.12"/></g><g id="b77d43d9-3937-423a-a4c2-972024c9b1af" data-name="g2847"><path id="ac11e92d-0054-4b30-8b68-7fa54c1665bc" data-name="path2096" d="M103.13,98.75,84.42,79.16,150.8,34.51Z" transform="translate(-4.94 -0.11)" style="fill:#ff5150"/><path id="e822e44b-264f-4bbd-b17e-8675a476ae13" data-name="path2099" d="M103.13,98.75,84.42,79.16,36.76,143.4Z" transform="translate(-4.94 -0.11)" style="fill:#f1f1f1"/><path id="aa0c4885-4aa6-4620-bba4-b8e96c704d07" data-name="path2112" d="M36.76,143.4l66.37-44.65L150.8,34.51Z" transform="translate(-4.94 -0.11)" style="opacity:0.24300000071525574;isolation:isolate"/></g></g></g></svg>';
$GLOBALS['UA_ICON']['Edge'] = '<svg id="aa18aec7-1a6d-485f-bae9-692f083c3c03" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 256.02 256.05"><defs><linearGradient id="bdabf876-4b2a-401b-ae2e-987cb08dd27d" x1="63.33" y1="995.95" x2="241.62" y2="995.95" gradientTransform="translate(-4.63 -818.92)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#0c59a4"/><stop offset="1" stop-color="#114a8b"/></linearGradient><radialGradient id="fe174677-dad1-4826-a441-99ddd979d00c" cx="161.83" cy="1032.72" r="95.38" gradientTransform="translate(-4.63 -802.63) scale(1 0.95)" gradientUnits="userSpaceOnUse"><stop offset="0.72" stop-opacity="0"/><stop offset="0.95" stop-opacity="0.53"/><stop offset="1"/></radialGradient><linearGradient id="b101dfb4-29c0-4fe4-8cae-e04038fc9a8c" x1="157.41" y1="918.66" x2="46.02" y2="1039.99" gradientTransform="translate(-4.63 -818.92)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#1b9de2"/><stop offset="0.16" stop-color="#1595df"/><stop offset="0.67" stop-color="#0680d7"/><stop offset="1" stop-color="#0078d4"/></linearGradient><radialGradient id="fe97b05f-3188-4793-8e1b-46ec3f337d64" cx="-1454.1" cy="1697.79" r="143.24" gradientTransform="matrix(0.15, -0.99, 0.8, 0.12, -1069.54, -1444.29)" gradientUnits="userSpaceOnUse"><stop offset="0.76" stop-opacity="0"/><stop offset="0.95" stop-opacity="0.5"/><stop offset="1"/></radialGradient><radialGradient id="b5860bc6-66c5-4872-a544-cd5083b094a5" cx="-338.41" cy="-298.47" r="202.43" gradientTransform="matrix(-0.04, 1, -2.13, -0.08, -623.44, 361.91)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#35c1f1"/><stop offset="0.11" stop-color="#34c1ed"/><stop offset="0.23" stop-color="#2fc2df"/><stop offset="0.31" stop-color="#2bc3d2"/><stop offset="0.67" stop-color="#36c752"/></radialGradient><radialGradient id="a5f0bf22-9f30-4c72-a92f-cd6ab2e6001d" cx="174.77" cy="-738.47" r="97.34" gradientTransform="matrix(0.28, 0.96, -0.78, 0.23, -384.89, 79.47)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#66eb6e"/><stop offset="1" stop-color="#66eb6e" stop-opacity="0"/></radialGradient></defs><path d="M231.05,190.54a92,92,0,0,1-10.54,4.71,101.85,101.85,0,0,1-35.9,6.46c-47.32,0-88.54-32.55-88.54-74.32a31.47,31.47,0,0,1,16.43-27.31c-42.8,1.8-53.8,46.4-53.8,72.53,0,73.88,68.09,81.37,82.76,81.37,7.91,0,19.84-2.3,27-4.56l1.31-.44a128.39,128.39,0,0,0,66.6-52.8,4,4,0,0,0-5.32-5.64Z" transform="translate(0.02 0)" style="fill:url(#bdabf876-4b2a-401b-ae2e-987cb08dd27d)"/><path d="M231.05,190.54a92,92,0,0,1-10.54,4.71,101.85,101.85,0,0,1-35.9,6.46c-47.32,0-88.54-32.55-88.54-74.32a31.47,31.47,0,0,1,16.43-27.31c-42.8,1.8-53.8,46.4-53.8,72.53,0,73.88,68.09,81.37,82.76,81.37,7.91,0,19.84-2.3,27-4.56l1.31-.44a128.39,128.39,0,0,0,66.6-52.8,4,4,0,0,0-5.32-5.64Z" transform="translate(0.02 0)" style="opacity:0.3499999940395355;isolation:isolate;fill:url(#fe174677-dad1-4826-a441-99ddd979d00c)"/><path d="M105.71,241.42A79.24,79.24,0,0,1,83,220.08a80.72,80.72,0,0,1,17.61-112.79,81.62,81.62,0,0,1,11.92-7.21c3.12-1.47,8.45-4.13,15.54-4a32.32,32.32,0,0,1,25.69,13,31.89,31.89,0,0,1,6.36,18.66c0-.21,24.46-79.6-80-79.6-43.9,0-80,41.66-80,78.21a130.17,130.17,0,0,0,12.11,56,128,128,0,0,0,156.38,67.11,75.53,75.53,0,0,1-62.78-8Z" transform="translate(0.02 0)" style="fill:url(#b101dfb4-29c0-4fe4-8cae-e04038fc9a8c)"/><path d="M105.71,241.42A79.24,79.24,0,0,1,83,220.08a80.72,80.72,0,0,1,17.61-112.79,81.62,81.62,0,0,1,11.92-7.21c3.12-1.47,8.45-4.13,15.54-4a32.32,32.32,0,0,1,25.69,13,31.89,31.89,0,0,1,6.36,18.66c0-.21,24.46-79.6-80-79.6-43.9,0-80,41.66-80,78.21a130.17,130.17,0,0,0,12.11,56,128,128,0,0,0,156.38,67.11,75.53,75.53,0,0,1-62.78-8Z" transform="translate(0.02 0)" style="opacity:0.4099999964237213;isolation:isolate;fill:url(#fe97b05f-3188-4793-8e1b-46ec3f337d64)"/><path d="M152.31,148.86c-.81,1-3.3,2.5-3.3,5.66,0,2.61,1.7,5.12,4.72,7.23,14.38,10,41.49,8.68,41.56,8.68a59.6,59.6,0,0,0,30.27-8.35A61.36,61.36,0,0,0,256,109.2c.26-22.41-8-37.31-11.34-43.91C223.46,23.84,177.72,0,128,0A128,128,0,0,0,0,126.2c.48-36.54,36.8-66.05,80-66.05,3.5,0,23.46.34,42,10.07,16.34,8.58,24.9,18.94,30.85,29.21,6.18,10.67,7.28,24.15,7.28,29.52S157.37,142.28,152.31,148.86Z" transform="translate(0.02 0)" style="fill:url(#b5860bc6-66c5-4872-a544-cd5083b094a5)"/><path d="M152.31,148.86c-.81,1-3.3,2.5-3.3,5.66,0,2.61,1.7,5.12,4.72,7.23,14.38,10,41.49,8.68,41.56,8.68a59.6,59.6,0,0,0,30.27-8.35A61.36,61.36,0,0,0,256,109.2c.26-22.41-8-37.31-11.34-43.91C223.46,23.84,177.72,0,128,0A128,128,0,0,0,0,126.2c.48-36.54,36.8-66.05,80-66.05,3.5,0,23.46.34,42,10.07,16.34,8.58,24.9,18.94,30.85,29.21,6.18,10.67,7.28,24.15,7.28,29.52S157.37,142.28,152.31,148.86Z" transform="translate(0.02 0)" style="fill:url(#a5f0bf22-9f30-4c72-a92f-cd6ab2e6001d)"/></svg>';
$GLOBALS['UA_ICON']['Windows'] = $GLOBALS['UA_ICON']['Windows Phone OS'] = '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 88 88" style="transform: scale(.9) translateY(-1px);"><path style="fill:#00adef;" d="m0,12.402,35.687-4.8602,0.0156,34.423-35.67,0.20313zm35.67,33.529,0.0277,34.453-35.67-4.9041-0.002-29.78zm4.3261-39.025,47.318-6.906,0,41.527-47.318,0.37565zm47.329,39.349-0.0111,41.34-47.318-6.6784-0.0663-34.739z"/>
</svg>';
$GLOBALS['UA_ICON']['Linux'] = '<svg id="b1f0cd52-3ebf-40f7-bd2c-de8826bece89" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 406.6 484.61" style="transform: translateY(-1px);"><path d="M272.26,72.52a26.67,26.67,0,0,0-10.2,12.64,31.08,31.08,0,0,0,.81,20.39,33.83,33.83,0,0,0,12.05,17.19,22.07,22.07,0,0,0,9.84,3.81,18,18,0,0,0,10.35-1.8,20.06,20.06,0,0,0,8.58-9.37,34.64,34.64,0,0,0,2.82-12.6,42.54,42.54,0,0,0-2-16.31A27.36,27.36,0,0,0,293.9,72.41a20.52,20.52,0,0,0-8.34-3.12,17.76,17.76,0,0,0-8.84,1,22,22,0,0,0-4.49,2.45" transform="translate(-62.18 -0.02)" style="fill:#fff"/><path d="M426.55,293.46a229.34,229.34,0,0,0-12.86-44.72,128.23,128.23,0,0,0-16-30.22C391,209.59,382.53,202.06,376,193c-3.47-4.69-6.41-9.94-10-14.58q-2.2-4.52-4.3-9.09c-4.34-9.45-8.28-19.14-13.43-28.12-.81-1.42-1.64-2.81-2.52-4.17-.65-8.66-1.56-17.3-2-26a243.89,243.89,0,0,0-4.36-51.87A84.57,84.57,0,0,0,329,36.05a79.49,79.49,0,0,0-20.16-21.72A77.86,77.86,0,0,0,264.21,0a69.74,69.74,0,0,0-33.82,7.81A60.85,60.85,0,0,0,205,34.32a87.61,87.61,0,0,0-8.1,36c-.4,12.18.72,24.37,1.18,36.57.47,12.67.21,25.39,1.26,38,.34,4.08.81,8.14.8,12.24,0,2-.13,4.09-.16,6.12l-.16.44a259.16,259.16,0,0,1-18.28,27q-6.94,8.84-14.06,17.55A106,106,0,0,0,152,231a164.19,164.19,0,0,0-6.72,22l-.17.62a170.47,170.47,0,0,1-9.73,25c-.36.75-.74,1.56-1.1,2.25q-3.52,7.25-7.31,14.36l-2.92,5.48a87.43,87.43,0,0,0-4.89,10.19,33.17,33.17,0,0,0-1.81,6.48,34.21,34.21,0,0,0,.87,14.06c.3,1.15.64,2.29,1,3.41a74.11,74.11,0,0,0,4.25,9.81c.75,1.45,1.57,2.89,2.33,4.31l.7,1c.78,1.36,1.58,2.69,2.41,4l.09.16c.95,1.49,1.93,3,2.94,4.42l.14.2q1.55,2.15,3.13,4.27a113.08,113.08,0,0,0,21.46,42.76c-1.56,2.73-3,5.5-4.56,8.2a123.91,123.91,0,0,0-13.56,25.65,30.87,30.87,0,0,0-1,14.38,20.67,20.67,0,0,0,7.09,12.36,21.52,21.52,0,0,0,8.58,4,38.13,38.13,0,0,0,9.47.83,153.75,153.75,0,0,0,35.41-7q10.38-2.73,20.89-4.88a125.06,125.06,0,0,1,22.2-3c1.85,0,3.69,0,5.52-.18a104.1,104.1,0,0,0,15.29.56l1.88-.11c1.32.16,2.65.24,4,.31,9,.52,17.94,1.39,26.81,2.74q11.73,1.77,23.27,4.59a178.32,178.32,0,0,0,36.4,7,40.11,40.11,0,0,0,9.72-.77,21.6,21.6,0,0,0,8.81-4A20.67,20.67,0,0,0,380,454.05a30.83,30.83,0,0,0-1.06-14.4,119.37,119.37,0,0,0-13.81-25.57c-2-3.32-3.8-6.71-5.75-10a177.17,177.17,0,0,0,22-30.58,30,30,0,0,0,11.13-1.4A46.68,46.68,0,0,0,416,354.52a26.82,26.82,0,0,0,3.92-8,53,53,0,0,0,7.5-19.09,95.65,95.65,0,0,0-.83-34Z" transform="translate(-62.18 -0.02)" style="fill:#020204"/><path d="M218.36,140.14a19.28,19.28,0,0,0-3.48,7.36,38.32,38.32,0,0,0-1,8.12,71.75,71.75,0,0,1-1.36,16.28,50.2,50.2,0,0,1-8.42,15.3,92.49,92.49,0,0,0-14.73,26.43,47,47,0,0,0-1.71,18.22,193,193,0,0,0-17,30.67,167.55,167.55,0,0,0-13.8,51.12,130.43,130.43,0,0,0,9.19,63.78,104.49,104.49,0,0,0,27.21,37.92,93.44,93.44,0,0,0,19.89,13.2,89.18,89.18,0,0,0,79.89-.78A155.48,155.48,0,0,0,327,401a118.75,118.75,0,0,0,17.19-19.53,109.56,109.56,0,0,0,14.35-47.69,155.39,155.39,0,0,0-9.17-86.19,95.19,95.19,0,0,0-17.18-24.7,135.46,135.46,0,0,0-10.94-36.78c-3.88-8.4-8.6-16.42-12.19-25-1.47-3.5-2.75-7.09-4.39-10.51a31.56,31.56,0,0,0-6.4-9.38,26.45,26.45,0,0,0-10-5.81,43.37,43.37,0,0,0-11.47-2c-7.81-.39-15.62.62-23.34.31-6.25-.25-12.36-1.32-18.54-1a28.47,28.47,0,0,0-9.07,1.9,18.27,18.27,0,0,0-7.45,5.39m2.52-67.52A12.66,12.66,0,0,0,213.05,76a17.76,17.76,0,0,0-4.55,7.35,44.5,44.5,0,0,0-1,17.31,51.08,51.08,0,0,0,2.73,15.5,20.44,20.44,0,0,0,4.21,6.63,14.42,14.42,0,0,0,6.71,4,13.69,13.69,0,0,0,7.32-.26,15.94,15.94,0,0,0,6.25-3.81A21.1,21.1,0,0,0,240,113.4a36.85,36.85,0,0,0,1.23-10.71,44.56,44.56,0,0,0-2.06-13.31,30.05,30.05,0,0,0-6.81-11.56,19.4,19.4,0,0,0-5.22-3.91,13,13,0,0,0-6.33-1.39m51.43,0a26.67,26.67,0,0,0-10.2,12.64,31.08,31.08,0,0,0,.81,20.39,33.83,33.83,0,0,0,12.05,17.19,22.07,22.07,0,0,0,9.84,3.81,18,18,0,0,0,10.35-1.8,20.06,20.06,0,0,0,8.58-9.37,34.64,34.64,0,0,0,2.82-12.6,42.54,42.54,0,0,0-2-16.31A27.36,27.36,0,0,0,293.9,72.41a20.52,20.52,0,0,0-8.34-3.12,17.76,17.76,0,0,0-8.84,1,22,22,0,0,0-4.49,2.45" transform="translate(-62.18 -0.02)" style="fill:#fff"/><path d="M282.73,86.21a10.44,10.44,0,0,0-4.81,1.56,12.8,12.8,0,0,0-3.66,3.56,18.4,18.4,0,0,0-2.92,9.69,20.73,20.73,0,0,0,1,7.58,14.42,14.42,0,0,0,4.27,6.25,12.39,12.39,0,0,0,7.22,2.81,12.08,12.08,0,0,0,7.43-2.13,13.29,13.29,0,0,0,4.1-4.68,17.74,17.74,0,0,0,1.86-6A18.41,18.41,0,0,0,295.5,94.1a15.11,15.11,0,0,0-8-7.27,11.38,11.38,0,0,0-4.69-.73" transform="translate(-62.18 -0.02)" style="fill:#020204"/><path d="M220.86,72.52A12.66,12.66,0,0,0,213.05,76a17.76,17.76,0,0,0-4.55,7.35,44.5,44.5,0,0,0-1,17.31,51.08,51.08,0,0,0,2.73,15.5,20.44,20.44,0,0,0,4.21,6.63,14.42,14.42,0,0,0,6.71,4,13.69,13.69,0,0,0,7.32-.26,15.94,15.94,0,0,0,6.25-3.81A21.1,21.1,0,0,0,240,113.4a36.85,36.85,0,0,0,1.23-10.71,44.56,44.56,0,0,0-2.06-13.31,30.05,30.05,0,0,0-6.81-11.56,19.4,19.4,0,0,0-5.22-3.91,13,13,0,0,0-6.33-1.39" transform="translate(-62.18 -0.02)" style="fill:#fff"/><path d="M213.85,96.58a22,22,0,0,0,.62,10.94,18.44,18.44,0,0,0,3.88,6.25,12.73,12.73,0,0,0,3.78,2.93,7,7,0,0,0,4.69.6,6.91,6.91,0,0,0,3.62-2.44,12,12,0,0,0,2-4A22.05,22.05,0,0,0,232.22,98a16.79,16.79,0,0,0-4.94-8,9.82,9.82,0,0,0-4-2.1,7,7,0,0,0-4.53.36,7.7,7.7,0,0,0-3.5,3.54,16.28,16.28,0,0,0-1.45,4.82" transform="translate(-62.18 -0.02)" style="fill:#020204"/><path d="M207,142.12a4.54,4.54,0,0,0,.53,1.38,6.17,6.17,0,0,0,1.83,1.84c.68.5,1.42.9,2.14,1.36a38.79,38.79,0,0,1,9.67,9.37,60.42,60.42,0,0,0,11.9,13.52A25.13,25.13,0,0,0,245,173.85a38.7,38.7,0,0,0,14.81-1.72,52.28,52.28,0,0,0,12.7-5.57,133,133,0,0,1,22.05-14.46c1.81-.65,3.68-1.14,5.42-1.95a8.54,8.54,0,0,0,4.22-3.78,18.52,18.52,0,0,0,1-5.42c.28-1.95.9-3.86,1.25-5.8a9.74,9.74,0,0,0-.45-5.82,7.55,7.55,0,0,0-3.43-3.41,11.71,11.71,0,0,0-4.68-1.17,69.53,69.53,0,0,0-9.77,1c-4.32.39-8.67-.16-13,0-5.39.16-10.72,1.37-16.11,1.56-6.15.3-12.3-.66-18.45-.91a38.14,38.14,0,0,0-8,.38,18.38,18.38,0,0,0-7.4,2.86,71.37,71.37,0,0,0-5.81,5.18,14.67,14.67,0,0,1-3.21,2.21,8.29,8.29,0,0,1-3.76.89,6.1,6.1,0,0,0-2,0,2.81,2.81,0,0,0-1,.65,6.3,6.3,0,0,0-.78,1,17.54,17.54,0,0,0-1.42,2.54" transform="translate(-62.18 -0.02)" style="fill:#d99a03"/><path d="M220.58,128.5c-2.17,1.31-4.3,2.71-6.36,4.19a7.76,7.76,0,0,0-2.73,2.76,6.51,6.51,0,0,0-.5,2.72,25.78,25.78,0,0,1,0,2.78c-.08.62-.25,1.25-.29,1.89a3.34,3.34,0,0,0,.1.94,1.94,1.94,0,0,0,.45.82,2.28,2.28,0,0,0,1.06.61,11.83,11.83,0,0,0,1.21.28,12,12,0,0,1,5,2.94c1.47,1.31,2.79,2.8,4.32,4a24.21,24.21,0,0,0,15,4.84,67,67,0,0,0,15.81-2.2,96,96,0,0,0,12-3.33A53.51,53.51,0,0,0,282.12,142a56.23,56.23,0,0,1,6.74-5.58c2.18-1.38,4.68-2.28,6.86-3.6a3.6,3.6,0,0,0,.57-.39,1.62,1.62,0,0,0,.44-.53,1.58,1.58,0,0,0,0-1.17,3.19,3.19,0,0,0-.48-1,7.44,7.44,0,0,0-.92-.94,14.79,14.79,0,0,0-8.53-3c-3.13-.23-6.15,0-9.16-.57a59.84,59.84,0,0,1-8.28-2.41,60.68,60.68,0,0,0-8.8-2.14,58.18,58.18,0,0,0-21.17.52,62,62,0,0,0-18.75,7.31" transform="translate(-62.18 -0.02)" style="fill:#604405"/><path d="M219.89,121.11a38.73,38.73,0,0,0-8.37,7.64,18,18,0,0,0-3.31,5.56,43.25,43.25,0,0,0-1.08,5,9.28,9.28,0,0,0-.28,1.89,3.34,3.34,0,0,0,.14.94,2.23,2.23,0,0,0,.48.83,2.7,2.7,0,0,0,1.41.7c.52.11,1.05.12,1.56.19a14.63,14.63,0,0,1,6.53,2.78c2,1.36,3.78,2.92,5.8,4.2a30.32,30.32,0,0,0,15,4.28,67.43,67.43,0,0,0,15.7-1.56,71.66,71.66,0,0,0,12.11-3.41,72.3,72.3,0,0,0,16.5-9.83,68.81,68.81,0,0,0,6.73-5.57c.72-.69,1.41-1.41,2.17-2a7.9,7.9,0,0,1,2.56-1.48,9.6,9.6,0,0,1,4.49-.08,15.91,15.91,0,0,0,3.37.39,5,5,0,0,0,1.67-.26,3.42,3.42,0,0,0,1.38-1,3.32,3.32,0,0,0,.72-2.07,4.21,4.21,0,0,0-.61-2.13,7.23,7.23,0,0,0-3.44-2.76,33.45,33.45,0,0,0-5.87-1.72,84.91,84.91,0,0,1-17.68-6.46c-2.79-1.39-5.51-2.92-8.28-4.4a48.66,48.66,0,0,0-8.79-3.91,34.58,34.58,0,0,0-21.17,1,45.34,45.34,0,0,0-19.52,13.36" transform="translate(-62.18 -0.02)" style="fill:#f5bd0c"/><path d="M255.14,112.53c.37,1.22,2.33,1,3.45,1.56s1.79,1.57,2.89,1.66,2.72-.37,2.86-1.42c.19-1.39-1.84-2.28-3.12-2.78a6.69,6.69,0,0,0-5.42-.11C255.42,111.66,255,112.13,255.14,112.53Zm-18.64-.69c-1.45-.47-3.84,2.08-3.12,3.39.22.36.87.82,1.31.58s1.22-1.69,1.94-2.19c.54-.35.43-1.6-.13-1.78Z" transform="translate(-62.18 -0.02)" style="fill:#cd8907"/><path d="M468.45,414.31a27.74,27.74,0,0,1-4.59,7.73,50.07,50.07,0,0,1-16.14,11.45,318.89,318.89,0,0,0-30.05,15.86A123.91,123.91,0,0,0,400,463.61a140.43,140.43,0,0,1-14.4,13.08,41.43,41.43,0,0,1-17.94,7.59,43,43,0,0,1-23.32-3.53,28,28,0,0,1-13-10.15,30.62,30.62,0,0,1-3.64-16.19,169,169,0,0,1,3.55-29.48c1.44-8.11,2.81-16.24,3.69-24.44a252,252,0,0,0,.52-44.88,33.77,33.77,0,0,1,0-7.52,9.55,9.55,0,0,1,9.71-8.91,35.83,35.83,0,0,1,6.93.58,145.23,145.23,0,0,1,16.14,2.8c3.32.87,6.57,2,9.9,2.95a45.4,45.4,0,0,0,17.08,1.56,133.08,133.08,0,0,1,18.31-2.87,26.83,26.83,0,0,1,7.48,1.31,15.93,15.93,0,0,1,6.72,3.75,14.82,14.82,0,0,1,3.13,5,30.26,30.26,0,0,1,1.9,8.56,73,73,0,0,0,.68,7.81,25.48,25.48,0,0,0,5.74,11.31,73.35,73.35,0,0,0,9.28,8.69,113.53,113.53,0,0,0,10.07,7.81c1.65,1.13,3.36,2.19,4.92,3.42a15.64,15.64,0,0,1,4,4.44,11,11,0,0,1,1.11,7.81" transform="translate(-62.18 -0.02)" style="fill:#f5bd0c"/><path d="M468.45,414.31a27.74,27.74,0,0,1-4.59,7.73,50.07,50.07,0,0,1-16.14,11.45,318.89,318.89,0,0,0-30.05,15.86A123.91,123.91,0,0,0,400,463.61a140.43,140.43,0,0,1-14.4,13.08,41.43,41.43,0,0,1-17.94,7.59,43,43,0,0,1-23.32-3.53,28,28,0,0,1-13-10.15,30.62,30.62,0,0,1-3.64-16.19,169,169,0,0,1,3.55-29.48c1.44-8.11,2.81-16.24,3.69-24.44a252,252,0,0,0,.52-44.88,33.77,33.77,0,0,1,0-7.52,9.55,9.55,0,0,1,9.71-8.91,35.83,35.83,0,0,1,6.93.58,145.23,145.23,0,0,1,16.14,2.8c3.32.87,6.57,2,9.9,2.95a45.4,45.4,0,0,0,17.08,1.56,133.08,133.08,0,0,1,18.31-2.87,26.83,26.83,0,0,1,7.48,1.31,15.93,15.93,0,0,1,6.72,3.75,14.82,14.82,0,0,1,3.13,5,30.26,30.26,0,0,1,1.9,8.56,73,73,0,0,0,.68,7.81,25.48,25.48,0,0,0,5.74,11.31,73.35,73.35,0,0,0,9.28,8.69,113.53,113.53,0,0,0,10.07,7.81c1.65,1.13,3.36,2.19,4.92,3.42a15.64,15.64,0,0,1,4,4.44,11,11,0,0,1,1.11,7.81M128.43,331.26a14.91,14.91,0,0,1,8.33-.76,20.46,20.46,0,0,1,7.81,3.29A49,49,0,0,1,156,346.15c7.63,10.5,15,21.22,21.61,32.34,5.39,9,10.34,18.36,16.58,26.84,4.06,5.54,8.65,10.68,12.75,16.19a55.86,55.86,0,0,1,9.53,18.14,36.31,36.31,0,0,1-2.66,26,34.32,34.32,0,0,1-12.68,13.61,32.71,32.71,0,0,1-18,4.68,88.07,88.07,0,0,1-28.48-9c-18.89-7.53-39.42-9.89-58.89-15.75-6-1.79-11.87-3.94-17.89-5.59A53.65,53.65,0,0,1,70,451.13a13.75,13.75,0,0,1-6.25-5.25,11.79,11.79,0,0,1-1.56-6.25,19.38,19.38,0,0,1,1.26-6.25c1.47-4,3.83-7.69,5.43-11.67A49.17,49.17,0,0,0,71.59,401c-.34-7-1.56-13.94-2-20.94a36.31,36.31,0,0,1,.3-9.37,14.06,14.06,0,0,1,11.83-12,38.31,38.31,0,0,1,8.62-.55,83.38,83.38,0,0,0,8.66,0,19.89,19.89,0,0,0,8.26-2.31,19.51,19.51,0,0,0,5.94-5.61,67.67,67.67,0,0,0,4.25-7,43.91,43.91,0,0,1,4.47-6.89,17.1,17.1,0,0,1,6.43-5" transform="translate(-62.18 -0.02)" style="fill:#f5bd0c"/><path d="M128.43,331.26a14.91,14.91,0,0,1,8.33-.76,20.46,20.46,0,0,1,7.81,3.29A49,49,0,0,1,156,346.15c7.63,10.5,15,21.22,21.61,32.34,5.39,9,10.34,18.36,16.58,26.84,4.06,5.54,8.65,10.68,12.75,16.19a55.86,55.86,0,0,1,9.53,18.14,36.31,36.31,0,0,1-2.66,26,34.32,34.32,0,0,1-12.68,13.61,32.71,32.71,0,0,1-18,4.68,88.07,88.07,0,0,1-28.48-9c-18.89-7.53-39.42-9.89-58.89-15.75-6-1.8-11.87-3.94-17.89-5.59A53.65,53.65,0,0,1,70,451.13a13.75,13.75,0,0,1-6.25-5.25,11.82,11.82,0,0,1-1.56-6.25,19.38,19.38,0,0,1,1.26-6.25c1.47-4,3.83-7.69,5.43-11.67A49.17,49.17,0,0,0,71.59,401c-.34-7-1.56-13.94-2-20.94a36.31,36.31,0,0,1,.3-9.37,14.06,14.06,0,0,1,11.83-12,38.31,38.31,0,0,1,8.62-.55,83.38,83.38,0,0,0,8.66,0,19.89,19.89,0,0,0,8.26-2.31,19.51,19.51,0,0,0,5.94-5.61,68.73,68.73,0,0,0,4.25-7,43.91,43.91,0,0,1,4.47-6.89,17.1,17.1,0,0,1,6.43-5" transform="translate(-62.18 -0.02)" style="fill:#f5bd0c"/><path d="M132.56,335.84a12.78,12.78,0,0,1,7.54-.5,17.79,17.79,0,0,1,6.83,3.34,43.44,43.44,0,0,1,9.55,11.86c6.48,10.5,12.81,21.1,18.75,31.92a210.75,210.75,0,0,0,14.42,24c3.68,5,7.9,9.51,11.67,14.42a48.73,48.73,0,0,1,8.79,16.2,31.91,31.91,0,0,1-2.43,23.21A30.9,30.9,0,0,1,196,472.5a30.1,30.1,0,0,1-16.45,4.06,92.06,92.06,0,0,1-26.08-8.07c-16.48-6-34.37-6.78-51.24-11.46-6.07-1.63-12-3.84-18.07-5.37a55.79,55.79,0,0,1-8-2.31,13,13,0,0,1-6.36-5.16,11.36,11.36,0,0,1-1.37-6,18.44,18.44,0,0,1,1.33-6c1.47-3.86,3.78-7.35,5.26-11.21a43.16,43.16,0,0,0,2.11-18.49c-.42-6.25-1.56-12.42-1.87-18.65a32,32,0,0,1,.37-8.35,14.36,14.36,0,0,1,3.75-7.35,14.56,14.56,0,0,1,8.13-3.77,38.14,38.14,0,0,1,9.06,0,59.84,59.84,0,0,0,9.08.42,17,17,0,0,0,8.59-2.62,17.3,17.3,0,0,0,5.3-6.25,66.57,66.57,0,0,0,3.22-7.53,35.68,35.68,0,0,1,3.62-7.36,14.21,14.21,0,0,1,6.25-5.28" transform="translate(-62.18 -0.02)" style="fill:#f5bd0c"/></svg>';
$GLOBALS['UA_ICON']['Android'] = $GLOBALS['UA_ICON']['Android Browser'] = '<svg id="f1171df4-2e92-4c28-bb88-f9a2bfb960bf" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 426.51 500" style="transform: scale(1.14) translateY(-1px);"><path d="M77.12,359.13a40.43,40.43,0,0,1-40.38-40.38V195.14a40.38,40.38,0,1,1,80.76,0V318.75a40.06,40.06,0,0,1-11.81,28.56,40.62,40.62,0,0,1-28.57,11.82" transform="translate(-36.74)" style="fill:#fff"/><path d="M77.12,165.54A29.45,29.45,0,0,0,47.65,195V318.62a29.48,29.48,0,1,0,59,0V195a29.54,29.54,0,0,0-29.48-29.47m316.67,6.1H106.08v-10.9c0-44.54,24.28-85.95,64-110.49l-15.45-28a14.78,14.78,0,0,1-1.3-11.29,15.14,15.14,0,0,1,7.27-9A14,14,0,0,1,167.75,0a14.92,14.92,0,0,1,13.11,7.79l16.23,29.47A153.27,153.27,0,0,1,249.81,28a151.44,151.44,0,0,1,53.1,9.35L319,7.79A14.92,14.92,0,0,1,332.12,0a15.3,15.3,0,0,1,7.14,1.82,14.51,14.51,0,0,1,7.27,9.09,14.6,14.6,0,0,1-1.3,11.42L329.79,50.51c39.73,24.54,64,65.95,64,110.49Z" transform="translate(-36.74)" style="fill:#fff"/><path d="M315.11,54.53l20.65-37.65a4.16,4.16,0,0,0-1.56-5.58A4.07,4.07,0,0,0,328.62,13L307.84,51a143.89,143.89,0,0,0-115.42,0L171.64,13.11a4.13,4.13,0,0,0-7.27,3.9L185,54.66c-40.51,20.9-67.91,60.63-67.91,106.34H383c-.13-45.83-27.4-85.56-67.91-106.47M189.43,112.7a11.17,11.17,0,1,1,11.17-11.17,11.17,11.17,0,0,1-11.17,11.17m121.14,0a11.17,11.17,0,1,1,11.16-11.17,11.18,11.18,0,0,1-11.16,11.17" transform="translate(-36.74)" style="fill:#fff"/><path d="M200.6,500a40.43,40.43,0,0,1-40.38-40.38V405.09H149.57a42.56,42.56,0,0,1-30-12.33,41.28,41.28,0,0,1-12.33-30V160.35H393V362.76a42.33,42.33,0,0,1-42.32,42.33H340v54.53a40.38,40.38,0,1,1-80.76,0V405.09H241.11v54.53A40.71,40.71,0,0,1,200.6,500" transform="translate(-36.74)" style="fill:#fff"/><path d="M118.15,362.63a31.5,31.5,0,0,0,31.55,31.55h21.42v65.44a29.48,29.48,0,1,0,58.95-.13V394.05h39.86v65.44a29.48,29.48,0,0,0,58.95,0V394.05h21.55A31.58,31.58,0,0,0,382,362.5V170.86H118l.13,191.77ZM422.88,359a40.42,40.42,0,0,1-40.38-40.38V195a40.38,40.38,0,0,1,80.76,0V318.62A40.34,40.34,0,0,1,422.88,359" transform="translate(-36.74)" style="fill:#fff"/><path d="M422.88,165.54A29.45,29.45,0,0,0,393.4,195V318.62a29.48,29.48,0,0,0,59,0V195a29.45,29.45,0,0,0-29.47-29.47" transform="translate(-36.74)" style="fill:#fff"/><path d="M77.12,165.54A29.45,29.45,0,0,0,47.65,195V318.62a29.48,29.48,0,0,0,59,0V195a29.54,29.54,0,0,0-29.48-29.47m238-111,20.65-37.65a4.21,4.21,0,0,0-1.56-5.58A4.1,4.1,0,0,0,328.62,13L307.84,51a143.49,143.49,0,0,0-115.55-.13L171.51,13a4.13,4.13,0,0,0-7.27,3.9l20.65,37.65C144.38,75.43,117,115.17,117,160.87H382.89c0-45.7-27.27-85.43-67.78-106.34M189.43,112.7a11.17,11.17,0,1,1,11.17-11.17,11.17,11.17,0,0,1-11.17,11.17m121.14,0a11.17,11.17,0,1,1,11.16-11.17,11.18,11.18,0,0,1-11.16,11.17M118,171.12V362.76a31.5,31.5,0,0,0,31.55,31.55H171v65.44a29.48,29.48,0,1,0,58.95-.13V394.18H269.8v65.44a29.48,29.48,0,0,0,58.95,0V394.18H350.3a31.5,31.5,0,0,0,31.55-31.55V171L118,171.12ZM452.48,195a29.48,29.48,0,0,0-59,0V318.62a29.48,29.48,0,0,0,59,0Z" transform="translate(-36.74)" style="fill:#3add85"/></svg>
';
$GLOBALS['UA_ICON']['Macintosh'] = $GLOBALS['UA_ICON']['iPhone'] = $GLOBALS['UA_ICON']['iPad'] = $GLOBALS['UA_ICON']['iPod Touch'] = '<svg id="b764f9c4-dffc-4cad-aa28-59910c4d72d8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1187.2"><path id="b45aef73-dbb2-41d4-9f90-8b9321b86147" data-name="path4" d="M979,925.19A645,645,0,0,1,915.21,1040q-50.34,71.77-82.22,99.37-49.25,45.29-105.67,46.53c-27,0-59.65-7.69-97.61-23.3s-73.09-23.23-105.09-23.23q-50.34,0-108.06,23.23-57.84,23.42-93.37,24.55-54.18,2.31-108.06-47.78-34.41-30-86-102.89Q73.81,958.61,38.19,855.79,0,744.66,0,640.5,0,521.16,51.63,434.82q40.59-69.29,108.12-109.38c45.06-26.74,93.75-40.37,146.19-41.25,28.69,0,66.31,8.88,113.06,26.32s76.56,26.37,89.68,26.37c9.82,0,43.07-10.37,99.45-31.06q80-28.79,135.17-24,149.82,12.09,224.82,118.37-134,81.19-132.64,227.19Q836.7,741,917.78,816.22A270.35,270.35,0,0,0,1000,870.15q-9.88,28.7-21,55ZM750,23.75q0,89.06-64.92,166.12c-52.18,61-115.29,96.25-183.73,90.69a185.21,185.21,0,0,1-1.38-22.5c0-57,24.82-118,68.88-167.87q33-37.87,83.91-63Q703.51,2.44,748.72,0A215.59,215.59,0,0,1,750,23.75Z" transform="translate(0 0)" style="fill:#888"/></svg>';
$GLOBALS['UA_ICON']['Unknown'] = '<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5005" style="transform: scale(1.05) translateY(-1px);"><path d="M512.49152 511.50848m-504.29952 0a504.29952 504.29952 0 1 0 1008.59904 0 504.29952 504.29952 0 1 0-1008.59904 0Z" fill="#B9C0CB" p-id="5006"></path><path d="M506.92096 682.47552c-21.9136 0-39.64928 18.18624-39.64928 40.59136s17.73568 40.59136 39.64928 40.59136 39.69024-18.18624 39.69024-40.59136-17.77664-40.59136-39.69024-40.59136z m0-423.15776c-85.52448 9.70752-132.99712 47.63648-142.49984 113.74592-1.88416 21.38112 8.56064 33.05472 31.37536 35.0208 11.38688 1.96608 20.8896-6.79936 28.50816-26.25536 11.38688-40.83712 38.912-61.2352 82.65728-61.2352 53.16608 3.8912 81.67424 31.08864 85.52448 81.67424 0 46.65344-30.96576 54.272-47.75936 68.36224-21.2992 17.8176-35.2256 35.92192-52.30592 66.60096-14.336 25.76384-16.7936 80.896-16.7936 80.896 0 23.3472 10.40384 35.0208 31.37536 35.0208 18.96448 0 29.45024-11.6736 31.37536-35.0208 0 0 2.49856-61.8496 26.91072-89.21088 27.648-31.00672 93.75744-52.71552 95.6416-132.46464-7.70048-83.64032-58.9824-129.35168-154.0096-137.13408z" fill="#FFFFFF" p-id="5007"></path></svg>';