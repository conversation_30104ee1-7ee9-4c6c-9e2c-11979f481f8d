{"Version": 3, "Meta": {"Duration": 3.37, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 111, "TotalSegmentCount": 476, "TotalPointCount": 1413, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.122, -1, 0.244, -12, 0.367, -12, 1, 0.544, -12, 0.722, 14, 0.9, 14, 1, 1.1, 14, 1.3, -18, 1.5, -18, 1, 1.7, -18, 1.9, 7, 2.1, 7, 1, 2.311, 7, 2.522, -11, 2.733, -11, 0, 3.367, -11]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.122, 0, 0.244, -2, 0.367, -2, 0, 3.367, -2]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.5, 0, 1, -8, 1.5, -8, 1, 1.967, -8, 2.433, 8, 2.9, 8, 0, 3.367, 8]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, 18.601, 0.233, 18.601, 1, 0.356, 18.601, 0.478, -15.859, 0.6, -15.859, 1, 0.667, -15.859, 0.733, 6.126, 0.8, 6.126, 1, 0.878, 6.126, 0.956, -2.454, 1.033, -2.454, 1, 1.111, -2.454, 1.189, 0.982, 1.267, 0.982, 1, 1.344, 0.982, 1.422, -0.392, 1.5, -0.392, 1, 1.578, -0.392, 1.656, 0.156, 1.733, 0.156, 1, 1.811, 0.156, 1.889, -0.062, 1.967, -0.062, 1, 2.044, -0.062, 2.122, 0.024, 2.2, 0.024, 1, 2.278, 0.024, 2.356, -0.01, 2.433, -0.01, 1, 2.5, -0.01, 2.567, 0.004, 2.633, 0.004, 1, 2.711, 0.004, 2.789, -0.002, 2.867, -0.002, 1, 2.944, -0.002, 3.022, 0.001, 3.1, 0.001, 1, 3.178, 0.001, 3.256, 0, 3.333, 0, 1, 3.344, 0, 3.356, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.178, 1, 0.356, 0, 0.533, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 0, 3.367, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.178, 1, 0.356, 0, 0.533, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 0, 3.367, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.8, 1, 0.178, -0.8, 0.356, 0.3, 0.533, 0.3, 1, 0.911, 0.3, 1.289, 0.1, 1.667, 0.1, 0, 3.367, 0.1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0.415, 0.533, 0.5, 1, 0.911, 0.68, 1.289, 0.7, 1.667, 0.7, 0, 3.367, 0.7]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.144, 0, 0.289, -6, 0.433, -6, 1, 0.967, -6, 1.5, -6, 2.033, -6, 1, 2.444, -6, 2.856, 4, 3.267, 4, 0, 3.367, 4]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.144, 0, 0.289, -2, 0.433, -2, 1, 0.967, -2, 1.5, -2, 2.033, -2, 1, 2.444, -2, 2.856, 3, 3.267, 3, 0, 3.367, 3]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, -30, 1, 0.4, -30, 0.8, 30, 1.2, 30, 1, 1.633, 30, 2.067, -30, 2.5, -30, 1, 2.789, -30, 3.078, 13, 3.367, 13]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, 0.173, 0.233, 0.173, 1, 0.411, 0.173, 0.589, -1.224, 0.767, -1.224, 1, 0.856, -1.224, 0.944, -1.088, 1.033, -1.088, 1, 1.233, -1.088, 1.433, -2.196, 1.633, -2.196, 1, 2.1, -2.196, 2.567, 1.948, 3.033, 1.948, 1, 3.133, 1.948, 3.233, 1.886, 3.333, 1.886, 1, 3.344, 1.886, 3.356, 1.886, 3.367, 1.886]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, 0.173, 0.233, 0.173, 1, 0.411, 0.173, 0.589, -1.224, 0.767, -1.224, 1, 0.856, -1.224, 0.944, -1.088, 1.033, -1.088, 1, 1.233, -1.088, 1.433, -2.196, 1.633, -2.196, 1, 2.1, -2.196, 2.567, 1.948, 3.033, 1.948, 1, 3.133, 1.948, 3.233, 1.886, 3.333, 1.886, 1, 3.344, 1.886, 3.356, 1.886, 3.367, 1.886]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.689, 0, 1.111, -4.27, 1.533, -4.27, 1, 1.989, -4.27, 2.444, 4.396, 2.9, 4.396, 1, 3, 4.396, 3.1, 4.097, 3.2, 4.097, 1, 3.256, 4.097, 3.311, 4.148, 3.367, 4.187]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0.145, 0.467, 0.145, 1, 0.556, 0.145, 0.644, -0.087, 0.733, -0.087, 1, 0.822, -0.087, 0.911, 0.086, 1, 0.086, 1, 1.1, 0.086, 1.2, -0.055, 1.3, -0.055, 1, 1.378, -0.055, 1.456, -0.005, 1.533, -0.005, 1, 1.633, -0.005, 1.733, -0.133, 1.833, -0.133, 1, 1.944, -0.133, 2.056, -0.011, 2.167, -0.011, 1, 2.222, -0.011, 2.278, -0.053, 2.333, -0.053, 1, 2.556, -0.053, 2.778, 0.076, 3, 0.076, 1, 3.1, 0.076, 3.2, -0.075, 3.3, -0.075, 1, 3.322, -0.075, 3.344, -0.067, 3.367, -0.055]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0.145, 0.467, 0.145, 1, 0.556, 0.145, 0.644, -0.087, 0.733, -0.087, 1, 0.822, -0.087, 0.911, 0.086, 1, 0.086, 1, 1.1, 0.086, 1.2, -0.055, 1.3, -0.055, 1, 1.378, -0.055, 1.456, -0.005, 1.533, -0.005, 1, 1.633, -0.005, 1.733, -0.133, 1.833, -0.133, 1, 1.944, -0.133, 2.056, -0.011, 2.167, -0.011, 1, 2.222, -0.011, 2.278, -0.053, 2.333, -0.053, 1, 2.556, -0.053, 2.778, 0.076, 3, 0.076, 1, 3.1, 0.076, 3.2, -0.075, 3.3, -0.075, 1, 3.322, -0.075, 3.344, -0.067, 3.367, -0.055]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.756, 0, 1.111, -1.04, 1.467, -1.04, 1, 1.944, -1.04, 2.422, 1.059, 2.9, 1.059, 1, 3, 1.059, 3.1, 0.981, 3.2, 0.981, 1, 3.256, 0.981, 3.311, 0.99, 3.367, 0.999]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.467, 0, 0.533, 0.2, 0.6, 0.2, 1, 0.7, 0.2, 0.8, -0.191, 0.9, -0.191, 1, 1.011, -0.191, 1.122, 0.128, 1.233, 0.128, 1, 1.344, 0.128, 1.456, -0.123, 1.567, -0.123, 1, 1.667, -0.123, 1.767, -0.058, 1.867, -0.058, 1, 1.889, -0.058, 1.911, -0.058, 1.933, -0.058, 1, 2.044, -0.058, 2.156, 0.019, 2.267, 0.019, 1, 2.344, 0.019, 2.422, -0.046, 2.5, -0.046, 1, 2.611, -0.046, 2.722, 0.108, 2.833, 0.108, 1, 3, 0.108, 3.167, -0.071, 3.333, -0.071, 1, 3.344, -0.071, 3.356, -0.07, 3.367, -0.068]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.756, 0, 1.111, -1.444, 1.467, -1.444, 1, 1.944, -1.444, 2.422, 1.471, 2.9, 1.471, 1, 3, 1.471, 3.1, 1.362, 3.2, 1.362, 1, 3.256, 1.362, 3.311, 1.375, 3.367, 1.387]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.756, 0, 1.111, -1.444, 1.467, -1.444, 1, 1.944, -1.444, 2.422, 1.471, 2.9, 1.471, 1, 3, 1.471, 3.1, 1.362, 3.2, 1.362, 1, 3.256, 1.362, 3.311, 1.375, 3.367, 1.387]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.756, 0, 1.111, -1.444, 1.467, -1.444, 1, 1.944, -1.444, 2.422, 1.471, 2.9, 1.471, 1, 3, 1.471, 3.1, 1.362, 3.2, 1.362, 1, 3.256, 1.362, 3.311, 1.375, 3.367, 1.387]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.756, 0, 1.111, -1.04, 1.467, -1.04, 1, 1.944, -1.04, 2.422, 1.059, 2.9, 1.059, 1, 3, 1.059, 3.1, 0.981, 3.2, 0.981, 1, 3.256, 0.981, 3.311, 0.99, 3.367, 0.999]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.467, 0, 0.533, 0.2, 0.6, 0.2, 1, 0.7, 0.2, 0.8, -0.191, 0.9, -0.191, 1, 1.011, -0.191, 1.122, 0.128, 1.233, 0.128, 1, 1.344, 0.128, 1.456, -0.123, 1.567, -0.123, 1, 1.667, -0.123, 1.767, -0.058, 1.867, -0.058, 1, 1.889, -0.058, 1.911, -0.058, 1.933, -0.058, 1, 2.044, -0.058, 2.156, 0.019, 2.267, 0.019, 1, 2.344, 0.019, 2.422, -0.046, 2.5, -0.046, 1, 2.611, -0.046, 2.722, 0.108, 2.833, 0.108, 1, 3, 0.108, 3.167, -0.071, 3.333, -0.071, 1, 3.344, -0.071, 3.356, -0.07, 3.367, -0.068]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.756, 0, 1.111, -1.444, 1.467, -1.444, 1, 1.944, -1.444, 2.422, 1.471, 2.9, 1.471, 1, 3, 1.471, 3.1, 1.362, 3.2, 1.362, 1, 3.256, 1.362, 3.311, 1.375, 3.367, 1.387]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.856, 0, 1.111, -1.626, 1.367, -1.626, 1, 1.878, -1.626, 2.389, 1.68, 2.9, 1.68, 1, 3.056, 1.68, 3.211, 1.431, 3.367, 1.343]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, -0.081, 1, 0.044, -0.058, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.456, 0, 0.544, -0.539, 0.633, -0.539, 1, 0.767, -0.539, 0.9, 0.177, 1.033, 0.177, 1, 1.122, 0.177, 1.211, 0.059, 1.3, 0.059, 1, 1.378, 0.059, 1.456, 0.1, 1.533, 0.1, 1, 1.711, 0.1, 1.889, -0.113, 2.067, -0.113, 1, 2.078, -0.113, 2.089, -0.113, 2.1, -0.113, 1, 2.167, -0.113, 2.233, -0.249, 2.3, -0.249, 1, 2.656, -0.249, 3.011, -0.072, 3.367, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0.161, 1, 0.044, 0.115, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.444, 0, 0.522, 0.223, 0.6, 0.223, 1, 0.678, 0.223, 0.756, -0.203, 0.833, -0.203, 1, 0.933, -0.203, 1.033, 0.255, 1.133, 0.255, 1, 1.233, 0.255, 1.333, -0.272, 1.433, -0.272, 1, 1.544, -0.272, 1.656, 0.086, 1.767, 0.086, 1, 1.878, 0.086, 1.989, -0.227, 2.1, -0.227, 1, 2.322, -0.227, 2.544, 0.063, 2.767, 0.063, 1, 2.822, 0.063, 2.878, 0.05, 2.933, 0.05, 1, 2.989, 0.05, 3.044, 0.063, 3.1, 0.063, 1, 3.189, 0.063, 3.278, -0.003, 3.367, -0.047]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, -0.081, 1, 0.044, -0.058, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.478, 0, 0.589, 0.242, 0.7, 0.242, 1, 0.789, 0.242, 0.878, -0.288, 0.967, -0.288, 1, 1.067, -0.288, 1.167, 0.394, 1.267, 0.394, 1, 1.378, 0.394, 1.489, -0.508, 1.6, -0.508, 1, 1.7, -0.508, 1.8, 0.291, 1.9, 0.291, 1, 2.011, 0.291, 2.122, -0.409, 2.233, -0.409, 1, 2.356, -0.409, 2.478, 0.208, 2.6, 0.208, 1, 2.711, 0.208, 2.822, -0.012, 2.933, -0.012, 1, 3.033, -0.012, 3.133, 0.085, 3.233, 0.085, 1, 3.278, 0.085, 3.322, 0.055, 3.367, 0.016]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.856, 0, 1.111, -1.626, 1.367, -1.626, 1, 1.878, -1.626, 2.389, 1.68, 2.9, 1.68, 1, 3.056, 1.68, 3.211, 1.431, 3.367, 1.343]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, -0.081, 1, 0.044, -0.058, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.456, 0, 0.544, -0.539, 0.633, -0.539, 1, 0.767, -0.539, 0.9, 0.177, 1.033, 0.177, 1, 1.122, 0.177, 1.211, 0.059, 1.3, 0.059, 1, 1.378, 0.059, 1.456, 0.1, 1.533, 0.1, 1, 1.711, 0.1, 1.889, -0.113, 2.067, -0.113, 1, 2.078, -0.113, 2.089, -0.113, 2.1, -0.113, 1, 2.167, -0.113, 2.233, -0.249, 2.3, -0.249, 1, 2.656, -0.249, 3.011, -0.072, 3.367, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0.161, 1, 0.044, 0.115, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.444, 0, 0.522, 0.223, 0.6, 0.223, 1, 0.678, 0.223, 0.756, -0.203, 0.833, -0.203, 1, 0.933, -0.203, 1.033, 0.255, 1.133, 0.255, 1, 1.233, 0.255, 1.333, -0.272, 1.433, -0.272, 1, 1.544, -0.272, 1.656, 0.086, 1.767, 0.086, 1, 1.878, 0.086, 1.989, -0.227, 2.1, -0.227, 1, 2.322, -0.227, 2.544, 0.063, 2.767, 0.063, 1, 2.822, 0.063, 2.878, 0.05, 2.933, 0.05, 1, 2.989, 0.05, 3.044, 0.063, 3.1, 0.063, 1, 3.189, 0.063, 3.278, -0.003, 3.367, -0.047]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.856, 0, 1.111, -1.626, 1.367, -1.626, 1, 1.878, -1.626, 2.389, 1.68, 2.9, 1.68, 1, 3.056, 1.68, 3.211, 1.431, 3.367, 1.343]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, -0.081, 1, 0.044, -0.058, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.456, 0, 0.544, -0.539, 0.633, -0.539, 1, 0.767, -0.539, 0.9, 0.177, 1.033, 0.177, 1, 1.122, 0.177, 1.211, 0.059, 1.3, 0.059, 1, 1.378, 0.059, 1.456, 0.1, 1.533, 0.1, 1, 1.711, 0.1, 1.889, -0.113, 2.067, -0.113, 1, 2.078, -0.113, 2.089, -0.113, 2.1, -0.113, 1, 2.167, -0.113, 2.233, -0.249, 2.3, -0.249, 1, 2.656, -0.249, 3.011, -0.072, 3.367, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, 0.161, 1, 0.044, 0.115, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.444, 0, 0.522, 0.223, 0.6, 0.223, 1, 0.678, 0.223, 0.756, -0.203, 0.833, -0.203, 1, 0.933, -0.203, 1.033, 0.255, 1.133, 0.255, 1, 1.233, 0.255, 1.333, -0.272, 1.433, -0.272, 1, 1.544, -0.272, 1.656, 0.086, 1.767, 0.086, 1, 1.878, 0.086, 1.989, -0.227, 2.1, -0.227, 1, 2.322, -0.227, 2.544, 0.063, 2.767, 0.063, 1, 2.822, 0.063, 2.878, 0.05, 2.933, 0.05, 1, 2.989, 0.05, 3.044, 0.063, 3.1, 0.063, 1, 3.189, 0.063, 3.278, -0.003, 3.367, -0.047]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, -0.081, 1, 0.044, -0.058, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.478, 0, 0.589, 0.242, 0.7, 0.242, 1, 0.789, 0.242, 0.878, -0.288, 0.967, -0.288, 1, 1.067, -0.288, 1.167, 0.394, 1.267, 0.394, 1, 1.378, 0.394, 1.489, -0.508, 1.6, -0.508, 1, 1.7, -0.508, 1.8, 0.291, 1.9, 0.291, 1, 2.011, 0.291, 2.122, -0.409, 2.233, -0.409, 1, 2.356, -0.409, 2.478, 0.208, 2.6, 0.208, 1, 2.711, 0.208, 2.822, -0.012, 2.933, -0.012, 1, 3.033, -0.012, 3.133, 0.085, 3.233, 0.085, 1, 3.278, 0.085, 3.322, 0.055, 3.367, 0.016]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.856, 0, 1.111, -1.626, 1.367, -1.626, 1, 1.878, -1.626, 2.389, 1.68, 2.9, 1.68, 1, 3.056, 1.68, 3.211, 1.431, 3.367, 1.343]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, -0.081, 1, 0.044, -0.058, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.456, 0, 0.544, -0.539, 0.633, -0.539, 1, 0.767, -0.539, 0.9, 0.177, 1.033, 0.177, 1, 1.122, 0.177, 1.211, 0.059, 1.3, 0.059, 1, 1.378, 0.059, 1.456, 0.1, 1.533, 0.1, 1, 1.711, 0.1, 1.889, -0.113, 2.067, -0.113, 1, 2.078, -0.113, 2.089, -0.113, 2.1, -0.113, 1, 2.167, -0.113, 2.233, -0.249, 2.3, -0.249, 1, 2.656, -0.249, 3.011, -0.072, 3.367, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, 0.161, 1, 0.044, 0.115, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.444, 0, 0.522, 0.223, 0.6, 0.223, 1, 0.678, 0.223, 0.756, -0.203, 0.833, -0.203, 1, 0.933, -0.203, 1.033, 0.255, 1.133, 0.255, 1, 1.233, 0.255, 1.333, -0.272, 1.433, -0.272, 1, 1.544, -0.272, 1.656, 0.086, 1.767, 0.086, 1, 1.878, 0.086, 1.989, -0.227, 2.1, -0.227, 1, 2.322, -0.227, 2.544, 0.063, 2.767, 0.063, 1, 2.822, 0.063, 2.878, 0.05, 2.933, 0.05, 1, 2.989, 0.05, 3.044, 0.063, 3.1, 0.063, 1, 3.189, 0.063, 3.278, -0.003, 3.367, -0.047]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.856, 0, 1.111, -1.626, 1.367, -1.626, 1, 1.878, -1.626, 2.389, 1.68, 2.9, 1.68, 1, 3.056, 1.68, 3.211, 1.431, 3.367, 1.343]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, -0.081, 1, 0.044, -0.058, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.456, 0, 0.544, -0.539, 0.633, -0.539, 1, 0.767, -0.539, 0.9, 0.177, 1.033, 0.177, 1, 1.122, 0.177, 1.211, 0.059, 1.3, 0.059, 1, 1.378, 0.059, 1.456, 0.1, 1.533, 0.1, 1, 1.711, 0.1, 1.889, -0.113, 2.067, -0.113, 1, 2.078, -0.113, 2.089, -0.113, 2.1, -0.113, 1, 2.167, -0.113, 2.233, -0.249, 2.3, -0.249, 1, 2.656, -0.249, 3.011, -0.072, 3.367, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, 0.161, 1, 0.044, 0.115, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.444, 0, 0.522, 0.223, 0.6, 0.223, 1, 0.678, 0.223, 0.756, -0.203, 0.833, -0.203, 1, 0.933, -0.203, 1.033, 0.255, 1.133, 0.255, 1, 1.233, 0.255, 1.333, -0.272, 1.433, -0.272, 1, 1.544, -0.272, 1.656, 0.086, 1.767, 0.086, 1, 1.878, 0.086, 1.989, -0.227, 2.1, -0.227, 1, 2.322, -0.227, 2.544, 0.063, 2.767, 0.063, 1, 2.822, 0.063, 2.878, 0.05, 2.933, 0.05, 1, 2.989, 0.05, 3.044, 0.063, 3.1, 0.063, 1, 3.189, 0.063, 3.278, -0.003, 3.367, -0.047]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.856, 0, 1.111, -1.626, 1.367, -1.626, 1, 1.878, -1.626, 2.389, 1.68, 2.9, 1.68, 1, 3.056, 1.68, 3.211, 1.431, 3.367, 1.343]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, -0.081, 1, 0.044, -0.058, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.456, 0, 0.544, -0.539, 0.633, -0.539, 1, 0.767, -0.539, 0.9, 0.177, 1.033, 0.177, 1, 1.122, 0.177, 1.211, 0.059, 1.3, 0.059, 1, 1.378, 0.059, 1.456, 0.1, 1.533, 0.1, 1, 1.711, 0.1, 1.889, -0.113, 2.067, -0.113, 1, 2.078, -0.113, 2.089, -0.113, 2.1, -0.113, 1, 2.167, -0.113, 2.233, -0.249, 2.3, -0.249, 1, 2.656, -0.249, 3.011, -0.072, 3.367, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, 0.161, 1, 0.044, 0.115, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0, 0.367, 0, 1, 0.444, 0, 0.522, 0.223, 0.6, 0.223, 1, 0.678, 0.223, 0.756, -0.203, 0.833, -0.203, 1, 0.933, -0.203, 1.033, 0.255, 1.133, 0.255, 1, 1.233, 0.255, 1.333, -0.272, 1.433, -0.272, 1, 1.544, -0.272, 1.656, 0.086, 1.767, 0.086, 1, 1.878, 0.086, 1.989, -0.227, 2.1, -0.227, 1, 2.322, -0.227, 2.544, 0.063, 2.767, 0.063, 1, 2.822, 0.063, 2.878, 0.05, 2.933, 0.05, 1, 2.989, 0.05, 3.044, 0.063, 3.1, 0.063, 1, 3.189, 0.063, 3.278, -0.003, 3.367, -0.047]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, 0.183, 0.233, 0.183, 1, 0.411, 0.183, 0.589, -1.296, 0.767, -1.296, 1, 0.856, -1.296, 0.944, -1.152, 1.033, -1.152, 1, 1.233, -1.152, 1.433, -2.325, 1.633, -2.325, 1, 2.1, -2.325, 2.567, 2.063, 3.033, 2.063, 1, 3.133, 2.063, 3.233, 1.997, 3.333, 1.997, 1, 3.344, 1.997, 3.356, 1.997, 3.367, 1.997]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, -0.008, 1, 0.056, -0.034, 0.111, -0.11, 0.167, -0.11, 1, 0.278, -0.11, 0.389, 0.499, 0.5, 0.499, 1, 0.611, 0.499, 0.722, -0.257, 0.833, -0.257, 1, 0.989, -0.257, 1.144, 0.305, 1.3, 0.305, 1, 1.511, 0.305, 1.722, -0.478, 1.933, -0.478, 1, 2.222, -0.478, 2.511, 0.008, 2.8, 0.008, 1, 2.844, 0.008, 2.889, -0.022, 2.933, -0.022, 1, 3.011, -0.022, 3.089, 0.049, 3.167, 0.049, 1, 3.233, 0.049, 3.3, 0.023, 3.367, 0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0.092, 1, 0.011, 0.092, 0.022, 0.092, 0.033, 0.092, 1, 0.067, 0.092, 0.1, 0.085, 0.133, 0.085, 1, 0.144, 0.085, 0.156, 0.086, 0.167, 0.086, 1, 0.2, 0.086, 0.233, 0.027, 0.267, 0.027, 1, 0.378, 0.027, 0.489, 0.782, 0.6, 0.782, 1, 0.722, 0.782, 0.844, -0.542, 0.967, -0.542, 1, 1.111, -0.542, 1.256, 0.732, 1.4, 0.732, 1, 1.622, 0.732, 1.844, -1.027, 2.067, -1.027, 1, 2.478, -1.027, 2.889, 0.08, 3.3, 0.08, 1, 3.322, 0.08, 3.344, 0.075, 3.367, 0.067]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, -0.109, 1, 0.056, -0.124, 0.111, -0.168, 0.167, -0.168, 1, 0.356, -0.168, 0.544, 0.885, 0.733, 0.885, 1, 0.867, 0.885, 1, -0.746, 1.133, -0.746, 1, 1.278, -0.746, 1.422, 0.752, 1.567, 0.752, 1, 1.778, 0.752, 1.989, -0.959, 2.2, -0.959, 1, 2.4, -0.959, 2.6, -0.026, 2.8, -0.026, 1, 2.822, -0.026, 2.844, -0.028, 2.867, -0.028, 1, 2.911, -0.028, 2.956, -0.024, 3, -0.024, 1, 3.022, -0.024, 3.044, -0.025, 3.067, -0.025, 1, 3.167, -0.025, 3.267, 0.047, 3.367, 0.072]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, -0.153, 0.3, -0.153, 1, 0.478, -0.153, 0.656, 0.999, 0.833, 0.999, 1, 0.967, 0.999, 1.1, -0.976, 1.233, -0.976, 1, 1.378, -0.976, 1.522, 1.033, 1.667, 1.033, 1, 1.867, 1.033, 2.067, -1.194, 2.267, -1.194, 1, 2.456, -1.194, 2.644, 0.066, 2.833, 0.066, 1, 2.944, 0.066, 3.056, -0.041, 3.167, -0.041, 1, 3.233, -0.041, 3.3, -0.007, 3.367, 0.027]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, 0.183, 0.233, 0.183, 1, 0.411, 0.183, 0.589, -1.296, 0.767, -1.296, 1, 0.856, -1.296, 0.944, -1.152, 1.033, -1.152, 1, 1.233, -1.152, 1.433, -2.325, 1.633, -2.325, 1, 2.1, -2.325, 2.567, 2.063, 3.033, 2.063, 1, 3.133, 2.063, 3.233, 1.997, 3.333, 1.997, 1, 3.344, 1.997, 3.356, 1.997, 3.367, 1.997]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, -0.008, 1, 0.056, -0.034, 0.111, -0.11, 0.167, -0.11, 1, 0.278, -0.11, 0.389, 0.499, 0.5, 0.499, 1, 0.611, 0.499, 0.722, -0.257, 0.833, -0.257, 1, 0.989, -0.257, 1.144, 0.305, 1.3, 0.305, 1, 1.511, 0.305, 1.722, -0.478, 1.933, -0.478, 1, 2.222, -0.478, 2.511, 0.008, 2.8, 0.008, 1, 2.844, 0.008, 2.889, -0.022, 2.933, -0.022, 1, 3.011, -0.022, 3.089, 0.049, 3.167, 0.049, 1, 3.233, 0.049, 3.3, 0.023, 3.367, 0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0.092, 1, 0.011, 0.092, 0.022, 0.092, 0.033, 0.092, 1, 0.067, 0.092, 0.1, 0.085, 0.133, 0.085, 1, 0.144, 0.085, 0.156, 0.086, 0.167, 0.086, 1, 0.2, 0.086, 0.233, 0.027, 0.267, 0.027, 1, 0.378, 0.027, 0.489, 0.782, 0.6, 0.782, 1, 0.722, 0.782, 0.844, -0.542, 0.967, -0.542, 1, 1.111, -0.542, 1.256, 0.732, 1.4, 0.732, 1, 1.622, 0.732, 1.844, -1.027, 2.067, -1.027, 1, 2.478, -1.027, 2.889, 0.08, 3.3, 0.08, 1, 3.322, 0.08, 3.344, 0.075, 3.367, 0.067]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, -0.109, 1, 0.056, -0.124, 0.111, -0.168, 0.167, -0.168, 1, 0.356, -0.168, 0.544, 0.885, 0.733, 0.885, 1, 0.867, 0.885, 1, -0.746, 1.133, -0.746, 1, 1.278, -0.746, 1.422, 0.752, 1.567, 0.752, 1, 1.778, 0.752, 1.989, -0.959, 2.2, -0.959, 1, 2.4, -0.959, 2.6, -0.026, 2.8, -0.026, 1, 2.822, -0.026, 2.844, -0.028, 2.867, -0.028, 1, 2.911, -0.028, 2.956, -0.024, 3, -0.024, 1, 3.022, -0.024, 3.044, -0.025, 3.067, -0.025, 1, 3.167, -0.025, 3.267, 0.047, 3.367, 0.072]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, -0.153, 0.3, -0.153, 1, 0.478, -0.153, 0.656, 0.999, 0.833, 0.999, 1, 0.967, 0.999, 1.1, -0.976, 1.233, -0.976, 1, 1.378, -0.976, 1.522, 1.033, 1.667, 1.033, 1, 1.867, 1.033, 2.067, -1.194, 2.267, -1.194, 1, 2.456, -1.194, 2.644, 0.066, 2.833, 0.066, 1, 2.944, 0.066, 3.056, -0.041, 3.167, -0.041, 1, 3.233, -0.041, 3.3, -0.007, 3.367, 0.027]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 1.122, 0, 2.244, 0, 3.367, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, -30, 1, 1.122, -30, 2.244, -30, 3.367, -30]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, -30, 1, 1.122, -30, 2.244, -30, 3.367, -30]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 3.37, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 3.37, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 3.37, 1]}]}