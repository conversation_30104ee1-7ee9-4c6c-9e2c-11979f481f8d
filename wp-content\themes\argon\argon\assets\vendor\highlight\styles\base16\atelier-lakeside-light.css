/*!
  Theme: Atelier Lakeside Light
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atelier-lakeside-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #ebf8ff  Default Background
base01  #c1e4f6  Lighter Background (Used for status bars, line number and folding marks)
base02  #7ea2b4  Selection Background
base03  #7195a8  Comments, Invisibles, Line Highlighting
base04  #5a7b8c  Dark Foreground (Used for status bars)
base05  #516d7b  Default Foreground, Caret, Delimiters, Operators
base06  #1f292e  Light Foreground (Not often used)
base07  #161b1d  Light Background (Not often used)
base08  #d22d72  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #935c25  Integers, <PERSON><PERSON><PERSON>, Constants, XML Attributes, Markup Link Url
base0A  #8a8a0f  Classes, Markup Bold, Search Text Background
base0B  #568c3b  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #2d8f6f  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #257fad  Functions, Methods, Attribute IDs, Headings
base0E  #6b6bb8  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b72dd2  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #516d7b;
  background: #ebf8ff;
}

.hljs::selection,
.hljs ::selection {
  background-color: #7ea2b4;
  color: #516d7b;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #7195a8 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #7195a8;
}

/* base04 - #5a7b8c -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #5a7b8c;
}

/* base05 - #516d7b -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #516d7b;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #d22d72;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #935c25;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #8a8a0f;
}

.hljs-strong {
  font-weight:bold;
  color: #8a8a0f;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #568c3b;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #2d8f6f;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #257fad;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #6b6bb8;
}
.hljs-emphasis {
  color: #6b6bb8;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #b72dd2;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
