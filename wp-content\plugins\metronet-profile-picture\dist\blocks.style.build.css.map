{"version": 3, "sources": ["webpack://user-profile-picture/./src/block/style.scss"], "names": [], "mappings": "AAEC,0dACC,WAED,+EACC,SACA,mBAED,6EACC,mBAGF,kBACC,cACA,WACA,kBACA,oBACA,mBACA,gBACA,mDACC,kBACA,gBAED,uDACC,kBAED,8HACC,WAED,qBACC,aACA,mBAED,4BACC,aAED,6CACC,kBACA,WACA,gBACA,aACA,gBACA,kBACA,gBACA,iBACA,gBACA,iBACA,WACA,kBACA,mBACA,oDACC,kBACA,gBACA,iBACA,gBACA,iBACA,aAED,uEACC,kBACA,MACA,OACA,YACA,WACA,UAED,2EACC,YACA,WACA,kBACA,UAED,+DACC,cACA,sBACA,iBACA,WAED,+DACC,WACA,gBAED,0CA9CD,6CA+CE,WAGF,4CACC,WACA,iBAED,0CACC,WACA,cACA,WACA,kBACA,kBACA,yBACA,WACA,kJAGC,cACA,WACA,YACA,WACA,qBAKH,mCACC,kBACA,cACA,gBACA,oEACC,kBACA,gBAED,wEACC,kBAED,oOACC,WAED,6CACC,aAED,8DACC,kBACA,WACA,gBACA,aACA,gBACA,kBACA,gBACA,iBACA,gBACA,iBACA,WACA,kBACA,mBACA,0CAdD,8DAeE,WACA,kBACA,eAED,qEACC,kBACA,gBACA,iBACA,gBACA,iBACA,aAED,wFACC,kBACA,MACA,OACA,YACA,WACA,UAGD,4FACC,YACA,WACA,kBACA,UAED,gFACC,cACA,sBACA,iBACA,WACA,0CALD,gFAME,cACA,kBACA,eAGF,gFACC,WACA,gBAED,0CAzDD,8DA0DE,WAID,0CADD,qDAEE,cACA,kBACA,eAGF,6DACC,WACA,mEACC,WACA,cACA,WAIF,2DACC,WACA,cACA,WACA,kBACA,kBACA,gBACA,kBACA,yBACA,WACA,qMAGC,cACA,WACA,YACA,WACA,qBAED,iEACC,WACA,cACA,WAED,0CAxBD,2DAyBE,cACA,UACA,cACA,kBACA,oBAIF,6DACC,cACA,WACA,kBACA,kBACA,gBACA,sBACA,WACA,2MAGC,cACA,WACA,YACA,WACA,qBAED,0CAjBD,6DAkBE,cACA,UACA,cACA,kBACA,oBASF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eARF,gOAKC,eACA,8dAEC,eAOH,YACC,gBACA,gBACC,eACA,gBACA,kBAED,wDAGC,gCAuCF,YACC,WAIC,6BACC,aADD,4BACC,aADD,6BACC,aADD,6BACC,aADD,6BACC,aADD,6BACC,aADD,6BACC,aADD,gCACC,aADD,8BACC,aADD,4BACC,aADD,gCACC,aADD,2BACC,aADD,4BACC,aADD,iCACC,aADD,+BACC,aADD,8BACC,aADD,0BACC,aADD,4BACC,aADD,0BACC,aADD,+BACC,aADD,4BACC,aADD,+BACC,aADD,4BACC,aADD,2BACC,aADD,6BACC,aADD,iCACC,aADD,iCACC,aADD,8BACC,aADD,gCACC,aADD,4BACC,aADD,iCACC,aADD,2BACC,aADD,+BACC,aADD,6BACC,aAKH,mCACC,aACA,sCACC,SACA,eACA,mBACA,kBACA,6CACC,aAED,0CARD,sCASE,mBAGF,yDACC,kBAED,8DACC,kBACA,WACA,cACA,aACA,gBACA,iBACA,gBACA,iBACA,kBAEA,0CAXD,8DAYE,WACA,kBACA,eAGF,qDACC,WACA,cACA,2DACC,WACA,cACA,WAGF,8CACC,eACA,WACA,0CAHD,8CAIE,WACA,kBACA,eAGF,+CACC,eACA,0CAFD,+CAGE,WACA,kBACA,eAMH,mBACC,aACA,yBACC,WACA,cACA,WAED,+BACC,aAED,8CAMC,mBALA,oDACC,WACA,cACA,WAIF,oIACC,WAED,sBACC,eACA,mBACA,kBACA,6BACC,aAGF,oCACC,WAED,uCACC,WACA,0CAFD,uCAGE,WACA,kBACA,cACA,WAGF,sCACC,YACA,0CAFD,sCAGE,WACA,kBACA,eAGF,mEACC,qBACA,mBACA,WACA,kBACA,eACA,yBACA,0CAPD,mEAQE,cACA,oBAGF,mDACC,eACA,gBACA,gBACA,kBAED,yCACC,kBAED,8CACC,WACA,kBACA,WACA,cACA,aACA,kBACA,kDACC,gBACA,iBACA,iBACA,gBAKF,qCACC,WACA,cACA,2CACC,WACA,cACA,WAGF,6CACC,WACA,yBACA,eAGA,yDACC,WACA,cACA,WAGF,4BACC,aAED,mCACC,cAED,sCACC,kBACA,qBACA,SACA,UACA,kBAEA,yCACC,eACA,kBACA,qBACA,gBACA,mBACA,iBACA,kBACA,kBACA,WACA,kBACA,eACA,0CAZD,yCAaE,WACA,eAGD,sDACC,WACA,cACA,kBACA,aACA,sBACA,8BACA,yBACA,mCACA,oCACA,0CAVD,sDAWE,cAIH,oDACC,eAED,uDACC,mBACA,oEACC,8BACA,yBAIH,6CACC,qBACA,SACA,UACA,gDACC,SAGA,6KAGC,cACA,gBACA,yBACA,kBACA,qBACA,WAED,2DACC,mBAID,6KAGC,cACA,mBACA,yBACA,kBACA,qBACA,WAED,2DACC,mBAID,6KAGC,cACA,gBACA,WACA,yBACA,kBACA,qBAED,2DACC,mBAID,mLAGC,cACA,gBACA,WACA,sBACA,kBACA,qBAED,6DACC,gBAID,0KAGC,cACA,mBACA,WACA,yBACA,kBACA,qBAED,0DACC,mBAID,6KAGC,cACA,mBACA,WACA,yBACA,kBACA,qBAED,2DACC,mBAOJ,mCACC,kBACA,aACA,gBACA,cACA,gBACA,0CACC,cAED,wCACC,cAED,yCACC,eAED,mDACC,eAED,sCACC,eACA,mBACA,kBACA,6CACC,aAGF,yDACC,kBAED,8DACC,kBACA,kBACA,cACA,aACA,gBACA,iBACA,gBACA,iBACA,cAED,qDACC,gBAED,qDACC,WACA,cACA,2DACC,WACA,cACA,WAGF,2DACC,WACA,cACA,kBACA,kBACA,gBACA,kBACA,yBACA,WACA,mBACA,qMAGC,cACA,WACA,YACA,WACA,qBAED,iEACC,WACA,cACA,WAIF,6DACC,cACA,kBACA,kBACA,gBACA,sBACA,WACA,2MAGC,cACA,WACA,YACA,WACA,qB", "file": "blocks.style.build.css", "sourcesContent": [".post-content .mpp-profile-wrap,\n.post-content .mpp-enhanced-profile-wrap {\n\th1, h2 , h3, h4 , h5, h6 {\n\t\tclear: none;\n\t}\n\th2 {\n\t\tmargin: 0;\n\t\tmargin-bottom: 10px;\n\t}\n\tp {\n\t\tmargin-bottom: 10px;\n\t}\n}\n.mpp-profile-wrap {\n\tmargin: 0 auto;\n\tpadding: 3%;\n\tborder-radius: 5px;\n\tmargin-bottom: 1.2em;\n\tmargin-bottom: 20px;\n\tline-height: 1.3;\n\t&.round .mpp-profile-image-wrapper {\n\t\tborder-radius: 50%;\n\t\toverflow: hidden;\n\t}\n\t&.round .mpp-profile-image-wrapper img {\n\t\tborder-radius: 50%;\n\t}\n\th1,h2,h3,h4,h5,h6 {\n\t\tclear: none;\n\t}\n\th2 {\n\t\tmargin-top: 0;\n\t\tmargin-bottom: 10px;\n\t}\n\th2:before {\n\t\tdisplay: none;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tposition: relative;\n\t\tfloat: left;\n\t\tline-height: 1.1;\n\t\tz-index: 1000;\n\t\tbackground: #ddd;\n\t\tposition: relative;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\twidth: 100%;\n\t\tmargin-right: 20px;\n\t\tmargin-bottom: 20px;\n\t\tbutton {\n\t\t\tposition: relative;\n\t\t\tmin-width: 150px;\n\t\t\tmin-height: 150px;\n\t\t\tmax-width: 150px;\n\t\t\tmax-height: 150px;\n\t\t\tz-index: 1000;\n\t\t}\n\t\t.mpp-profile-image-square {\n\t\t\tposition: relative;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\theight: 100%;\n\t\t\twidth: 100%;\n\t\t\tz-index: 5;\n\t\t}\n\t\t.mpp-profile-image-square img {\n\t\t\theight: 100%;\n\t\t\twidth: 100%;\n\t\t\tposition: relative;\n\t\t\tz-index: 5;\n\t\t}\n\t\t.mpp-content-wrap {\n\t\t\tdisplay: block;\n\t\t\tpadding: 0 15px 0 15px;\n\t\t\t-ms-flex: 3 0 0px;\n\t\t\tflex: 3 0 0;\n\t\t}\n\t\t.mpp-profile-name {\n\t\t\tfont: 1.4em;\n\t\t\tline-height: 1.2;\n\t\t}\n\t\t@media only screen and (max-width: 600px) {\n\t\t\tflex: auto;\n\t\t}\n\t}\n\t.mpp-gutenberg-view-posts {\n\t\tclear: both;\n\t\tpadding-top: 20px;\n\t}\n\t.mpp-profile-view-posts {\n\t\tclear: both;\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tbackground-color: #cf6d38;\n\t\tcolor: #FFF;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t}\n}\n\n.mpp-enhanced-profile-wrap.regular {\n\tposition: relative;\n\tmargin: 0 auto;\n\tline-height: 1.5;\n\t&.round .mpp-profile-image-wrapper {\n\t\tborder-radius: 50%;\n\t\toverflow: hidden;\n\t}\n\t&.round .mpp-profile-image-wrapper img {\n\t\tborder-radius: 50%;\n\t}\n\th1,h2,h3,h4,h5,h6 {\n\t\tclear: none;\n\t}\n\th2:before {\n\t\tdisplay: none;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tposition: relative;\n\t\tfloat: left;\n\t\tline-height: 1.1;\n\t\tz-index: 1000;\n\t\tbackground: #ddd;\n\t\tposition: relative;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\twidth: 100%;\n\t\tmargin-right: 20px;\n\t\tmargin-bottom: 20px;\n\t\t@media only screen and (max-width: 400px) {\n\t\t\tfloat: none;\n\t\t\ttext-align: center;\n\t\t\tmargin: 0 auto;\n\t\t}\n\t\tbutton {\n\t\t\tposition: relative;\n\t\t\tmin-width: 150px;\n\t\t\tmin-height: 150px;\n\t\t\tmax-width: 150px;\n\t\t\tmax-height: 150px;\n\t\t\tz-index: 1000;\n\t\t}\n\t\t.mpp-profile-image-square {\n\t\t\tposition: relative;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\theight: 100%;\n\t\t\twidth: 100%;\n\t\t\tz-index: 5;\n\n\t\t}\n\t\t.mpp-profile-image-square img {\n\t\t\theight: 100%;\n\t\t\twidth: 100%;\n\t\t\tposition: relative;\n\t\t\tz-index: 5;\n\t\t}\n\t\t.mpp-content-wrap {\n\t\t\tdisplay: block;\n\t\t\tpadding: 0 15px 0 15px;\n\t\t\t-ms-flex: 3 0 0px;\n\t\t\tflex: 3 0 0;\n\t\t\t@media only screen and (max-width: 400px) {\n\t\t\t\tdisplay: block;\n\t\t\t\ttext-align: center;\n\t\t\t\tmargin: 0 auto;\n\t\t\t}\n\t\t}\n\t\t.mpp-profile-name {\n\t\t\tfont: 1.4em;\n\t\t\tline-height: 1.2;\n\t\t}\n\t\t@media only screen and (max-width: 600px) {\n\t\t\tflex: auto;\n\t\t}\n\t}\n\t.mpp-content-wrap {\n\t\t@media only screen and (max-width: 400px) {\n\t\t\tdisplay: block;\n\t\t\ttext-align: center;\n\t\t\tmargin: 0 auto;\n\t\t}\n\t}\n\t.mpp-gutenberg-view-posts {\n\t\tclear: both;\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\n\t.mpp-profile-view-posts {\n\t\tclear: both;\n\t\tdisplay: block;\n\t\tfloat: left;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tmargin-right: 20px;\n\t\tbackground-color: #cf6d38;\n\t\tcolor: #FFF;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t\t@media only screen and (max-width: 400px) {\n\t\t\tdisplay: block;\n\t\t\twidth: 90%;\n\t\t\tmargin: 0 auto;\n\t\t\ttext-align: center;\n\t\t\tmargin-bottom: 10px;\n\t\t}\n\t}\n\n\t.mpp-profile-view-website {\n\t\tdisplay: block;\n\t\tfloat: left;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tbackground-color: #333;\n\t\tcolor: #FFF;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t\t@media only screen and (max-width: 400px) {\n\t\t\tdisplay: block;\n\t\t\twidth: 90%;\n\t\t\tmargin: 0 auto;\n\t\t\ttext-align: center;\n\t\t\tmargin-bottom: 10px;\n\t\t}\n\t}\n}\n\n\n/* Icon Sizes */\n$sizes: 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24;\n@each $size in $sizes {\n\t.mpp-profile-wrap.mt-font-size-#{$size},\n\t.mpp-enhanced-profile-wrap.regular .mt-font-size-#{$size},\n\t.mpp-enhanced-profile-wrap.profile .mt-font-size-#{$size},\n\t.mpp-enhanced-profile-wrap.compact .mt-font-size-#{$size},\n\t.mpp-profile-text.mt-font-size-#{$size} {\n\t\tfont-size: #{$size}px;\n\t\tp,\n\t\tdiv {\n\t\t\tfont-size: #{$size}px;\n\t\t}\n\n\t}\n}\n\n/* For Social Media */\n.mpp-social {\n\tmargin-top: 15px;\n\tsvg {\n\t\tmax-width: 32px;\n\t\tmax-height: 32px;\n\t\tmargin-right: 10px;\n\t}\n\ta,\n\ta:hover,\n\ta:visited {\n\t\ttext-decoration: none !important;\n\t}\n}\n/* Social Media Colors */\n$colors: twitter #00aced,\n\tamazon #000000,\n\tbehance #0692e9,\n\tblogger #fb8f3d,\n\tcodepen #000000,\n\tdribble #F46899,\n\tdropbox #018BD3,\n\teventbrite #f6682F,\n\tfacebook #3b5998,\n\tflickr #ff0084,\n\tfoursquare #0072b1,\n\tghost #000000,\n\tgithub #070709,\n\tgoogle-plus #CF3D2E,\n\tinstagram #A1755C,\n\tlinkedin #0085AE,\n\tfeed #f26522,\n\tmedium #000000,\n\tpath #000000,\n\tpinterest #CC2127,\n\tpocket #000000,\n\tpolldaddy #bc0b0b,\n\treddit #000000,\n\tskype #01AEF2,\n\tspotify #1ed760,\n\tsquarespace #000000,\n\tstumbleupon #EB4823,\n\ttelegram #000000,\n\ttumblr-alt #314E6C,\n\ttwitch #4b367c,\n\ttwitter-alt #00aced,\n\tvimeo #1ab7ea,\n\twordpress #21759b,\n\tyoutube #bb0000\n;\n.mpp-social {\n\tclear: both;\n}\n.mpp-social svg {\n\t@each $color in $colors {\n\t\t&.icon-#{nth($color,1)} {\n\t\t\tfill: #{nth($color,2)};\n\t\t}\n\t}\n}\n\n.mpp-enhanced-profile-wrap.profile {\n\tpadding: 10px;\n\th2 {\n\t\tmargin: 0;\n\t\tfont-size: 0.8em;\n\t\tfont-weight: normal;\n\t\tmargin-bottom: 5px;\n\t\t&:before {\n\t\t\tdisplay: none;\n\t\t}\n\t\t@media only screen and (max-width: 400px) {\n\t\t\ttext-align: center;\n\t\t}\n\t}\n\t&.round .profile-avatar {\n\t\tborder-radius: 50%;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tposition: relative;\n\t\tfloat: left;\n\t\tline-height: 1.0;\n\t\tz-index: 1000;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\tmargin-right: 15px;\n\n\t\t@media only screen and (max-width: 400px) {\n\t\t\tfloat: none;\n\t\t\ttext-align: center;\n\t\t\tmargin: 0 auto;\n\t\t}\n\t}\n\t.mpp-profile-meta {\n\t\tclear: both;\n\t\tmargin: 10px 0;\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\t.alignleft {\n\t\tmax-width: none;\n\t\tfloat: left;\n\t\t@media only screen and (max-width: 400px) {\n\t\t\tfloat: none;\n\t\t\ttext-align: center;\n\t\t\tmargin: 0 auto;\n\t\t}\n\t}\n\t.alignright {\n\t\tmax-width: none;\n\t\t@media only screen and (max-width: 400px) {\n\t\t\tfloat: none;\n\t\t\ttext-align: center;\n\t\t\tmargin: 0 auto;\n\t\t}\n\t}\n}\n\n/* For Tabbed Theme */\n.mpp-author-tabbed {\n\tpadding: 10px;\n\t&:after {\n\t\tcontent: \"\";\n\t\tdisplay: table;\n\t\tclear: both;\n\t}\n\t.mpp-social {\n\t\tmargin-top: 0;\n\t}\n\t.mpp-author-social-wrapper {\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t\tmargin-bottom: 10px;\n\t}\n\th1, h2, h3, h4, h5 , h6 {\n\t\tclear: none;\n\t}\n\th2 {\n\t\tfont-size: 0.8em;\n\t\tfont-weight: normal;\n\t\tmargin-bottom: 5px;\n\t\t&:before {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\t.mpp-tab-wrapper {\n\t\tclear: both;\n\t}\n\t.mpp-author-heading {\n\t\tfloat: left;\n\t\t@media only screen and (max-width: 600px) {\n\t\t\tfloat: none;\n\t\t\ttext-align: center;\n\t\t\tmargin: 0 auto;\n\t\t\twidth: 90%;\n\t\t}\n\t}\n\t.mpp-author-social {\n\t\tfloat: right;\n\t\t@media only screen and (max-width: 600px) {\n\t\t\tfloat: none;\n\t\t\ttext-align: center;\n\t\t\tmargin: 0 auto;\n\t\t}\n\t}\n\t.mpp-author-heading .mpp-author-profile-heading {\n\t\tdisplay: inline-block;\n\t\tbackground: #42737b;\n\t\tcolor: #FFF;\n\t\tpadding: 10px 20px;\n\t\tfont-size: 14px;\n\t\ttext-transform: uppercase;\n\t\t@media only screen and (max-width: 600px) {\n\t\t\tdisplay: block;\n\t\t\tmargin-bottom: 10px;\n\t\t}\n\t}\n\t.mpp-author-profile-sub-heading {\n\t\tfont-size: 14px;\n\t\tline-height: 1.1;\n\t\tmax-width: 150px;\n\t\ttext-align: center;\n\t}\n\t&.round .profile-avatar {\n\t\tborder-radius: 50%;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tclear: both;\n\t\tposition: relative;\n\t\tfloat: left;\n\t\tline-height: 1.0;\n\t\tz-index: 1000;\n\t\tmargin-right: 30px;\n\t\timg {\n\t\t\tmin-width: 150px;\n\t\t\tmin-height: 150px;\n\t\t\tmax-height: 150px;\n\t\t\tmax-width: 150px;\n\t\t}\n\n\n\t}\n\t.mpp-profile-meta {\n\t\tclear: both;\n\t\tmargin: 10px 0;\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\t.mpp-author-profile-title {\n\t\tcolor: gray;\n\t\ttext-transform: uppercase;\n\t\tfont-size: 12px;\n\t}\n\t.mpp-tabbed-profile-information {\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\t.mpp-tab {\n\t\tdisplay: none;\n\t}\n\t.mpp-tab-active {\n\t\tdisplay: block;\n\t}\n\tul.mpp-author-tabs {\n\t\tposition:relative;\n\t\tlist-style-type: none;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\ttext-align: center;\n\n\t\tli {\n\t\t\tcursor: pointer;\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tmin-width: 200px;\n\t\t\tbackground: #42737b;\n\t\t\tmargin: 0 5px 0 0;\n\t\t\ttext-align: center;\n\t\t\tmargin-right: 10px;\n\t\t\tcolor: #FFF;\n\t\t\tpadding: 10px 20px;\n\t\t\tfont-size: 16px;\n\t\t\t@media only screen and (max-width: 500px) {\n\t\t\t\twidth: 100%;\n\t\t\t\tdisplay: block;\n\t\t\t}\n\n\t\t\t&.active:after {\n\t\t\t\tcontent: \"\";\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: -10px;\n\t\t\t\tleft: calc(50% - 10px);\n\t\t\t\tborder-top: 10px solid #42737b;\n\t\t\t\tborder-top-color: #42737b;\n\t\t\t\tborder-left: 10px solid transparent;\n\t\t\t\tborder-right: 10px solid transparent;\n\t\t\t\t@media only screen and (max-width: 500px) {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tli:last-child {\n\t\t\tmargin-right: 0;\n\t\t}\n\t\tli.mpp-tab-posts {\n\t\t\tbackground: #30424b;\n\t\t\t&.active:after {\n\t\t\t\tborder-top: 10px solid #30424b;\n\t\t\t\tborder-top-color: #30424b;\n\t\t\t}\n\t\t}\n\t}\n\tul.mpp-author-tab-content {\n\t\tlist-style-type: none;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\tli {\n\t\t\tmargin: 0;\n\t\t}\n\t\t&.white {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #FFFFFF;\n\t\t\t\tborder: 1px solid darken(#FFFFFF, 5%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#FFFFFF, 5%);\n\t\t\t}\n\t\t}\n\t\t&.light {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #f7f7f7;\n\t\t\t\tborder: 1px solid darken(#f7f7f7, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#f7f7f7, 10%);\n\t\t\t}\n\t\t}\n\t\t&.black {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #333;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tborder: 1px solid darken(#333, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#333, 10%);\n\t\t\t}\n\t\t}\n\t\t&.magenta {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #FF00FF;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tborder: 1px solid darken(#FF00FF, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#FF00FF, 10%);\n\t\t\t}\n\t\t}\n\t\t&.blue {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #0009c1;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tborder: 1px solid darken(#0009c1, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#0009c1, 10%);\n\t\t\t}\n\t\t}\n\t\t&.green {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #03ac27;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tborder: 1px solid darken(#03ac27, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#03ac27, 10%);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* For Compact Theme */\n.mpp-enhanced-profile-wrap.compact {\n\ttext-align: center;\n\tpadding: 10px;\n\tmax-width: 400px;\n\tmargin: 0 auto;\n\tline-height: 1.1;\n\t&.center {\n\t\tmargin: 0 auto;\n\t}\n\t&.left {\n\t\tmargin-left: 0;\n\t}\n\t&.right {\n\t\tmargin-right: 0;\n\t}\n\t.mpp-social svg {\n\t\tmargin-right: 0;\n\t}\n\th2 {\n\t\tfont-size: 0.8em;\n\t\tfont-weight: normal;\n\t\tmargin-bottom: 5px;\n\t\t&:before {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\t&.round .profile-avatar {\n\t\tborder-radius: 50%;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tposition: relative;\n\t\ttext-align: center;\n\t\tline-height: 1.0;\n\t\tz-index: 1000;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\tmargin: 0 auto;\n\t}\n\t.mpp-profile-text {\n\t\tline-height: 1.1;\n\t}\n\t.mpp-compact-meta {\n\t\tclear: both;\n\t\tmargin: 10px 0;\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\t.mpp-profile-view-posts {\n\t\tclear: both;\n\t\tdisplay: block;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tmargin-right: 20px;\n\t\tbackground-color: #cf6d38;\n\t\tcolor: #FFF;\n\t\tmargin-bottom: 10px;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\n\t.mpp-profile-view-website {\n\t\tdisplay: block;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tbackground-color: #333;\n\t\tcolor: #FFF;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t}\n}"], "sourceRoot": ""}