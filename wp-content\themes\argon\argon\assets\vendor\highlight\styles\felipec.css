/*!
 * Theme: FelipeC
 * Author: (c) 2021 <PERSON> <<EMAIL>>
 * Website: https://github.com/felipec/vim-felipec
 *
 * Autogenerated with vim-felipec's generator.
*/

.hljs {
  color: #dddde1;
  background: #1e1e22;
}

.hljs::selection,
.hljs ::selection {
  color: #1e1e22;
  background: #bf8fef;
}

.hljs-comment,
.hljs-code,
.hljs-quote {
  color: #888896;
}

.hljs-number,
.hljs-literal,
.hljs-deletion {
  color: #ef8f8f;
}

.hljs-punctuation,
.hljs-meta,
.hljs-operator,
.hljs-subst,
.hljs-doctag,
.hljs-template-variable,
.hljs-selector-attr {
  color: #efbf8f;
}

.hljs-type {
  color: #efef8f;
}

.hljs-tag,
.hljs-title,
.hljs-selector-class,
.hljs-selector-id {
  color: #bfef8f;
}

.hljs-string,
.hljs-regexp,
.hljs-addition {
  color: #8fef8f;
}

.hljs-class,
.hljs-property {
  color: #8fefbf;
}

.hljs-name,
.hljs-selector-tag {
  color: #8fefef;
}

.hljs-keyword,
.hljs-built_in {
  color: #8fbfef;
}

.hljs-section,
.hljs-bullet {
  color: #8f8fef;
}

.hljs-selector-pseudo {
  color: #bf8fef;
}

.hljs-variable,
.hljs-params,
.hljs-attr,
.hljs-attribute {
  color: #ef8fef;
}

.hljs-symbol,
.hljs-link {
  color: #ef8fbf;
}

.hljs-strong,
.hljs-literal,
.hljs-title {
  font-weight: bold;
}

.hljs-emphasis {
  font-style: italic;
}
