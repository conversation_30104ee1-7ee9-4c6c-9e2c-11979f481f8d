{"Version": 3, "Meta": {"Duration": 6.667, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 25, "TotalSegmentCount": 120, "TotalPointCount": 319, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param3", "Segments": [0, -30, 0, 1.933, -30, 1, 2.444, -30, 2.956, 30, 3.467, 30, 1, 3.956, 30, 4.444, -30, 4.933, -30, 1, 5.356, -30, 5.778, 10, 6.2, 10, 0, 6.667, 10]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, -30, 0, 6.667, -30]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -30, 0, 6.667, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.333, 0, 0.667, -2, 1, -2, 1, 1.267, -2, 1.533, -0.247, 1.8, 4, 1, 2.033, 7.716, 2.267, 10, 2.5, 10, 1, 2.711, 10, 2.922, 3, 3.133, 3, 1, 3.378, 3, 3.622, 8, 3.867, 8, 1, 4.111, 8, 4.356, -1, 4.6, -1, 1, 5.078, -1, 5.556, 2, 6.033, 2, 0, 6.667, 2]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 1, 1, 1, 1, 1.267, 1, 1.533, -4.759, 1.8, -5, 1, 2.733, -5.845, 3.667, -6, 4.6, -6, 0, 6.667, -6]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.333, 0, 0.667, -1, 1, -1, 1, 1.267, -1, 1.533, 7, 1.8, 7, 1, 2.733, 7, 3.667, 5, 4.6, 5, 1, 5.078, 5, 5.556, 8, 6.033, 8, 0, 6.667, 8]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.333, 0, 0.667, -0.078, 1, 2, 1, 1.267, 3.662, 1.533, 11, 1.8, 11, 1, 2.733, 11, 3.667, -6, 4.6, -6, 1, 5.078, -6, 5.556, 2, 6.033, 2, 0, 6.667, 2]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 1, 1, 0.333, 1, 0.667, 0.162, 1, -6, 1, 1.267, -10.929, 1.533, -19, 1.8, -19, 1, 2.733, -19, 3.667, -18.745, 4.6, -15, 1, 5.078, -13.083, 5.556, 0, 6.033, 0, 0, 6.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.6, 0, 1.2, 19, 1.8, 19, 1, 2.733, 19, 3.667, -25, 4.6, -25, 1, 5.078, -25, 5.556, -4, 6.033, -4, 0, 6.667, -4]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.5, 1, 1, 0.544, 1, 0.589, 0, 0.633, 0, 1, 0.678, 0, 0.722, 1, 0.767, 1, 1, 0.811, 1, 0.856, 0, 0.9, 0, 1, 0.944, 0, 0.989, 1, 1.033, 1, 1, 1.333, 1, 1.633, 1, 1.933, 1, 1, 2.044, 1, 2.156, 0, 2.267, 0, 1, 2.589, 0, 2.911, 0, 3.233, 0, 1, 3.389, 0, 3.544, 1, 3.7, 1, 1, 4.078, 1, 4.456, 1, 4.833, 1, 1, 4.878, 1, 4.922, 0, 4.967, 0, 1, 5.011, 0, 5.056, 1, 5.1, 1, 1, 5.144, 1, 5.189, 0, 5.233, 0, 1, 5.278, 0, 5.322, 1, 5.367, 1, 0, 6.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.5, 1, 1, 0.544, 1, 0.589, 0, 0.633, 0, 1, 0.678, 0, 0.722, 1, 0.767, 1, 1, 0.811, 1, 0.856, 0, 0.9, 0, 1, 0.944, 0, 0.989, 1, 1.033, 1, 1, 1.333, 1, 1.633, 1, 1.933, 1, 1, 2.044, 1, 2.156, 0, 2.267, 0, 1, 2.589, 0, 2.911, 0, 3.233, 0, 1, 3.389, 0, 3.544, 1, 3.7, 1, 1, 4.078, 1, 4.456, 1, 4.833, 1, 1, 4.878, 1, 4.922, 0, 4.967, 0, 1, 5.011, 0, 5.056, 1, 5.1, 1, 1, 5.144, 1, 5.189, 0, 5.233, 0, 1, 5.278, 0, 5.322, 1, 5.367, 1, 0, 6.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 1.033, 0, 1, 1.3, 0, 1.567, -1, 1.833, -1, 0, 6.667, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.1, 0, 1.033, -0.1, 1, 1.3, -0.1, 1.567, 1, 1.833, 1, 0, 6.667, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 6.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 6.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 6.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 6.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.1, 1, 0.611, 0.1, 1.222, 0.4, 1.833, 0.4, 0, 6.667, 0.4]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.611, 0, 1.222, 0.4, 1.833, 0.4, 0, 6.667, 0.4]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.611, 0, 1.222, -0.6, 1.833, -0.6, 0, 6.667, -0.6]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.611, 0, 1.222, -0.4, 1.833, -0.4, 0, 6.667, -0.4]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0.1, 0, 1.267, 0.1, 1, 1.389, 0.1, 1.511, -0.5, 1.633, -0.5, 1, 1.656, -0.5, 1.678, -0.047, 1.7, 0, 1, 1.744, 0.095, 1.789, 0.1, 1.833, 0.1, 1, 1.9, 0.1, 1.967, -0.3, 2.033, -0.3, 1, 2.1, -0.3, 2.167, 0.4, 2.233, 0.4, 1, 2.322, 0.4, 2.411, 0, 2.5, 0, 1, 2.578, 0, 2.656, 0.1, 2.733, 0.1, 1, 2.811, 0.1, 2.889, 0, 2.967, 0, 1, 3.056, 0, 3.144, 0.2, 3.233, 0.2, 1, 3.311, 0.2, 3.389, 0.196, 3.467, 0.1, 1, 3.578, -0.038, 3.689, -0.2, 3.8, -0.2, 1, 4.389, -0.2, 4.978, 0.3, 5.567, 0.3, 0, 6.667, 0.3]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 1.267, 0, 1, 1.389, 0, 1.511, 0.7, 1.633, 0.7, 1, 1.656, 0.7, 1.678, 0, 1.7, 0, 1, 1.744, 0, 1.789, 0.7, 1.833, 0.7, 1, 1.9, 0.7, 1.967, 0.707, 2.033, 0.6, 1, 2.1, 0.493, 2.167, 0.2, 2.233, 0.2, 1, 2.322, 0.2, 2.411, 0.6, 2.5, 0.6, 1, 2.578, 0.6, 2.656, 0.4, 2.733, 0.4, 1, 2.811, 0.4, 2.889, 0.6, 2.967, 0.6, 1, 3.056, 0.6, 3.144, 0.4, 3.233, 0.4, 1, 3.311, 0.4, 3.389, 0.5, 3.467, 0.5, 1, 3.578, 0.5, 3.689, 0, 3.8, 0, 1, 4.389, 0, 4.978, 0, 5.567, 0, 0, 6.667, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 1.067, 0, 1, 1.333, 0, 1.6, 1, 1.867, 1, 0, 6.667, 1]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 1, 0.322, 0, 0.644, -16, 0.967, -16, 1, 1.3, -16, 1.633, 28, 1.967, 28, 0, 6.667, 28]}]}