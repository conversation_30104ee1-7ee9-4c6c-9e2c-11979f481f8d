/*!
  Theme: <PERSON><PERSON><PERSON>
  Author: <PERSON> <<EMAIL>>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme ros-pine-dawn
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #faf4ed  Default Background
base01  #fffaf3  Lighter Background (Used for status bars, line number and folding marks)
base02  #f2e9de  Selection Background
base03  #9893a5  Comments, Invisibles, Line Highlighting
base04  #6e6a86  Dark Foreground (Used for status bars)
base05  #575279  Default Foreground, Caret, Delimiters, Operators
base06  #555169  Light Foreground (Not often used)
base07  #26233a  Light Background (Not often used)
base08  #1f1d2e  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #b4637a  Integers, <PERSON><PERSON><PERSON>, Constants, XML Attributes, Markup Link Url
base0A  #ea9d34  Classes, Markup Bold, Search Text Background
base0B  #d7827e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #286983  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #56949f  Functions, Methods, Attribute IDs, Headings
base0E  #907aa9  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #c5c3ce  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #575279;
  background: #faf4ed;
}

.hljs::selection,
.hljs ::selection {
  background-color: #f2e9de;
  color: #575279;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #9893a5 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #9893a5;
}

/* base04 - #6e6a86 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #6e6a86;
}

/* base05 - #575279 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #575279;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #1f1d2e;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #b4637a;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #ea9d34;
}

.hljs-strong {
  font-weight:bold;
  color: #ea9d34;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #d7827e;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #286983;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #56949f;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #907aa9;
}
.hljs-emphasis {
  color: #907aa9;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #c5c3ce;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
