msgid ""
msgstr ""
"Project-Id-Version: argon\n"
"POT-Creation-Date: 2022-02-14 19:14+0800\n"
"PO-Revision-Date: 2022-02-14 19:14+0800\n"
"Last-Translator: \n"
"Language-Team: solstice23\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0.1\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_x:1,2c;_ex:1,2c;_n;_nx\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: assets\n"
"X-Poedit-SearchPathExcluded-1: languages\n"
"X-Poedit-SearchPathExcluded-2: theme-update-checker\n"
"X-Poedit-SearchPathExcluded-3: argontheme.js\n"
"X-Poedit-SearchPathExcluded-4: gut<PERSON>\n"

#: 404.php:11
msgid "404 - 找不到页面"
msgstr "404 - Page Not Found"

#: 404.php:101
msgid "这个页面不见了"
msgstr "Please check if the URL is correct."

#: 404.php:105 template-parts/content-none-search.php:13
#: template-parts/content-none-tag.php:9
msgid "返回上一页"
msgstr "Back"

#: 404.php:109
msgid "回到首页"
msgstr "Home"

#: archive.php:14
msgid "篇文章"
msgstr "Posts"

#: comments.php:12 header.php:524 settings.php:1250
#: template-parts/shuoshuo-operations.php:10
msgid "评论"
msgstr "Comments"

#: comments.php:51
msgid "暂无评论"
msgstr "No Comments"

#: comments.php:59
msgid "本文评论已关闭"
msgstr "Commenting is disabled"

#: comments.php:76 settings.php:1290
msgid "发送评论"
msgstr "Send Comment"

#: comments.php:77
msgid "编辑评论"
msgstr "Edit Comment"

#: comments.php:80
msgid "正在回复"
msgstr "Replying"

#: comments.php:80
msgid " 的评论"
msgstr "'s comment"

#: comments.php:82
msgid "取消回复"
msgstr "Cancel"

#: comments.php:87
msgid "评论内容"
msgstr "Your comment..."

#: comments.php:126
msgid "昵称"
msgstr "Name"

#: comments.php:136
msgid "邮箱"
msgstr "Email"

#: comments.php:136
msgid " / QQ 号"
msgstr "/ QQ Number"

#: comments.php:146
msgid "验证码"
msgstr "CAPTCHA"

#: comments.php:166
msgid "获取验证码失败"
msgstr "Failed to get CAPTCHA"

#: comments.php:182
msgid "网站"
msgstr "Website"

#: comments.php:189
msgid "展开附加字段"
msgstr "Show Extra Field"

#: comments.php:189
msgid "折叠附加字段"
msgstr "Hide Extra Field"

#: comments.php:202
msgid "评论仅发送者和博主可见"
msgstr "Only sender and blog owner can see the comment"

#: comments.php:204 functions.php:1065
msgid "悄悄话"
msgstr "Private Comment"

#: comments.php:208
msgid "有回复时邮件通知我"
msgstr "Email me when comment is replied"

#: comments.php:210
msgid "邮件提醒"
msgstr "Email Notification"

#: comments.php:216
msgid "发送"
msgstr "Send"

#: comments.php:217 functions.php:1100
msgid "编辑"
msgstr "Edit"

#: comments.php:221
msgid "取消"
msgstr "Cancel"

#: comments.php:224
msgid "表情"
msgstr "Emotions"

#: emotions.php:4
msgid "颜文字"
msgstr "ASCII Arts"

#: functions.php:3
msgid "Argon 主题不支持 Wordpress 4.4 以下版本，请更新 Wordpress"
msgstr ""
"Argon theme does not support Wordpress version below 4.4, please update "
"Wordpress"

#: functions.php:157
msgid "左侧栏小工具"
msgstr "Left sidebar widget"

#: functions.php:159
msgid "左侧栏小工具 (如果设置会在侧栏增加一个 Tab)"
msgstr "Left sidebar widget (if set, a Tab will be added to the sidebar)"

#: functions.php:168
msgid "右侧栏小工具"
msgstr "Right sidebar widget"

#: functions.php:170
msgid "右侧栏小工具 (在 \"Argon 主题选项\" 中选择 \"三栏布局\" 才会显示)"
msgstr ""
"Right sidebar widget (only displayed when \"three-column layout\" is "
"selected in \"Argon Theme Options\")"

#: functions.php:179 functions.php:181
msgid "站点概览额外内容"
msgstr "Extra content of site overview"

#: functions.php:415
msgid "这是一个加密页面，需要密码来查看"
msgstr "This page requires a password to view"

#: functions.php:574
msgid "几秒读完"
msgstr "Few seconds"

#: functions.php:577
msgid "1 分钟内"
msgstr "A minute"

#: functions.php:580
msgid "分钟"
msgstr "minutes"

#: functions.php:582
msgid "小时"
msgstr "hour"

#: functions.php:607 functions.php:1062
#: template-parts/content-shuoshuo-details.php:16
#: template-parts/content-shuoshuo-preview.php:24
#: template-parts/content-shuoshuo.php:16
msgctxt "pinned"
msgid "置顶"
msgstr "Pinned"

#: functions.php:613
msgid "需要密码"
msgstr "Password Required"

#: functions.php:619 functions.php:627
msgid "发布于"
msgstr "Posted at"

#: functions.php:619 functions.php:627
msgid "编辑于"
msgstr "Edited at"

#: functions.php:673
#, php-format
msgid "包含 %d 行代码"
msgstr "Includes %d lines of code"

#: functions.php:677
msgid "字"
msgstr "Words"

#: functions.php:826
msgid "该评论为悄悄话"
msgstr "This comment is private"

#: functions.php:891
msgid "最初版本"
msgstr "First Edition"

#: functions.php:935
msgid "查看图片"
msgstr "Show Image"

#: functions.php:1004 unsubscribe-comment-mailnotice.php:12
msgid "评论不存在"
msgstr "Comment not found."

#: functions.php:1012
msgid "该评论已被赞过"
msgstr "This comment has been voted"

#: functions.php:1021 functions.php:1948
msgid "点赞成功"
msgstr "Vote success"

#: functions.php:1059 functions.php:1117
msgid "博主"
msgstr "Owner"

#: functions.php:1068 functions.php:1120
msgid "待审核"
msgstr "Pending"

#: functions.php:1077
msgid "已编辑"
msgstr "Edited"

#: functions.php:1081
msgid "前"
msgstr " ago"

#: functions.php:1094
msgid "取消置顶"
msgstr "Unpin"

#: functions.php:1096
msgctxt "to pin"
msgid "置顶"
msgstr "Pin"

#: functions.php:1102
msgid "回复"
msgstr "Reply"

#: functions.php:1226
msgid "验证码错误"
msgstr "CAPTCHA Wrong"

#: functions.php:1273
msgid "不能回复其他人的悄悄话评论"
msgstr "Cannot reply to other's private comments"

#: functions.php:1403 functions.php:1404
msgid "您在"
msgstr "Your Comment on"

#: functions.php:1403 functions.php:1404
msgid "的评论有了新的回复"
msgstr "Has a New Reply"

#: functions.php:1419
msgid "回复了你"
msgstr "Replys"

#: functions.php:1430
msgid "前往查看"
msgstr "View"

#: functions.php:1447
msgid "退订该评论的邮件提醒"
msgstr "Unsubscribe"

#: functions.php:1530
msgid "博主关闭了编辑评论功能"
msgstr "Comment editing unavailable"

#: functions.php:1539
msgid "您不是这条评论的作者或 Token 已过期"
msgstr "You are not the author of this comment or the token has expired"

#: functions.php:1545
msgid "新的评论为空"
msgstr "New comment is empty"

#: functions.php:1571
msgid "编辑评论成功"
msgstr "Comment Edited"

#: functions.php:1579
msgid "编辑评论失败，可能原因: 与原评论相同"
msgstr "Failed, possible reason: same as old comment"

#: functions.php:1591
msgid "博主关闭了评论置顶功能"
msgstr "Comment pinning unavailable"

#: functions.php:1597
msgid "您没有权限进行此操作"
msgstr "You have no permission to do this"

#: functions.php:1606
msgid "评论已经是置顶状态"
msgstr "The comment is already pinned"

#: functions.php:1606
msgid "评论已经是取消置顶状态"
msgstr "The comment is already unpinned"

#: functions.php:1612
msgid "不能置顶子评论"
msgstr "Cannot pin a sub-comment"

#: functions.php:1618
msgid "不能置顶悄悄话"
msgstr "Cannot pin a private comment"

#: functions.php:1624
msgid "置顶评论成功"
msgstr "Pinned successfully"

#: functions.php:1624
msgid "取消置顶成功"
msgstr "Unpinned successfully"

#: functions.php:1939
msgid "该说说已被赞过"
msgstr "This essay has been voted"

#: functions.php:1957
msgid ""
"警告：你可能修改了 Argon 主题页脚的版权声明，Argon 主题要求你至少保留主题的 "
"Github 链接或主题的发布文章链接。"
msgstr ""
"Alert: The copyright in the footer may have been modified. You should "
"maintain the GitHub Repo link of the theme."

#: functions.php:2106 settings.php:668
msgid "显示字数和预计阅读时间"
msgstr "Display word count and estimated reading time"

#: functions.php:2109 functions.php:2123 functions.php:2131
msgid "跟随全局设置"
msgstr "Follow general"

#: functions.php:2110 settings.php:543 settings.php:553 settings.php:564
#: settings.php:629 settings.php:673 settings.php:740 settings.php:751
#: settings.php:905 settings.php:966 settings.php:1447 settings.php:1463
#: settings.php:1573
msgid "不显示"
msgstr "Hide"

#: functions.php:2112
msgid "是否显示字数和预计阅读时间 Meta 信息"
msgstr "Show meta of word count and estimated reading time"

#: functions.php:2113
msgid "Meta 中隐藏发布时间和分类"
msgstr "Hide post time and categories in meta"

#: functions.php:2116 settings.php:1307
msgid "不隐藏"
msgstr "Show"

#: functions.php:2117 settings.php:386 settings.php:1308
msgid "隐藏"
msgstr "Hide"

#: functions.php:2119
msgid ""
"适合特定的页面，例如友链页面。开启后文章 Meta 的第一行只显示阅读数和评论数。"
msgstr ""
"For special pages, like friend links page. If this option is on, the first "
"line of the meta will only show the number of reading and comments."

#: functions.php:2120
msgid "使用文章中第一张图作为头图"
msgstr "Use the first image of article as the thumbnail by default"

#: functions.php:2124
msgid "使用"
msgstr "Use"

#: functions.php:2125 settings.php:283 settings.php:1102
msgid "不使用"
msgstr "Do not use"

#: functions.php:2127
msgid "显示文章过时信息"
msgstr "Show Article Outdated Information"

#: functions.php:2132
msgid "一直显示"
msgstr "Always"

#: functions.php:2133
msgid "永不显示"
msgstr "Never"

#: functions.php:2135
msgid "应用"
msgstr "Apply"

#: functions.php:2137
msgid "单独控制该文章的过时信息显示。"
msgstr ""
"Separately control the display of outdated information for this article."

#: functions.php:2138 settings.php:765 settings.php:767
msgid "文末附加内容"
msgstr "Additional Content After Post"

#: functions.php:2141
msgid ""
"给该文章设置单独的文末附加内容，留空则跟随全局，设为 <code>--none--</code> 则"
"不显示。"
msgstr ""
"Set a additional content at the end of the article, leave it blank to follow "
"the global setting, set it to <code>--none--</code> to not display it."

#: functions.php:2142
msgid "自定义 CSS"
msgstr "Custom CSS"

#: functions.php:2145
msgid "给该文章添加单独的 CSS"
msgstr "Custom CSS for this page."

#: functions.php:2181 functions.php:2190
msgid "应用失败"
msgstr "failed"

#: functions.php:2185
msgid "应用成功"
msgstr "Successfully applied"

#: functions.php:2198
msgid "文章设置"
msgstr "Article Settings"

#: functions.php:2961 settings.php:727
msgid "参考"
msgstr "References"

#: functions.php:3024 settings.php:40
msgid "Argon 主题设置"
msgstr "Argon Theme Options"

#: functions.php:3024
msgid "Argon 主题选项"
msgstr "Argon Options"

#: functions.php:3032
msgid "顶部导航"
msgstr "Toolbar Menu"

#: functions.php:3033
msgid "左侧栏菜单"
msgstr "Left Sidebar Menu"

#: functions.php:3034
msgid "左侧栏作者个人链接"
msgstr "Left Sidebar Author Links"

#: functions.php:3035
msgid "左侧栏友情链接"
msgstr "Left Sidebar Friend Links"

#: functions.php:3046 functions.php:3047 functions.php:3057 shuoshuo.php:13
msgid "说说"
msgstr "Essays"

#: functions.php:3048 functions.php:3049
msgid "发表说说"
msgstr "Post a essay"

#: functions.php:3050
msgid "编辑说说"
msgstr "Edit essay"

#: functions.php:3051
msgid "新说说"
msgstr "Add new essay"

#: functions.php:3052
msgid "查看说说"
msgstr "View essay"

#: functions.php:3053
msgid "搜索说说"
msgstr "Search essays"

#: functions.php:3054
msgid "暂无说说"
msgstr "No essays"

#: functions.php:3055
msgid "没有已遗弃的说说"
msgstr "No trashed essays"

#: header.php:374 searchform.php:7 sidebar.php:67
msgid "搜索什么..."
msgstr "Search..."

#: header.php:392 searchform.php:11 sidebar.php:66
msgid "搜索"
msgstr "Search"

#: header.php:517
msgid "移至左侧"
msgstr "Move To Left"

#: header.php:517
msgid "移至右侧"
msgstr "Move To Right"

#: header.php:521
msgid "回到顶部"
msgstr "Back To Top"

#: header.php:527 header.php:536 settings.php:122
msgid "夜间模式"
msgstr "Dark Mode"

#: header.php:527 header.php:536
msgid "暗黑模式"
msgstr "Black Mode"

#: header.php:527
msgid "日间模式"
msgstr "Light Mode"

#: header.php:530
msgid "设置"
msgstr "Settings"

#: header.php:536
msgid "切换到夜间模式"
msgstr "Switch To Dark Mode"

#: header.php:536
msgid "切换到暗黑模式"
msgstr "Switch To Black Mode"

#: header.php:543 settings.php:259
msgid "字体"
msgstr "Font"

#: header.php:549
msgid "阴影"
msgstr "Shadow"

#: header.php:551 settings.php:162
msgid "浅阴影"
msgstr "Small"

#: header.php:551 settings.php:166
msgid "深阴影"
msgstr "Large"

#: header.php:555
msgid "滤镜"
msgstr "Filter"

#: header.php:557 settings.php:111 settings.php:355 settings.php:396
#: settings.php:779 settings.php:1425 settings.php:1608
msgid "关闭"
msgstr "Disabled"

#: header.php:558
msgid "日落"
msgstr "Sunset"

#: header.php:559
msgid "暗化"
msgstr "Brightless"

#: header.php:560
msgid "灰度"
msgstr "Greyscale"

#: header.php:564
msgid "恢复默认"
msgstr "Set To Default"

#: header.php:564
msgid "圆角"
msgstr "Radius"

#: header.php:571 settings.php:48
msgid "主题色"
msgstr "Theme Color"

#: header.php:576
msgid "阅读进度"
msgstr "Reading Progress"

#: msgboard.php:42
msgid "留言板"
msgstr "Message Board"

#: msgboard.php:53
msgid "发送留言"
msgstr "Send Message"

#: search.php:8
msgid "的搜索结果"
msgstr "Search Results"

#: search.php:36
msgid "个结果"
msgstr "Results"

#: settings.php:41
msgid "按下"
msgstr "Press"

#: settings.php:41
msgid "或在右侧目录中来查找设置"
msgstr "or find options in right catalog"

#: settings.php:47
msgid "全局"
msgstr "General"

#: settings.php:50
msgid "主题颜色"
msgstr "Theme Color"

#: settings.php:54
msgid "选择预置颜色 或"
msgstr "Choose preset colors or"

#: settings.php:54
msgid "自定义色值"
msgstr "custom color"

#: settings.php:55
msgid "预置颜色："
msgstr "Presets"

#: settings.php:57
msgid "默认"
msgstr "Default"

#: settings.php:58
msgid "粉"
msgstr "Pink"

#: settings.php:59
msgid "水鸭青"
msgstr "Teal"

#: settings.php:60
msgid "蓝灰"
msgstr "Bluegrey"

#: settings.php:61
msgid "天蓝"
msgstr "Blue"

#: settings.php:62
msgid "靛蓝"
msgstr "Indigo"

#: settings.php:63
msgid "橙"
msgstr "Orange"

#: settings.php:64
msgid "绿"
msgstr "Green"

#: settings.php:65
msgid "红"
msgstr "Red"

#: settings.php:66
msgid "紫"
msgstr "Purple"

#: settings.php:67
msgid "黑"
msgstr "Black"

#: settings.php:68
msgid "棕"
msgstr "Brown"

#: settings.php:70
msgid "主题色与 \"Banner 渐变背景样式\" 选项搭配使用效果更佳"
msgstr ""
"Use this option with \"Banner gradient background style\" for better effects"

#: settings.php:99
msgid "允许用户自定义主题色（位于博客浮动操作栏设置菜单中）"
msgstr "Allow custom theme color (at blog floating settings menu)"

#: settings.php:106
msgid "沉浸式主题色"
msgstr "Immersive theme color"

#: settings.php:110 settings.php:356 settings.php:397 settings.php:1426
#: settings.php:1609
msgid "开启"
msgstr "Enabled"

#: settings.php:113
msgid ""
"开启后，主题色将会全局沉浸。</br>页面背景、卡片及页面上的其它元素会变为沉浸式"
"主题色（气氛色）。类似 Material You。"
msgstr ""
"If turn it on, the theme color will be globally immersed. </br>The "
"background, cards, and other elements on the page change to the immersive "
"theme color. Just like Material You."

#: settings.php:124
msgid "夜间模式切换方案"
msgstr "Darkmode Toggle Plan"

#: settings.php:128
msgid "默认使用日间模式"
msgstr "Lightmode as default"

#: settings.php:129
msgid "默认使用夜间模式"
msgstr "Darkmode as default"

#: settings.php:130
msgid "跟随系统夜间模式"
msgstr "Follow system"

#: settings.php:131
msgid "根据时间切换夜间模式 (22:00 ~ 7:00)"
msgstr "Toggle by time (22.p.m to 7.a.m)"

#: settings.php:133
msgid "Argon 主题会根据这里的选项来决定是否默认使用夜间模式。"
msgstr ""
"Theme will decide whether to use darkmode by default according to the "
"options here."

#: settings.php:133
msgid "用户也可以手动切换夜间模式，用户的设置将保留到标签页关闭为止。"
msgstr ""
"Users can also manually switch the darkmode, and the user's settings will be "
"retained until the tab is closed."

#: settings.php:137
msgid "夜间模式颜色方案"
msgstr "Darkmode Color Scheme"

#: settings.php:141
msgid "灰黑"
msgstr "Dark Grey"

#: settings.php:142
msgid "暗黑 (AMOLED Black)"
msgstr "Black (For AMOLED)"

#: settings.php:144
msgid "夜间模式默认的配色方案。"
msgstr "The default color scheme of darkmode."

#: settings.php:147
msgid "卡片"
msgstr "Cards"

#: settings.php:149
msgid "卡片圆角大小"
msgstr "Card radius"

#: settings.php:152
msgid "卡片的圆角大小，默认为"
msgstr "The default radius of card, default is"

#: settings.php:152
msgid "。建议设置为"
msgstr ". Recommend :"

#: settings.php:156
msgid "卡片阴影"
msgstr "Card Shadow"

#: settings.php:169
msgid "卡片默认阴影大小。"
msgstr "Default shadow size of cards."

#: settings.php:172 settings.php:243 settings.php:249 settings.php:255
msgid "布局"
msgstr "Layouts"

#: settings.php:174
msgid "页面布局"
msgstr "Page Layout"

#: settings.php:181
msgid "双栏"
msgstr "Double-Column"

#: settings.php:187
msgid "单栏"
msgstr "Single-Column"

#: settings.php:193
msgid "三栏"
msgstr "Triple-Column"

#: settings.php:199
msgid "双栏(反转)"
msgstr "Double-Column (Reverse)"

#: settings.php:201
msgid "使用单栏时，关于左侧栏的设置将失效。"
msgstr ""
"If using single column mod, the settings about the left sidebar will take no "
"effect."

#: settings.php:201
msgid "使用三栏时，请前往 \"外观-小工具\" 设置页面配置右侧栏内容。"
msgstr ""
"If using three columns, please go to the \"Appearance - Widgets\" setting "
"page to configure the content of the right column."

#: settings.php:205
msgid "文章列表布局"
msgstr "Article list layout"

#: settings.php:212
msgid "单列"
msgstr "Single Column"

#: settings.php:218
msgid "瀑布流 (2 列)"
msgstr "Waterfall (2 Columns)"

#: settings.php:224
msgid "瀑布流 (3 列)"
msgstr "Waterfall (3 Columns)"

#: settings.php:230
msgid "瀑布流 (列数自适应)"
msgstr "Waterfall (Adaptive)"

#: settings.php:232
msgid "列数自适应的瀑布流会根据可视区宽度自动调整瀑布流列数。"
msgstr ""
"The adaptive waterfall will automatically adjust the count of columns "
"according to the width of the view."

#: settings.php:232
msgid "建议只有使用单栏页面布局时才开启 3 列瀑布流。"
msgstr ""
"It is recommended to enable the 3-column waterfall flow only when using "
"single-column page layout."

#: settings.php:232
msgid "所有瀑布流布局都会在屏幕宽度过小时变为单列布局。"
msgstr ""
"All waterfall layouts will change to single-column when the screen is too "
"narrow."

#: settings.php:236
msgid "文章列表卡片布局"
msgstr "Article list card layout"

#: settings.php:261
msgid "默认字体"
msgstr "Default Font"

#: settings.php:274
msgid "默认使用无衬线字体/衬线字体。"
msgstr "Default font style"

#: settings.php:288
msgid ""
"选择主题资源文件的引用地址。使用 CDN 可以加速资源文件的访问并减少服务器压力。"
msgstr ""
"The address of the theme resource file. Using CDN can speed up access to "
"resource files and reduce server pressure."

#: settings.php:291
msgid "子目录"
msgstr "Sub Path"

#: settings.php:293
msgid "Wordpress 安装目录"
msgstr "WordPress Installation Directory"

#: settings.php:296
msgid "如果 Wordpress 安装在子目录中，请在此填写子目录地址（例如"
msgstr ""
"If Wordpress is installed in a subdirectory, please fill in the subdirectory "
"address here (for example"

#: settings.php:296
msgid "），注意前后各有一个斜杠。默认为"
msgstr "), there is a slash before and after each. The default is"

#: settings.php:296
msgid "。"
msgstr "."

#: settings.php:296
msgid "如果不清楚该选项的用处，请保持默认。"
msgstr "If you don't know about this option, keep the default."

#: settings.php:299 settings.php:301
msgid "日期格式"
msgstr "Date Format"

#: settings.php:312
msgid "顶栏"
msgstr "Toolbar"

#: settings.php:313
msgid "状态"
msgstr "Status"

#: settings.php:315
msgid "顶栏显示状态"
msgstr "Toolbar Display Status"

#: settings.php:319
msgid "始终固定悬浮"
msgstr "Fixed"

#: settings.php:320
msgid "滚动时自动折叠"
msgstr "Collapse when scrolling"

#: settings.php:321
msgid "不固定"
msgstr "Absolute"

#: settings.php:323
msgid "始终固定悬浮: 永远固定悬浮在页面最上方"
msgstr "Fixed: Always show on the top of the page"

#: settings.php:323
msgid "滚动时自动折叠: 在页面向下滚动时隐藏顶栏，向上滚动时显示顶栏"
msgstr ""
"Collapse when scrolling: hide the top bar when the page is scrolling down, "
"and show the top bar when scrolling up"

#: settings.php:323
msgid "不固定: 只有在滚动到页面最顶端时才显示顶栏"
msgstr ""
"Absolute: The top bar is only displayed when scrolled to the top of the page"

#: settings.php:326 settings.php:795
msgid "标题"
msgstr "Title"

#: settings.php:328
msgid "顶栏标题"
msgstr "Toolbar Title"

#: settings.php:331
msgid "留空则显示博客名称，输入 <code>--hidden--</code> 可以隐藏标题"
msgstr ""
"Leave it blank to display the blog name, enter <code>--hidden--</code> to "
"hide the title"

#: settings.php:334
msgid "顶栏图标"
msgstr "Toolbar Icon"

#: settings.php:336
msgid "图标地址"
msgstr "Icon URL"

#: settings.php:339
msgid "图片地址，留空则不显示"
msgstr "The URL of the icon, if this option is empty, it will not show."

#: settings.php:343
msgid "图标链接"
msgstr "Icon Link"

#: settings.php:346
msgid "点击图标后会跳转到的链接，留空则不跳转"
msgstr ""
"The link that will jump to when clicking the icon, if this option is empty, "
"it will not jump when clicking."

#: settings.php:349 settings.php:377 settings.php:898
msgid "外观"
msgstr "Appearance"

#: settings.php:351
msgid "顶栏毛玻璃效果"
msgstr "Toolbar Blur Effect"

#: settings.php:358
msgid "开启会带来微小的性能损失。"
msgstr "There will be a small performance loss if turned it on."

#: settings.php:361
msgid "顶部 Banner (封面)"
msgstr "Banner"

#: settings.php:362
msgid "内容"
msgstr "Content"

#: settings.php:364
msgid "Banner 标题"
msgstr "Banner Title"

#: settings.php:367 settings.php:496
msgid "留空则显示博客名称"
msgstr "If this option is empty, theme will show the blog name."

#: settings.php:371
msgid "Banner 副标题"
msgstr "Banner Subtitle"

#: settings.php:374
msgid "显示在 Banner 标题下，留空则不显示"
msgstr ""
"Show below the banner title, if this option is empty, it will not show."

#: settings.php:379
msgid "Banner 显示状态"
msgstr "Banner Size"

#: settings.php:383
msgid "完整"
msgstr "Full"

#: settings.php:384
msgid "迷你"
msgstr "Mini"

#: settings.php:385
msgid "全屏"
msgstr "Full screen"

#: settings.php:388
msgid "完整: Banner 高度占用半屏"
msgstr "Full: Banner takes up half the screen height"

#: settings.php:388
msgid "迷你: 减小 Banner 的内边距"
msgstr "Mini: Reduce the padding of the banner"

#: settings.php:388
msgid "全屏: Banner 占用全屏作为封面（仅在首页生效）"
msgstr ""
"Full screen: Banner occupies the full screen as the cover (only active on "
"the homepage)"

#: settings.php:388
msgid "隐藏: 完全隐藏 Banner"
msgstr "Hide: Hide Banner"

#: settings.php:392
msgid "Banner 透明化"
msgstr "Banner Transparent"

#: settings.php:402
msgid "在顶栏添加浅色遮罩，Banner 标题添加阴影（当背景过亮影响文字阅读时勾选）"
msgstr ""
"Add a mask to the top bar, and add a shadow to the banner title (check when "
"the background is too bright and affect text reading)"

#: settings.php:405
msgid ""
"Banner 透明化可以使博客背景沉浸。建议在设置背景时开启此选项。该选项仅会在设置"
"页面背景时生效。"
msgstr ""
"Banner transparent option can immerse the blog background. It is recommended "
"to enable this option when setting the background. This option will only "
"take effect when setting the page background."

#: settings.php:405
msgid "开启后，Banner 背景图和渐变背景选项将失效。"
msgstr "When turned on, the Banner background options will be disabled."

#: settings.php:409
msgid "Banner 背景图 (地址)"
msgstr "Banner Background URL"

#: settings.php:412
msgid "需带上 http(s) ，留空则显示默认背景"
msgstr "With http(s) prefix, leave it blank to show the default background."

#: settings.php:412 settings.php:503
msgid "输入"
msgstr "Input"

#: settings.php:412
msgid "调用必应每日一图"
msgstr "to use everyday background of Bing."

#: settings.php:416
msgid "Banner 渐变背景样式"
msgstr "Banner gradient background style"

#: settings.php:420 settings.php:421 settings.php:422 settings.php:423
#: settings.php:424 settings.php:425 settings.php:426 settings.php:435
#: settings.php:436 settings.php:437 settings.php:438 settings.php:439
#: settings.php:440 settings.php:441
msgid "样式"
msgstr "Style"

#: settings.php:430
msgid "隐藏背景半透明圆"
msgstr "Hide background semi-transparent circle"

#: settings.php:432
msgid "如果设置了背景图则不生效"
msgstr "If the background image is set, it will not take effect"

#: settings.php:433
msgid "样式预览 (推荐选择前三个样式)"
msgstr "Style preview (recommended to choose the first three styles)"

#: settings.php:450 settings.php:1213
msgid "动画"
msgstr "Animation"

#: settings.php:452
msgid "Banner 标题打字动画"
msgstr "Banner Title Typing Animation"

#: settings.php:456 settings.php:932 settings.php:975 settings.php:1007
#: settings.php:1233 settings.php:1504 settings.php:1553
msgid "不启用"
msgstr "Disabled"

#: settings.php:457 settings.php:718 settings.php:933 settings.php:976
#: settings.php:1061 settings.php:1113 settings.php:1137 settings.php:1234
#: settings.php:1296 settings.php:1330 settings.php:1437 settings.php:1492
#: settings.php:1503 settings.php:1554
msgid "启用"
msgstr "Enabled"

#: settings.php:459
msgid "启用后 Banner 标题会以打字的形式出现。"
msgstr "When enabled, the Banner title will appear as typing effect."

#: settings.php:463
msgid "Banner 标题打字动画时长"
msgstr "Banner Title Typing Animation Speed"

#: settings.php:465
msgid "ms/字"
msgstr "ms/letter"

#: settings.php:469 settings.php:471
msgid "页面背景"
msgstr "Page Background"

#: settings.php:474
msgid ""
"页面背景的地址，需带上 http(s)。留空则不设置页面背景。如果设置了背景，推荐开"
"启 Banner 透明化。"
msgstr ""
"The URL of the page background, with http(s) prefix. Leave it blank to not "
"set the page background. If the background is set. If background is set, it "
"is recommended to turn on Banner Transparent."

#: settings.php:478
msgid "页面背景（夜间模式时）"
msgstr "Page Background (Darkmode)"

#: settings.php:481
msgid ""
"夜间模式时页面背景的地址，需带上 http(s)。设置后日间模式和夜间模式会使用不同"
"的背景。留空则跟随日间模式背景。该选项仅在设置了日间模式背景时生效。"
msgstr ""
"The URL of the page background at darkmode, with http(s) prefix. If set, day "
"mode and night mode will use different backgrounds. Leave blank to follow "
"the background of day mode. This option only takes effect when the day mode "
"background is set."

#: settings.php:485
msgid "背景不透明度"
msgstr "Background Opacity"

#: settings.php:488
msgid "0 ~ 1 的小数，越小透明度越高，默认为 1 不透明"
msgstr ""
"Decimal from 0 to 1, the smaller the higher the transparency, the default is "
"1."

#: settings.php:491
msgid "左侧栏"
msgstr "Left Sidebar"

#: settings.php:493
msgid "左侧栏标题"
msgstr "Left Sidebar Title"

#: settings.php:500
msgid "左侧栏子标题（格言）"
msgstr "Left Sidebar Subtitle"

#: settings.php:503 settings.php:524
msgid "留空则不显示"
msgstr "Leave blank to not show."

#: settings.php:503
msgid "调用一言 API"
msgstr "to use Hitokoto API."

#: settings.php:507
msgid "左侧栏作者名称"
msgstr "Left Sidebar Author Name"

#: settings.php:510
msgid "留空则显示博客名"
msgstr "If this option is empty, theme will show the blog name."

#: settings.php:514
msgid "左侧栏作者头像地址"
msgstr "Left Sidebar Author Avatar URL"

#: settings.php:517
msgid "需带上 http(s) 开头"
msgstr "With http(s) prefix"

#: settings.php:521
msgid "左侧栏作者简介"
msgstr "Left Sidebar Author Description"

#: settings.php:527
msgid "博客公告"
msgstr "Blog Announcement"

#: settings.php:529
msgid "公告内容"
msgstr "Announcement Content"

#: settings.php:532
msgid "显示在左侧栏顶部，留空则不显示，支持 HTML 标签"
msgstr ""
"Display at the top of the left sidebar, leave blank to not display, HTML "
"tags are supported."

#: settings.php:535
msgid "浮动操作按钮"
msgstr "Float Action Buttons"

#: settings.php:536
msgid "浮动操作按钮位于页面右下角（或左下角）"
msgstr ""
"Floating action buttons is placed in the right bottom corner (or lower left "
"corner) of the page."

#: settings.php:538
msgid "显示设置按钮"
msgstr "Show Settings Button"

#: settings.php:542 settings.php:554 settings.php:565 settings.php:625
#: settings.php:672 settings.php:752 settings.php:904 settings.php:965
#: settings.php:1462 settings.php:1574
msgid "显示"
msgstr "Show"

#: settings.php:545
msgid ""
"是否在浮动操作按钮栏中显示设置按钮。点击设置按钮可以唤出设置菜单修改夜间模式/"
"字体/滤镜等外观选项。"
msgstr ""
"Whether to display the setting button in the floating action buttons bar. "
"Click the settings button to call up the settings menu to modify appearance "
"options such as darkmode/font/filter."

#: settings.php:549
msgid "显示夜间模式切换按钮"
msgstr "Display Darkmode Switch Button"

#: settings.php:556
msgid ""
"如果开启了设置按钮显示，建议关闭此选项。（夜间模式选项在设置菜单中已经存在）"
msgstr ""
"If the setting button display option is turned on, it is recommended to turn "
"off this option. (The darkmode option already exists in the settings menu)"

#: settings.php:560
msgid "显示跳转到评论按钮"
msgstr "Show Jump To Comment Area Button"

#: settings.php:567
msgid "仅在允许评论的文章中显示"
msgstr "Only show in articles that allow comments"

#: settings.php:572
msgid "网站描述 (Description Meta 标签)"
msgstr "SEO Description Meta"

#: settings.php:575
msgid "设置针对搜索引擎的 Description Meta 标签内容。"
msgstr "Set the description meta content for the search engine spiders."

#: settings.php:575
msgid ""
"在文章中，Argon 会自动根据文章内容生成描述。在其他页面中，Argon 将使用这里设"
"置的内容。如不填，Argon 将不会在其他页面输出 Description Meta 标签。"
msgstr ""
"In the article, Argon will automatically generate a description based on the "
"content of the article. In other pages, Argon will use the content set here. "
"If you leave it blank, Argon will not output Description Meta tags on other "
"pages."

#: settings.php:579
msgid "搜索引擎关键词（Keywords Meta 标签）"
msgstr "SEO Keywords Meta tag"

#: settings.php:582
msgid ""
"设置针对搜索引擎使用的关键词（Keywords Meta 标签内容）。用英文逗号隔开。不设"
"置则不输出该 Meta 标签。"
msgstr ""
"Set keyword meta tags for search engine spiders. Separate with commas. If "
"not set, the meta tags will not be output."

#: settings.php:585 sidebar.php:134
msgid "文章"
msgstr "Articles"

#: settings.php:586
msgid "文章 Meta 信息"
msgstr "Article Meta Information"

#: settings.php:588
msgid "第一行"
msgstr "First Line"

#: settings.php:623
msgid "拖动来自定义文章 Meta 信息的显示和顺序"
msgstr ""
"Drag to customize the display and order of the meta information of the "
"article."

#: settings.php:631 settings.php:791 settings.php:875
msgid "发布时间"
msgstr "Post time"

#: settings.php:632 settings.php:792
msgid "修改时间"
msgstr "Edit time"

#: settings.php:633
msgid "浏览量"
msgstr "Views"

#: settings.php:634
msgid "评论数"
msgstr "Comments"

#: settings.php:635
msgid "所属分类"
msgstr "Categories"

#: settings.php:636 settings.php:796
msgid "作者"
msgstr "Author"

#: settings.php:666
msgid "第二行"
msgstr "Second Line"

#: settings.php:678
msgid "每分钟阅读字数（中文）"
msgstr "Number of CJK words read per minute"

#: settings.php:681
msgid "字/分钟"
msgstr "words/minute"

#: settings.php:686
msgid "每分钟阅读单词数（英文）"
msgstr "Number of English words read per minute"

#: settings.php:689
msgid "单词/分钟"
msgstr "words/minute"

#: settings.php:693
msgid "每分钟阅读代码行数"
msgstr "Number of Code lines read per minute"

#: settings.php:696
msgid "行/分钟"
msgstr "lines/minute"

#: settings.php:697
msgid "预计阅读时间由每分钟阅读字数计算"
msgstr ""
"Estimated reading time is calculated by the number of words read per minute"

#: settings.php:700
msgid "文章头图 (特色图片)"
msgstr "Featured Picture"

#: settings.php:702
msgid "文章头图的位置"
msgstr "Featured Picture Position"

#: settings.php:706
msgid "文章卡片顶端"
msgstr "Top of article card"

#: settings.php:707
msgid "Banner (顶部背景)"
msgstr "Banner Background"

#: settings.php:709
msgid "阅读界面中文章头图的位置"
msgstr "The position of the article header image in the article page"

#: settings.php:713
msgid "默认使用文章中第一张图作为头图"
msgstr "Use the first image of article as the thumbnail by default"

#: settings.php:717 settings.php:1062 settings.php:1114 settings.php:1138
#: settings.php:1172 settings.php:1245 settings.php:1297 settings.php:1329
#: settings.php:1436 settings.php:1491 settings.php:1595 settings.php:1649
#: settings.php:1660
msgid "禁用"
msgstr "Disabled"

#: settings.php:720
msgid "也可以针对每篇文章单独设置"
msgstr "Also can be set separately for every article."

#: settings.php:723
msgid "脚注(引用)"
msgstr "Footnote (References)"

#: settings.php:725
msgid "脚注列表标题"
msgstr "Footnote List Title"

#: settings.php:728
msgid ""
"脚注列表显示在文末，在文章中有脚注的时候会显示。</br>使用 <code>ref</code> 短"
"代码可以在文中插入脚注。"
msgstr ""
"The footnotes list is shown at the end of the post, and is displayed when "
"there are footnotes in the article. </br> Use the <code>ref</code> shortcode "
"to insert footnotes into the post."

#: settings.php:731 template-parts/share.php:53
msgid "分享"
msgstr "Share"

#: settings.php:733
msgid "显示文章分享按钮"
msgstr "Show Share Button"

#: settings.php:737
msgid "显示全部社交媒体"
msgstr "All social media"

#: settings.php:738
msgid "显示国内社交媒体"
msgstr "Chinese social media"

#: settings.php:739
msgid "显示国外社交媒体"
msgstr "Non-Chinese social media"

#: settings.php:745
msgid "左侧栏文章目录"
msgstr "Catalog In Left Sidebar"

#: settings.php:747
msgid "在目录中显示序号"
msgstr "Show chapter number in the catalog"

#: settings.php:754
msgid "例：3.2.5"
msgstr "For example: 3.2.5"

#: settings.php:757 template-parts/content-single.php:100
msgid "赞赏"
msgstr "Donate"

#: settings.php:759
msgid "赞赏二维码图片链接"
msgstr "Donating QR code image link"

#: settings.php:762
msgid ""
"赞赏二维码图片链接，填写后会在文章最后显示赞赏按钮，留空则不显示赞赏按钮"
msgstr ""
"After filling in, the donate button will be displayed at the end of the "
"article, leave it blank to not display the donate button"

#: settings.php:770
msgid "将会显示在每篇文章末尾，支持 HTML 标签，留空则不显示。"
msgstr ""
"Will be displayed at the end of each article, support HTML tags, leave it "
"blank to not display."

#: settings.php:770
msgid ""
"使用 <code>%url%</code> 来代替当前页面 URL，<code>%link%</code> 来代替当前页"
"面链接，<code>%title%</code> 来代替当前文章标题，<code>%author%</code> 来代替"
"当前文章作者。"
msgstr ""
"Use <code>%url%</code> to replace the current page URL, <code>%link%</code> "
"to replace the current page link, and <code>%title%</code> to replace the "
"current article title,< code>%author%</code> to replace the current article "
"author."

#: settings.php:773 settings.php:775
msgid "相似文章推荐"
msgstr "Related Posts Recommending"

#: settings.php:780
msgid "根据分类推荐"
msgstr "Recommend by categories"

#: settings.php:781
msgid "根据标签推荐"
msgstr "Recommend by tags"

#: settings.php:782
msgid "根据分类和标签推荐"
msgstr "Recommend by both tags and categories"

#: settings.php:783
msgid "显示在文章卡片后"
msgstr "The related posts card will be displayed after post."

#: settings.php:787
msgid "排序依据"
msgstr "Sort by"

#: settings.php:793
msgid "阅读量"
msgstr "Views"

#: settings.php:797
msgid "随机"
msgstr "Random"

#: settings.php:802
msgid "顺序"
msgstr "Order"

#: settings.php:806
msgid "倒序"
msgstr "DESC"

#: settings.php:807
msgid "正序"
msgstr "ASC"

#: settings.php:812
msgid "推荐文章数"
msgstr "Related Posts Limit"

#: settings.php:815
msgid "最多推荐多少篇文章"
msgstr "The limit number of related posts."

#: settings.php:818 settings.php:820
msgid "文章内标题样式"
msgstr "Header style in articles"

#: settings.php:824 settings.php:829
msgid "默认样式"
msgstr "Default Style"

#: settings.php:825 settings.php:830
msgid "样式 1"
msgstr "Style 1"

#: settings.php:826 settings.php:831
msgid "样式 2"
msgstr "Style 2"

#: settings.php:828
msgid "样式预览"
msgstr "Styles Preview"

#: settings.php:867
msgid "其他"
msgstr "Other"

#: settings.php:869
msgid "文章过时信息显示"
msgstr "Article outdated information"

#: settings.php:871
msgid "当一篇文章的"
msgstr "When the"

#: settings.php:874
msgid "最后修改时间"
msgstr "Last Modified Time"

#: settings.php:877
msgid "距离现在超过"
msgstr "of an article from now more then"

#: settings.php:879
msgid "天时，用"
msgstr "days, show a tip by"

#: settings.php:882
msgid "在文章顶部显示信息条"
msgstr "Showing a information bar at the top of the article"

#: settings.php:883
msgid "在页面右上角弹出提示条"
msgstr "Popping up a alert bar at the right top of the page"

#: settings.php:885
msgid "的方式提示"
msgstr "."

#: settings.php:887
msgid ""
"本文最后更新于 %date_delta% 天前，其中的信息可能已经有所发展或是发生改变。"
msgstr ""
"This article was last updated %date_delta% days ago. The information in it "
"may have been changed."

#: settings.php:888
msgid "天数为 -1 表示永不提示。"
msgstr "Set days to -1 to never show it."

#: settings.php:888
msgid "表示文章发布/修改时间与当前时间的差距，"
msgstr ""
"means the gap between the post/modify time of the article and the current "
"time,"

#: settings.php:888
msgid "表示文章发布时间与当前时间的差距，"
msgstr ""
"means the gap between the post time of the article and the current time,"

#: settings.php:888
msgid "表示文章修改时间与当前时间的差距（单位: 天）。"
msgstr ""
"means the gap between the modify time of the article and the current time,"

#: settings.php:891
msgid "归档页面"
msgstr "Archive page"

#: settings.php:893
msgid "介绍"
msgstr "Description"

#: settings.php:895
msgid ""
"新建一个页面，并将其模板设为 \"归档时间轴\"，即可创建一个归档页面。归档页面会"
"按照时间顺序在时间轴上列出博客的所有文章。"
msgstr ""
"Create a new page and set its template to \"Archive Timeline\" to create an "
"archive page. The archive page will list all the blog posts on the timeline "
"in chronological order."

#: settings.php:900
msgid "在时间轴上显示月份"
msgstr "Show month on timeline"

#: settings.php:907
msgid "关闭后，时间轴只会按年份分节"
msgstr "If disabled, the timeline will only be divided into sections by year"

#: settings.php:910
msgid "配置"
msgstr "Configuration"

#: settings.php:912
msgid "归档页面链接"
msgstr "Archive page link"

#: settings.php:915
msgid ""
"归档页面的 URL。点击左侧栏 \"博客概览\" 中的 \"博文总数\" 一栏时可跳转到该地"
"址。"
msgstr ""
"The URL of the archive page. You can jump to this address when you click on "
"the \"Total Number of Blog Posts\" column in the \"Blog Overview\" on the "
"left column."

#: settings.php:918
msgid "页脚"
msgstr "Footer"

#: settings.php:920
msgid "页脚内容"
msgstr "Footer"

#: settings.php:923 settings.php:1203 settings.php:1210
msgid "HTML , 支持 script 等标签"
msgstr "HTML, script and other tags are supported."

#: settings.php:926
msgid "代码高亮"
msgstr "Code Highlight"

#: settings.php:928
msgid "启用 Highlight.js 代码高亮"
msgstr "Enable Highlight.js code highlighting"

#: settings.php:935
msgid "所有 pre 下的 code 标签会被自动解析"
msgstr "All code tags under pre will be automatically parsed"

#: settings.php:939
msgid "高亮配色方案（主题）"
msgstr "Highlight color scheme"

#: settings.php:957
msgid "查看所有主题预览"
msgstr "View all color scheme previews"

#: settings.php:961
msgid "默认显示行号"
msgstr "Show Line Numbers as Default"

#: settings.php:971
msgid "默认启用自动折行"
msgstr "Enable Line Break as Default"

#: settings.php:981
msgid "行号背景透明"
msgstr "Enable line number background transparent as Default"

#: settings.php:985
msgid "不透明"
msgstr "Opaque"

#: settings.php:986
msgid "透明"
msgstr "Transparent"

#: settings.php:988
msgid "适用于某些背景渐变的高亮主题"
msgstr "For some highlight themes which has gradient background."

#: settings.php:992
msgid ""
"如果您想使用其他代码高亮插件，而非 Argon 自带高亮，请前往 \"杂项\" 打开 \"禁"
"用 Argon 代码块样式\" 来防止样式冲突"
msgstr ""
"If you want to use other code highlighting plugins instead of Argon's "
"highlighting, go to the Misc section and turn on \"Disable Argon code block "
"styles\" to prevent style conflicts"

#: settings.php:996
msgid "数学公式"
msgstr "Formulas Rendering"

#: settings.php:998
msgid "数学公式渲染方案"
msgstr "Formula rendering library"

#: settings.php:1017 settings.php:1030 settings.php:1043
msgid "地址"
msgstr "URL"

#: settings.php:1019 settings.php:1032
msgid "，默认为"
msgstr ", Default is"

#: settings.php:1045
msgid "Argon 会同时引用"
msgstr "Argon will import both"

#: settings.php:1045
msgid "和"
msgstr "and"

#: settings.php:1045
msgid ""
"两个文件，所以在此填写的是上层的路径，而不是具体的文件。注意路径后要带一个斜"
"杠。"
msgstr ""
"two files, so input the parent directory path in here, not the specific "
"file. There is a slash after the path."

#: settings.php:1045
msgid "默认为"
msgstr "Default is"

#: settings.php:1057
msgid "是否启用 Lazyload"
msgstr "Enable Lazyload"

#: settings.php:1064
msgid "是否启用 Lazyload 加载文章内图片"
msgstr "Lazyload will load images when the page is about to scroll to them."

#: settings.php:1068
msgid "提前加载阈值"
msgstr "Lazyload threshold"

#: settings.php:1071
msgid "图片距离页面底部还有多少距离就开始提前加载"
msgstr ""
"The distance from the bottom of the screen when the image start loading."

#: settings.php:1075
msgid "LazyLoad 图片加载完成过渡"
msgstr "Transition after the image is loaded"

#: settings.php:1081
msgid "不使用过渡"
msgstr "No transitions"

#: settings.php:1087
msgid "LazyLoad 图片加载动效"
msgstr "Lazyload Loading Animation"

#: settings.php:1091 settings.php:1092 settings.php:1093 settings.php:1094
#: settings.php:1095 settings.php:1096 settings.php:1097 settings.php:1098
#: settings.php:1099 settings.php:1100 settings.php:1101
msgid "加载动画"
msgstr "Loading Animation"

#: settings.php:1104
msgid "在图片被加载之前显示的加载效果"
msgstr "The loading animation displayed before the image is loaded."

#: settings.php:1104
msgid "预览所有效果"
msgstr "Preview all animations"

#: settings.php:1107
msgid "图片放大浏览"
msgstr "Image zooming in"

#: settings.php:1109
msgid "是否启用图片放大浏览 (Fancybox)"
msgstr "Enable Fancybox"

#: settings.php:1116
msgid "开启后，文章中图片被单击时会放大预览"
msgstr ""
"If enabled, the image in the article will be enlarged and previewed when "
"clicked"

#: settings.php:1120
msgid "展开旧版图片放大浏览 (Zoomify) 设置 ▼"
msgstr "Expand Zoomify Settings (Old) ▼"

#: settings.php:1133
msgid "是否启用旧版图片放大浏览 (Zoomify)"
msgstr "Enable Zoomify"

#: settings.php:1140
msgid ""
"自 Argon 1.1.0 版本后，图片缩放预览库由 Zoomify 更换为 Fancybox，如果您还想使"
"用旧版图片预览，请开启此选项。注意: Zoomify 和 Fancybox 不能同时开启。"
msgstr ""
"Since Argon 1.1.0, the image zoom preview library has been replaced by "
"Fancybox instead of Zoomify, so turn this option on if you still want to use "
"the old image preview. Note: Zoomify and Fancybox cannot be turned on at the "
"same time."

#: settings.php:1144
msgid "缩放动画长度"
msgstr "Zooming Transition Duration"

#: settings.php:1147
msgid "图片被单击后缩放到全屏动画的时间长度"
msgstr "The duration of the image is clicked to zoom to full screen animation"

#: settings.php:1151
msgid "缩放动画曲线"
msgstr "Zooming Transition Timing Function"

#: settings.php:1155
msgid "例："
msgstr "For example: "

#: settings.php:1155
msgid "如果你不知道这是什么，参考"
msgstr "If you don‘t know what this is, see"

#: settings.php:1155
msgid "这里"
msgstr "here"

#: settings.php:1160
msgid "图片最大缩放比例"
msgstr "Image maximum zoom ratio"

#: settings.php:1163
msgid "图片相对于页面的最大缩放比例 (0 ~ 1 的小数)"
msgstr ""
"The maximum zoom ratio of the image relative to the page (decimal from 0 to "
"1)"

#: settings.php:1168
msgid "启用 Pangu.js (自动在中英文之间添加空格)"
msgstr "Enable Pangu.js (automatically add spaces between Chinese and English)"

#: settings.php:1173
msgid "格式化文章内容"
msgstr "Format article content"

#: settings.php:1174
msgid "格式化说说"
msgstr "Format essays"

#: settings.php:1175
msgid "格式化评论区"
msgstr "Format comments"

#: settings.php:1176
msgid "格式化文章内容和评论区"
msgstr "Format articles and comments"

#: settings.php:1177
msgid "格式化文章内容和说说"
msgstr "Format articles and essays"

#: settings.php:1178
msgid "格式化说说和评论区"
msgstr "Format essays and comments"

#: settings.php:1179
msgid "格式化文章内容、说说和评论区"
msgstr "Format article, essays and comments"

#: settings.php:1181
msgid "开启后，会自动在中文和英文之间添加空格"
msgstr ""
"After opening, it will automatically add space between CJK Characters and "
"English"

#: settings.php:1184
msgid "脚本"
msgstr "Scripts"

#: settings.php:1186
msgid "注意"
msgstr "Attention"

#: settings.php:1188
msgid ""
"Argon 使用 pjax 方式加载页面 (无刷新加载) , 所以除非页面手动刷新，否则您的脚"
"本只会被执行一次。"
msgstr ""
"Argon uses Pjax to load the page (loading without refresh), so unless the "
"page is manually refreshed, your script will only be executed once."

#: settings.php:1189
msgid "如果您想让每次页面跳转(加载新页面)时都执行脚本，请将脚本写入"
msgstr ""
"If you want to execute the script every time loading a new page, please "
"write the script into"

#: settings.php:1189
msgid "中"
msgstr "function"

#: settings.php:1189
msgid "示例写法"
msgstr "For example"

#: settings.php:1192
msgid "页面每次跳转都会执行这里的代码"
msgstr "The code here will be executed every time the page loads"

#: settings.php:1196
msgid "当页面第一次载入时，"
msgstr "When the page loads for the first time, the script in"

#: settings.php:1196
msgid "中的脚本不会执行，所以您可以手动执行"
msgstr "will not be executed, so you can manually execute"

#: settings.php:1196
msgid "来让页面初次加载时也执行脚本"
msgstr "to execute the script when the page first loads."

#: settings.php:1200
msgid "页头脚本"
msgstr "Header script"

#: settings.php:1203
msgid "插入到 body 之前"
msgstr "Will be inserted before body"

#: settings.php:1207
msgid "页尾脚本"
msgstr "Footer script"

#: settings.php:1210
msgid "插入到 body 之后"
msgstr "Will be inserted after body"

#: settings.php:1215
msgid "是否启用平滑滚动"
msgstr "Enable Smooth Scroll"

#: settings.php:1219
msgid "使用平滑滚动方案 1 (平滑) (推荐)"
msgstr "Smooth Scroll Scheme 1 (Smooth)(Recommend)"

#: settings.php:1220
msgid "使用平滑滚动方案 1 (脉冲式滚动) (仿 Edge) (推荐)"
msgstr "Smooth Scroll Scheme 1 (Impulsive, Edge like)(Recommend)"

#: settings.php:1221
msgid "使用平滑滚动方案 2 (较稳)"
msgstr "Smooth Scroll Scheme 2"

#: settings.php:1222
msgid "使用平滑滚动方案 3"
msgstr "Smooth Scroll Scheme 3"

#: settings.php:1223
msgid "不使用平滑滚动"
msgstr "Disabled"

#: settings.php:1225
msgid ""
"能增强浏览体验，但可能出现一些小问题，如果有问题请切换方案或关闭平滑滚动"
msgstr ""
"Can enhance the scrolling experience, but some minor problems may occur. If "
"there is a problem, please switch the scheme or turn off smooth scrolling."

#: settings.php:1229
msgid "是否启用进入文章动画"
msgstr "Enable Entering Article Animation"

#: settings.php:1236
msgid "从首页或分类目录进入文章时，使用平滑过渡（可能影响加载文章时的性能）"
msgstr ""
"Use transition when entering articles from the homepage or category "
"directory (may affect the performance when loading articles)"

#: settings.php:1240
msgid "禁用 Pjax 加载后的页面滚动动画"
msgstr "Disable Pjax Scroll Animation"

#: settings.php:1244 settings.php:1648 settings.php:1659
msgid "不禁用"
msgstr "Do not disable"

#: settings.php:1247
msgid "Pjax 替换页面内容后会平滑滚动到页面顶部，如果你不喜欢，可以禁用这个选项"
msgstr ""
"Pjax will scroll to the top of the page smoothly after replacing the page "
"content. If you don‘t like it, you can disable this option."

#: settings.php:1251
msgid "评论分页"
msgstr "Comment pagination"

#: settings.php:1253
msgid "评论分页方式"
msgstr "Comment pagination"

#: settings.php:1257
msgid "无限加载"
msgstr "Append"

#: settings.php:1258
msgid "页码"
msgstr "Page Number"

#: settings.php:1261
msgid "无限加载：点击 \"加载更多\" 按钮来加载更多评论。"
msgstr "Append: Click the \"Load more\" button to load more comments."

#: settings.php:1262
msgid "页码：显示页码来分页。"
msgstr "Page Number: Display the page number buttons."

#: settings.php:1263
msgid ""
"选择\"无限加载\"时，如果开启了评论分页，请将 Wordpress 的讨论设置设为 \"默认"
"显示<b>最后</b>一页，在每个页面顶部显示<b>新的</b>评论\"。"
msgstr ""
"When selecting \"Append\", it is recommended to set the discussion setting "
"item in WordPress settings to \"Display the last page by default, and "
"display new comments at the top of each page\"."

#: settings.php:1263
#, fuzzy
#| msgid "设置"
msgid "去设置"
msgstr "Settings"

#: settings.php:1292
msgid "评论表情面板"
msgstr "Comment Emotions Keyboard"

#: settings.php:1299
msgid "开启后评论支持插入表情，会在评论输入框下显示表情键盘按钮。"
msgstr ""
"When enabled, comments support inserting emoticons, and emoticon keyboard "
"buttons will be displayed under the comment box."

#: settings.php:1299
msgid "如何添加新的表情或修改已有表情列表？"
msgstr "How to add new emoticons or modify default emoticon list?"

#: settings.php:1303
msgid "是否隐藏 \"昵称\"、\"邮箱\"、\"网站\" 输入框"
msgstr "Hide the \"name\", \"mail\", and \"website\" fields"

#: settings.php:1310
msgid ""
"选项仅在 \"设置-评论-评论作者必须填入姓名和电子邮件地址\" 选项未勾选的前提下"
"生效。如勾选了 \"评论作者必须填入姓名和电子邮件地址\"，则只有 \"网站\" 输入框"
"会被隐藏。"
msgstr ""
"The option only takes effect if the option \"Settings-Comment-Comment author "
"must fill in name and email address\" is not checked. If the \"Comment "
"author must fill in the name and email address\" is checked, only the "
"\"Website\" input box will be hidden."

#: settings.php:1314
msgid "评论是否需要验证码"
msgstr "Comment CAPTCHA"

#: settings.php:1318
msgid "需要"
msgstr "Required"

#: settings.php:1319
msgid "不需要"
msgstr "Not Required"

#: settings.php:1325
msgid "使用 Ajax 获取评论验证码"
msgstr "Get CAPTCHA by ajax"

#: settings.php:1332
msgid "如果使用了 CDN 缓存，验证码不会刷新，请开启此选项，否则请不要开启。"
msgstr ""
"If the CAPTCHA is cached by the CDN and will not be refreshed, enable this "
"option, otherwise please do not enable it."

#: settings.php:1336
msgid "是否允许在评论中使用 Markdown 语法"
msgstr "Allow Markdown In Comment"

#: settings.php:1340 settings.php:1351 settings.php:1363 settings.php:1374
#: settings.php:1391
msgid "允许"
msgstr "Allow"

#: settings.php:1341 settings.php:1352 settings.php:1362 settings.php:1373
#: settings.php:1390
msgid "不允许"
msgstr "Disallow"

#: settings.php:1347
msgid "是否允许评论者再次编辑评论"
msgstr "Allow Comment Editing"

#: settings.php:1354
msgid "同一个评论者可以再次编辑评论。"
msgstr "Commenter can edit the comment again."

#: settings.php:1358
msgid "是否允许评论者使用悄悄话模式"
msgstr "Allow private comment mode"

#: settings.php:1365
msgid "评论者使用悄悄话模式发送的评论和其下的所有回复只有发送者和博主能看到。"
msgstr ""
"Only the sender and blog owner can see the comment sent using the private "
"comment mode and all the replies under it."

#: settings.php:1369
msgid "是否允许评论者接收评论回复邮件提醒"
msgstr "Allow comment reply email notification"

#: settings.php:1379
msgid "评论时默认勾选 \"启用邮件通知\" 复选框"
msgstr "check the \"Enable Email Notification\" checkbox by default"

#: settings.php:1382
msgid "评论者开启邮件提醒后，其评论有回复时会有邮件通知。"
msgstr ""
"If the commenter enable the email notification, there will be an email "
"notification when the comment has a reply."

#: settings.php:1386
msgid "允许评论者使用 QQ 头像"
msgstr "Allow Using QQ Avatars"

#: settings.php:1393
msgid ""
"开启后，评论者可以使用 QQ 号代替邮箱输入，头像会根据评论者的 QQ 号获取。"
msgstr ""
"If this option is enabled, commenters can use QQ number instead of email, "
"and the avatar will auto get from the commenter‘s QQ number."

#: settings.php:1396
msgid "评论区"
msgstr "Comments"

#: settings.php:1398
msgid "评论头像垂直位置"
msgstr "Avatar vertical position"

#: settings.php:1402
msgid "居上"
msgstr "Top"

#: settings.php:1403
msgid "居中"
msgstr "Middle"

#: settings.php:1409
msgid "谁可以查看评论编辑记录"
msgstr "Who can see comment edit history"

#: settings.php:1413
msgid "只有博主"
msgstr "Only blog owner"

#: settings.php:1414
msgid "评论发送者和博主"
msgstr "Comment sender and blog owner"

#: settings.php:1415
msgid "任何人"
msgstr "Everyone"

#: settings.php:1417
msgid "点击评论右侧的 \"已编辑\" 标记来查看编辑记录"
msgstr ""
"Click the \"Edited\" mark on the right side of the comment to view the edit "
"history"

#: settings.php:1421
msgid "开启评论置顶功能"
msgstr "Enable comment pinning"

#: settings.php:1428
msgid ""
"开启后，博主将可以置顶评论。已置顶的评论将会在评论区顶部显示。如果关闭，评论"
"将以正常顺序显示。"
msgstr ""
"If enabled, blog owner can pin comments. Pinned comments will be displayed "
"at the top of the comment block. If disabled, comments will appear in their "
"normal order."

#: settings.php:1432
msgid "评论点赞"
msgstr "Comment Upvoting"

#: settings.php:1439
msgid "开启后，每一条评论的头像下方会出现点赞按钮"
msgstr "A upvote button will appear below the avatar of each comment."

#: settings.php:1443
msgid "评论者 UA 显示"
msgstr "Show commenters' User Agent"

#: settings.php:1448
msgid "浏览器"
msgstr "Browser"

#: settings.php:1449
msgid "浏览器+版本号"
msgstr "Browser+Version"

#: settings.php:1450
msgid "平台+浏览器+版本号"
msgstr "Platform+Browser+Version"

#: settings.php:1451
msgid "平台+浏览器"
msgstr "Platform+Browser"

#: settings.php:1452
msgid "平台"
msgstr "Platform"

#: settings.php:1454
msgid "设置是否在评论区显示评论者 UA 及显示哪些部分"
msgstr ""
"Whether to display the commenter's UA in the comment area and which parts "
"are displayed"

#: settings.php:1458
msgid "在子评论中显示被回复者用户名"
msgstr "Display respondent username in sub-comments"

#: settings.php:1465
msgid ""
"开启后，被回复的评论者昵称会显示在子评论中，鼠标移上后会高亮被回复的评论"
msgstr ""
"When enabled, the commenter's nickname will be displayed in the sub-"
"comments, and the commented will be highlighted when the mouse is moved up"

#: settings.php:1469
msgid "折叠过长评论"
msgstr "Fold Long Comments"

#: settings.php:1473 settings.php:1584
msgid "不折叠"
msgstr "Do not fold"

#: settings.php:1474 settings.php:1585
msgid "折叠"
msgstr "Fold"

#: settings.php:1476
msgid "开启后，过长的评论会被折叠，需要手动展开"
msgstr "After opening, comments that are too long will be collapsed"

#: settings.php:1483
msgid ""
"使用 CDN 来加速 Gravatar 在某些地区的访问，填写 CDN 地址，留空则不使用。"
msgstr ""
"Use CDN to speed up Gravatar's access in some areas, fill in the CDN "
"address, leave it blank to not use CDN."

#: settings.php:1483
msgid "在中国速度较快的一些 CDN :"
msgstr "Some CDN that are fast in China:"

#: settings.php:1487
msgid "评论文字头像"
msgstr "Comment Text Avatar"

#: settings.php:1494
msgid ""
"在评论者没有设置 Gravatar 时自动生成文字头像，头像颜色由邮箱哈希计算。生成时"
"会在 Console 中抛出 404 错误，但没有影响。"
msgstr ""
"When the commenter does not set Gravatar, theme will generate a text avatar "
"for them. The color of the avatar is calculated by the hash of email. Error "
"404 will be thrown in the Console but no problem."

#: settings.php:1497
msgid "杂项"
msgstr "Misc"

#: settings.php:1499
msgid "是否启用 Pjax"
msgstr "Enable Pjax"

#: settings.php:1506
msgid "Pjax 可以增强页面的跳转体验"
msgstr "Pjax can enhance the page jumping experience"

#: settings.php:1510
msgid "首页隐藏特定 分类/Tag 下的文章"
msgstr "Hide specific categories/tags articles in home page"

#: settings.php:1513
msgid "输入要隐藏的 分类/Tag 的 ID，用英文逗号分隔，留空则不隐藏"
msgstr ""
"Enter the IDs of the categories/Tags to be hidden, separated by commas, "
"leave blank to not hide"

#: settings.php:1513
msgid "点此查看"
msgstr "Click Here"

#: settings.php:1513
msgid "所有分类和 Tag 的 ID"
msgstr "to view IDs of all categories and tags"

#: settings.php:1549
msgid "美化登录界面"
msgstr "Beautify the login page"

#: settings.php:1556
msgid "使用 Argon Design 风格的登录界面"
msgstr "Use Argon Design Style login interface"

#: settings.php:1560
msgid "美化后台界面"
msgstr "Beautify the background interface"

#: settings.php:1563
msgid "使用 Argon Design 风格的后台界面"
msgstr "Use Argon Design Style background interface"

#: settings.php:1564
#, php-format
msgid ""
"前往<a href=\"%s\" target=\"_blank\">个人资料</a>页面将 \"管理界面配色方案\" "
"设为 \"Argon\" 即可开启。"
msgstr ""
"Go to the <a href=\"%s\" target=\"_blank\">profile</a> page and set the "
"\"Admin UI Color Scheme\" to \"Argon\" to enable it."

#: settings.php:1569
msgid "博客首页是否显示说说"
msgstr "Show essays on the homepage"

#: settings.php:1576
msgid "开启后，博客首页文章和说说穿插显示"
msgstr ""
"If enabled, articles and essays will be displayed on the homepage at the "
"same time"

#: settings.php:1580
msgid "折叠长说说"
msgstr "Collapse long essay"

#: settings.php:1587
msgid "开启后，长说说在预览状态下会被折叠，需要手动展开"
msgstr "After opening, essay that are too long will be collapsed"

#: settings.php:1591
msgid "搜索结果类型过滤器"
msgstr "Search result type filter"

#: settings.php:1596
msgid "启用，默认不包括说说"
msgstr "Enable, exclude essays by default"

#: settings.php:1597
msgid "启用，默认包括说说"
msgstr "Enable, include essays by default"

#: settings.php:1598
msgid "启用，隐藏说说分类"
msgstr "Enable, hide essays checkbox"

#: settings.php:1600
msgid "开启后，将会在搜索结果界面显示一个过滤器，支持搜索说说"
msgstr ""
"After enabling, a filter will be displayed on the search results page , "
"which supports search essays"

#: settings.php:1604
msgid "是否修正时区错误"
msgstr "Time zone fix"

#: settings.php:1611
msgid "如遇到时区错误（例如一条刚发的评论显示 8 小时前），这个选项"
msgstr ""
"If you encounter a time zone problem (for example, a comment is just posted "
"but displays 8 hours ago), this option"

#: settings.php:1611
msgid "可能"
msgstr "may"

#: settings.php:1611
msgid "可以修复这个问题"
msgstr " can fix this problem"

#: settings.php:1615
msgid "是否在文章列表内容预览中隐藏短代码"
msgstr "Hide shortcodes in preview"

#: settings.php:1619 settings.php:1637
msgid "否"
msgstr "No"

#: settings.php:1620 settings.php:1638
msgid "是"
msgstr "Yes"

#: settings.php:1626
msgid "文章内容预览截取字数"
msgstr "Article Content Preview Trim Word Count"

#: settings.php:1629
msgid "设为 0 来隐藏文章内容预览"
msgstr "Set to 0 to hide article content preview."

#: settings.php:1633
msgid "是否允许移动端缩放页面"
msgstr "Allow mobile browser zoom the page"

#: settings.php:1644
msgid "禁用 Google 字体"
msgstr "Disable Google Fonts"

#: settings.php:1651
msgid ""
"Google 字体在中国大陆访问可能会阻塞，禁用可以解决页面加载被阻塞的问题。禁用"
"后，Serif 字体将失效。"
msgstr ""
"Google Fonts may be blocked when accessed in mainland China, disabling it "
"will fix the blocked page load. By disabling it, Serif Fonts will be "
"disabled."

#: settings.php:1655
msgid "禁用 Argon 代码块样式"
msgstr "Disable Argon code block styles"

#: settings.php:1662
msgid ""
"如果您启用了其他代码高亮插件，发现代码块样式被 Argon 覆盖，出现了显示错误，请"
"将此选项设为禁用"
msgstr ""
"If you enabled other code highlighting plugins and the code block style is "
"covered by Argon, please disabled this option."

#: settings.php:1666
msgid "检测更新源"
msgstr "Theme update source"

#: settings.php:1674
msgid "暂停更新 (不推荐)"
msgstr "Stop Update"

#: settings.php:1676
msgid "如更新主题速度较慢，可考虑更换更新源。"
msgstr "If updating theme is slow, you can changing the update source."

#: settings.php:1680
msgid "页脚附加内容"
msgstr "Additional content in footer"

#: settings.php:1693
msgid "保存更改"
msgstr "Save Changes"

#: settings.php:1694
msgid "导入设置"
msgstr "Import"

#: settings.php:1695
msgid "导出设置"
msgstr "Export"

#: settings.php:1700
msgid "收起"
msgstr "Hide"

#: settings.php:1703
msgid "到顶部"
msgstr "To the top"

#: settings.php:1703
msgid "到底部"
msgstr "To the bottom"

#: settings.php:1704
msgid "请复制并保存导出后的 JSON"
msgstr "Copy and save the JSON"

#: settings.php:1704
msgid "确定"
msgstr "OK"

#: settings.php:1781
msgid "展开"
msgstr "Show"

#: settings.php:1947
msgid "字段导入失败"
msgstr "Option Import Failed"

#: settings.php:1958
msgid "请输入要导入的备份 JSON"
msgstr "Input JSON"

#: settings.php:1961
msgid "已导入，请保存更改"
msgstr "Imported, Place save changes."

#: shuoshuo.php:21
msgid "条说说"
msgstr "Essays"

#: sidebar.php:9
msgid "公告"
msgstr "Announcement"

#: sidebar.php:83
msgid "文章目录"
msgstr "Catalog"

#: sidebar.php:87
msgid "站点概览"
msgstr "Site"

#: sidebar.php:91
msgid "功能"
msgstr "Functions"

#: sidebar.php:140 sidebar.php:232
msgid "分类"
msgstr "Categories"

#: sidebar.php:146 sidebar.php:260
msgid "标签"
msgstr "Tags"

#: single.php:27
msgid "上一篇"
msgstr "Previous"

#: single.php:32
msgid "下一篇"
msgstr "Next"

#: single.php:86
msgid "推荐文章"
msgstr "Related Posts"

#: template-parts/content-none-search.php:4
msgid "没有搜索结果"
msgstr "No Search Results"

#: template-parts/content-none-search.php:6
msgid "似乎没有勾选任何分类"
msgstr "No category seems to be checked"

#: template-parts/content-none-search.php:8
msgid "换个关键词试试 ?"
msgstr "Try another keyword?"

#: template-parts/content-none-tag.php:4
msgid "此分类没有文章"
msgstr "No Posts In This Category"

#: template-parts/content-none-tag.php:5
msgid "这里什么都没有"
msgstr "Nothing here."

#: template-parts/content-page.php:60 template-parts/content-single.php:57
#: template-parts/content-timeline.php:57
msgid "这是一篇受密码保护的文章，您需要提供访问密码"
msgstr "Password Required"

#: template-parts/content-page.php:67 template-parts/content-single.php:64
msgid "密码"
msgstr "Password"

#: template-parts/content-page.php:77 template-parts/content-single.php:74
msgid "确认"
msgstr "Submit"

#: template-parts/content-preview-1.php:59
#: template-parts/content-preview-2.php:29
#: template-parts/content-preview-3.php:55
msgid "这篇文章受密码保护，输入密码才能阅读"
msgstr "This article requires a password to view."

#: template-parts/content-preview-1.php:62
#: template-parts/content-preview-2.php:32
#: template-parts/content-preview-3.php:58
msgid "这篇文章没有摘要"
msgstr "No abstract."

#: template-parts/content-shuoshuo-details.php:5
#: template-parts/content-shuoshuo-preview.php:13
#: template-parts/content-shuoshuo.php:5
msgid "月"
msgstr "/"

#: template-parts/content-shuoshuo-details.php:6
#: template-parts/content-shuoshuo-preview.php:14
#: template-parts/content-shuoshuo.php:6
msgid "日"
msgstr " "

#: template-parts/share.php:4 template-parts/share.php:61
msgid "分享到微信"
msgstr "Share to Wechat"

#: template-parts/share.php:9
msgid "分享到豆瓣"
msgstr "Share To Douban"

#: template-parts/share.php:14
msgid "分享到 QQ"
msgstr "Share To QQ"

#: template-parts/share.php:19
msgid "分享到 QQ 空间"
msgstr "Share To Qzone"

#: template-parts/share.php:24
msgid "分享到微博"
msgstr "Share to Weibo"

#: template-parts/share.php:30
msgid "分享到 Facebook"
msgstr "Share To Facebook"

#: template-parts/share.php:35
msgid "分享到 Twitter"
msgstr "Share To Twitter"

#: template-parts/share.php:41
msgid "分享到 Telegram"
msgstr "Share To Telegram"

#: template-parts/share.php:47
msgid "复制链接"
msgstr "Copy Link"

#: template-parts/share.php:62
msgid "微信扫描二维码"
msgstr "Scan QR Code"

#: template-parts/share.php:77
msgid "链接已复制"
msgstr "Copied"

#: template-parts/share.php:78
msgid "链接已复制到剪贴板"
msgstr "The link has been copied to the clipboard."

#: template-parts/share.php:91
msgid "复制失败"
msgstr "Failed"

#: template-parts/share.php:92
msgid "请手动复制链接"
msgstr "Please copy the link manually."

#: template-parts/shuoshuo-operations.php:10
msgid "已关闭"
msgstr " Disabled"

#: unsubscribe-comment-mailnotice.php:7
msgid "参数错误"
msgstr "Wrong parameters"

#: unsubscribe-comment-mailnotice.php:8 unsubscribe-comment-mailnotice.php:13
msgid "错误"
msgstr "Error"

#: unsubscribe-comment-mailnotice.php:9
msgid "提供的参数错误"
msgstr "Wrong parameters were given."

#: unsubscribe-comment-mailnotice.php:14 unsubscribe-comment-mailnotice.php:19
msgid "评论 #"
msgstr "Comment #"

#: unsubscribe-comment-mailnotice.php:14
msgid " 不存在"
msgstr "not found"

#: unsubscribe-comment-mailnotice.php:17 unsubscribe-comment-mailnotice.php:18
msgid "无需退订"
msgstr "No Need To Unsubscribe"

#: unsubscribe-comment-mailnotice.php:19
msgid " 的邮件通知已被退订或没有开启邮件通知"
msgstr " is already unsubscribed."

#: unsubscribe-comment-mailnotice.php:22 unsubscribe-comment-mailnotice.php:23
msgid "退订失败"
msgstr "Unsubscribe Failed"

#: unsubscribe-comment-mailnotice.php:24
msgid "Token 不正确"
msgstr "Wrong Token."

#: unsubscribe-comment-mailnotice.php:28 unsubscribe-comment-mailnotice.php:29
msgid "退订成功"
msgstr "Unsubscribed"

#: unsubscribe-comment-mailnotice.php:30
msgid "您已成功退订评论 #"
msgstr "Email notification of comment #"

#: unsubscribe-comment-mailnotice.php:30
msgid " 的邮件通知<br>该评论下有新回复时您将不会再收到通知"
msgstr " is unsubscribed successfully."

#~ msgid "自动折叠顶栏"
#~ msgstr "Auto Fold Toolbar"

#, fuzzy
#~| msgid "显示"
#~ msgid "提示"
#~ msgstr "Show"

#, fuzzy
#~| msgid "主题色"
#~ msgid "颜色"
#~ msgstr "Theme Color"

#, fuzzy
#~| msgid "发布时间"
#~ msgid "时间"
#~ msgstr "Post time"

#~ msgid "菜单"
#~ msgstr "Menu"

#~ msgid "是否使用 v2ex CDN 代理的 gravatar"
#~ msgstr "Use V2EX CDN Gravatar"

#~ msgid "可以大幅增加国内 gravatar 头像加载的速度"
#~ msgstr "Can increase the loading speed of gravatar in China."

#, fuzzy
#~| msgid "评论内容"
#~ msgid "评论内容不能为空"
#~ msgstr "Your comment..."

#, fuzzy
#~| msgid "发送"
#~ msgid "发送中"
#~ msgstr "Send"

#, fuzzy
#~| msgid "编辑"
#~ msgid "编辑中"
#~ msgstr "Edit"

#, fuzzy
#~| msgid "链接已复制到剪贴板"
#~ msgid "代码已复制到剪贴板"
#~ msgstr "The link has been copied to the clipboard."

#, fuzzy
#~| msgid "请手动复制链接"
#~ msgid "请手动复制代码"
#~ msgstr "Please copy the link manually."

#, fuzzy
#~| msgid "分钟"
#~ msgid "分钟前"
#~ msgstr "minutes"

#, fuzzy
#~| msgid "小时"
#~ msgid "小时前"
#~ msgstr "hour"

#, fuzzy
#~| msgid "前"
#~ msgid "前天"
#~ msgstr " ago"

#, fuzzy
#~| msgid "前"
#~ msgid "天前"
#~ msgstr " ago"

#~ msgid "的评论"
#~ msgstr "'s comment"

#~ msgid "您在xxx[的评论有了新的回复]"
#~ msgstr "Has a New Reply"
