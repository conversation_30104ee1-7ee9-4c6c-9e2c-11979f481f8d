{"Version": 3, "Meta": {"Duration": 4.53, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 110, "TotalSegmentCount": 739, "TotalPointCount": 2201, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 2, 0.333, 2, 1, 0.956, 2, 1.578, 2, 2.2, 2, 1, 2.367, 2, 2.533, 19, 2.7, 19, 0, 4.533, 19]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.111, -1, 0.222, -19, 0.333, -19, 1, 0.956, -19, 1.578, -19, 2.2, -19, 1, 2.367, -19, 2.533, -15, 2.7, -15, 0, 4.533, -15]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 1, 0, 4.533, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, -10, 0.333, -10, 1, 0.956, -10, 1.578, -10, 2.2, -10, 1, 2.367, -10, 2.533, -1, 2.7, -1, 0, 4.533, -1]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.233, 0, 0.3, 0, 0.367, 0, 1, 0.378, 0, 0.389, 0, 0.4, 0, 1, 0.456, 0, 0.511, 0, 0.567, 0, 1, 0.589, 0, 0.611, 30, 0.633, 30, 1, 0.656, 30, 0.678, 30, 0.7, 30, 1, 0.711, 30, 0.722, -30, 0.733, -30, 1, 0.756, -30, 0.778, -30, 0.8, -30, 1, 0.833, -30, 0.867, 30, 0.9, 30, 1, 0.922, 30, 0.944, 30, 0.967, 30, 1, 1.033, 30, 1.1, -13.699, 1.167, -13.699, 1, 1.244, -13.699, 1.322, 5.294, 1.4, 5.294, 1, 1.467, 5.294, 1.533, -2.102, 1.6, -2.102, 1, 1.678, -2.102, 1.756, 0.844, 1.833, 0.844, 1, 1.911, 0.844, 1.989, -0.337, 2.067, -0.337, 1, 2.189, -0.337, 2.311, 20.324, 2.433, 20.324, 1, 2.544, 20.324, 2.656, -18.875, 2.767, -18.875, 1, 2.844, -18.875, 2.922, 7.187, 3, 7.187, 1, 3.067, 7.187, 3.133, -2.854, 3.2, -2.854, 1, 3.278, -2.854, 3.356, 1.145, 3.433, 1.145, 1, 3.511, 1.145, 3.589, -0.458, 3.667, -0.458, 1, 3.744, -0.458, 3.822, 0.182, 3.9, 0.182, 1, 3.978, 0.182, 4.056, -0.072, 4.133, -0.072, 1, 4.211, -0.072, 4.289, 0.029, 4.367, 0.029, 1, 4.422, 0.029, 4.478, 0.008, 4.533, -0.003]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.178, 1, 0.356, 1, 0.533, 1, 1, 0.578, 1, 0.622, 0, 0.667, 0, 1, 0.7, 0, 0.733, 1, 0.767, 1, 1, 1.244, 1, 1.722, 1, 2.2, 1, 1, 2.367, 1, 2.533, 0, 2.7, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.178, 1, 0.356, 1, 0.533, 1, 1, 0.578, 1, 0.622, 0, 0.667, 0, 1, 0.7, 0, 0.733, 1, 0.767, 1, 1, 1.244, 1, 1.722, 1, 2.2, 1, 1, 2.367, 1, 2.533, 0, 2.7, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.1, 0.333, -0.1, 1, 0.967, -0.1, 1.6, -0.1, 2.233, -0.1, 1, 2.389, -0.1, 2.544, -1, 2.7, -1, 0, 4.533, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.1, 1, 0.111, -0.1, 0.222, 0.5, 0.333, 0.5, 1, 0.967, 0.5, 1.6, 0.5, 2.233, 0.5, 1, 2.389, 0.5, 2.544, 1, 2.7, 1, 0, 4.533, 1]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.9, 0.333, -0.9, 1, 0.956, -0.9, 1.578, -0.9, 2.2, -0.9, 1, 2.367, -0.9, 2.533, 0, 2.7, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.9, 0.333, -0.9, 1, 0.956, -0.9, 1.578, -0.9, 2.2, -0.9, 1, 2.367, -0.9, 2.533, 0, 2.7, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.8, 0.333, -0.8, 1, 0.956, -0.8, 1.578, -0.8, 2.2, -0.8, 1, 2.367, -0.8, 2.533, -0.4, 2.7, -0.4, 0, 4.533, -0.4]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.8, 0.333, -0.8, 1, 0.956, -0.8, 1.578, -0.8, 2.2, -0.8, 1, 2.367, -0.8, 2.533, -0.4, 2.7, -0.4, 0, 4.533, -0.4]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.2, 0.333, -0.2, 1, 0.956, -0.2, 1.578, -0.2, 2.2, -0.2, 1, 2.367, -0.2, 2.533, 0.7, 2.7, 0.7, 0, 4.533, 0.7]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.2, 0.333, -0.2, 1, 0.956, -0.2, 1.578, -0.2, 2.2, -0.2, 1, 2.367, -0.2, 2.533, 0.6, 2.7, 0.6, 0, 4.533, 0.6]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.8, 1, 0.222, -0.8, 0.444, -0.8, 0.667, -0.8, 1, 0.733, -0.8, 0.8, -0.4, 0.867, -0.4, 1, 0.933, -0.4, 1, -0.4, 1.067, -0.4, 1, 1.133, -0.4, 1.2, -0.5, 1.267, -0.5, 1, 1.322, -0.5, 1.378, -0.1, 1.433, -0.1, 1, 1.478, -0.1, 1.522, -0.4, 1.567, -0.4, 1, 1.578, -0.4, 1.589, -0.3, 1.6, -0.3, 1, 1.656, -0.3, 1.711, -0.413, 1.767, -0.5, 1, 1.911, -0.727, 2.056, -0.8, 2.2, -0.8, 1, 2.378, -0.8, 2.556, 0, 2.733, 0, 1, 2.8, 0, 2.867, -0.222, 2.933, -0.4, 1, 3, -0.578, 3.067, -0.6, 3.133, -0.6, 1, 3.2, -0.6, 3.267, -0.4, 3.333, -0.4, 1, 3.411, -0.4, 3.489, -0.8, 3.567, -0.8, 1, 3.644, -0.8, 3.722, -0.4, 3.8, -0.4, 0, 4.533, -0.4]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.733, 0, 0.8, 0.4, 0.867, 0.4, 1, 0.933, 0.4, 1, 0.2, 1.067, 0.2, 1, 1.133, 0.2, 1.2, 0.5, 1.267, 0.5, 1, 1.322, 0.5, 1.378, 0.468, 1.433, 0.3, 1, 1.478, 0.165, 1.522, 0, 1.567, 0, 1, 1.578, 0, 1.589, 0.3, 1.6, 0.3, 1, 1.656, 0.3, 1.711, 0, 1.767, 0, 1, 1.911, 0, 2.056, 0, 2.2, 0, 1, 2.378, 0, 2.556, 0.5, 2.733, 0.5, 1, 2.8, 0.5, 2.867, 0.4, 2.933, 0.4, 1, 3, 0.4, 3.067, 0.8, 3.133, 0.8, 1, 3.2, 0.8, 3.267, 0.2, 3.333, 0.2, 1, 3.411, 0.2, 3.489, 0.211, 3.567, 0.3, 1, 3.644, 0.389, 3.722, 0.5, 3.8, 0.5, 0, 4.533, 0.5]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.253, 0.033, -0.253, 1, 0.1, -0.253, 0.167, 0.231, 0.233, 0.231, 1, 0.333, 0.231, 0.433, -0.116, 0.533, -0.116, 1, 0.633, -0.116, 0.733, 0.031, 0.833, 0.031, 1, 0.944, 0.031, 1.056, -0.008, 1.167, -0.008, 1, 1.278, -0.008, 1.389, 0.002, 1.5, 0.002, 1, 1.6, 0.002, 1.7, -0.001, 1.8, -0.001, 1, 1.911, -0.001, 2.022, 0, 2.133, 0, 1, 2.256, 0, 2.378, -1.398, 2.5, -1.398, 1, 2.611, -1.398, 2.722, 0.813, 2.833, 0.813, 1, 2.944, 0.813, 3.056, -0.211, 3.167, -0.211, 1, 3.267, -0.211, 3.367, 0.056, 3.467, 0.056, 1, 3.578, 0.056, 3.689, -0.015, 3.8, -0.015, 1, 3.911, -0.015, 4.022, 0.004, 4.133, 0.004, 1, 4.233, 0.004, 4.333, -0.001, 4.433, -0.001, 1, 4.467, -0.001, 4.5, -0.001, 4.533, -0.001]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.253, 0.033, -0.253, 1, 0.1, -0.253, 0.167, 0.231, 0.233, 0.231, 1, 0.333, 0.231, 0.433, -0.116, 0.533, -0.116, 1, 0.633, -0.116, 0.733, 0.031, 0.833, 0.031, 1, 0.944, 0.031, 1.056, -0.008, 1.167, -0.008, 1, 1.278, -0.008, 1.389, 0.002, 1.5, 0.002, 1, 1.6, 0.002, 1.7, -0.001, 1.8, -0.001, 1, 1.911, -0.001, 2.022, 0, 2.133, 0, 1, 2.256, 0, 2.378, -1.398, 2.5, -1.398, 1, 2.611, -1.398, 2.722, 0.813, 2.833, 0.813, 1, 2.944, 0.813, 3.056, -0.211, 3.167, -0.211, 1, 3.267, -0.211, 3.367, 0.056, 3.467, 0.056, 1, 3.578, 0.056, 3.689, -0.015, 3.8, -0.015, 1, 3.911, -0.015, 4.022, 0.004, 4.133, 0.004, 1, 4.233, 0.004, 4.333, -0.001, 4.433, -0.001, 1, 4.467, -0.001, 4.5, -0.001, 4.533, -0.001]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 1.511, 0, 3.022, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, -30, 1, 1.511, -30, 3.022, -30, 4.533, -30]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, -30, 1, 1.511, -30, 3.022, -30, 4.533, -30]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.262, 0.2, -0.262, 1, 0.3, -0.262, 0.4, 0.399, 0.5, 0.399, 1, 0.6, 0.399, 0.7, -0.092, 0.8, -0.092, 1, 0.9, -0.092, 1, 0.133, 1.1, 0.133, 1, 1.211, 0.133, 1.322, 0.03, 1.433, 0.03, 1, 1.533, 0.03, 1.633, 0.078, 1.733, 0.078, 1, 1.844, 0.078, 1.956, 0.056, 2.067, 0.056, 1, 2.122, 0.056, 2.178, 0.063, 2.233, 0.063, 1, 2.311, 0.063, 2.389, -1.418, 2.467, -1.418, 1, 2.578, -1.418, 2.689, 1.782, 2.8, 1.782, 1, 2.9, 1.782, 3, -0.715, 3.1, -0.715, 1, 3.211, -0.715, 3.322, 0.423, 3.433, 0.423, 1, 3.533, 0.423, 3.633, -0.102, 3.733, -0.102, 1, 3.844, -0.102, 3.956, 0.138, 4.067, 0.138, 1, 4.167, 0.138, 4.267, 0.028, 4.367, 0.028, 1, 4.422, 0.028, 4.478, 0.044, 4.533, 0.058]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0.057, 1, 0.056, 0.057, 0.111, 0.239, 0.167, 0.239, 1, 0.244, 0.239, 0.322, -0.518, 0.4, -0.518, 1, 0.489, -0.518, 0.578, 0.594, 0.667, 0.594, 1, 0.778, 0.594, 0.889, -0.473, 1, -0.473, 1, 1.1, -0.473, 1.2, 0.311, 1.3, 0.311, 1, 1.411, 0.311, 1.522, -0.184, 1.633, -0.184, 1, 1.744, -0.184, 1.856, 0.102, 1.967, 0.102, 1, 2.056, 0.102, 2.144, -0.048, 2.233, -0.048, 1, 2.289, -0.048, 2.344, 0.609, 2.4, 0.609, 1, 2.489, 0.609, 2.578, -1.916, 2.667, -1.916, 1, 2.767, -1.916, 2.867, 2.932, 2.967, 2.932, 1, 3.078, 2.932, 3.189, -2.425, 3.3, -2.425, 1, 3.4, -2.425, 3.5, 1.612, 3.6, 1.612, 1, 3.711, 1.612, 3.822, -0.966, 3.933, -0.966, 1, 4.044, -0.966, 4.156, 0.539, 4.267, 0.539, 1, 4.356, 0.539, 4.444, 0.011, 4.533, -0.201]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.364, 0.2, -0.364, 1, 0.3, -0.364, 0.4, 0.554, 0.5, 0.554, 1, 0.6, 0.554, 0.7, -0.128, 0.8, -0.128, 1, 0.9, -0.128, 1, 0.185, 1.1, 0.185, 1, 1.211, 0.185, 1.322, 0.042, 1.433, 0.042, 1, 1.533, 0.042, 1.633, 0.108, 1.733, 0.108, 1, 1.844, 0.108, 1.956, 0.078, 2.067, 0.078, 1, 2.122, 0.078, 2.178, 0.087, 2.233, 0.087, 1, 2.311, 0.087, 2.389, -1.969, 2.467, -1.969, 1, 2.578, -1.969, 2.689, 2.474, 2.8, 2.474, 1, 2.9, 2.474, 3, -0.993, 3.1, -0.993, 1, 3.211, -0.993, 3.322, 0.587, 3.433, 0.587, 1, 3.533, 0.587, 3.633, -0.142, 3.733, -0.142, 1, 3.844, -0.142, 3.956, 0.192, 4.067, 0.192, 1, 4.167, 0.192, 4.267, 0.039, 4.367, 0.039, 1, 4.422, 0.039, 4.478, 0.061, 4.533, 0.08]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.364, 0.2, -0.364, 1, 0.3, -0.364, 0.4, 0.554, 0.5, 0.554, 1, 0.6, 0.554, 0.7, -0.128, 0.8, -0.128, 1, 0.9, -0.128, 1, 0.185, 1.1, 0.185, 1, 1.211, 0.185, 1.322, 0.042, 1.433, 0.042, 1, 1.533, 0.042, 1.633, 0.108, 1.733, 0.108, 1, 1.844, 0.108, 1.956, 0.078, 2.067, 0.078, 1, 2.122, 0.078, 2.178, 0.087, 2.233, 0.087, 1, 2.311, 0.087, 2.389, -1.969, 2.467, -1.969, 1, 2.578, -1.969, 2.689, 2.474, 2.8, 2.474, 1, 2.9, 2.474, 3, -0.993, 3.1, -0.993, 1, 3.211, -0.993, 3.322, 0.587, 3.433, 0.587, 1, 3.533, 0.587, 3.633, -0.142, 3.733, -0.142, 1, 3.844, -0.142, 3.956, 0.192, 4.067, 0.192, 1, 4.167, 0.192, 4.267, 0.039, 4.367, 0.039, 1, 4.422, 0.039, 4.478, 0.061, 4.533, 0.08]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.364, 0.2, -0.364, 1, 0.3, -0.364, 0.4, 0.554, 0.5, 0.554, 1, 0.6, 0.554, 0.7, -0.128, 0.8, -0.128, 1, 0.9, -0.128, 1, 0.185, 1.1, 0.185, 1, 1.211, 0.185, 1.322, 0.042, 1.433, 0.042, 1, 1.533, 0.042, 1.633, 0.108, 1.733, 0.108, 1, 1.844, 0.108, 1.956, 0.078, 2.067, 0.078, 1, 2.122, 0.078, 2.178, 0.087, 2.233, 0.087, 1, 2.311, 0.087, 2.389, -1.969, 2.467, -1.969, 1, 2.578, -1.969, 2.689, 2.474, 2.8, 2.474, 1, 2.9, 2.474, 3, -0.993, 3.1, -0.993, 1, 3.211, -0.993, 3.322, 0.587, 3.433, 0.587, 1, 3.533, 0.587, 3.633, -0.142, 3.733, -0.142, 1, 3.844, -0.142, 3.956, 0.192, 4.067, 0.192, 1, 4.167, 0.192, 4.267, 0.039, 4.367, 0.039, 1, 4.422, 0.039, 4.478, 0.061, 4.533, 0.08]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.262, 0.2, -0.262, 1, 0.3, -0.262, 0.4, 0.399, 0.5, 0.399, 1, 0.6, 0.399, 0.7, -0.092, 0.8, -0.092, 1, 0.9, -0.092, 1, 0.133, 1.1, 0.133, 1, 1.211, 0.133, 1.322, 0.03, 1.433, 0.03, 1, 1.533, 0.03, 1.633, 0.078, 1.733, 0.078, 1, 1.844, 0.078, 1.956, 0.056, 2.067, 0.056, 1, 2.122, 0.056, 2.178, 0.063, 2.233, 0.063, 1, 2.311, 0.063, 2.389, -1.418, 2.467, -1.418, 1, 2.578, -1.418, 2.689, 1.782, 2.8, 1.782, 1, 2.9, 1.782, 3, -0.715, 3.1, -0.715, 1, 3.211, -0.715, 3.322, 0.423, 3.433, 0.423, 1, 3.533, 0.423, 3.633, -0.102, 3.733, -0.102, 1, 3.844, -0.102, 3.956, 0.138, 4.067, 0.138, 1, 4.167, 0.138, 4.267, 0.028, 4.367, 0.028, 1, 4.422, 0.028, 4.478, 0.044, 4.533, 0.058]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0.057, 1, 0.056, 0.057, 0.111, 0.239, 0.167, 0.239, 1, 0.244, 0.239, 0.322, -0.518, 0.4, -0.518, 1, 0.489, -0.518, 0.578, 0.594, 0.667, 0.594, 1, 0.778, 0.594, 0.889, -0.473, 1, -0.473, 1, 1.1, -0.473, 1.2, 0.311, 1.3, 0.311, 1, 1.411, 0.311, 1.522, -0.184, 1.633, -0.184, 1, 1.744, -0.184, 1.856, 0.102, 1.967, 0.102, 1, 2.056, 0.102, 2.144, -0.048, 2.233, -0.048, 1, 2.289, -0.048, 2.344, 0.609, 2.4, 0.609, 1, 2.489, 0.609, 2.578, -1.916, 2.667, -1.916, 1, 2.767, -1.916, 2.867, 2.932, 2.967, 2.932, 1, 3.078, 2.932, 3.189, -2.425, 3.3, -2.425, 1, 3.4, -2.425, 3.5, 1.612, 3.6, 1.612, 1, 3.711, 1.612, 3.822, -0.966, 3.933, -0.966, 1, 4.044, -0.966, 4.156, 0.539, 4.267, 0.539, 1, 4.356, 0.539, 4.444, 0.011, 4.533, -0.201]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.364, 0.2, -0.364, 1, 0.3, -0.364, 0.4, 0.554, 0.5, 0.554, 1, 0.6, 0.554, 0.7, -0.128, 0.8, -0.128, 1, 0.9, -0.128, 1, 0.185, 1.1, 0.185, 1, 1.211, 0.185, 1.322, 0.042, 1.433, 0.042, 1, 1.533, 0.042, 1.633, 0.108, 1.733, 0.108, 1, 1.844, 0.108, 1.956, 0.078, 2.067, 0.078, 1, 2.122, 0.078, 2.178, 0.087, 2.233, 0.087, 1, 2.311, 0.087, 2.389, -1.969, 2.467, -1.969, 1, 2.578, -1.969, 2.689, 2.474, 2.8, 2.474, 1, 2.9, 2.474, 3, -0.993, 3.1, -0.993, 1, 3.211, -0.993, 3.322, 0.587, 3.433, 0.587, 1, 3.533, 0.587, 3.633, -0.142, 3.733, -0.142, 1, 3.844, -0.142, 3.956, 0.192, 4.067, 0.192, 1, 4.167, 0.192, 4.267, 0.039, 4.367, 0.039, 1, 4.422, 0.039, 4.478, 0.061, 4.533, 0.08]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -0.743, 0.3, -0.743, 1, 0.467, -0.743, 0.633, 0.399, 0.8, 0.399, 1, 0.989, 0.399, 1.178, -0.004, 1.367, -0.004, 1, 1.567, -0.004, 1.767, 0.114, 1.967, 0.114, 1, 2.178, 0.114, 2.389, -3.921, 2.6, -3.921, 1, 2.756, -3.921, 2.911, 2.087, 3.067, 2.087, 1, 3.256, 2.087, 3.444, -0.498, 3.633, -0.498, 1, 3.833, -0.498, 4.033, 0.259, 4.233, 0.259, 1, 4.333, 0.259, 4.433, 0.196, 4.533, 0.138]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 0.629, 0.267, 0.629, 1, 0.367, 0.629, 0.467, -0.116, 0.567, -0.116, 1, 0.644, -0.116, 0.722, 0.012, 0.8, 0.012, 1, 0.9, 0.012, 1, -0.116, 1.1, -0.116, 1, 1.233, -0.116, 1.367, 0.075, 1.5, 0.075, 1, 1.633, 0.075, 1.767, -0.023, 1.9, -0.023, 1, 2.111, -0.023, 2.322, 2.78, 2.533, 2.78, 1, 2.656, 2.78, 2.778, -0.619, 2.9, -0.619, 1, 2.978, -0.619, 3.056, -0.306, 3.133, -0.306, 1, 3.211, -0.306, 3.289, -0.537, 3.367, -0.537, 1, 3.511, -0.537, 3.656, 0.388, 3.8, 0.388, 1, 3.944, 0.388, 4.089, -0.127, 4.233, -0.127, 1, 4.333, -0.127, 4.433, -0.062, 4.533, -0.022]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.03, 1, 0.111, -0.03, 0.222, 0.348, 0.333, 0.348, 1, 0.433, 0.348, 0.533, -0.446, 0.633, -0.446, 1, 0.744, -0.446, 0.856, 0.451, 0.967, 0.451, 1, 1.078, 0.451, 1.189, -0.323, 1.3, -0.323, 1, 1.422, -0.323, 1.544, 0.184, 1.667, 0.184, 1, 1.778, 0.184, 1.889, -0.095, 2, -0.095, 1, 2.178, -0.095, 2.356, 0.472, 2.533, 0.472, 1, 2.644, 0.472, 2.756, -1.366, 2.867, -1.366, 1, 2.989, -1.366, 3.111, 1.766, 3.233, 1.766, 1, 3.356, 1.766, 3.478, -1.354, 3.6, -1.354, 1, 3.722, -1.354, 3.844, 0.758, 3.967, 0.758, 1, 4.078, 0.758, 4.189, -0.377, 4.3, -0.377, 1, 4.378, -0.377, 4.456, -0.093, 4.533, 0.078]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0.431, 1, 0.078, 0.27, 0.156, -0.373, 0.233, -0.373, 1, 0.322, -0.373, 0.411, 0.36, 0.5, 0.36, 1, 0.6, 0.36, 0.7, -0.596, 0.8, -0.596, 1, 0.9, -0.596, 1, 0.792, 1.1, 0.792, 1, 1.211, 0.792, 1.322, -0.731, 1.433, -0.731, 1, 1.556, -0.731, 1.678, 0.559, 1.8, 0.559, 1, 1.911, 0.559, 2.022, -0.372, 2.133, -0.372, 1, 2.289, -0.372, 2.444, 0.602, 2.6, 0.602, 1, 2.733, 0.602, 2.867, -1.905, 3, -1.905, 1, 3.122, -1.905, 3.244, 2.984, 3.367, 2.984, 1, 3.478, 2.984, 3.589, -2.984, 3.7, -2.984, 1, 3.822, -2.984, 3.944, 2.273, 4.067, 2.273, 1, 4.189, 2.273, 4.311, -1.481, 4.433, -1.481, 1, 4.467, -1.481, 4.5, -1.264, 4.533, -0.96]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -0.743, 0.3, -0.743, 1, 0.467, -0.743, 0.633, 0.399, 0.8, 0.399, 1, 0.989, 0.399, 1.178, -0.004, 1.367, -0.004, 1, 1.567, -0.004, 1.767, 0.114, 1.967, 0.114, 1, 2.178, 0.114, 2.389, -3.921, 2.6, -3.921, 1, 2.756, -3.921, 2.911, 2.087, 3.067, 2.087, 1, 3.256, 2.087, 3.444, -0.498, 3.633, -0.498, 1, 3.833, -0.498, 4.033, 0.259, 4.233, 0.259, 1, 4.333, 0.259, 4.433, 0.196, 4.533, 0.138]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 0.629, 0.267, 0.629, 1, 0.367, 0.629, 0.467, -0.116, 0.567, -0.116, 1, 0.644, -0.116, 0.722, 0.012, 0.8, 0.012, 1, 0.9, 0.012, 1, -0.116, 1.1, -0.116, 1, 1.233, -0.116, 1.367, 0.075, 1.5, 0.075, 1, 1.633, 0.075, 1.767, -0.023, 1.9, -0.023, 1, 2.111, -0.023, 2.322, 2.78, 2.533, 2.78, 1, 2.656, 2.78, 2.778, -0.619, 2.9, -0.619, 1, 2.978, -0.619, 3.056, -0.306, 3.133, -0.306, 1, 3.211, -0.306, 3.289, -0.537, 3.367, -0.537, 1, 3.511, -0.537, 3.656, 0.388, 3.8, 0.388, 1, 3.944, 0.388, 4.089, -0.127, 4.233, -0.127, 1, 4.333, -0.127, 4.433, -0.062, 4.533, -0.022]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.03, 1, 0.111, -0.03, 0.222, 0.348, 0.333, 0.348, 1, 0.433, 0.348, 0.533, -0.446, 0.633, -0.446, 1, 0.744, -0.446, 0.856, 0.451, 0.967, 0.451, 1, 1.078, 0.451, 1.189, -0.323, 1.3, -0.323, 1, 1.422, -0.323, 1.544, 0.184, 1.667, 0.184, 1, 1.778, 0.184, 1.889, -0.095, 2, -0.095, 1, 2.178, -0.095, 2.356, 0.472, 2.533, 0.472, 1, 2.644, 0.472, 2.756, -1.366, 2.867, -1.366, 1, 2.989, -1.366, 3.111, 1.766, 3.233, 1.766, 1, 3.356, 1.766, 3.478, -1.354, 3.6, -1.354, 1, 3.722, -1.354, 3.844, 0.758, 3.967, 0.758, 1, 4.078, 0.758, 4.189, -0.377, 4.3, -0.377, 1, 4.378, -0.377, 4.456, -0.093, 4.533, 0.078]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -0.743, 0.3, -0.743, 1, 0.467, -0.743, 0.633, 0.399, 0.8, 0.399, 1, 0.989, 0.399, 1.178, -0.004, 1.367, -0.004, 1, 1.567, -0.004, 1.767, 0.114, 1.967, 0.114, 1, 2.178, 0.114, 2.389, -3.921, 2.6, -3.921, 1, 2.756, -3.921, 2.911, 2.087, 3.067, 2.087, 1, 3.256, 2.087, 3.444, -0.498, 3.633, -0.498, 1, 3.833, -0.498, 4.033, 0.259, 4.233, 0.259, 1, 4.333, 0.259, 4.433, 0.196, 4.533, 0.138]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 0.629, 0.267, 0.629, 1, 0.367, 0.629, 0.467, -0.116, 0.567, -0.116, 1, 0.644, -0.116, 0.722, 0.012, 0.8, 0.012, 1, 0.9, 0.012, 1, -0.116, 1.1, -0.116, 1, 1.233, -0.116, 1.367, 0.075, 1.5, 0.075, 1, 1.633, 0.075, 1.767, -0.023, 1.9, -0.023, 1, 2.111, -0.023, 2.322, 2.78, 2.533, 2.78, 1, 2.656, 2.78, 2.778, -0.619, 2.9, -0.619, 1, 2.978, -0.619, 3.056, -0.306, 3.133, -0.306, 1, 3.211, -0.306, 3.289, -0.537, 3.367, -0.537, 1, 3.511, -0.537, 3.656, 0.388, 3.8, 0.388, 1, 3.944, 0.388, 4.089, -0.127, 4.233, -0.127, 1, 4.333, -0.127, 4.433, -0.062, 4.533, -0.022]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, -0.03, 1, 0.111, -0.03, 0.222, 0.348, 0.333, 0.348, 1, 0.433, 0.348, 0.533, -0.446, 0.633, -0.446, 1, 0.744, -0.446, 0.856, 0.451, 0.967, 0.451, 1, 1.078, 0.451, 1.189, -0.323, 1.3, -0.323, 1, 1.422, -0.323, 1.544, 0.184, 1.667, 0.184, 1, 1.778, 0.184, 1.889, -0.095, 2, -0.095, 1, 2.178, -0.095, 2.356, 0.472, 2.533, 0.472, 1, 2.644, 0.472, 2.756, -1.366, 2.867, -1.366, 1, 2.989, -1.366, 3.111, 1.766, 3.233, 1.766, 1, 3.356, 1.766, 3.478, -1.354, 3.6, -1.354, 1, 3.722, -1.354, 3.844, 0.758, 3.967, 0.758, 1, 4.078, 0.758, 4.189, -0.377, 4.3, -0.377, 1, 4.378, -0.377, 4.456, -0.093, 4.533, 0.078]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, 0.431, 1, 0.078, 0.27, 0.156, -0.373, 0.233, -0.373, 1, 0.322, -0.373, 0.411, 0.36, 0.5, 0.36, 1, 0.6, 0.36, 0.7, -0.596, 0.8, -0.596, 1, 0.9, -0.596, 1, 0.792, 1.1, 0.792, 1, 1.211, 0.792, 1.322, -0.731, 1.433, -0.731, 1, 1.556, -0.731, 1.678, 0.559, 1.8, 0.559, 1, 1.911, 0.559, 2.022, -0.372, 2.133, -0.372, 1, 2.289, -0.372, 2.444, 0.602, 2.6, 0.602, 1, 2.733, 0.602, 2.867, -1.905, 3, -1.905, 1, 3.122, -1.905, 3.244, 2.984, 3.367, 2.984, 1, 3.478, 2.984, 3.589, -2.984, 3.7, -2.984, 1, 3.822, -2.984, 3.944, 2.273, 4.067, 2.273, 1, 4.189, 2.273, 4.311, -1.481, 4.433, -1.481, 1, 4.467, -1.481, 4.5, -1.264, 4.533, -0.96]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -0.743, 0.3, -0.743, 1, 0.467, -0.743, 0.633, 0.399, 0.8, 0.399, 1, 0.989, 0.399, 1.178, -0.004, 1.367, -0.004, 1, 1.567, -0.004, 1.767, 0.114, 1.967, 0.114, 1, 2.178, 0.114, 2.389, -3.921, 2.6, -3.921, 1, 2.756, -3.921, 2.911, 2.087, 3.067, 2.087, 1, 3.256, 2.087, 3.444, -0.498, 3.633, -0.498, 1, 3.833, -0.498, 4.033, 0.259, 4.233, 0.259, 1, 4.333, 0.259, 4.433, 0.196, 4.533, 0.138]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 0.629, 0.267, 0.629, 1, 0.367, 0.629, 0.467, -0.116, 0.567, -0.116, 1, 0.644, -0.116, 0.722, 0.012, 0.8, 0.012, 1, 0.9, 0.012, 1, -0.116, 1.1, -0.116, 1, 1.233, -0.116, 1.367, 0.075, 1.5, 0.075, 1, 1.633, 0.075, 1.767, -0.023, 1.9, -0.023, 1, 2.111, -0.023, 2.322, 2.78, 2.533, 2.78, 1, 2.656, 2.78, 2.778, -0.619, 2.9, -0.619, 1, 2.978, -0.619, 3.056, -0.306, 3.133, -0.306, 1, 3.211, -0.306, 3.289, -0.537, 3.367, -0.537, 1, 3.511, -0.537, 3.656, 0.388, 3.8, 0.388, 1, 3.944, 0.388, 4.089, -0.127, 4.233, -0.127, 1, 4.333, -0.127, 4.433, -0.062, 4.533, -0.022]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, -0.03, 1, 0.111, -0.03, 0.222, 0.348, 0.333, 0.348, 1, 0.433, 0.348, 0.533, -0.446, 0.633, -0.446, 1, 0.744, -0.446, 0.856, 0.451, 0.967, 0.451, 1, 1.078, 0.451, 1.189, -0.323, 1.3, -0.323, 1, 1.422, -0.323, 1.544, 0.184, 1.667, 0.184, 1, 1.778, 0.184, 1.889, -0.095, 2, -0.095, 1, 2.178, -0.095, 2.356, 0.472, 2.533, 0.472, 1, 2.644, 0.472, 2.756, -1.366, 2.867, -1.366, 1, 2.989, -1.366, 3.111, 1.766, 3.233, 1.766, 1, 3.356, 1.766, 3.478, -1.354, 3.6, -1.354, 1, 3.722, -1.354, 3.844, 0.758, 3.967, 0.758, 1, 4.078, 0.758, 4.189, -0.377, 4.3, -0.377, 1, 4.378, -0.377, 4.456, -0.093, 4.533, 0.078]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -0.743, 0.3, -0.743, 1, 0.467, -0.743, 0.633, 0.399, 0.8, 0.399, 1, 0.989, 0.399, 1.178, -0.004, 1.367, -0.004, 1, 1.567, -0.004, 1.767, 0.114, 1.967, 0.114, 1, 2.178, 0.114, 2.389, -3.921, 2.6, -3.921, 1, 2.756, -3.921, 2.911, 2.087, 3.067, 2.087, 1, 3.256, 2.087, 3.444, -0.498, 3.633, -0.498, 1, 3.833, -0.498, 4.033, 0.259, 4.233, 0.259, 1, 4.333, 0.259, 4.433, 0.196, 4.533, 0.138]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 0.629, 0.267, 0.629, 1, 0.367, 0.629, 0.467, -0.116, 0.567, -0.116, 1, 0.644, -0.116, 0.722, 0.012, 0.8, 0.012, 1, 0.9, 0.012, 1, -0.116, 1.1, -0.116, 1, 1.233, -0.116, 1.367, 0.075, 1.5, 0.075, 1, 1.633, 0.075, 1.767, -0.023, 1.9, -0.023, 1, 2.111, -0.023, 2.322, 2.78, 2.533, 2.78, 1, 2.656, 2.78, 2.778, -0.619, 2.9, -0.619, 1, 2.978, -0.619, 3.056, -0.306, 3.133, -0.306, 1, 3.211, -0.306, 3.289, -0.537, 3.367, -0.537, 1, 3.511, -0.537, 3.656, 0.388, 3.8, 0.388, 1, 3.944, 0.388, 4.089, -0.127, 4.233, -0.127, 1, 4.333, -0.127, 4.433, -0.062, 4.533, -0.022]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, -0.03, 1, 0.111, -0.03, 0.222, 0.348, 0.333, 0.348, 1, 0.433, 0.348, 0.533, -0.446, 0.633, -0.446, 1, 0.744, -0.446, 0.856, 0.451, 0.967, 0.451, 1, 1.078, 0.451, 1.189, -0.323, 1.3, -0.323, 1, 1.422, -0.323, 1.544, 0.184, 1.667, 0.184, 1, 1.778, 0.184, 1.889, -0.095, 2, -0.095, 1, 2.178, -0.095, 2.356, 0.472, 2.533, 0.472, 1, 2.644, 0.472, 2.756, -1.366, 2.867, -1.366, 1, 2.989, -1.366, 3.111, 1.766, 3.233, 1.766, 1, 3.356, 1.766, 3.478, -1.354, 3.6, -1.354, 1, 3.722, -1.354, 3.844, 0.758, 3.967, 0.758, 1, 4.078, 0.758, 4.189, -0.377, 4.3, -0.377, 1, 4.378, -0.377, 4.456, -0.093, 4.533, 0.078]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -0.743, 0.3, -0.743, 1, 0.467, -0.743, 0.633, 0.399, 0.8, 0.399, 1, 0.989, 0.399, 1.178, -0.004, 1.367, -0.004, 1, 1.567, -0.004, 1.767, 0.114, 1.967, 0.114, 1, 2.178, 0.114, 2.389, -3.921, 2.6, -3.921, 1, 2.756, -3.921, 2.911, 2.087, 3.067, 2.087, 1, 3.256, 2.087, 3.444, -0.498, 3.633, -0.498, 1, 3.833, -0.498, 4.033, 0.259, 4.233, 0.259, 1, 4.333, 0.259, 4.433, 0.196, 4.533, 0.138]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 0.629, 0.267, 0.629, 1, 0.367, 0.629, 0.467, -0.116, 0.567, -0.116, 1, 0.644, -0.116, 0.722, 0.012, 0.8, 0.012, 1, 0.9, 0.012, 1, -0.116, 1.1, -0.116, 1, 1.233, -0.116, 1.367, 0.075, 1.5, 0.075, 1, 1.633, 0.075, 1.767, -0.023, 1.9, -0.023, 1, 2.111, -0.023, 2.322, 2.78, 2.533, 2.78, 1, 2.656, 2.78, 2.778, -0.619, 2.9, -0.619, 1, 2.978, -0.619, 3.056, -0.306, 3.133, -0.306, 1, 3.211, -0.306, 3.289, -0.537, 3.367, -0.537, 1, 3.511, -0.537, 3.656, 0.388, 3.8, 0.388, 1, 3.944, 0.388, 4.089, -0.127, 4.233, -0.127, 1, 4.333, -0.127, 4.433, -0.062, 4.533, -0.022]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, -0.03, 1, 0.111, -0.03, 0.222, 0.348, 0.333, 0.348, 1, 0.433, 0.348, 0.533, -0.446, 0.633, -0.446, 1, 0.744, -0.446, 0.856, 0.451, 0.967, 0.451, 1, 1.078, 0.451, 1.189, -0.323, 1.3, -0.323, 1, 1.422, -0.323, 1.544, 0.184, 1.667, 0.184, 1, 1.778, 0.184, 1.889, -0.095, 2, -0.095, 1, 2.178, -0.095, 2.356, 0.472, 2.533, 0.472, 1, 2.644, 0.472, 2.756, -1.366, 2.867, -1.366, 1, 2.989, -1.366, 3.111, 1.766, 3.233, 1.766, 1, 3.356, 1.766, 3.478, -1.354, 3.6, -1.354, 1, 3.722, -1.354, 3.844, 0.758, 3.967, 0.758, 1, 4.078, 0.758, 4.189, -0.377, 4.3, -0.377, 1, 4.378, -0.377, 4.456, -0.093, 4.533, 0.078]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.267, 0.033, -0.267, 1, 0.1, -0.267, 0.167, 0.245, 0.233, 0.245, 1, 0.333, 0.245, 0.433, -0.123, 0.533, -0.123, 1, 0.633, -0.123, 0.733, 0.032, 0.833, 0.032, 1, 0.944, 0.032, 1.056, -0.009, 1.167, -0.009, 1, 1.278, -0.009, 1.389, 0.002, 1.5, 0.002, 1, 1.6, 0.002, 1.7, -0.001, 1.8, -0.001, 1, 1.911, -0.001, 2.022, 0, 2.133, 0, 1, 2.256, 0, 2.378, -1.481, 2.5, -1.481, 1, 2.611, -1.481, 2.722, 0.86, 2.833, 0.86, 1, 2.944, 0.86, 3.056, -0.224, 3.167, -0.224, 1, 3.267, -0.224, 3.367, 0.059, 3.467, 0.059, 1, 3.578, 0.059, 3.689, -0.016, 3.8, -0.016, 1, 3.911, -0.016, 4.022, 0.004, 4.133, 0.004, 1, 4.233, 0.004, 4.333, -0.001, 4.433, -0.001, 1, 4.467, -0.001, 4.5, -0.001, 4.533, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.265, 0.033, 0.265, 1, 0.089, 0.265, 0.144, -0.244, 0.2, -0.244, 1, 0.278, -0.244, 0.356, 0.346, 0.433, 0.346, 1, 0.522, 0.346, 0.611, -0.172, 0.7, -0.172, 1, 0.811, -0.172, 0.922, 0.068, 1.033, 0.068, 1, 1.133, 0.068, 1.233, -0.024, 1.333, -0.024, 1, 1.433, -0.024, 1.533, 0.008, 1.633, 0.008, 1, 1.744, 0.008, 1.856, -0.002, 1.967, -0.002, 1, 2.111, -0.002, 2.256, 0.717, 2.4, 0.717, 1, 2.511, 0.717, 2.622, -1.376, 2.733, -1.376, 1, 2.822, -1.376, 2.911, 1.023, 3, 1.023, 1, 3.1, 1.023, 3.2, -0.442, 3.3, -0.442, 1, 3.411, -0.442, 3.522, 0.16, 3.633, 0.16, 1, 3.744, 0.16, 3.856, -0.051, 3.967, -0.051, 1, 4.067, -0.051, 4.167, 0.016, 4.267, 0.016, 1, 4.356, 0.016, 4.444, 0.003, 4.533, -0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0.012, 1, 0.033, 0.034, 0.067, 0.078, 0.1, 0.078, 1, 0.167, 0.078, 0.233, -0.24, 0.3, -0.24, 1, 0.389, -0.24, 0.478, 0.382, 0.567, 0.382, 1, 0.667, 0.382, 0.767, -0.265, 0.867, -0.265, 1, 0.978, -0.265, 1.089, 0.125, 1.2, 0.125, 1, 1.311, 0.125, 1.422, -0.047, 1.533, -0.047, 1, 1.633, -0.047, 1.733, 0.016, 1.833, 0.016, 1, 1.944, 0.016, 2.056, -0.005, 2.167, -0.005, 1, 2.278, -0.005, 2.389, 0.692, 2.5, 0.692, 1, 2.611, 0.692, 2.722, -2.27, 2.833, -2.27, 1, 2.944, -2.27, 3.056, 1.752, 3.167, 1.752, 1, 3.267, 1.752, 3.367, -0.851, 3.467, -0.851, 1, 3.578, -0.851, 3.689, 0.335, 3.8, 0.335, 1, 3.911, 0.335, 4.022, -0.116, 4.133, -0.116, 1, 4.244, -0.116, 4.356, 0.037, 4.467, 0.037, 1, 4.489, 0.037, 4.511, 0.035, 4.533, 0.032]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, -0.001, 1, 0.056, -0.001, 0.111, 0.086, 0.167, 0.086, 1, 0.256, 0.086, 0.344, -0.244, 0.433, -0.244, 1, 0.533, -0.244, 0.633, 0.39, 0.733, 0.39, 1, 0.833, 0.39, 0.933, -0.309, 1.033, -0.309, 1, 1.144, -0.309, 1.256, 0.165, 1.367, 0.165, 1, 1.478, 0.165, 1.589, -0.071, 1.7, -0.071, 1, 1.811, -0.071, 1.922, 0.026, 2.033, 0.026, 1, 2.111, 0.026, 2.189, -0.004, 2.267, -0.004, 1, 2.389, -0.004, 2.511, 1.147, 2.633, 1.147, 1, 2.744, 1.147, 2.856, -2.436, 2.967, -2.436, 1, 3.078, -2.436, 3.189, 2.089, 3.3, 2.089, 1, 3.411, 2.089, 3.522, -1.165, 3.633, -1.165, 1, 3.744, -1.165, 3.856, 0.512, 3.967, 0.512, 1, 4.078, 0.512, 4.189, -0.193, 4.3, -0.193, 1, 4.378, -0.193, 4.456, -0.066, 4.533, 0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0.122, 1, 0.078, 0.138, 0.156, 0.2, 0.233, 0.2, 1, 0.333, 0.2, 0.433, -0.501, 0.533, -0.501, 1, 0.633, -0.501, 0.733, 0.514, 0.833, 0.514, 1, 0.944, 0.514, 1.056, -0.392, 1.167, -0.392, 1, 1.278, -0.392, 1.389, 0.228, 1.5, 0.228, 1, 1.611, 0.228, 1.722, -0.107, 1.833, -0.107, 1, 1.944, -0.107, 2.056, 0.044, 2.167, 0.044, 1, 2.222, 0.044, 2.278, 0.016, 2.333, 0.016, 1, 2.467, 0.016, 2.6, 1.201, 2.733, 1.201, 1, 2.844, 1.201, 2.956, -2.75, 3.067, -2.75, 1, 3.189, -2.75, 3.311, 2.607, 3.433, 2.607, 1, 3.544, 2.607, 3.656, -1.612, 3.767, -1.612, 1, 3.889, -1.612, 4.011, 0.776, 4.133, 0.776, 1, 4.244, 0.776, 4.356, -0.323, 4.467, -0.323, 1, 4.489, -0.323, 4.511, -0.305, 4.533, -0.277]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.267, 0.033, -0.267, 1, 0.1, -0.267, 0.167, 0.245, 0.233, 0.245, 1, 0.333, 0.245, 0.433, -0.123, 0.533, -0.123, 1, 0.633, -0.123, 0.733, 0.032, 0.833, 0.032, 1, 0.944, 0.032, 1.056, -0.009, 1.167, -0.009, 1, 1.278, -0.009, 1.389, 0.002, 1.5, 0.002, 1, 1.6, 0.002, 1.7, -0.001, 1.8, -0.001, 1, 1.911, -0.001, 2.022, 0, 2.133, 0, 1, 2.256, 0, 2.378, -1.481, 2.5, -1.481, 1, 2.611, -1.481, 2.722, 0.86, 2.833, 0.86, 1, 2.944, 0.86, 3.056, -0.224, 3.167, -0.224, 1, 3.267, -0.224, 3.367, 0.059, 3.467, 0.059, 1, 3.578, 0.059, 3.689, -0.016, 3.8, -0.016, 1, 3.911, -0.016, 4.022, 0.004, 4.133, 0.004, 1, 4.233, 0.004, 4.333, -0.001, 4.433, -0.001, 1, 4.467, -0.001, 4.5, -0.001, 4.533, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.265, 0.033, 0.265, 1, 0.089, 0.265, 0.144, -0.244, 0.2, -0.244, 1, 0.278, -0.244, 0.356, 0.346, 0.433, 0.346, 1, 0.522, 0.346, 0.611, -0.172, 0.7, -0.172, 1, 0.811, -0.172, 0.922, 0.068, 1.033, 0.068, 1, 1.133, 0.068, 1.233, -0.024, 1.333, -0.024, 1, 1.433, -0.024, 1.533, 0.008, 1.633, 0.008, 1, 1.744, 0.008, 1.856, -0.002, 1.967, -0.002, 1, 2.111, -0.002, 2.256, 0.717, 2.4, 0.717, 1, 2.511, 0.717, 2.622, -1.376, 2.733, -1.376, 1, 2.822, -1.376, 2.911, 1.023, 3, 1.023, 1, 3.1, 1.023, 3.2, -0.442, 3.3, -0.442, 1, 3.411, -0.442, 3.522, 0.16, 3.633, 0.16, 1, 3.744, 0.16, 3.856, -0.051, 3.967, -0.051, 1, 4.067, -0.051, 4.167, 0.016, 4.267, 0.016, 1, 4.356, 0.016, 4.444, 0.003, 4.533, -0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0.012, 1, 0.033, 0.034, 0.067, 0.078, 0.1, 0.078, 1, 0.167, 0.078, 0.233, -0.24, 0.3, -0.24, 1, 0.389, -0.24, 0.478, 0.382, 0.567, 0.382, 1, 0.667, 0.382, 0.767, -0.265, 0.867, -0.265, 1, 0.978, -0.265, 1.089, 0.125, 1.2, 0.125, 1, 1.311, 0.125, 1.422, -0.047, 1.533, -0.047, 1, 1.633, -0.047, 1.733, 0.016, 1.833, 0.016, 1, 1.944, 0.016, 2.056, -0.005, 2.167, -0.005, 1, 2.278, -0.005, 2.389, 0.692, 2.5, 0.692, 1, 2.611, 0.692, 2.722, -2.27, 2.833, -2.27, 1, 2.944, -2.27, 3.056, 1.752, 3.167, 1.752, 1, 3.267, 1.752, 3.367, -0.851, 3.467, -0.851, 1, 3.578, -0.851, 3.689, 0.335, 3.8, 0.335, 1, 3.911, 0.335, 4.022, -0.116, 4.133, -0.116, 1, 4.244, -0.116, 4.356, 0.037, 4.467, 0.037, 1, 4.489, 0.037, 4.511, 0.035, 4.533, 0.032]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, -0.001, 1, 0.056, -0.001, 0.111, 0.086, 0.167, 0.086, 1, 0.256, 0.086, 0.344, -0.244, 0.433, -0.244, 1, 0.533, -0.244, 0.633, 0.39, 0.733, 0.39, 1, 0.833, 0.39, 0.933, -0.309, 1.033, -0.309, 1, 1.144, -0.309, 1.256, 0.165, 1.367, 0.165, 1, 1.478, 0.165, 1.589, -0.071, 1.7, -0.071, 1, 1.811, -0.071, 1.922, 0.026, 2.033, 0.026, 1, 2.111, 0.026, 2.189, -0.004, 2.267, -0.004, 1, 2.389, -0.004, 2.511, 1.147, 2.633, 1.147, 1, 2.744, 1.147, 2.856, -2.436, 2.967, -2.436, 1, 3.078, -2.436, 3.189, 2.089, 3.3, 2.089, 1, 3.411, 2.089, 3.522, -1.165, 3.633, -1.165, 1, 3.744, -1.165, 3.856, 0.512, 3.967, 0.512, 1, 4.078, 0.512, 4.189, -0.193, 4.3, -0.193, 1, 4.378, -0.193, 4.456, -0.066, 4.533, 0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0.122, 1, 0.078, 0.138, 0.156, 0.2, 0.233, 0.2, 1, 0.333, 0.2, 0.433, -0.501, 0.533, -0.501, 1, 0.633, -0.501, 0.733, 0.514, 0.833, 0.514, 1, 0.944, 0.514, 1.056, -0.392, 1.167, -0.392, 1, 1.278, -0.392, 1.389, 0.228, 1.5, 0.228, 1, 1.611, 0.228, 1.722, -0.107, 1.833, -0.107, 1, 1.944, -0.107, 2.056, 0.044, 2.167, 0.044, 1, 2.222, 0.044, 2.278, 0.016, 2.333, 0.016, 1, 2.467, 0.016, 2.6, 1.201, 2.733, 1.201, 1, 2.844, 1.201, 2.956, -2.75, 3.067, -2.75, 1, 3.189, -2.75, 3.311, 2.607, 3.433, 2.607, 1, 3.544, 2.607, 3.656, -1.612, 3.767, -1.612, 1, 3.889, -1.612, 4.011, 0.776, 4.133, 0.776, 1, 4.244, 0.776, 4.356, -0.323, 4.467, -0.323, 1, 4.489, -0.323, 4.511, -0.305, 4.533, -0.277]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 1.511, 0, 3.022, 0, 4.533, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, -30, 1, 1.511, -30, 3.022, -30, 4.533, -30]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, -30, 1, 1.511, -30, 3.022, -30, 4.533, -30]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 0, 0, 4.53, 0]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 4.53, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 4.53, 1]}]}