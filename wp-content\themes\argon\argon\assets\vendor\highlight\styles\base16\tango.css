/*!
  Theme: Tango
  Author: @<PERSON><PERSON><PERSON><PERSON>, based on the Tango Desktop Project
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme tango
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #2e3436  Default Background
base01  #8ae234  Lighter Background (Used for status bars, line number and folding marks)
base02  #fce94f  Selection Background
base03  #555753  Comments, Invisibles, Line Highlighting
base04  #729fcf  Dark Foreground (Used for status bars)
base05  #d3d7cf  Default Foreground, Caret, Delimiters, Operators
base06  #ad7fa8  Light Foreground (Not often used)
base07  #eeeeec  Light Background (Not often used)
base08  #cc0000  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ef2929  Integers, Bo<PERSON>an, Constants, XML Attributes, Markup Link Url
base0A  #c4a000  Classes, Markup Bold, Search Text Background
base0B  #4e9a06  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #06989a  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #3465a4  Functions, Methods, Attribute IDs, Headings
base0E  #75507b  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #34e2e2  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #d3d7cf;
  background: #2e3436;
}

.hljs::selection,
.hljs ::selection {
  background-color: #fce94f;
  color: #d3d7cf;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #555753 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #555753;
}

/* base04 - #729fcf -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #729fcf;
}

/* base05 - #d3d7cf -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #d3d7cf;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #cc0000;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ef2929;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #c4a000;
}

.hljs-strong {
  font-weight:bold;
  color: #c4a000;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #4e9a06;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #06989a;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #3465a4;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #75507b;
}
.hljs-emphasis {
  color: #75507b;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #34e2e2;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
