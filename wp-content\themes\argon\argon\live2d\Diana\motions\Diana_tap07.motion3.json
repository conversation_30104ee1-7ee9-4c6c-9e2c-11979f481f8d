{"Version": 3, "Meta": {"Duration": 7.067, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 50, "TotalSegmentCount": 712, "TotalPointCount": 2118, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param43", "Segments": [0, -0.265, 1, 0.089, -0.265, 0.178, 0.301, 0.267, 0.301, 1, 0.344, 0.301, 0.422, -0.162, 0.5, -0.162, 1, 0.622, -0.162, 0.744, 0.23, 0.867, 0.23, 1, 1, 0.23, 1.133, -0.078, 1.267, -0.078, 1, 1.356, -0.078, 1.444, 0.284, 1.533, 0.284, 1, 1.656, 0.284, 1.778, -0.198, 1.9, -0.198, 1, 2, -0.198, 2.1, 0.023, 2.2, 0.023, 1, 2.289, 0.023, 2.378, -1.31, 2.467, -1.31, 1, 2.522, -1.31, 2.578, 0.277, 2.633, 0.277, 1, 2.744, 0.277, 2.856, -5.981, 2.967, -5.981, 1, 3.1, -5.981, 3.233, 7.13, 3.367, 7.13, 1, 3.511, 7.13, 3.656, -6.636, 3.8, -6.636, 1, 3.922, -6.636, 4.044, 4.552, 4.167, 4.552, 1, 4.3, 4.552, 4.433, -0.425, 4.567, -0.425, 1, 4.678, -0.425, 4.789, 1.304, 4.9, 1.304, 1, 5.033, 1.304, 5.167, -0.173, 5.3, -0.173, 1, 5.4, -0.173, 5.5, 0.232, 5.6, 0.232, 1, 5.711, 0.232, 5.822, -0.027, 5.933, -0.027, 1, 6.044, -0.027, 6.156, 0.072, 6.267, 0.072, 1, 6.444, 0.072, 6.622, -0.014, 6.8, -0.014, 1, 6.889, -0.014, 6.978, -0.004, 7.067, 0.002]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, -30, 0, 2.267, -30, 1, 2.667, -30, 3.067, 30, 3.467, 30, 1, 3.978, 30, 4.489, 30, 5, 30, 1, 5.367, 30, 5.733, -30, 6.1, -30, 0, 7.067, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 1.522, 0, 1.978, -4, 2.433, -4, 1, 2.589, -4, 2.744, 5, 2.9, 5, 1, 3.056, 5, 3.211, -3, 3.367, -3, 1, 3.511, -3, 3.656, 3, 3.8, 3, 1, 3.978, 3, 4.156, 0, 4.333, 0, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.711, 0, 1.422, 0, 2.133, 0, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.711, 0, 1.422, 0, 2.133, 0, 1, 2.378, 0, 2.622, 5, 2.867, 5, 1, 4.144, 5, 5.422, -4, 6.7, -4, 0, 7.067, -4]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 0.2, 0, 1, 0.544, 0, 0.889, -2, 1.233, -2, 1, 1.556, -2, 1.878, 4, 2.2, 4, 1, 2.422, 4, 2.644, -1, 2.867, -1, 1, 3.089, -1, 3.311, 1, 3.533, 1, 1, 3.756, 1, 3.978, 0.234, 4.2, -1, 1, 4.456, -2.419, 4.711, -3, 4.967, -3, 1, 5.222, -3, 5.478, -1.721, 5.733, -1, 1, 6.056, -0.091, 6.378, 0, 6.7, 0, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 2, 0, 0.2, 2, 1, 0.544, 2, 0.889, -30, 1.233, -30, 1, 1.556, -30, 1.878, -22.694, 2.2, -16, 1, 2.422, -11.383, 2.644, -7.895, 2.867, -6, 1, 3.089, -4.105, 3.311, -4, 3.533, -4, 1, 3.756, -4, 3.978, -6, 4.2, -6, 1, 4.456, -6, 4.711, 6, 4.967, 6, 1, 5.222, 6, 5.478, -14, 5.733, -14, 1, 6.056, -14, 6.378, -4, 6.7, -4, 0, 7.067, -4]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 2.167, 0, 1, 2.4, 0, 2.633, 30, 2.867, 30, 1, 3.311, 30, 3.756, 30, 4.2, 30, 1, 4.456, 30, 4.711, 10.462, 4.967, 7, 1, 5.544, -0.827, 6.122, -2, 6.7, -2, 0, 7.067, -2]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.6, 0, 0.633, 30, 0.667, 30, 1, 0.7, 30, 0.733, 30, 0.767, 30, 1, 0.811, 30, 0.856, -30, 0.9, -30, 1, 0.956, -30, 1.011, -30, 1.067, -30, 1, 1.1, -30, 1.133, 30, 1.167, 30, 1, 1.2, 30, 1.233, 30, 1.267, 30, 1, 1.344, 30, 1.422, -15.591, 1.5, -15.591, 1, 1.578, -15.591, 1.656, 6.497, 1.733, 6.497, 1, 1.822, 6.497, 1.911, -2.643, 2, -2.643, 1, 2.078, -2.643, 2.156, 1.126, 2.233, 1.126, 1, 2.311, 1.126, 2.389, -0.459, 2.467, -0.459, 1, 2.6, -0.459, 2.733, 30, 2.867, 30, 1, 2.878, 30, 2.889, 30, 2.9, 30, 1, 2.978, 30, 3.056, -30, 3.133, -30, 1, 3.144, -30, 3.156, -30, 3.167, -30, 1, 3.244, -30, 3.322, 13.074, 3.4, 13.074, 1, 3.489, 13.074, 3.578, -5.35, 3.667, -5.35, 1, 3.744, -5.35, 3.822, 2.235, 3.9, 2.235, 1, 3.989, 2.235, 4.078, -0.927, 4.167, -0.927, 1, 4.2, -0.927, 4.233, -0.287, 4.267, -0.287, 1, 4.311, -0.287, 4.356, -30, 4.4, -30, 1, 4.422, -30, 4.444, -30, 4.467, -30, 1, 4.533, -30, 4.6, 30, 4.667, 30, 1, 4.689, 30, 4.711, 30, 4.733, 30, 1, 4.811, 30, 4.889, -13.712, 4.967, -13.712, 1, 5.044, -13.712, 5.122, 5.721, 5.2, 5.721, 1, 5.289, 5.721, 5.378, -2.337, 5.467, -2.337, 1, 5.544, -2.337, 5.622, 0.993, 5.7, 0.993, 1, 5.789, 0.993, 5.878, -0.406, 5.967, -0.406, 1, 6.044, -0.406, 6.122, 0.173, 6.2, 0.173, 1, 6.278, 0.173, 6.356, -0.07, 6.433, -0.07, 1, 6.522, -0.07, 6.611, 0.03, 6.7, 0.03, 1, 6.778, 0.03, 6.856, -0.012, 6.933, -0.012, 1, 6.978, -0.012, 7.022, -0.008, 7.067, -0.004]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.6, 0, 0.633, 30, 0.667, 30, 1, 0.7, 30, 0.733, 30, 0.767, 30, 1, 0.811, 30, 0.856, -30, 0.9, -30, 1, 0.956, -30, 1.011, -30, 1.067, -30, 1, 1.1, -30, 1.133, 30, 1.167, 30, 1, 1.2, 30, 1.233, 30, 1.267, 30, 1, 1.344, 30, 1.422, -15.591, 1.5, -15.591, 1, 1.578, -15.591, 1.656, 6.497, 1.733, 6.497, 1, 1.822, 6.497, 1.911, -2.643, 2, -2.643, 1, 2.078, -2.643, 2.156, 1.126, 2.233, 1.126, 1, 2.311, 1.126, 2.389, -0.459, 2.467, -0.459, 1, 2.6, -0.459, 2.733, 30, 2.867, 30, 1, 2.878, 30, 2.889, 30, 2.9, 30, 1, 2.978, 30, 3.056, -30, 3.133, -30, 1, 3.144, -30, 3.156, -30, 3.167, -30, 1, 3.244, -30, 3.322, 13.074, 3.4, 13.074, 1, 3.489, 13.074, 3.578, -5.35, 3.667, -5.35, 1, 3.744, -5.35, 3.822, 2.235, 3.9, 2.235, 1, 3.989, 2.235, 4.078, -0.927, 4.167, -0.927, 1, 4.2, -0.927, 4.233, -0.287, 4.267, -0.287, 1, 4.311, -0.287, 4.356, -30, 4.4, -30, 1, 4.422, -30, 4.444, -30, 4.467, -30, 1, 4.533, -30, 4.6, 30, 4.667, 30, 1, 4.689, 30, 4.711, 30, 4.733, 30, 1, 4.811, 30, 4.889, -13.712, 4.967, -13.712, 1, 5.044, -13.712, 5.122, 5.721, 5.2, 5.721, 1, 5.289, 5.721, 5.378, -2.337, 5.467, -2.337, 1, 5.544, -2.337, 5.622, 0.993, 5.7, 0.993, 1, 5.789, 0.993, 5.878, -0.406, 5.967, -0.406, 1, 6.044, -0.406, 6.122, 0.173, 6.2, 0.173, 1, 6.278, 0.173, 6.356, -0.07, 6.433, -0.07, 1, 6.522, -0.07, 6.611, 0.03, 6.7, 0.03, 1, 6.778, 0.03, 6.856, -0.012, 6.933, -0.012, 1, 6.978, -0.012, 7.022, -0.008, 7.067, -0.004]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.533, 1, 1, 0.633, 1, 0.733, 0, 0.833, 0, 1, 0.922, 0, 1.011, 1, 1.1, 1, 1, 1.622, 1, 2.144, 1, 2.667, 1, 1, 2.8, 1, 2.933, 0, 3.067, 0, 1, 3.456, 0, 3.844, 0, 4.233, 0, 1, 4.356, 0, 4.478, 1, 4.6, 1, 0, 7.067, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 2.667, 0, 1, 2.8, 0, 2.933, 1, 3.067, 1, 1, 3.456, 1, 3.844, 1, 4.233, 1, 1, 4.356, 1, 4.478, 0, 4.6, 0, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.533, 1, 1, 0.633, 1, 0.733, 0, 0.833, 0, 1, 0.922, 0, 1.011, 1, 1.1, 1, 1, 1.622, 1, 2.144, 1, 2.667, 1, 1, 2.8, 1, 2.933, 0, 3.067, 0, 1, 3.456, 0, 3.844, 0, 4.233, 0, 1, 4.356, 0, 4.478, 1, 4.6, 1, 0, 7.067, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 2.667, 0, 1, 2.8, 0, 2.933, 1, 3.067, 1, 1, 3.456, 1, 3.844, 1, 4.233, 1, 1, 4.356, 1, 4.478, 0, 4.6, 0, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.189, 0, 0.378, -0.031, 0.567, -0.1, 1, 0.744, -0.165, 0.922, -0.2, 1.1, -0.2, 1, 2.433, -0.2, 3.767, 0, 5.1, 0, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.744, 0, 0.922, 1, 1.1, 1, 1, 2.433, 1, 3.767, 0, 5.1, 0, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0.1, 0, 2.633, 0.1, 1, 2.767, 0.1, 2.9, 0.1, 3.033, 0.1, 1, 3.122, 0.1, 3.211, -0.122, 3.3, -0.3, 1, 3.389, -0.478, 3.478, -0.5, 3.567, -0.5, 1, 3.667, -0.5, 3.767, 0.2, 3.867, 0.2, 1, 3.967, 0.2, 4.067, 0.026, 4.167, -0.1, 1, 4.244, -0.198, 4.322, -0.2, 4.4, -0.2, 1, 4.467, -0.2, 4.533, 0.6, 4.6, 0.6, 1, 4.856, 0.6, 5.111, 0.3, 5.367, 0.3, 0, 7.067, 0.3]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 2.633, 0, 1, 2.767, 0, 2.9, 0.8, 3.033, 0.8, 1, 3.122, 0.8, 3.211, 0.2, 3.3, 0.2, 1, 3.389, 0.2, 3.478, 0.5, 3.567, 0.5, 1, 3.667, 0.5, 3.767, 0.4, 3.867, 0.4, 1, 3.967, 0.4, 4.067, 0.4, 4.167, 0.4, 1, 4.244, 0.4, 4.322, 0.9, 4.4, 0.9, 1, 4.467, 0.9, 4.533, 0.601, 4.6, 0.5, 1, 4.856, 0.113, 5.111, 0, 5.367, 0, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -30, 0, 0.267, -30, 1, 0.433, -30, 0.6, 30, 0.767, 30, 0, 7.067, 30]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, -30, 0, 0.267, -30, 1, 0.433, -30, 0.6, 30, 0.767, 30, 0, 7.067, 30]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, -30, 1, 0.333, -30, 0.667, -30, 1, -30, 1, 1.044, -30, 1.089, 30, 1.133, 30, 1, 1.167, 30, 1.2, -30, 1.233, -30, 1, 1.289, -30, 1.344, 30, 1.4, 30, 1, 1.467, 30, 1.533, -30, 1.6, -30, 1, 1.856, -30, 2.111, -30, 2.367, -30, 1, 2.511, -30, 2.656, 30, 2.8, 30, 1, 3.556, 30, 4.311, 30, 5.067, 30, 1, 5.156, 30, 5.244, -30, 5.333, -30, 0, 7.067, -30]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, -30, 0, 0.267, -30, 1, 0.433, -30, 0.6, 30, 0.767, 30, 0, 7.067, 30]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, -1, 0, 2.6, -1, 1, 2.8, -1, 3, 30, 3.2, 30, 1, 3.567, 30, 3.933, 30, 4.3, 30, 1, 4.544, 30, 4.789, -19, 5.033, -19, 1, 5.444, -19, 5.856, 1, 6.267, 1, 0, 7.067, 1]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 14.919, 0.767, 14.919, 1, 0.856, 14.919, 0.944, -17.763, 1.033, -17.763, 1, 1.122, -17.763, 1.211, 4.882, 1.3, 4.882, 1, 1.411, 4.882, 1.522, -0.932, 1.633, -0.932, 1, 1.733, -0.932, 1.833, 0.181, 1.933, 0.181, 1, 2.044, 0.181, 2.156, -0.035, 2.267, -0.035, 1, 2.367, -0.035, 2.467, 0.007, 2.567, 0.007, 1, 2.611, 0.007, 2.656, 0.002, 2.7, 0.002, 1, 2.778, 0.002, 2.856, 12.183, 2.933, 12.183, 1, 3.033, 12.183, 3.133, -4.867, 3.233, -4.867, 1, 3.333, -4.867, 3.433, 0.928, 3.533, 0.928, 1, 3.644, 0.928, 3.756, -0.178, 3.867, -0.178, 1, 3.967, -0.178, 4.067, 0.035, 4.167, 0.035, 1, 4.278, 0.035, 4.389, -12.967, 4.5, -12.967, 1, 4.589, -12.967, 4.678, 5.072, 4.767, 5.072, 1, 4.867, 5.072, 4.967, -0.949, 5.067, -0.949, 1, 5.178, -0.949, 5.289, 0.186, 5.4, 0.186, 1, 5.5, 0.186, 5.6, -0.036, 5.7, -0.036, 1, 5.811, -0.036, 5.922, 0.007, 6.033, 0.007, 1, 6.133, 0.007, 6.233, -0.001, 6.333, -0.001, 1, 6.433, -0.001, 6.533, 0, 6.633, 0, 1, 6.733, 0, 6.833, 0, 6.933, 0, 1, 6.944, 0, 6.956, 0, 6.967, 0, 1, 7, 0, 7.033, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.244, 0, 1.389, 1.71, 1.533, 1.71, 1, 1.789, 1.71, 2.044, 0.286, 2.3, 0.286, 1, 2.356, 0.286, 2.411, 0.831, 2.467, 0.831, 1, 2.556, 0.831, 2.644, -10.749, 2.733, -10.749, 1, 2.878, -10.749, 3.022, 26.059, 3.167, 26.059, 1, 3.322, 26.059, 3.478, -17.453, 3.633, -17.453, 1, 3.767, -17.453, 3.9, 16.906, 4.033, 16.906, 1, 4.189, 16.906, 4.344, -5.142, 4.5, -5.142, 1, 4.622, -5.142, 4.744, 2.26, 4.867, 2.26, 1, 5.044, 2.26, 5.222, -2.066, 5.4, -2.066, 1, 5.467, -2.066, 5.533, -1.896, 5.6, -1.896, 1, 5.978, -1.896, 6.356, -3.534, 6.733, -3.534, 1, 6.844, -3.534, 6.956, -3.499, 7.067, -3.484]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.244, 0, 1.389, 1.71, 1.533, 1.71, 1, 1.789, 1.71, 2.044, 0.286, 2.3, 0.286, 1, 2.356, 0.286, 2.411, 0.831, 2.467, 0.831, 1, 2.556, 0.831, 2.644, -10.749, 2.733, -10.749, 1, 2.878, -10.749, 3.022, 26.059, 3.167, 26.059, 1, 3.322, 26.059, 3.478, -17.453, 3.633, -17.453, 1, 3.767, -17.453, 3.9, 16.906, 4.033, 16.906, 1, 4.189, 16.906, 4.344, -5.142, 4.5, -5.142, 1, 4.622, -5.142, 4.744, 2.26, 4.867, 2.26, 1, 5.044, 2.26, 5.222, -2.066, 5.4, -2.066, 1, 5.467, -2.066, 5.533, -1.896, 5.6, -1.896, 1, 5.978, -1.896, 6.356, -3.534, 6.733, -3.534, 1, 6.844, -3.534, 6.956, -3.499, 7.067, -3.484]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.244, 0, 1.389, 1.71, 1.533, 1.71, 1, 1.789, 1.71, 2.044, 0.286, 2.3, 0.286, 1, 2.356, 0.286, 2.411, 0.831, 2.467, 0.831, 1, 2.556, 0.831, 2.644, -10.749, 2.733, -10.749, 1, 2.878, -10.749, 3.022, 26.059, 3.167, 26.059, 1, 3.322, 26.059, 3.478, -17.453, 3.633, -17.453, 1, 3.767, -17.453, 3.9, 16.906, 4.033, 16.906, 1, 4.189, 16.906, 4.344, -5.142, 4.5, -5.142, 1, 4.622, -5.142, 4.744, 2.26, 4.867, 2.26, 1, 5.044, 2.26, 5.222, -2.066, 5.4, -2.066, 1, 5.467, -2.066, 5.533, -1.896, 5.6, -1.896, 1, 5.978, -1.896, 6.356, -3.534, 6.733, -3.534, 1, 6.844, -3.534, 6.956, -3.499, 7.067, -3.484]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0.118, 1, 0.011, 0.118, 0.022, 0.127, 0.033, 0.127, 1, 0.122, 0.127, 0.211, -0.095, 0.3, -0.095, 1, 0.411, -0.095, 0.522, 0.217, 0.633, 0.217, 1, 0.789, 0.217, 0.944, -0.01, 1.1, -0.01, 1, 1.178, -0.01, 1.256, 0.197, 1.333, 0.197, 1, 1.444, 0.197, 1.556, -0.18, 1.667, -0.18, 1, 1.944, -0.18, 2.222, 3.028, 2.5, 3.028, 1, 2.556, 3.028, 2.611, 1.941, 2.667, 1.941, 1, 2.811, 1.941, 2.956, 13.205, 3.1, 13.205, 1, 3.256, 13.205, 3.411, 0.698, 3.567, 0.698, 1, 3.7, 0.698, 3.833, 10.855, 3.967, 10.855, 1, 4.144, 10.855, 4.322, 3.876, 4.5, 3.876, 1, 4.511, 3.876, 4.522, 3.879, 4.533, 3.879, 1, 5.244, 3.879, 5.956, -1.755, 6.667, -1.755, 1, 6.778, -1.755, 6.889, -1.741, 7, -1.741, 1, 7.022, -1.741, 7.044, -1.742, 7.067, -1.742]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0.118, 1, 0.011, 0.118, 0.022, 0.127, 0.033, 0.127, 1, 0.122, 0.127, 0.211, -0.095, 0.3, -0.095, 1, 0.411, -0.095, 0.522, 0.217, 0.633, 0.217, 1, 0.789, 0.217, 0.944, -0.01, 1.1, -0.01, 1, 1.178, -0.01, 1.256, 0.197, 1.333, 0.197, 1, 1.444, 0.197, 1.556, -0.18, 1.667, -0.18, 1, 1.944, -0.18, 2.222, 3.028, 2.5, 3.028, 1, 2.556, 3.028, 2.611, 1.941, 2.667, 1.941, 1, 2.811, 1.941, 2.956, 13.205, 3.1, 13.205, 1, 3.256, 13.205, 3.411, 0.698, 3.567, 0.698, 1, 3.7, 0.698, 3.833, 10.855, 3.967, 10.855, 1, 4.144, 10.855, 4.322, 3.876, 4.5, 3.876, 1, 4.511, 3.876, 4.522, 3.879, 4.533, 3.879, 1, 5.244, 3.879, 5.956, -1.755, 6.667, -1.755, 1, 6.778, -1.755, 6.889, -1.741, 7, -1.741, 1, 7.022, -1.741, 7.044, -1.742, 7.067, -1.742]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, -0.371, 1, 0.089, -0.371, 0.178, 0.421, 0.267, 0.421, 1, 0.344, 0.421, 0.422, -0.227, 0.5, -0.227, 1, 0.622, -0.227, 0.744, 0.322, 0.867, 0.322, 1, 1, 0.322, 1.133, -0.109, 1.267, -0.109, 1, 1.356, -0.109, 1.444, 0.398, 1.533, 0.398, 1, 1.656, 0.398, 1.778, -0.277, 1.9, -0.277, 1, 2, -0.277, 2.1, 0.032, 2.2, 0.032, 1, 2.289, 0.032, 2.378, -1.835, 2.467, -1.835, 1, 2.522, -1.835, 2.578, 0.388, 2.633, 0.388, 1, 2.744, 0.388, 2.856, -8.374, 2.967, -8.374, 1, 3.1, -8.374, 3.233, 9.982, 3.367, 9.982, 1, 3.511, 9.982, 3.656, -9.29, 3.8, -9.29, 1, 3.922, -9.29, 4.044, 6.373, 4.167, 6.373, 1, 4.3, 6.373, 4.433, -0.595, 4.567, -0.595, 1, 4.678, -0.595, 4.789, 1.826, 4.9, 1.826, 1, 5.033, 1.826, 5.167, -0.242, 5.3, -0.242, 1, 5.4, -0.242, 5.5, 0.325, 5.6, 0.325, 1, 5.711, 0.325, 5.822, -0.038, 5.933, -0.038, 1, 6.044, -0.038, 6.156, 0.101, 6.267, 0.101, 1, 6.444, 0.101, 6.622, -0.019, 6.8, -0.019, 1, 6.889, -0.019, 6.978, -0.005, 7.067, 0.002]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0.118, 1, 0.011, 0.118, 0.022, 0.127, 0.033, 0.127, 1, 0.122, 0.127, 0.211, -0.095, 0.3, -0.095, 1, 0.411, -0.095, 0.522, 0.217, 0.633, 0.217, 1, 0.789, 0.217, 0.944, -0.01, 1.1, -0.01, 1, 1.178, -0.01, 1.256, 0.197, 1.333, 0.197, 1, 1.444, 0.197, 1.556, -0.18, 1.667, -0.18, 1, 1.944, -0.18, 2.222, 3.028, 2.5, 3.028, 1, 2.556, 3.028, 2.611, 1.941, 2.667, 1.941, 1, 2.811, 1.941, 2.956, 13.205, 3.1, 13.205, 1, 3.256, 13.205, 3.411, 0.698, 3.567, 0.698, 1, 3.7, 0.698, 3.833, 10.855, 3.967, 10.855, 1, 4.144, 10.855, 4.322, 3.876, 4.5, 3.876, 1, 4.511, 3.876, 4.522, 3.879, 4.533, 3.879, 1, 5.244, 3.879, 5.956, -1.755, 6.667, -1.755, 1, 6.778, -1.755, 6.889, -1.741, 7, -1.741, 1, 7.022, -1.741, 7.044, -1.742, 7.067, -1.742]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, -0.265, 1, 0.089, -0.265, 0.178, 0.301, 0.267, 0.301, 1, 0.344, 0.301, 0.422, -0.162, 0.5, -0.162, 1, 0.622, -0.162, 0.744, 0.23, 0.867, 0.23, 1, 1, 0.23, 1.133, -0.078, 1.267, -0.078, 1, 1.356, -0.078, 1.444, 0.284, 1.533, 0.284, 1, 1.656, 0.284, 1.778, -0.198, 1.9, -0.198, 1, 2, -0.198, 2.1, 0.023, 2.2, 0.023, 1, 2.289, 0.023, 2.378, -1.31, 2.467, -1.31, 1, 2.522, -1.31, 2.578, 0.277, 2.633, 0.277, 1, 2.744, 0.277, 2.856, -5.981, 2.967, -5.981, 1, 3.1, -5.981, 3.233, 7.13, 3.367, 7.13, 1, 3.511, 7.13, 3.656, -6.636, 3.8, -6.636, 1, 3.922, -6.636, 4.044, 4.552, 4.167, 4.552, 1, 4.3, 4.552, 4.433, -0.425, 4.567, -0.425, 1, 4.678, -0.425, 4.789, 1.304, 4.9, 1.304, 1, 5.033, 1.304, 5.167, -0.173, 5.3, -0.173, 1, 5.4, -0.173, 5.5, 0.232, 5.6, 0.232, 1, 5.711, 0.232, 5.822, -0.027, 5.933, -0.027, 1, 6.044, -0.027, 6.156, 0.072, 6.267, 0.072, 1, 6.444, 0.072, 6.622, -0.014, 6.8, -0.014, 1, 6.889, -0.014, 6.978, -0.004, 7.067, 0.002]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0.118, 1, 0.011, 0.118, 0.022, 0.127, 0.033, 0.127, 1, 0.122, 0.127, 0.211, -0.095, 0.3, -0.095, 1, 0.411, -0.095, 0.522, 0.217, 0.633, 0.217, 1, 0.789, 0.217, 0.944, -0.01, 1.1, -0.01, 1, 1.178, -0.01, 1.256, 0.197, 1.333, 0.197, 1, 1.444, 0.197, 1.556, -0.18, 1.667, -0.18, 1, 1.944, -0.18, 2.222, 3.028, 2.5, 3.028, 1, 2.556, 3.028, 2.611, 1.941, 2.667, 1.941, 1, 2.811, 1.941, 2.956, 13.205, 3.1, 13.205, 1, 3.256, 13.205, 3.411, 0.698, 3.567, 0.698, 1, 3.7, 0.698, 3.833, 10.855, 3.967, 10.855, 1, 4.144, 10.855, 4.322, 3.876, 4.5, 3.876, 1, 4.511, 3.876, 4.522, 3.879, 4.533, 3.879, 1, 5.244, 3.879, 5.956, -1.755, 6.667, -1.755, 1, 6.778, -1.755, 6.889, -1.741, 7, -1.741, 1, 7.022, -1.741, 7.044, -1.742, 7.067, -1.742]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, -0.265, 1, 0.089, -0.265, 0.178, 0.301, 0.267, 0.301, 1, 0.344, 0.301, 0.422, -0.162, 0.5, -0.162, 1, 0.622, -0.162, 0.744, 0.23, 0.867, 0.23, 1, 1, 0.23, 1.133, -0.078, 1.267, -0.078, 1, 1.356, -0.078, 1.444, 0.284, 1.533, 0.284, 1, 1.656, 0.284, 1.778, -0.198, 1.9, -0.198, 1, 2, -0.198, 2.1, 0.023, 2.2, 0.023, 1, 2.289, 0.023, 2.378, -1.31, 2.467, -1.31, 1, 2.522, -1.31, 2.578, 0.277, 2.633, 0.277, 1, 2.744, 0.277, 2.856, -5.981, 2.967, -5.981, 1, 3.1, -5.981, 3.233, 7.13, 3.367, 7.13, 1, 3.511, 7.13, 3.656, -6.636, 3.8, -6.636, 1, 3.922, -6.636, 4.044, 4.552, 4.167, 4.552, 1, 4.3, 4.552, 4.433, -0.425, 4.567, -0.425, 1, 4.678, -0.425, 4.789, 1.304, 4.9, 1.304, 1, 5.033, 1.304, 5.167, -0.173, 5.3, -0.173, 1, 5.4, -0.173, 5.5, 0.232, 5.6, 0.232, 1, 5.711, 0.232, 5.822, -0.027, 5.933, -0.027, 1, 6.044, -0.027, 6.156, 0.072, 6.267, 0.072, 1, 6.444, 0.072, 6.622, -0.014, 6.8, -0.014, 1, 6.889, -0.014, 6.978, -0.004, 7.067, 0.002]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0.141, 1, 0.011, 0.141, 0.022, 0.153, 0.033, 0.153, 1, 0.122, 0.153, 0.211, -0.114, 0.3, -0.114, 1, 0.411, -0.114, 0.522, 0.26, 0.633, 0.26, 1, 0.789, 0.26, 0.944, -0.012, 1.1, -0.012, 1, 1.178, -0.012, 1.256, 0.237, 1.333, 0.237, 1, 1.444, 0.237, 1.556, -0.216, 1.667, -0.216, 1, 1.944, -0.216, 2.222, 3.634, 2.5, 3.634, 1, 2.556, 3.634, 2.611, 2.329, 2.667, 2.329, 1, 2.811, 2.329, 2.956, 15.846, 3.1, 15.846, 1, 3.256, 15.846, 3.411, 0.837, 3.567, 0.837, 1, 3.7, 0.837, 3.833, 13.027, 3.967, 13.027, 1, 4.144, 13.027, 4.322, 4.651, 4.5, 4.651, 1, 4.511, 4.651, 4.522, 4.655, 4.533, 4.655, 1, 5.244, 4.655, 5.956, -2.106, 6.667, -2.106, 1, 6.778, -2.106, 6.889, -2.09, 7, -2.09, 1, 7.022, -2.09, 7.044, -2.09, 7.067, -2.09]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, -0.318, 1, 0.089, -0.318, 0.178, 0.361, 0.267, 0.361, 1, 0.344, 0.361, 0.422, -0.195, 0.5, -0.195, 1, 0.622, -0.195, 0.744, 0.276, 0.867, 0.276, 1, 1, 0.276, 1.133, -0.094, 1.267, -0.094, 1, 1.356, -0.094, 1.444, 0.341, 1.533, 0.341, 1, 1.656, 0.341, 1.778, -0.237, 1.9, -0.237, 1, 2, -0.237, 2.1, 0.027, 2.2, 0.027, 1, 2.289, 0.027, 2.378, -1.573, 2.467, -1.573, 1, 2.522, -1.573, 2.578, 0.333, 2.633, 0.333, 1, 2.744, 0.333, 2.856, -7.178, 2.967, -7.178, 1, 3.1, -7.178, 3.233, 8.556, 3.367, 8.556, 1, 3.511, 8.556, 3.656, -7.963, 3.8, -7.963, 1, 3.922, -7.963, 4.044, 5.462, 4.167, 5.462, 1, 4.3, 5.462, 4.433, -0.51, 4.567, -0.51, 1, 4.678, -0.51, 4.789, 1.565, 4.9, 1.565, 1, 5.033, 1.565, 5.167, -0.208, 5.3, -0.208, 1, 5.4, -0.208, 5.5, 0.278, 5.6, 0.278, 1, 5.711, 0.278, 5.822, -0.033, 5.933, -0.033, 1, 6.044, -0.033, 6.156, 0.087, 6.267, 0.087, 1, 6.444, 0.087, 6.622, -0.016, 6.8, -0.016, 1, 6.889, -0.016, 6.978, -0.004, 7.067, 0.002]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0.141, 1, 0.011, 0.141, 0.022, 0.153, 0.033, 0.153, 1, 0.122, 0.153, 0.211, -0.114, 0.3, -0.114, 1, 0.411, -0.114, 0.522, 0.26, 0.633, 0.26, 1, 0.789, 0.26, 0.944, -0.012, 1.1, -0.012, 1, 1.178, -0.012, 1.256, 0.237, 1.333, 0.237, 1, 1.444, 0.237, 1.556, -0.216, 1.667, -0.216, 1, 1.944, -0.216, 2.222, 3.634, 2.5, 3.634, 1, 2.556, 3.634, 2.611, 2.329, 2.667, 2.329, 1, 2.811, 2.329, 2.956, 15.846, 3.1, 15.846, 1, 3.256, 15.846, 3.411, 0.837, 3.567, 0.837, 1, 3.7, 0.837, 3.833, 13.027, 3.967, 13.027, 1, 4.144, 13.027, 4.322, 4.651, 4.5, 4.651, 1, 4.511, 4.651, 4.522, 4.655, 4.533, 4.655, 1, 5.244, 4.655, 5.956, -2.106, 6.667, -2.106, 1, 6.778, -2.106, 6.889, -2.09, 7, -2.09, 1, 7.022, -2.09, 7.044, -2.09, 7.067, -2.09]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, -0.318, 1, 0.089, -0.318, 0.178, 0.361, 0.267, 0.361, 1, 0.344, 0.361, 0.422, -0.195, 0.5, -0.195, 1, 0.622, -0.195, 0.744, 0.276, 0.867, 0.276, 1, 1, 0.276, 1.133, -0.094, 1.267, -0.094, 1, 1.356, -0.094, 1.444, 0.341, 1.533, 0.341, 1, 1.656, 0.341, 1.778, -0.237, 1.9, -0.237, 1, 2, -0.237, 2.1, 0.027, 2.2, 0.027, 1, 2.289, 0.027, 2.378, -1.573, 2.467, -1.573, 1, 2.522, -1.573, 2.578, 0.333, 2.633, 0.333, 1, 2.744, 0.333, 2.856, -7.178, 2.967, -7.178, 1, 3.1, -7.178, 3.233, 8.556, 3.367, 8.556, 1, 3.511, 8.556, 3.656, -7.963, 3.8, -7.963, 1, 3.922, -7.963, 4.044, 5.462, 4.167, 5.462, 1, 4.3, 5.462, 4.433, -0.51, 4.567, -0.51, 1, 4.678, -0.51, 4.789, 1.565, 4.9, 1.565, 1, 5.033, 1.565, 5.167, -0.208, 5.3, -0.208, 1, 5.4, -0.208, 5.5, 0.278, 5.6, 0.278, 1, 5.711, 0.278, 5.822, -0.033, 5.933, -0.033, 1, 6.044, -0.033, 6.156, 0.087, 6.267, 0.087, 1, 6.444, 0.087, 6.622, -0.016, 6.8, -0.016, 1, 6.889, -0.016, 6.978, -0.004, 7.067, 0.002]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.633, 0, 0.7, 4.973, 0.767, 4.973, 1, 0.856, 4.973, 0.944, -5.921, 1.033, -5.921, 1, 1.122, -5.921, 1.211, 1.627, 1.3, 1.627, 1, 1.411, 1.627, 1.522, -0.311, 1.633, -0.311, 1, 1.733, -0.311, 1.833, 0.06, 1.933, 0.06, 1, 2.044, 0.06, 2.156, -0.012, 2.267, -0.012, 1, 2.367, -0.012, 2.467, 0.002, 2.567, 0.002, 1, 2.611, 0.002, 2.656, 0.001, 2.7, 0.001, 1, 2.778, 0.001, 2.856, 4.061, 2.933, 4.061, 1, 3.033, 4.061, 3.133, -1.622, 3.233, -1.622, 1, 3.333, -1.622, 3.433, 0.309, 3.533, 0.309, 1, 3.644, 0.309, 3.756, -0.059, 3.867, -0.059, 1, 3.967, -0.059, 4.067, 0.012, 4.167, 0.012, 1, 4.278, 0.012, 4.389, -4.322, 4.5, -4.322, 1, 4.589, -4.322, 4.678, 1.691, 4.767, 1.691, 1, 4.867, 1.691, 4.967, -0.316, 5.067, -0.316, 1, 5.178, -0.316, 5.289, 0.062, 5.4, 0.062, 1, 5.5, 0.062, 5.6, -0.012, 5.7, -0.012, 1, 5.811, -0.012, 5.922, 0.002, 6.033, 0.002, 1, 6.133, 0.002, 6.233, 0, 6.333, 0, 1, 6.433, 0, 6.533, 0, 6.633, 0, 1, 6.733, 0, 6.833, 0, 6.933, 0, 1, 6.944, 0, 6.956, 0, 6.967, 0, 1, 7, 0, 7.033, 0, 7.067, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0.094, 1, 0.011, 0.094, 0.022, 0.102, 0.033, 0.102, 1, 0.122, 0.102, 0.211, -0.076, 0.3, -0.076, 1, 0.411, -0.076, 0.522, 0.174, 0.633, 0.174, 1, 0.789, 0.174, 0.944, -0.008, 1.1, -0.008, 1, 1.178, -0.008, 1.256, 0.158, 1.333, 0.158, 1, 1.444, 0.158, 1.556, -0.144, 1.667, -0.144, 1, 1.944, -0.144, 2.222, 2.422, 2.5, 2.422, 1, 2.556, 2.422, 2.611, 1.553, 2.667, 1.553, 1, 2.811, 1.553, 2.956, 10.564, 3.1, 10.564, 1, 3.256, 10.564, 3.411, 0.558, 3.567, 0.558, 1, 3.7, 0.558, 3.833, 8.684, 3.967, 8.684, 1, 4.144, 8.684, 4.322, 3.1, 4.5, 3.1, 1, 4.511, 3.1, 4.522, 3.103, 4.533, 3.103, 1, 5.244, 3.103, 5.956, -1.404, 6.667, -1.404, 1, 6.778, -1.404, 6.889, -1.393, 7, -1.393, 1, 7.022, -1.393, 7.044, -1.393, 7.067, -1.393]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -0.212, 1, 0.089, -0.212, 0.178, 0.241, 0.267, 0.241, 1, 0.344, 0.241, 0.422, -0.13, 0.5, -0.13, 1, 0.622, -0.13, 0.744, 0.184, 0.867, 0.184, 1, 1, 0.184, 1.133, -0.063, 1.267, -0.063, 1, 1.356, -0.063, 1.444, 0.228, 1.533, 0.228, 1, 1.656, 0.228, 1.778, -0.158, 1.9, -0.158, 1, 2, -0.158, 2.1, 0.018, 2.2, 0.018, 1, 2.289, 0.018, 2.378, -1.048, 2.467, -1.048, 1, 2.522, -1.048, 2.578, 0.222, 2.633, 0.222, 1, 2.744, 0.222, 2.856, -4.785, 2.967, -4.785, 1, 3.1, -4.785, 3.233, 5.704, 3.367, 5.704, 1, 3.511, 5.704, 3.656, -5.309, 3.8, -5.309, 1, 3.922, -5.309, 4.044, 3.642, 4.167, 3.642, 1, 4.3, 3.642, 4.433, -0.34, 4.567, -0.34, 1, 4.678, -0.34, 4.789, 1.044, 4.9, 1.044, 1, 5.033, 1.044, 5.167, -0.138, 5.3, -0.138, 1, 5.4, -0.138, 5.5, 0.185, 5.6, 0.185, 1, 5.711, 0.185, 5.822, -0.022, 5.933, -0.022, 1, 6.044, -0.022, 6.156, 0.058, 6.267, 0.058, 1, 6.444, 0.058, 6.622, -0.011, 6.8, -0.011, 1, 6.889, -0.011, 6.978, -0.003, 7.067, 0.001]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0.188, 1, 0.011, 0.188, 0.022, 0.204, 0.033, 0.204, 1, 0.122, 0.204, 0.211, -0.153, 0.3, -0.153, 1, 0.411, -0.153, 0.522, 0.347, 0.633, 0.347, 1, 0.789, 0.347, 0.944, -0.016, 1.1, -0.016, 1, 1.178, -0.016, 1.256, 0.316, 1.333, 0.316, 1, 1.444, 0.316, 1.556, -0.288, 1.667, -0.288, 1, 1.944, -0.288, 2.222, 4.845, 2.5, 4.845, 1, 2.556, 4.845, 2.611, 3.106, 2.667, 3.106, 1, 2.811, 3.106, 2.956, 21.127, 3.1, 21.127, 1, 3.256, 21.127, 3.411, 1.116, 3.567, 1.116, 1, 3.7, 1.116, 3.833, 17.369, 3.967, 17.369, 1, 4.144, 17.369, 4.322, 6.201, 4.5, 6.201, 1, 4.511, 6.201, 4.522, 6.206, 4.533, 6.206, 1, 5.244, 6.206, 5.956, -2.808, 6.667, -2.808, 1, 6.778, -2.808, 6.889, -2.786, 7, -2.786, 1, 7.022, -2.786, 7.044, -2.786, 7.067, -2.787]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.378, 0, 0.522, 0.185, 0.667, 0.185, 1, 0.811, 0.185, 0.956, -0.003, 1.1, -0.003, 1, 1.178, -0.003, 1.256, 0.075, 1.333, 0.075, 1, 1.456, 0.075, 1.578, -0.047, 1.7, -0.047, 1, 1.967, -0.047, 2.233, 0.923, 2.5, 0.923, 1, 2.567, 0.923, 2.633, 0.39, 2.7, 0.39, 1, 2.856, 0.39, 3.011, 4.086, 3.167, 4.086, 1, 3.322, 4.086, 3.478, 0.113, 3.633, 0.113, 1, 3.767, 0.113, 3.9, 3.137, 4.033, 3.137, 1, 4.922, 3.137, 5.811, -0.528, 6.7, -0.528, 1, 6.822, -0.528, 6.944, -0.523, 7.067, -0.523]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.356, 0, 0.478, -0.173, 0.6, -0.173, 1, 0.7, -0.173, 0.8, 0.161, 0.9, 0.161, 1, 1.022, 0.161, 1.144, -0.137, 1.267, -0.137, 1, 1.367, -0.137, 1.467, 0.082, 1.567, 0.082, 1, 1.689, 0.082, 1.811, -0.065, 1.933, -0.065, 1, 2.022, -0.065, 2.111, -0.019, 2.2, -0.019, 1, 2.289, -0.019, 2.378, -0.366, 2.467, -0.366, 1, 2.544, -0.366, 2.622, 0.601, 2.7, 0.601, 1, 2.811, 0.601, 2.922, -1.006, 3.033, -1.006, 1, 3.156, -1.006, 3.278, 1.474, 3.4, 1.474, 1, 3.533, 1.474, 3.667, -1.644, 3.8, -1.644, 1, 3.922, -1.644, 4.044, 1.357, 4.167, 1.357, 1, 4.311, 1.357, 4.456, -0.687, 4.6, -0.687, 1, 4.711, -0.687, 4.822, 0.349, 4.933, 0.349, 1, 5.056, 0.349, 5.178, -0.141, 5.3, -0.141, 1, 5.422, -0.141, 5.544, 0.087, 5.667, 0.087, 1, 5.789, 0.087, 5.911, -0.011, 6.033, -0.011, 1, 6.144, -0.011, 6.256, 0.011, 6.367, 0.011, 1, 6.5, 0.011, 6.633, -0.009, 6.767, -0.009, 1, 6.867, -0.009, 6.967, -0.002, 7.067, 0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0.04, 1, 0.033, 0.04, 0.067, 0.058, 0.1, 0.058, 1, 0.211, 0.058, 0.322, -0.055, 0.433, -0.055, 1, 0.5, -0.055, 0.567, 0.006, 0.633, 0.006, 1, 0.689, 0.006, 0.744, -0.142, 0.8, -0.142, 1, 0.9, -0.142, 1, 0.211, 1.1, 0.211, 1, 1.211, 0.211, 1.322, -0.199, 1.433, -0.199, 1, 1.544, -0.199, 1.656, 0.158, 1.767, 0.158, 1, 1.878, 0.158, 1.989, -0.144, 2.1, -0.144, 1, 2.178, -0.144, 2.256, -0.034, 2.333, -0.034, 1, 2.422, -0.034, 2.511, -0.453, 2.6, -0.453, 1, 2.667, -0.453, 2.733, -0.017, 2.8, -0.017, 1, 2.9, -0.017, 3, -1.343, 3.1, -1.343, 1, 3.233, -1.343, 3.367, 3.082, 3.5, 3.082, 1, 3.633, 3.082, 3.767, -3.891, 3.9, -3.891, 1, 4.033, -3.891, 4.167, 3.618, 4.3, 3.618, 1, 4.444, 3.618, 4.589, -2.025, 4.733, -2.025, 1, 4.856, -2.025, 4.978, 1.227, 5.1, 1.227, 1, 5.222, 1.227, 5.344, -0.607, 5.467, -0.607, 1, 5.6, -0.607, 5.733, 0.303, 5.867, 0.303, 1, 5.989, 0.303, 6.111, -0.12, 6.233, -0.12, 1, 6.356, -0.12, 6.478, 0.052, 6.6, 0.052, 1, 6.722, 0.052, 6.844, -0.031, 6.967, -0.031, 1, 7, -0.031, 7.033, -0.027, 7.067, -0.022]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, -0.18, 1, 0.111, -0.18, 0.222, 0.169, 0.333, 0.169, 1, 0.444, 0.169, 0.556, -0.159, 0.667, -0.159, 1, 0.856, -0.159, 1.044, 0.213, 1.233, 0.213, 1, 1.356, 0.213, 1.478, -0.271, 1.6, -0.271, 1, 1.711, -0.271, 1.822, 0.252, 1.933, 0.252, 1, 2.044, 0.252, 2.156, -0.241, 2.267, -0.241, 1, 2.344, -0.241, 2.422, -0.049, 2.5, -0.049, 1, 2.567, -0.049, 2.633, -0.18, 2.7, -0.18, 1, 2.778, -0.18, 2.856, 0.194, 2.933, 0.194, 1, 3.044, 0.194, 3.156, -1.793, 3.267, -1.793, 1, 3.389, -1.793, 3.511, 4.048, 3.633, 4.048, 1, 3.778, 4.048, 3.922, -5.494, 4.067, -5.494, 1, 4.2, -5.494, 4.333, 5.497, 4.467, 5.497, 1, 4.6, 5.497, 4.733, -3.923, 4.867, -3.923, 1, 5, -3.923, 5.133, 2.596, 5.267, 2.596, 1, 5.389, 2.596, 5.511, -1.516, 5.633, -1.516, 1, 5.756, -1.516, 5.878, 0.834, 6, 0.834, 1, 6.133, 0.834, 6.267, -0.416, 6.4, -0.416, 1, 6.522, -0.416, 6.644, 0.197, 6.767, 0.197, 1, 6.867, 0.197, 6.967, -0.001, 7.067, -0.073]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.378, 0, 0.522, 0.185, 0.667, 0.185, 1, 0.811, 0.185, 0.956, -0.003, 1.1, -0.003, 1, 1.178, -0.003, 1.256, 0.075, 1.333, 0.075, 1, 1.456, 0.075, 1.578, -0.047, 1.7, -0.047, 1, 1.967, -0.047, 2.233, 0.923, 2.5, 0.923, 1, 2.567, 0.923, 2.633, 0.39, 2.7, 0.39, 1, 2.856, 0.39, 3.011, 4.086, 3.167, 4.086, 1, 3.322, 4.086, 3.478, 0.113, 3.633, 0.113, 1, 3.767, 0.113, 3.9, 3.137, 4.033, 3.137, 1, 4.922, 3.137, 5.811, -0.528, 6.7, -0.528, 1, 6.822, -0.528, 6.944, -0.523, 7.067, -0.523]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.356, 0, 0.478, -0.173, 0.6, -0.173, 1, 0.7, -0.173, 0.8, 0.161, 0.9, 0.161, 1, 1.022, 0.161, 1.144, -0.137, 1.267, -0.137, 1, 1.367, -0.137, 1.467, 0.082, 1.567, 0.082, 1, 1.689, 0.082, 1.811, -0.065, 1.933, -0.065, 1, 2.022, -0.065, 2.111, -0.019, 2.2, -0.019, 1, 2.289, -0.019, 2.378, -0.366, 2.467, -0.366, 1, 2.544, -0.366, 2.622, 0.601, 2.7, 0.601, 1, 2.811, 0.601, 2.922, -1.006, 3.033, -1.006, 1, 3.156, -1.006, 3.278, 1.474, 3.4, 1.474, 1, 3.533, 1.474, 3.667, -1.644, 3.8, -1.644, 1, 3.922, -1.644, 4.044, 1.357, 4.167, 1.357, 1, 4.311, 1.357, 4.456, -0.687, 4.6, -0.687, 1, 4.711, -0.687, 4.822, 0.349, 4.933, 0.349, 1, 5.056, 0.349, 5.178, -0.141, 5.3, -0.141, 1, 5.422, -0.141, 5.544, 0.087, 5.667, 0.087, 1, 5.789, 0.087, 5.911, -0.011, 6.033, -0.011, 1, 6.144, -0.011, 6.256, 0.011, 6.367, 0.011, 1, 6.5, 0.011, 6.633, -0.009, 6.767, -0.009, 1, 6.867, -0.009, 6.967, -0.002, 7.067, 0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0.04, 1, 0.033, 0.04, 0.067, 0.058, 0.1, 0.058, 1, 0.211, 0.058, 0.322, -0.055, 0.433, -0.055, 1, 0.5, -0.055, 0.567, 0.006, 0.633, 0.006, 1, 0.689, 0.006, 0.744, -0.142, 0.8, -0.142, 1, 0.9, -0.142, 1, 0.211, 1.1, 0.211, 1, 1.211, 0.211, 1.322, -0.199, 1.433, -0.199, 1, 1.544, -0.199, 1.656, 0.158, 1.767, 0.158, 1, 1.878, 0.158, 1.989, -0.144, 2.1, -0.144, 1, 2.178, -0.144, 2.256, -0.034, 2.333, -0.034, 1, 2.422, -0.034, 2.511, -0.453, 2.6, -0.453, 1, 2.667, -0.453, 2.733, -0.017, 2.8, -0.017, 1, 2.9, -0.017, 3, -1.343, 3.1, -1.343, 1, 3.233, -1.343, 3.367, 3.082, 3.5, 3.082, 1, 3.633, 3.082, 3.767, -3.891, 3.9, -3.891, 1, 4.033, -3.891, 4.167, 3.618, 4.3, 3.618, 1, 4.444, 3.618, 4.589, -2.025, 4.733, -2.025, 1, 4.856, -2.025, 4.978, 1.227, 5.1, 1.227, 1, 5.222, 1.227, 5.344, -0.607, 5.467, -0.607, 1, 5.6, -0.607, 5.733, 0.303, 5.867, 0.303, 1, 5.989, 0.303, 6.111, -0.12, 6.233, -0.12, 1, 6.356, -0.12, 6.478, 0.052, 6.6, 0.052, 1, 6.722, 0.052, 6.844, -0.031, 6.967, -0.031, 1, 7, -0.031, 7.033, -0.027, 7.067, -0.022]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, -0.18, 1, 0.111, -0.18, 0.222, 0.169, 0.333, 0.169, 1, 0.444, 0.169, 0.556, -0.159, 0.667, -0.159, 1, 0.856, -0.159, 1.044, 0.213, 1.233, 0.213, 1, 1.356, 0.213, 1.478, -0.271, 1.6, -0.271, 1, 1.711, -0.271, 1.822, 0.252, 1.933, 0.252, 1, 2.044, 0.252, 2.156, -0.241, 2.267, -0.241, 1, 2.344, -0.241, 2.422, -0.049, 2.5, -0.049, 1, 2.567, -0.049, 2.633, -0.18, 2.7, -0.18, 1, 2.778, -0.18, 2.856, 0.194, 2.933, 0.194, 1, 3.044, 0.194, 3.156, -1.793, 3.267, -1.793, 1, 3.389, -1.793, 3.511, 4.048, 3.633, 4.048, 1, 3.778, 4.048, 3.922, -5.494, 4.067, -5.494, 1, 4.2, -5.494, 4.333, 5.497, 4.467, 5.497, 1, 4.6, 5.497, 4.733, -3.923, 4.867, -3.923, 1, 5, -3.923, 5.133, 2.596, 5.267, 2.596, 1, 5.389, 2.596, 5.511, -1.516, 5.633, -1.516, 1, 5.756, -1.516, 5.878, 0.834, 6, 0.834, 1, 6.133, 0.834, 6.267, -0.416, 6.4, -0.416, 1, 6.522, -0.416, 6.644, 0.197, 6.767, 0.197, 1, 6.867, 0.197, 6.967, -0.001, 7.067, -0.073]}]}