{"version": 3, "sources": ["webpack://user-profile-picture/./src/block/editor.scss"], "names": [], "mappings": "AACC,kBACC,cACA,kBACA,WACA,cAGF,yCACC,cACA,WACA,kBACA,oBACA,mBACA,gBACA,0EACC,kBACA,gBAED,8EACC,kBAED,2EACC,YAED,wQACC,WAED,mDACC,aAED,oEACC,kBACA,WACA,gBACA,aACA,gBACA,kBACA,gBACA,iBACA,gBACA,iBACA,WACA,kBACA,wKAEC,kBACA,gBACA,iBACA,gBACA,iBACA,aAED,8FACC,kBACA,MACA,OACA,YACA,WACA,UAED,kGACC,YACA,WACA,kBACA,UAED,sFACC,cACA,sBACA,iBACA,WAED,sFACC,WACA,gBAED,0CA9CD,oEA+CE,WAGF,iEACC,cACA,WACA,kBACA,kBACA,gBACA,yBACA,WACA,uNAGC,cACA,WACA,YACA,WACA,qBAKF,kCACC,kBACA,gBACA,iBACA,gBACA,iBACA,aAGF,0DACC,kBACA,cACA,gBACA,2FACC,kBACA,gBAED,+FACC,kBAED,oEACC,aAED,qFACC,kBACA,WACA,gBACA,aACA,gBACA,kBACA,gBACA,iBACA,gBACA,iBACA,WACA,kBACA,4FACC,kBACA,gBACA,iBACA,gBACA,iBACA,aAED,+GACC,kBACA,MACA,OACA,YACA,WACA,UAED,mHACC,YACA,WACA,kBACA,UAED,uGACC,cACA,sBACA,iBACA,WAED,uGACC,WACA,gBAED,0CA7CD,qFA8CE,WAGF,oFACC,WACA,0FACC,WACA,cACA,WAIF,kFACC,WACA,cACA,WACA,kBACA,kBACA,gBACA,kBACA,yBACA,WACA,0QAGC,cACA,WACA,YACA,WACA,qBAED,wFACC,WACA,cACA,WAIF,oFACC,cACA,WACA,kBACA,kBACA,gBACA,sBACA,WACA,gRAGC,cACA,WACA,YACA,WACA,qBAGF,wFACC,YACA,iBAED,yFACC,qBACA,WACA,iBAQD,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eARF,mVAKC,eACA,osBAEC,eAOH,YACC,gBACA,gBACC,eACA,gBACA,kBAuCF,YACC,WAIC,6BACC,aADD,4BACC,aADD,6BACC,aADD,6BACC,aADD,6BACC,aADD,6BACC,aADD,6BACC,aADD,gCACC,aADD,8BACC,aADD,4BACC,aADD,gCACC,aADD,2BACC,aADD,4BACC,aADD,iCACC,aADD,+BACC,aADD,8BACC,aADD,0BACC,aADD,4BACC,aADD,0BACC,aADD,+BACC,aADD,4BACC,aADD,+BACC,aADD,4BACC,aADD,2BACC,aADD,6BACC,aADD,iCACC,aADD,iCACC,aADD,8BACC,aADD,gCACC,aADD,4BACC,aADD,iCACC,aADD,2BACC,aADD,+BACC,aADD,6BACC,aAMH,0DACC,aACA,6DACC,eACA,mBACA,kBACA,oEACC,aAGF,gFACC,kBAED,qFACC,kBACA,WACA,cACA,aACA,gBACA,iBACA,gBACA,iBACA,kBAED,4EACC,WACA,cACA,kFACC,WACA,cACA,WAGF,qEACC,eACA,WAED,sEACC,eACA,YAKF,0CACC,aACA,gDACC,WACA,cACA,WAED,sDACC,aAED,qEAMC,mBALA,2EACC,WACA,cACA,WAIF,6CACC,eACA,mBACA,kBACA,oDACC,aAGF,2DACC,WAED,8DACC,WAED,6DACC,YAED,0FACC,qBACA,mBACA,WACA,kBACA,eACA,yBAED,0EACC,eACA,gBACA,gBACA,kBAED,gEACC,kBAED,qEACC,WACA,kBACA,WACA,cACA,aACA,kBACA,yEACC,gBACA,iBACA,iBACA,gBAIF,4DACC,WACA,cACA,kEACC,WACA,cACA,WAGF,oEACC,WACA,yBACA,eAGA,gFACC,WACA,cACA,WAGF,6DACC,kBACA,qBACA,SACA,UACA,kBAEA,gEACC,eACA,kBACA,qBACA,gBACA,mBACA,iBACA,kBACA,kBACA,WACA,kBACA,eAED,2EACC,eAED,8EACC,mBAGF,oEACC,qBACA,SACA,uEACC,SAGA,kPAGC,cACA,gBACA,yBACA,kBACA,qBACA,WAED,kFACC,mBAID,kPAGC,cACA,mBACA,yBACA,kBACA,qBACA,WAED,kFACC,mBAID,kPAGC,cACA,gBACA,WACA,yBACA,kBACA,qBAED,kFACC,mBAID,wPAGC,cACA,gBACA,WACA,sBACA,kBACA,qBAED,oFACC,gBAID,+OAGC,cACA,mBACA,WACA,yBACA,kBACA,qBAED,iFACC,mBAID,kPAGC,cACA,mBACA,WACA,yBACA,kBACA,qBAED,kFACC,mBAOJ,0DACC,kBACA,aACA,gBACA,cACA,gBACA,6DACC,eACA,mBACA,kBACA,oEACC,aAGF,gFACC,kBAED,qFACC,kBACA,kBACA,cACA,aACA,gBACA,iBACA,gBACA,iBACA,cAED,4EACC,gBAED,4EACC,WACA,cACA,kFACC,WACA,cACA,WAGF,kFACC,WACA,cACA,kBACA,kBACA,gBACA,kBACA,yBACA,WACA,mBACA,0QAGC,cACA,WACA,YACA,WACA,qBAED,wFACC,WACA,cACA,WAIF,oFACC,cACA,kBACA,kBACA,gBACA,sBACA,WACA,gRAGC,cACA,WACA,YACA,WACA,qB", "file": "blocks.editor.build.css", "sourcesContent": [".mpp-spinner {\n\tspan {\n\t\tdisplay: block;\n\t\ttext-align: center;\n\t\tfloat: none;\n\t\tmargin: 0 auto;\n\t}\n}\n.editor-styles-wrapper .mpp-profile-wrap {\n\tmargin: 0 auto;\n\tpadding: 3%;\n\tborder-radius: 5px;\n\tmargin-bottom: 1.2em;\n\tmargin-bottom: 20px;\n\tline-height: 1.1;\n\t&.round .mpp-profile-image-wrapper {\n\t\tborder-radius: 50%;\n\t\toverflow: hidden;\n\t}\n\t&.round .mpp-profile-image-wrapper img {\n\t\tborder-radius: 50%;\n\t}\n\t.mpp-profile-image-wrapper button {\n\t\theight: auto;\n\t}\n\th1,h2,h3,h4,h5,h6 {\n\t\tclear: none;\n\t}\n\th2:before {\n\t\tdisplay: none;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tposition: relative;\n\t\tfloat: left;\n\t\tline-height: 1.1;\n\t\tz-index: 1000;\n\t\tbackground: #ddd;\n\t\tposition: relative;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\twidth: 100%;\n\t\tmargin-right: 20px;\n\t\tbutton.components-button,\n\t\tbutton {\n\t\t\tposition: relative;\n\t\t\tmin-width: 150px;\n\t\t\tmin-height: 150px;\n\t\t\tmax-width: 150px;\n\t\t\tmax-height: 150px;\n\t\t\tz-index: 1000;\n\t\t}\n\t\t.mpp-profile-image-square {\n\t\t\tposition: relative;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\theight: 100%;\n\t\t\twidth: 100%;\n\t\t\tz-index: 5;\n\t\t}\n\t\t.mpp-profile-image-square img {\n\t\t\theight: 100%;\n\t\t\twidth: 100%;\n\t\t\tposition: relative;\n\t\t\tz-index: 5;\n\t\t}\n\t\t.mpp-content-wrap {\n\t\t\tdisplay: block;\n\t\t\tpadding: 0 15px 0 15px;\n\t\t\t-ms-flex: 3 0 0px;\n\t\t\tflex: 3 0 0;\n\t\t}\n\t\t.mpp-profile-name {\n\t\t\tfont: 1.4em;\n\t\t\tline-height: 1.2;\n\t\t}\n\t\t@media only screen and (max-width: 600px) {\n\t\t\tflex: auto;\n\t\t}\n\t}\n\t.mpp-profile-view-posts {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tbackground-color: #cf6d38;\n\t\tcolor: #FFF;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t}\n}\n.mpp-profile-image-wrapper {\n\tbutton {\n\t\tposition: relative;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\tz-index: 1000;\n\t}\n}\n.editor-styles-wrapper .mpp-enhanced-profile-wrap.regular {\n\tposition: relative;\n\tmargin: 0 auto;\n\tline-height: 1.1;\n\t&.round .mpp-profile-image-wrapper {\n\t\tborder-radius: 50%;\n\t\toverflow: hidden;\n\t}\n\t&.round .mpp-profile-image-wrapper img {\n\t\tborder-radius: 50%;\n\t}\n\th2:before {\n\t\tdisplay: none;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tposition: relative;\n\t\tfloat: left;\n\t\tline-height: 1.1;\n\t\tz-index: 1000;\n\t\tbackground: #ddd;\n\t\tposition: relative;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\twidth: 100%;\n\t\tmargin-right: 20px;\n\t\tbutton {\n\t\t\tposition: relative;\n\t\t\tmin-width: 150px;\n\t\t\tmin-height: 150px;\n\t\t\tmax-width: 150px;\n\t\t\tmax-height: 150px;\n\t\t\tz-index: 1000;\n\t\t}\n\t\t.mpp-profile-image-square {\n\t\t\tposition: relative;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\theight: 100%;\n\t\t\twidth: 100%;\n\t\t\tz-index: 5;\n\t\t}\n\t\t.mpp-profile-image-square img {\n\t\t\theight: 100%;\n\t\t\twidth: 100%;\n\t\t\tposition: relative;\n\t\t\tz-index: 5;\n\t\t}\n\t\t.mpp-content-wrap {\n\t\t\tdisplay: block;\n\t\t\tpadding: 0 15px 0 15px;\n\t\t\t-ms-flex: 3 0 0px;\n\t\t\tflex: 3 0 0;\n\t\t}\n\t\t.mpp-profile-name {\n\t\t\tfont: 1.4em;\n\t\t\tline-height: 1.2;\n\t\t}\n\t\t@media only screen and (max-width: 600px) {\n\t\t\tflex: auto;\n\t\t}\n\t}\n\t.mpp-gutenberg-view-posts {\n\t\tclear: both;\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\n\t.mpp-profile-view-posts {\n\t\tclear: both;\n\t\tdisplay: block;\n\t\tfloat: left;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tmargin-right: 20px;\n\t\tbackground-color: #cf6d38;\n\t\tcolor: #FFF;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\n\t.mpp-profile-view-website {\n\t\tdisplay: block;\n\t\tfloat: left;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tbackground-color: #333;\n\t\tcolor: #FFF;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t}\n\t&.right .mpp-profile-view-posts {\n\t\tfloat: right;\n\t\tmargin-left: 20px;\n\t}\n\t&.center .mpp-profile-view-posts {\n\t\tdisplay: inline-block;\n\t\tfloat: none;\n\t\tmargin-left: 20px;\n\t}\n}\n\n\n/* Icon Sizes */\n$sizes: 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24;\n@each $size in $sizes {\n\t.editor-styles-wrapper .mpp-profile-wrap.mt-font-size-#{$size},\n\t.editor-styles-wrapper .mpp-enhanced-profile-wrap.regular .mt-font-size-#{$size},\n\t.editor-styles-wrapper .mpp-enhanced-profile-wrap.profile .mt-font-size-#{$size},\n\t.editor-styles-wrapper .mpp-enhanced-profile-wrap.compact .mt-font-size-#{$size},\n\t.editor-styles-wrapper .mpp-profile-text.mt-font-size-#{$size} {\n\t\tfont-size: #{$size}px;\n\t\tp,\n\t\tdiv {\n\t\t\tfont-size: #{$size}px;\n\t\t}\n\n\t}\n}\n\n/* For Social Media */\n.mpp-social {\n\tmargin-top: 15px;\n\tsvg {\n\t\tmax-width: 32px;\n\t\tmax-height: 32px;\n\t\tmargin-right: 10px;\n\t}\n}\n/* Social Media Colors */\n$colors: twitter #00aced,\n\tamazon #000000,\n\tbehance #0692e9,\n\tblogger #fb8f3d,\n\tcodepen #000000,\n\tdribble #F46899,\n\tdropbox #018BD3,\n\teventbrite #f6682F,\n\tfacebook #3b5998,\n\tflickr #ff0084,\n\tfoursquare #0072b1,\n\tghost #000000,\n\tgithub #070709,\n\tgoogle-plus #CF3D2E,\n\tinstagram #A1755C,\n\tlinkedin #0085AE,\n\tfeed #f26522,\n\tmedium #000000,\n\tpath #000000,\n\tpinterest #CC2127,\n\tpocket #000000,\n\tpolldaddy #bc0b0b,\n\treddit #000000,\n\tskype #01AEF2,\n\tspotify #1ed760,\n\tsquarespace #000000,\n\tstumbleupon #EB4823,\n\ttelegram #000000,\n\ttumblr-alt #314E6C,\n\ttwitch #4b367c,\n\ttwitter-alt #00aced,\n\tvimeo #1ab7ea,\n\twordpress #21759b,\n\tyoutube #bb0000\n;\n.mpp-social {\n\tclear: both;\n}\n.mpp-social svg {\n\t@each $color in $colors {\n\t\t&.icon-#{nth($color,1)} {\n\t\t\tfill: #{nth($color,2)};\n\t\t}\n\t}\n}\n\n/* For Profile Theme */\n.editor-styles-wrapper .mpp-enhanced-profile-wrap.profile {\n\tpadding: 10px;\n\th2 {\n\t\tfont-size: 0.8em;\n\t\tfont-weight: normal;\n\t\tmargin-bottom: 5px;\n\t\t&:before {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\t&.round .profile-avatar {\n\t\tborder-radius: 50%;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tposition: relative;\n\t\tfloat: left;\n\t\tline-height: 1.0;\n\t\tz-index: 1000;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\tmargin-right: 15px;\n\t}\n\t.mpp-profile-meta {\n\t\tclear: both;\n\t\tmargin: 10px 0;\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\t.alignleft {\n\t\tmax-width: none;\n\t\tfloat: left;\n\t}\n\t.alignright {\n\t\tmax-width: none;\n\t\tfloat: right;\n\t}\n}\n\n/* For Tabbed Theme */\n.editor-styles-wrapper .mpp-author-tabbed {\n\tpadding: 10px;\n\t&:after {\n\t\tcontent: \"\";\n\t\tdisplay: table;\n\t\tclear: both;\n\t}\n\t.mpp-social {\n\t\tmargin-top: 0;\n\t}\n\t.mpp-author-social-wrapper {\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t\tmargin-bottom: 10px;\n\t}\n\th2 {\n\t\tfont-size: 0.8em;\n\t\tfont-weight: normal;\n\t\tmargin-bottom: 5px;\n\t\t&:before {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\t.mpp-tab-wrapper {\n\t\tclear: both;\n\t}\n\t.mpp-author-heading {\n\t\tfloat: left;\n\t}\n\t.mpp-author-social {\n\t\tfloat: right;\n\t}\n\t.mpp-author-heading .mpp-author-profile-heading {\n\t\tdisplay: inline-block;\n\t\tbackground: #42737b;\n\t\tcolor: #FFF;\n\t\tpadding: 10px 20px;\n\t\tfont-size: 14px;\n\t\ttext-transform: uppercase;\n\t}\n\t.mpp-author-profile-sub-heading {\n\t\tfont-size: 14px;\n\t\tline-height: 1.1;\n\t\tmax-width: 150px;\n\t\ttext-align: center;\n\t}\n\t&.round .profile-avatar {\n\t\tborder-radius: 50%;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tclear: both;\n\t\tposition: relative;\n\t\tfloat: left;\n\t\tline-height: 1.0;\n\t\tz-index: 1000;\n\t\tmargin-right: 30px;\n\t\timg {\n\t\t\tmin-width: 150px;\n\t\t\tmin-height: 150px;\n\t\t\tmax-height: 150px;\n\t\t\tmax-width: 150px;\n\t\t}\n\n\t}\n\t.mpp-profile-meta {\n\t\tclear: both;\n\t\tmargin: 10px 0;\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\t.mpp-author-profile-title {\n\t\tcolor: gray;\n\t\ttext-transform: uppercase;\n\t\tfont-size: 12px;\n\t}\n\t.mpp-tabbed-profile-information {\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\tul.mpp-author-tabs {\n\t\tposition:relative;\n\t\tlist-style-type: none;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\ttext-align: center;\n\n\t\tli {\n\t\t\tcursor: pointer;\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tmin-width: 200px;\n\t\t\tbackground: #42737b;\n\t\t\tmargin: 0 5px 0 0;\n\t\t\ttext-align: center;\n\t\t\tmargin-right: 10px;\n\t\t\tcolor: #FFF;\n\t\t\tpadding: 10px 20px;\n\t\t\tfont-size: 16px;\n\t\t}\n\t\tli:last-child {\n\t\t\tmargin-right: 0;\n\t\t}\n\t\tli.mpp-tab-posts {\n\t\t\tbackground: #30424b;\n\t\t}\n\t}\n\tul.mpp-author-tab-content {\n\t\tlist-style-type: none;\n\t\tmargin: 0;\n\t\tli {\n\t\t\tmargin: 0;\n\t\t}\n\t\t&.white {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #FFFFFF;\n\t\t\t\tborder: 1px solid darken(#FFFFFF, 5%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#FFFFFF, 5%);\n\t\t\t}\n\t\t}\n\t\t&.light {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #f7f7f7;\n\t\t\t\tborder: 1px solid darken(#f7f7f7, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#f7f7f7, 10%);\n\t\t\t}\n\t\t}\n\t\t&.black {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #333;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tborder: 1px solid darken(#333, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#333, 10%);\n\t\t\t}\n\t\t}\n\t\t&.magenta {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #FF00FF;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tborder: 1px solid darken(#FF00FF, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#FF00FF, 10%);\n\t\t\t}\n\t\t}\n\t\t&.blue {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #0009c1;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tborder: 1px solid darken(#0009c1, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#0009c1, 10%);\n\t\t\t}\n\t\t}\n\t\t&.green {\n\t\t\ta,\n\t\t\ta:hover,\n\t\t\ta:visited {\n\t\t\t\tdisplay: block;\n\t\t\t\tbackground: #03ac27;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tborder: 1px solid darken(#03ac27, 10%);\n\t\t\t\tpadding: 10px 20px;\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\t\t\ta:hover {\n\t\t\t\tbackground: darken(#03ac27, 10%);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* For Compact Theme */\n.editor-styles-wrapper .mpp-enhanced-profile-wrap.compact {\n\ttext-align: center;\n\tpadding: 10px;\n\tmax-width: 400px;\n\tmargin: 0 auto;\n\tline-height: 1.1;\n\th2 {\n\t\tfont-size: 0.8em;\n\t\tfont-weight: normal;\n\t\tmargin-bottom: 5px;\n\t\t&:before {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\t&.round .profile-avatar {\n\t\tborder-radius: 50%;\n\t}\n\t.mpp-profile-image-wrapper {\n\t\tposition: relative;\n\t\ttext-align: center;\n\t\tline-height: 1.0;\n\t\tz-index: 1000;\n\t\tmin-width: 150px;\n\t\tmin-height: 150px;\n\t\tmax-width: 150px;\n\t\tmax-height: 150px;\n\t\tmargin: 0 auto;\n\t}\n\t.mpp-profile-text {\n\t\tline-height: 1.1;\n\t}\n\t.mpp-compact-meta {\n\t\tclear: both;\n\t\tmargin: 10px 0;\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\t.mpp-profile-view-posts {\n\t\tclear: both;\n\t\tdisplay: block;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tmargin-right: 20px;\n\t\tbackground-color: #cf6d38;\n\t\tcolor: #FFF;\n\t\tmargin-bottom: 10px;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t\t&:after {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: table;\n\t\t\tclear: both;\n\t\t}\n\t}\n\n\t.mpp-profile-view-website {\n\t\tdisplay: block;\n\t\ttext-align: center;\n\t\tpadding: 10px 20px;\n\t\tmargin-top: 20px;\n\t\tbackground-color: #333;\n\t\tcolor: #FFF;\n\t\ta,\n\t\ta:hover,\n\t\ta:visited {\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tcolor: #FFF;\n\t\t\ttext-decoration: none;\n\t\t}\n\t}\n}"], "sourceRoot": ""}