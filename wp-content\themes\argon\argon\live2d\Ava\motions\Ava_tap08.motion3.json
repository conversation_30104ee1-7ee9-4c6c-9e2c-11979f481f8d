{"Version": 3, "Meta": {"Duration": 2.77, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 114, "TotalSegmentCount": 484, "TotalPointCount": 1432, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.378, 0, 0.522, 1, 0.667, 1, 1, 0.844, 1, 1.022, 0, 1.2, 0, 1, 1.367, 0, 1.533, 2, 1.7, 2, 0, 2.767, 2]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.078, -1, 0.156, 11, 0.233, 11, 1, 0.378, 11, 0.522, -12, 0.667, -12, 1, 0.844, -12, 1.022, 14, 1.2, 14, 1, 1.367, 14, 1.533, -16, 1.7, -16, 0, 2.767, -16]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.167, 0, 0.333, -6, 0.5, -6, 0, 2.767, -6]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, 20.324, 0.233, 20.324, 1, 0.344, 20.324, 0.456, -18.894, 0.567, -18.894, 1, 0.644, -18.894, 0.722, 7.194, 0.8, 7.194, 1, 0.867, 7.194, 0.933, -2.856, 1, -2.856, 1, 1.078, -2.856, 1.156, 1.146, 1.233, 1.146, 1, 1.311, 1.146, 1.389, -0.458, 1.467, -0.458, 1, 1.544, -0.458, 1.622, 0.182, 1.7, 0.182, 1, 1.833, 0.182, 1.967, -30, 2.1, -30, 1, 2.178, -30, 2.256, 30, 2.333, 30, 1, 2.344, 30, 2.356, 30, 2.367, 30, 1, 2.433, 30, 2.5, -11.814, 2.567, -11.814, 1, 2.633, -11.814, 2.7, 0.298, 2.767, 3.759]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.167, 1, 0.333, 0, 0.5, 0, 1, 0.767, 0, 1.033, 0, 1.3, 0, 1, 1.5, 0, 1.7, 0, 1.9, 0, 1, 2.022, 0, 2.144, 1, 2.267, 1, 0, 2.767, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0.5, 0.5, 0.5, 1, 0.767, 0.5, 1.033, 0.5, 1.3, 0.5, 1, 1.5, 0.5, 1.7, 0.5, 1.9, 0.5, 0, 2.767, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.167, 1, 0.333, 0, 0.5, 0, 1, 0.767, 0, 1.033, 0, 1.3, 0, 1, 1.5, 0, 1.7, 0, 1.9, 0, 1, 2.022, 0, 2.144, 1, 2.267, 1, 0, 2.767, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0.5, 0.5, 0.5, 1, 0.767, 0.5, 1.033, 0.5, 1.3, 0.5, 1, 1.5, 0.5, 1.7, 0.5, 1.9, 0.5, 0, 2.767, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.3, 1, 0.167, -0.3, 0.333, -0.8, 0.5, -0.8, 0, 2.767, -0.8]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.3, 1, 0.167, -0.3, 0.333, -0.8, 0.5, -0.8, 0, 2.767, -0.8]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.167, 1, 0.333, 1, 0.5, 1, 1, 0.722, 1, 0.944, 1, 1.167, 1, 1, 1.4, 1, 1.633, 1, 1.867, 1, 1, 2.089, 1, 2.311, 1, 2.533, 1, 0, 2.767, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 1, 0.5, 1, 1, 0.722, 1, 0.944, 0.8, 1.167, 0.8, 1, 1.4, 0.8, 1.633, 1, 1.867, 1, 1, 2.089, 1, 2.311, 0, 2.533, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.167, 0, 0.333, -20, 0.5, -20, 1, 0.767, -20, 1.033, -20, 1.3, -20, 1, 1.722, -20, 2.144, -10, 2.567, -10, 0, 2.767, -10]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.167, 0, 0.333, -19, 0.5, -19, 1, 0.767, -19, 1.033, -19, 1.3, -19, 1, 1.722, -19, 2.144, -11, 2.567, -11, 0, 2.767, -11]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 10, 1, 0.311, 10, 0.622, -9, 0.933, -9, 1, 1.233, -9, 1.533, 7, 1.833, 7, 1, 2.144, 7, 2.456, -12, 2.767, -12]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 12, 0, 0.4, 12, 1, 0.478, 12, 0.556, -21, 0.633, -21, 1, 0.711, -21, 0.789, 30, 0.867, 30, 1, 0.922, 30, 0.978, -18, 1.033, -18, 1, 1.078, -18, 1.122, 19, 1.167, 19, 0, 2.767, 19]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 30, 1, 0.167, 30, 0.333, -30, 0.5, -30, 1, 0.7, -30, 0.9, 30, 1.1, 30, 1, 1.311, 30, 1.522, -30, 1.733, -30, 0, 2.767, -30]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.756, 0.2, -0.756, 1, 0.3, -0.756, 0.4, 1.074, 0.5, 1.074, 1, 0.644, 1.074, 0.789, -1.028, 0.933, -1.028, 1, 1.1, -1.028, 1.267, 0.893, 1.433, 0.893, 1, 1.567, 0.893, 1.7, -0.471, 1.833, -0.471, 1, 1.956, -0.471, 2.078, 0.135, 2.2, 0.135, 1, 2.322, 0.135, 2.444, -0.039, 2.567, -0.039, 1, 2.633, -0.039, 2.7, -0.024, 2.767, -0.01]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.756, 0.2, -0.756, 1, 0.3, -0.756, 0.4, 1.074, 0.5, 1.074, 1, 0.644, 1.074, 0.789, -1.028, 0.933, -1.028, 1, 1.1, -1.028, 1.267, 0.893, 1.433, 0.893, 1, 1.567, 0.893, 1.7, -0.471, 1.833, -0.471, 1, 1.956, -0.471, 2.078, 0.135, 2.2, 0.135, 1, 2.322, 0.135, 2.444, -0.039, 2.567, -0.039, 1, 2.633, -0.039, 2.7, -0.024, 2.767, -0.01]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.922, 0, 1.844, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.922, 0, 1.844, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.922, 0, 1.844, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.344, 0, 0.422, -0.224, 0.5, -0.224, 1, 0.611, -0.224, 0.722, 0.217, 0.833, 0.217, 1, 1.022, 0.217, 1.211, -0.163, 1.4, -0.163, 1, 1.533, -0.163, 1.667, 0.185, 1.8, 0.185, 1, 1.9, 0.185, 2, -0.085, 2.1, -0.085, 1, 2.211, -0.085, 2.322, 0.039, 2.433, 0.039, 1, 2.533, 0.039, 2.633, -0.018, 2.733, -0.018, 1, 2.744, -0.018, 2.756, -0.018, 2.767, -0.017]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0.061, 1, 0.067, 0.029, 0.133, -0.085, 0.2, -0.085, 1, 0.289, -0.085, 0.378, 0.199, 0.467, 0.199, 1, 0.556, 0.199, 0.644, -0.389, 0.733, -0.389, 1, 0.833, -0.389, 0.933, 0.407, 1.033, 0.407, 1, 1.133, 0.407, 1.233, -0.266, 1.333, -0.266, 1, 1.533, -0.266, 1.733, 0.241, 1.933, 0.241, 1, 2.044, 0.241, 2.156, -0.239, 2.267, -0.239, 1, 2.378, -0.239, 2.489, 0.167, 2.6, 0.167, 1, 2.656, 0.167, 2.711, 0.083, 2.767, 0.009]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.344, 0, 0.422, -0.311, 0.5, -0.311, 1, 0.611, -0.311, 0.722, 0.302, 0.833, 0.302, 1, 1.022, 0.302, 1.211, -0.226, 1.4, -0.226, 1, 1.533, -0.226, 1.667, 0.257, 1.8, 0.257, 1, 1.9, 0.257, 2, -0.118, 2.1, -0.118, 1, 2.211, -0.118, 2.322, 0.054, 2.433, 0.054, 1, 2.533, 0.054, 2.633, -0.025, 2.733, -0.025, 1, 2.744, -0.025, 2.756, -0.024, 2.767, -0.024]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.344, 0, 0.422, -0.311, 0.5, -0.311, 1, 0.611, -0.311, 0.722, 0.302, 0.833, 0.302, 1, 1.022, 0.302, 1.211, -0.226, 1.4, -0.226, 1, 1.533, -0.226, 1.667, 0.257, 1.8, 0.257, 1, 1.9, 0.257, 2, -0.118, 2.1, -0.118, 1, 2.211, -0.118, 2.322, 0.054, 2.433, 0.054, 1, 2.533, 0.054, 2.633, -0.025, 2.733, -0.025, 1, 2.744, -0.025, 2.756, -0.024, 2.767, -0.024]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.344, 0, 0.422, -0.311, 0.5, -0.311, 1, 0.611, -0.311, 0.722, 0.302, 0.833, 0.302, 1, 1.022, 0.302, 1.211, -0.226, 1.4, -0.226, 1, 1.533, -0.226, 1.667, 0.257, 1.8, 0.257, 1, 1.9, 0.257, 2, -0.118, 2.1, -0.118, 1, 2.211, -0.118, 2.322, 0.054, 2.433, 0.054, 1, 2.533, 0.054, 2.633, -0.025, 2.733, -0.025, 1, 2.744, -0.025, 2.756, -0.024, 2.767, -0.024]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.344, 0, 0.422, -0.224, 0.5, -0.224, 1, 0.611, -0.224, 0.722, 0.217, 0.833, 0.217, 1, 1.022, 0.217, 1.211, -0.163, 1.4, -0.163, 1, 1.533, -0.163, 1.667, 0.185, 1.8, 0.185, 1, 1.9, 0.185, 2, -0.085, 2.1, -0.085, 1, 2.211, -0.085, 2.322, 0.039, 2.433, 0.039, 1, 2.533, 0.039, 2.633, -0.018, 2.733, -0.018, 1, 2.744, -0.018, 2.756, -0.018, 2.767, -0.017]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0.061, 1, 0.067, 0.029, 0.133, -0.085, 0.2, -0.085, 1, 0.289, -0.085, 0.378, 0.199, 0.467, 0.199, 1, 0.556, 0.199, 0.644, -0.389, 0.733, -0.389, 1, 0.833, -0.389, 0.933, 0.407, 1.033, 0.407, 1, 1.133, 0.407, 1.233, -0.266, 1.333, -0.266, 1, 1.533, -0.266, 1.733, 0.241, 1.933, 0.241, 1, 2.044, 0.241, 2.156, -0.239, 2.267, -0.239, 1, 2.378, -0.239, 2.489, 0.167, 2.6, 0.167, 1, 2.656, 0.167, 2.711, 0.083, 2.767, 0.009]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.344, 0, 0.422, -0.311, 0.5, -0.311, 1, 0.611, -0.311, 0.722, 0.302, 0.833, 0.302, 1, 1.022, 0.302, 1.211, -0.226, 1.4, -0.226, 1, 1.533, -0.226, 1.667, 0.257, 1.8, 0.257, 1, 1.9, 0.257, 2, -0.118, 2.1, -0.118, 1, 2.211, -0.118, 2.322, 0.054, 2.433, 0.054, 1, 2.533, 0.054, 2.633, -0.025, 2.733, -0.025, 1, 2.744, -0.025, 2.756, -0.024, 2.767, -0.024]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.367, 0, 0.467, -0.137, 0.567, -0.137, 1, 0.689, -0.137, 0.811, 0.1, 0.933, 0.1, 1, 1.111, 0.1, 1.289, -0.144, 1.467, -0.144, 1, 1.6, -0.144, 1.733, 0.07, 1.867, 0.07, 1, 1.989, 0.07, 2.111, -0.017, 2.233, -0.017, 1, 2.367, -0.017, 2.5, 0.004, 2.633, 0.004, 1, 2.678, 0.004, 2.722, 0.004, 2.767, 0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.356, 0, 0.444, 0.311, 0.533, 0.311, 1, 0.622, 0.311, 0.711, -0.378, 0.8, -0.378, 1, 0.933, -0.378, 1.067, 0.26, 1.2, 0.26, 1, 1.378, 0.26, 1.556, -0.274, 1.733, -0.274, 1, 1.844, -0.274, 1.956, 0.211, 2.067, 0.211, 1, 2.189, 0.211, 2.311, -0.091, 2.433, -0.091, 1, 2.544, -0.091, 2.656, 0.011, 2.767, 0.03]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.556, 0, 0.611, 0.288, 0.667, 0.288, 1, 0.756, 0.288, 0.844, -0.424, 0.933, -0.424, 1, 1.067, -0.424, 1.2, 0.362, 1.333, 0.362, 1, 1.489, 0.362, 1.644, -0.351, 1.8, -0.351, 1, 1.933, -0.351, 2.067, 0.29, 2.2, 0.29, 1, 2.322, 0.29, 2.444, -0.16, 2.567, -0.16, 1, 2.633, -0.16, 2.7, -0.091, 2.767, -0.029]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.689, 0, 0.744, 0.302, 0.8, 0.302, 1, 0.9, 0.302, 1, -0.494, 1.1, -0.494, 1, 1.222, -0.494, 1.344, 0.509, 1.467, 0.509, 1, 1.611, 0.509, 1.756, -0.479, 1.9, -0.479, 1, 2.044, -0.479, 2.189, 0.402, 2.333, 0.402, 1, 2.456, 0.402, 2.578, -0.256, 2.7, -0.256, 1, 2.722, -0.256, 2.744, -0.245, 2.767, -0.227]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.367, 0, 0.467, -0.343, 0.567, -0.343, 1, 0.689, -0.343, 0.811, 0.25, 0.933, 0.25, 1, 1.111, 0.25, 1.289, -0.359, 1.467, -0.359, 1, 1.6, -0.359, 1.733, 0.175, 1.867, 0.175, 1, 1.989, 0.175, 2.111, -0.043, 2.233, -0.043, 1, 2.367, -0.043, 2.5, 0.011, 2.633, 0.011, 1, 2.678, 0.011, 2.722, 0.009, 2.767, 0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.356, 0, 0.444, 0.311, 0.533, 0.311, 1, 0.622, 0.311, 0.711, -0.378, 0.8, -0.378, 1, 0.933, -0.378, 1.067, 0.26, 1.2, 0.26, 1, 1.378, 0.26, 1.556, -0.274, 1.733, -0.274, 1, 1.844, -0.274, 1.956, 0.211, 2.067, 0.211, 1, 2.189, 0.211, 2.311, -0.091, 2.433, -0.091, 1, 2.544, -0.091, 2.656, 0.011, 2.767, 0.03]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.556, 0, 0.611, 0.288, 0.667, 0.288, 1, 0.756, 0.288, 0.844, -0.424, 0.933, -0.424, 1, 1.067, -0.424, 1.2, 0.362, 1.333, 0.362, 1, 1.489, 0.362, 1.644, -0.351, 1.8, -0.351, 1, 1.933, -0.351, 2.067, 0.29, 2.2, 0.29, 1, 2.322, 0.29, 2.444, -0.16, 2.567, -0.16, 1, 2.633, -0.16, 2.7, -0.091, 2.767, -0.029]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.367, 0, 0.467, -0.137, 0.567, -0.137, 1, 0.689, -0.137, 0.811, 0.1, 0.933, 0.1, 1, 1.111, 0.1, 1.289, -0.144, 1.467, -0.144, 1, 1.6, -0.144, 1.733, 0.07, 1.867, 0.07, 1, 1.989, 0.07, 2.111, -0.017, 2.233, -0.017, 1, 2.367, -0.017, 2.5, 0.004, 2.633, 0.004, 1, 2.678, 0.004, 2.722, 0.004, 2.767, 0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.356, 0, 0.444, 0.311, 0.533, 0.311, 1, 0.622, 0.311, 0.711, -0.378, 0.8, -0.378, 1, 0.933, -0.378, 1.067, 0.26, 1.2, 0.26, 1, 1.378, 0.26, 1.556, -0.274, 1.733, -0.274, 1, 1.844, -0.274, 1.956, 0.211, 2.067, 0.211, 1, 2.189, 0.211, 2.311, -0.091, 2.433, -0.091, 1, 2.544, -0.091, 2.656, 0.011, 2.767, 0.03]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.556, 0, 0.611, 0.288, 0.667, 0.288, 1, 0.756, 0.288, 0.844, -0.424, 0.933, -0.424, 1, 1.067, -0.424, 1.2, 0.362, 1.333, 0.362, 1, 1.489, 0.362, 1.644, -0.351, 1.8, -0.351, 1, 1.933, -0.351, 2.067, 0.29, 2.2, 0.29, 1, 2.322, 0.29, 2.444, -0.16, 2.567, -0.16, 1, 2.633, -0.16, 2.7, -0.091, 2.767, -0.029]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.689, 0, 0.744, 0.302, 0.8, 0.302, 1, 0.9, 0.302, 1, -0.494, 1.1, -0.494, 1, 1.222, -0.494, 1.344, 0.509, 1.467, 0.509, 1, 1.611, 0.509, 1.756, -0.479, 1.9, -0.479, 1, 2.044, -0.479, 2.189, 0.402, 2.333, 0.402, 1, 2.456, 0.402, 2.578, -0.256, 2.7, -0.256, 1, 2.722, -0.256, 2.744, -0.245, 2.767, -0.227]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.367, 0, 0.467, -0.343, 0.567, -0.343, 1, 0.689, -0.343, 0.811, 0.25, 0.933, 0.25, 1, 1.111, 0.25, 1.289, -0.359, 1.467, -0.359, 1, 1.6, -0.359, 1.733, 0.175, 1.867, 0.175, 1, 1.989, 0.175, 2.111, -0.043, 2.233, -0.043, 1, 2.367, -0.043, 2.5, 0.011, 2.633, 0.011, 1, 2.678, 0.011, 2.722, 0.009, 2.767, 0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.356, 0, 0.444, 0.311, 0.533, 0.311, 1, 0.622, 0.311, 0.711, -0.378, 0.8, -0.378, 1, 0.933, -0.378, 1.067, 0.26, 1.2, 0.26, 1, 1.378, 0.26, 1.556, -0.274, 1.733, -0.274, 1, 1.844, -0.274, 1.956, 0.211, 2.067, 0.211, 1, 2.189, 0.211, 2.311, -0.091, 2.433, -0.091, 1, 2.544, -0.091, 2.656, 0.011, 2.767, 0.03]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.556, 0, 0.611, 0.288, 0.667, 0.288, 1, 0.756, 0.288, 0.844, -0.424, 0.933, -0.424, 1, 1.067, -0.424, 1.2, 0.362, 1.333, 0.362, 1, 1.489, 0.362, 1.644, -0.351, 1.8, -0.351, 1, 1.933, -0.351, 2.067, 0.29, 2.2, 0.29, 1, 2.322, 0.29, 2.444, -0.16, 2.567, -0.16, 1, 2.633, -0.16, 2.7, -0.091, 2.767, -0.029]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.367, 0, 0.467, -0.343, 0.567, -0.343, 1, 0.689, -0.343, 0.811, 0.25, 0.933, 0.25, 1, 1.111, 0.25, 1.289, -0.359, 1.467, -0.359, 1, 1.6, -0.359, 1.733, 0.175, 1.867, 0.175, 1, 1.989, 0.175, 2.111, -0.043, 2.233, -0.043, 1, 2.367, -0.043, 2.5, 0.011, 2.633, 0.011, 1, 2.678, 0.011, 2.722, 0.009, 2.767, 0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.356, 0, 0.444, 0.311, 0.533, 0.311, 1, 0.622, 0.311, 0.711, -0.378, 0.8, -0.378, 1, 0.933, -0.378, 1.067, 0.26, 1.2, 0.26, 1, 1.378, 0.26, 1.556, -0.274, 1.733, -0.274, 1, 1.844, -0.274, 1.956, 0.211, 2.067, 0.211, 1, 2.189, 0.211, 2.311, -0.091, 2.433, -0.091, 1, 2.544, -0.091, 2.656, 0.011, 2.767, 0.03]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.556, 0, 0.611, 0.288, 0.667, 0.288, 1, 0.756, 0.288, 0.844, -0.424, 0.933, -0.424, 1, 1.067, -0.424, 1.2, 0.362, 1.333, 0.362, 1, 1.489, 0.362, 1.644, -0.351, 1.8, -0.351, 1, 1.933, -0.351, 2.067, 0.29, 2.2, 0.29, 1, 2.322, 0.29, 2.444, -0.16, 2.567, -0.16, 1, 2.633, -0.16, 2.7, -0.091, 2.767, -0.029]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.367, 0, 0.467, -0.343, 0.567, -0.343, 1, 0.689, -0.343, 0.811, 0.25, 0.933, 0.25, 1, 1.111, 0.25, 1.289, -0.359, 1.467, -0.359, 1, 1.6, -0.359, 1.733, 0.175, 1.867, 0.175, 1, 1.989, 0.175, 2.111, -0.043, 2.233, -0.043, 1, 2.367, -0.043, 2.5, 0.011, 2.633, 0.011, 1, 2.678, 0.011, 2.722, 0.009, 2.767, 0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.356, 0, 0.444, 0.311, 0.533, 0.311, 1, 0.622, 0.311, 0.711, -0.378, 0.8, -0.378, 1, 0.933, -0.378, 1.067, 0.26, 1.2, 0.26, 1, 1.378, 0.26, 1.556, -0.274, 1.733, -0.274, 1, 1.844, -0.274, 1.956, 0.211, 2.067, 0.211, 1, 2.189, 0.211, 2.311, -0.091, 2.433, -0.091, 1, 2.544, -0.091, 2.656, 0.011, 2.767, 0.03]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.556, 0, 0.611, 0.288, 0.667, 0.288, 1, 0.756, 0.288, 0.844, -0.424, 0.933, -0.424, 1, 1.067, -0.424, 1.2, 0.362, 1.333, 0.362, 1, 1.489, 0.362, 1.644, -0.351, 1.8, -0.351, 1, 1.933, -0.351, 2.067, 0.29, 2.2, 0.29, 1, 2.322, 0.29, 2.444, -0.16, 2.567, -0.16, 1, 2.633, -0.16, 2.7, -0.091, 2.767, -0.029]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.63, 0.2, -0.63, 1, 0.3, -0.63, 0.4, 0.895, 0.5, 0.895, 1, 0.644, 0.895, 0.789, -0.856, 0.933, -0.856, 1, 1.1, -0.856, 1.267, 0.744, 1.433, 0.744, 1, 1.567, 0.744, 1.7, -0.392, 1.833, -0.392, 1, 1.956, -0.392, 2.078, 0.113, 2.2, 0.113, 1, 2.322, 0.113, 2.444, -0.032, 2.567, -0.032, 1, 2.633, -0.032, 2.7, -0.02, 2.767, -0.009]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, 0.438, 0.167, 0.438, 1, 0.244, 0.438, 0.322, -0.943, 0.4, -0.943, 1, 0.511, -0.943, 0.622, 1.045, 0.733, 1.045, 1, 0.856, 1.045, 0.978, -0.756, 1.1, -0.756, 1, 1.278, -0.756, 1.456, 0.448, 1.633, 0.448, 1, 1.756, 0.448, 1.878, -0.458, 2, -0.458, 1, 2.122, -0.458, 2.244, 0.244, 2.367, 0.244, 1, 2.478, 0.244, 2.589, -0.102, 2.7, -0.102, 1, 2.722, -0.102, 2.744, -0.098, 2.767, -0.09]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0.134, 1, 0.078, 0.174, 0.156, 0.336, 0.233, 0.336, 1, 0.333, 0.336, 0.433, -0.806, 0.533, -0.806, 1, 0.644, -0.806, 0.756, 1.025, 0.867, 1.025, 1, 0.989, 1.025, 1.111, -0.807, 1.233, -0.807, 1, 1.378, -0.807, 1.522, 0.392, 1.667, 0.392, 1, 1.822, 0.392, 1.978, -0.402, 2.133, -0.402, 1, 2.244, -0.402, 2.356, 0.225, 2.467, 0.225, 1, 2.567, 0.225, 2.667, -0.016, 2.767, -0.103]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, -0.125, 1, 0.011, -0.125, 0.022, -0.125, 0.033, -0.125, 1, 0.122, -0.125, 0.211, 0.336, 0.3, 0.336, 1, 0.4, 0.336, 0.5, -0.675, 0.6, -0.675, 1, 0.7, -0.675, 0.8, 0.97, 0.9, 0.97, 1, 1.022, 0.97, 1.144, -0.988, 1.267, -0.988, 1, 1.389, -0.988, 1.511, 0.593, 1.633, 0.593, 1, 1.811, 0.593, 1.989, -0.404, 2.167, -0.404, 1, 2.278, -0.404, 2.389, 0.344, 2.5, 0.344, 1, 2.589, 0.344, 2.678, 0.061, 2.767, -0.094]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.311, 0, 0.356, 0.327, 0.4, 0.327, 1, 0.489, 0.327, 0.578, -0.831, 0.667, -0.831, 1, 0.778, -0.831, 0.889, 1.369, 1, 1.369, 1, 1.111, 1.369, 1.222, -1.449, 1.333, -1.449, 1, 1.456, -1.449, 1.578, 0.965, 1.7, 0.965, 1, 1.867, 0.965, 2.033, -0.458, 2.2, -0.458, 1, 2.322, -0.458, 2.444, 0.463, 2.567, 0.463, 1, 2.633, 0.463, 2.7, 0.182, 2.767, -0.043]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -0.63, 0.2, -0.63, 1, 0.3, -0.63, 0.4, 0.895, 0.5, 0.895, 1, 0.644, 0.895, 0.789, -0.856, 0.933, -0.856, 1, 1.1, -0.856, 1.267, 0.744, 1.433, 0.744, 1, 1.567, 0.744, 1.7, -0.392, 1.833, -0.392, 1, 1.956, -0.392, 2.078, 0.113, 2.2, 0.113, 1, 2.322, 0.113, 2.444, -0.032, 2.567, -0.032, 1, 2.633, -0.032, 2.7, -0.02, 2.767, -0.009]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, 0.438, 0.167, 0.438, 1, 0.244, 0.438, 0.322, -0.943, 0.4, -0.943, 1, 0.511, -0.943, 0.622, 1.045, 0.733, 1.045, 1, 0.856, 1.045, 0.978, -0.756, 1.1, -0.756, 1, 1.278, -0.756, 1.456, 0.448, 1.633, 0.448, 1, 1.756, 0.448, 1.878, -0.458, 2, -0.458, 1, 2.122, -0.458, 2.244, 0.244, 2.367, 0.244, 1, 2.478, 0.244, 2.589, -0.102, 2.7, -0.102, 1, 2.722, -0.102, 2.744, -0.098, 2.767, -0.09]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0.134, 1, 0.078, 0.174, 0.156, 0.336, 0.233, 0.336, 1, 0.333, 0.336, 0.433, -0.806, 0.533, -0.806, 1, 0.644, -0.806, 0.756, 1.025, 0.867, 1.025, 1, 0.989, 1.025, 1.111, -0.807, 1.233, -0.807, 1, 1.378, -0.807, 1.522, 0.392, 1.667, 0.392, 1, 1.822, 0.392, 1.978, -0.402, 2.133, -0.402, 1, 2.244, -0.402, 2.356, 0.225, 2.467, 0.225, 1, 2.567, 0.225, 2.667, -0.016, 2.767, -0.103]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, -0.125, 1, 0.011, -0.125, 0.022, -0.125, 0.033, -0.125, 1, 0.122, -0.125, 0.211, 0.336, 0.3, 0.336, 1, 0.4, 0.336, 0.5, -0.675, 0.6, -0.675, 1, 0.7, -0.675, 0.8, 0.97, 0.9, 0.97, 1, 1.022, 0.97, 1.144, -0.988, 1.267, -0.988, 1, 1.389, -0.988, 1.511, 0.593, 1.633, 0.593, 1, 1.811, 0.593, 1.989, -0.404, 2.167, -0.404, 1, 2.278, -0.404, 2.389, 0.344, 2.5, 0.344, 1, 2.589, 0.344, 2.678, 0.061, 2.767, -0.094]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.311, 0, 0.356, 0.327, 0.4, 0.327, 1, 0.489, 0.327, 0.578, -0.831, 0.667, -0.831, 1, 0.778, -0.831, 0.889, 1.369, 1, 1.369, 1, 1.111, 1.369, 1.222, -1.449, 1.333, -1.449, 1, 1.456, -1.449, 1.578, 0.965, 1.7, 0.965, 1, 1.867, 0.965, 2.033, -0.458, 2.2, -0.458, 1, 2.322, -0.458, 2.444, 0.463, 2.567, 0.463, 1, 2.633, 0.463, 2.7, 0.182, 2.767, -0.043]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, -1.718, 1, 0.144, -8.994, 0.289, -14, 0.433, -14, 1, 0.733, -14, 1.033, 14, 1.333, 14, 1, 1.633, 14, 1.933, -11, 2.233, -11, 1, 2.411, -11, 2.589, 0, 2.767, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, -1.554, 1, 0.211, -11.051, 0.422, -14, 0.633, -14, 1, 0.933, -14, 1.233, 14, 1.533, 14, 1, 1.833, 14, 2.133, -10, 2.433, -10, 1, 2.544, -10, 2.656, -9.969, 2.767, -9.91]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, -1.045, 1, 0.289, -9.914, 0.578, -14, 0.867, -14, 1, 1.167, -14, 1.467, 12, 1.767, 12, 1, 2.067, 12, 2.367, -12, 2.667, -12, 1, 2.7, -12, 2.733, -11.996, 2.767, -11.989]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 0, 0, 2.77, 0]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 2.77, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 2.77, 1]}]}