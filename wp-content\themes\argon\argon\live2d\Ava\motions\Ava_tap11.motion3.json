{"Version": 3, "Meta": {"Duration": 8.4, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 118, "TotalSegmentCount": 1200, "TotalPointCount": 3576, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.267, 0, 0.533, 11.727, 0.8, 13, 1, 1.378, 15.757, 1.956, 16, 2.533, 16, 1, 2.833, 16, 3.133, -23, 3.433, -23, 1, 4.567, -23, 5.7, 0, 6.833, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.267, -1, 0.533, -30, 0.8, -30, 1, 1.378, -30, 1.956, -21, 2.533, -21, 1, 2.833, -21, 3.133, -30, 3.433, -30, 1, 4.567, -30, 5.7, 30, 6.833, 30, 0, 8.4, 30]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 30, 0, 8.4, 30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -0.075, 1, 0.267, -1.254, 0.533, -16, 0.8, -16, 1, 1.389, -16, 1.978, -15.929, 2.567, -14, 1, 2.856, -13.053, 3.144, 15, 3.433, 15, 1, 4.578, 15, 5.722, 1, 6.867, 1, 0, 8.4, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.267, 0, 0.533, -9, 0.8, -9, 1, 1.389, -9, 1.978, -8.973, 2.567, -8, 1, 2.856, -7.523, 3.144, 7, 3.433, 7, 1, 4.567, 7, 5.7, -1, 6.833, -1, 0, 8.4, -1]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.289, 0, 0.578, 0, 0.867, 0, 1, 0.889, 0, 0.911, 30, 0.933, 30, 1, 0.967, 30, 1, 30, 1.033, 30, 1, 1.067, 30, 1.1, -30, 1.133, -30, 1, 1.178, -30, 1.222, -30, 1.267, -30, 1, 1.3, -30, 1.333, 30, 1.367, 30, 1, 1.389, 30, 1.411, 30, 1.433, 30, 1, 1.5, 30, 1.567, -13.993, 1.633, -13.993, 1, 1.711, -13.993, 1.789, 5.431, 1.867, 5.431, 1, 1.944, 5.431, 2.022, -2.132, 2.1, -2.132, 1, 2.178, -2.132, 2.256, 0.836, 2.333, 0.836, 1, 2.4, 0.836, 2.467, -0.336, 2.533, -0.336, 1, 2.611, -0.336, 2.689, 0.135, 2.767, 0.135, 1, 2.844, 0.135, 2.922, -0.054, 3, -0.054, 1, 3.078, -0.054, 3.156, 0.021, 3.233, 0.021, 1, 3.311, 0.021, 3.389, -0.008, 3.467, -0.008, 1, 3.544, -0.008, 3.622, 0.003, 3.7, 0.003, 1, 3.722, 0.003, 3.744, 0.002, 3.767, 0.002, 1, 3.789, 0.002, 3.811, 30, 3.833, 30, 1, 3.867, 30, 3.9, 30, 3.933, 30, 1, 3.967, 30, 4, -30, 4.033, -30, 1, 4.078, -30, 4.122, -30, 4.167, -30, 1, 4.189, -30, 4.211, 30, 4.233, 30, 1, 4.278, 30, 4.322, 30, 4.367, 30, 1, 4.4, 30, 4.433, -30, 4.467, -30, 1, 4.5, -30, 4.533, -30, 4.567, -30, 1, 4.6, -30, 4.633, 30, 4.667, 30, 1, 4.678, 30, 4.689, 30, 4.7, 30, 1, 4.778, 30, 4.856, -12.214, 4.933, -12.214, 1, 5.011, -12.214, 5.089, 4.746, 5.167, 4.746, 1, 5.244, 4.746, 5.322, -1.859, 5.4, -1.859, 1, 5.467, -1.859, 5.533, 0.746, 5.6, 0.746, 1, 5.678, 0.746, 5.756, -0.299, 5.833, -0.299, 1, 5.911, -0.299, 5.989, 0.119, 6.067, 0.119, 1, 6.144, 0.119, 6.222, -0.048, 6.3, -0.048, 1, 6.378, -0.048, 6.456, 0.019, 6.533, 0.019, 1, 6.611, 0.019, 6.689, -0.007, 6.767, -0.007, 1, 6.856, -0.007, 6.944, 30, 7.033, 30, 1, 7.067, 30, 7.1, 30, 7.133, 30, 1, 7.167, 30, 7.2, -30, 7.233, -30, 1, 7.278, -30, 7.322, -30, 7.367, -30, 1, 7.4, -30, 7.433, 30, 7.467, 30, 1, 7.489, 30, 7.511, 30, 7.533, 30, 1, 7.6, 30, 7.667, -13.993, 7.733, -13.993, 1, 7.811, -13.993, 7.889, 5.431, 7.967, 5.431, 1, 8.044, 5.431, 8.122, -2.132, 8.2, -2.132, 1, 8.267, -2.132, 8.333, 0.836, 8.4, 0.836]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.278, 1, 0.556, 1, 0.833, 1, 1, 0.9, 1, 0.967, 0, 1.033, 0, 1, 1.111, 0, 1.189, 1, 1.267, 1, 1, 2.089, 1, 2.911, 1, 3.733, 1, 1, 3.8, 1, 3.867, 0, 3.933, 0, 1, 4.011, 0, 4.089, 1, 4.167, 1, 1, 4.233, 1, 4.3, 0, 4.367, 0, 1, 4.422, 0, 4.478, 1, 4.533, 1, 1, 5.333, 1, 6.133, 1, 6.933, 1, 1, 7, 1, 7.067, 0, 7.133, 0, 1, 7.211, 0, 7.289, 1, 7.367, 1, 0, 8.4, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.278, 1, 0.556, 1, 0.833, 1, 1, 0.9, 1, 0.967, 0, 1.033, 0, 1, 1.111, 0, 1.189, 1, 1.267, 1, 1, 2.089, 1, 2.911, 1, 3.733, 1, 1, 3.8, 1, 3.867, 0, 3.933, 0, 1, 4.011, 0, 4.089, 1, 4.167, 1, 1, 4.233, 1, 4.3, 0, 4.367, 0, 1, 4.422, 0, 4.478, 1, 4.533, 1, 1, 5.333, 1, 6.133, 1, 6.933, 1, 1, 7, 1, 7.067, 0, 7.133, 0, 1, 7.211, 0, 7.289, 1, 7.367, 1, 0, 8.4, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0.45, 0.5, 0.5, 1, 1.056, 0.667, 1.611, 0.7, 2.167, 0.7, 1, 2.589, 0.7, 3.011, -0.7, 3.433, -0.7, 1, 4.156, -0.7, 4.878, -0.701, 5.6, -0.5, 1, 6.011, -0.386, 6.422, 1, 6.833, 1, 1, 7.078, 1, 7.322, 0, 7.567, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.167, 0, 0.333, -1, 0.5, -1, 1, 1.056, -1, 1.611, -0.5, 2.167, -0.5, 1, 2.589, -0.5, 3.011, -0.8, 3.433, -0.8, 1, 4.156, -0.8, 4.878, 1, 5.6, 1, 1, 6.011, 1, 6.422, 1, 6.833, 1, 1, 7.078, 1, 7.322, 1, 7.567, 1, 0, 8.4, 1]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.3, 1, 0.156, -0.3, 0.311, 0, 0.467, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.3, 1, 0.156, -0.3, 0.311, 0, 0.467, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.8, 1, 0.156, -0.8, 0.311, -0.1, 0.467, -0.1, 1, 1.233, -0.1, 2, -0.1, 2.767, -0.1, 1, 3.011, -0.1, 3.256, -0.4, 3.5, -0.4, 0, 8.4, -0.4]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.156, 0, 0.311, 0, 0.467, 0, 1, 1.233, 0, 2, 0, 2.767, 0, 1, 3.011, 0, 3.256, 0.4, 3.5, 0.4, 0, 8.4, 0.4]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.356, 0, 0.611, -3.033, 0.867, -3.033, 1, 0.989, -3.033, 1.111, -2.782, 1.233, -2.782, 1, 1.322, -2.782, 1.411, -2.816, 1.5, -2.816, 1, 2.167, -2.816, 2.833, 2.482, 3.5, 2.482, 1, 4.622, 2.482, 5.744, -0.326, 6.867, -0.326, 1, 6.989, -0.326, 7.111, -0.312, 7.233, -0.312, 1, 7.344, -0.312, 7.456, -0.315, 7.567, -0.315, 1, 7.578, -0.315, 7.589, -0.315, 7.6, -0.315, 1, 7.711, -0.315, 7.822, -0.314, 7.933, -0.314, 1, 8.056, -0.314, 8.178, -0.314, 8.3, -0.314, 1, 8.333, -0.314, 8.367, -0.314, 8.4, -0.314]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.278, 0, 0.522, -5.768, 0.767, -5.768, 1, 0.811, -5.768, 0.856, -5.564, 0.9, -5.564, 1, 1, -5.564, 1.1, -6.114, 1.2, -6.114, 1, 1.322, -6.114, 1.444, -5.798, 1.567, -5.798, 1, 1.633, -5.798, 1.7, -5.837, 1.767, -5.837, 1, 2.3, -5.837, 2.833, 4.077, 3.367, 4.077, 1, 3.422, 4.077, 3.478, 3.206, 3.533, 3.206, 1, 3.633, 3.206, 3.733, 5.513, 3.833, 5.513, 1, 3.956, 5.513, 4.078, 3.907, 4.2, 3.907, 1, 4.256, 3.907, 4.311, 4.015, 4.367, 4.015, 1, 5.167, 4.015, 5.967, -0.104, 6.767, -0.104, 1, 6.811, -0.104, 6.856, -0.081, 6.9, -0.081, 1, 7, -0.081, 7.1, -0.217, 7.2, -0.217, 1, 7.3, -0.217, 7.4, -0.155, 7.5, -0.155, 1, 7.611, -0.155, 7.722, -0.183, 7.833, -0.183, 1, 7.933, -0.183, 8.033, -0.17, 8.133, -0.17, 1, 8.222, -0.17, 8.311, -0.176, 8.4, -0.176]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, -30, 1, 0.289, -30, 0.578, 30, 0.867, 30, 1, 2.9, 30, 4.933, 30, 6.967, 30, 1, 7.333, 30, 7.7, -30, 8.067, -30, 0, 8.4, -30]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 1, 1, 0.144, 1, 0.289, -30, 0.433, -30, 1, 0.667, -30, 0.9, 30, 1.133, 30, 1, 1.378, 30, 1.622, -30, 1.867, -30, 1, 2.1, -30, 2.333, 30, 2.567, 30, 1, 2.822, 30, 3.078, -30, 3.333, -30, 1, 3.6, -30, 3.867, 30, 4.133, 30, 1, 4.389, 30, 4.644, -30, 4.9, -30, 1, 5.167, -30, 5.433, 30, 5.7, 30, 1, 5.956, 30, 6.211, -30, 6.467, -30, 1, 6.778, -30, 7.089, 30, 7.4, 30, 1, 7.656, 30, 7.911, -30, 8.167, -30, 0, 8.4, -30]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, -30, 0, 0.033, -30, 1, 0.467, -30, 0.9, -30, 1.333, -30, 1, 3.622, -30, 5.911, 13, 8.2, 13, 0, 8.4, 13]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, -30, 0, 0.033, -30, 1, 2.767, -30, 5.5, 30, 8.233, 30, 0, 8.4, 30]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0.017, 1, 0.056, 0.072, 0.111, 0.235, 0.167, 0.235, 1, 0.4, 0.235, 0.633, -4.587, 0.867, -4.587, 1, 0.989, -4.587, 1.111, -3.689, 1.233, -3.689, 1, 1.344, -3.689, 1.456, -3.897, 1.567, -3.897, 1, 2.178, -3.897, 2.789, 3.239, 3.4, 3.239, 1, 3.5, 3.239, 3.6, 2.552, 3.7, 2.552, 1, 3.767, 2.552, 3.833, 2.614, 3.9, 2.614, 1, 4.811, 2.614, 5.722, -0.43, 6.633, -0.43, 1, 6.733, -0.43, 6.833, -0.369, 6.933, -0.369, 1, 7.056, -0.369, 7.178, -0.433, 7.3, -0.433, 1, 7.422, -0.433, 7.544, -0.415, 7.667, -0.415, 1, 7.789, -0.415, 7.911, -0.42, 8.033, -0.42, 1, 8.156, -0.42, 8.278, -0.419, 8.4, -0.419]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.017, 1, 0.056, 0.072, 0.111, 0.235, 0.167, 0.235, 1, 0.4, 0.235, 0.633, -4.587, 0.867, -4.587, 1, 0.989, -4.587, 1.111, -3.689, 1.233, -3.689, 1, 1.344, -3.689, 1.456, -3.897, 1.567, -3.897, 1, 2.178, -3.897, 2.789, 3.239, 3.4, 3.239, 1, 3.5, 3.239, 3.6, 2.552, 3.7, 2.552, 1, 3.767, 2.552, 3.833, 2.614, 3.9, 2.614, 1, 4.811, 2.614, 5.722, -0.43, 6.633, -0.43, 1, 6.733, -0.43, 6.833, -0.369, 6.933, -0.369, 1, 7.056, -0.369, 7.178, -0.433, 7.3, -0.433, 1, 7.422, -0.433, 7.544, -0.415, 7.667, -0.415, 1, 7.789, -0.415, 7.911, -0.42, 8.033, -0.42, 1, 8.156, -0.42, 8.278, -0.419, 8.4, -0.419]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.367, 0, 0.6, -4.992, 0.833, -4.992, 1, 0.922, -4.992, 1.011, -4.586, 1.1, -4.586, 1, 1.189, -4.586, 1.278, -4.748, 1.367, -4.748, 1, 2.067, -4.748, 2.767, 4.099, 3.467, 4.099, 1, 3.567, 4.099, 3.667, 3.389, 3.767, 3.389, 1, 3.822, 3.389, 3.878, 3.466, 3.933, 3.466, 1, 4.644, 3.466, 5.356, 0, 6.067, 0, 1, 6.189, 0, 6.311, 0, 6.433, 0, 1, 6.533, 0, 6.633, -0.694, 6.733, -0.694, 1, 6.822, -0.694, 6.911, -0.45, 7, -0.45, 1, 7.1, -0.45, 7.2, -0.557, 7.3, -0.557, 1, 7.389, -0.557, 7.478, -0.509, 7.567, -0.509, 1, 7.667, -0.509, 7.767, -0.53, 7.867, -0.53, 1, 7.956, -0.53, 8.044, -0.521, 8.133, -0.521, 1, 8.222, -0.521, 8.311, -0.525, 8.4, -0.525]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0.267, 0.367, 0.267, 1, 0.467, 0.267, 0.567, -0.182, 0.667, -0.182, 1, 0.844, -0.182, 1.022, 0.073, 1.2, 0.073, 1, 1.3, 0.073, 1.4, -0.059, 1.5, -0.059, 1, 1.589, -0.059, 1.678, 0.034, 1.767, 0.034, 1, 1.867, 0.034, 1.967, -0.025, 2.067, -0.025, 1, 2.156, -0.025, 2.244, 0.006, 2.333, 0.006, 1, 2.522, 0.006, 2.711, -0.289, 2.9, -0.289, 1, 3.022, -0.289, 3.144, 0.149, 3.267, 0.149, 1, 3.322, 0.149, 3.378, 0.085, 3.433, 0.085, 1, 3.478, 0.085, 3.522, 0.119, 3.567, 0.119, 1, 3.667, 0.119, 3.767, -0.123, 3.867, -0.123, 1, 3.956, -0.123, 4.044, 0.105, 4.133, 0.105, 1, 4.233, 0.105, 4.333, -0.049, 4.433, -0.049, 1, 4.522, -0.049, 4.611, 0.043, 4.7, 0.043, 1, 4.8, 0.043, 4.9, -0.009, 5, -0.009, 1, 5.089, -0.009, 5.178, 0.017, 5.267, 0.017, 1, 5.367, 0.017, 5.467, 0.002, 5.567, 0.002, 1, 5.644, 0.002, 5.722, 0.007, 5.8, 0.007, 1, 6.011, 0.007, 6.222, -0.131, 6.433, -0.131, 1, 6.5, -0.131, 6.567, 0.107, 6.633, 0.107, 1, 6.711, 0.107, 6.789, -0.111, 6.867, -0.111, 1, 6.956, -0.111, 7.044, 0.081, 7.133, 0.081, 1, 7.233, 0.081, 7.333, -0.05, 7.433, -0.05, 1, 7.522, -0.05, 7.611, 0.029, 7.7, 0.029, 1, 7.8, 0.029, 7.9, -0.015, 8, -0.015, 1, 8.089, -0.015, 8.178, 0.008, 8.267, 0.008, 1, 8.311, 0.008, 8.356, 0.002, 8.4, 0.002]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0.267, 0.367, 0.267, 1, 0.467, 0.267, 0.567, -0.182, 0.667, -0.182, 1, 0.844, -0.182, 1.022, 0.073, 1.2, 0.073, 1, 1.3, 0.073, 1.4, -0.059, 1.5, -0.059, 1, 1.589, -0.059, 1.678, 0.034, 1.767, 0.034, 1, 1.867, 0.034, 1.967, -0.025, 2.067, -0.025, 1, 2.156, -0.025, 2.244, 0.006, 2.333, 0.006, 1, 2.522, 0.006, 2.711, -0.289, 2.9, -0.289, 1, 3.022, -0.289, 3.144, 0.149, 3.267, 0.149, 1, 3.322, 0.149, 3.378, 0.085, 3.433, 0.085, 1, 3.478, 0.085, 3.522, 0.119, 3.567, 0.119, 1, 3.667, 0.119, 3.767, -0.123, 3.867, -0.123, 1, 3.956, -0.123, 4.044, 0.105, 4.133, 0.105, 1, 4.233, 0.105, 4.333, -0.049, 4.433, -0.049, 1, 4.522, -0.049, 4.611, 0.043, 4.7, 0.043, 1, 4.8, 0.043, 4.9, -0.009, 5, -0.009, 1, 5.089, -0.009, 5.178, 0.017, 5.267, 0.017, 1, 5.367, 0.017, 5.467, 0.002, 5.567, 0.002, 1, 5.644, 0.002, 5.722, 0.007, 5.8, 0.007, 1, 6.011, 0.007, 6.222, -0.131, 6.433, -0.131, 1, 6.5, -0.131, 6.567, 0.107, 6.633, 0.107, 1, 6.711, 0.107, 6.789, -0.111, 6.867, -0.111, 1, 6.956, -0.111, 7.044, 0.081, 7.133, 0.081, 1, 7.233, 0.081, 7.333, -0.05, 7.433, -0.05, 1, 7.522, -0.05, 7.611, 0.029, 7.7, 0.029, 1, 7.8, 0.029, 7.9, -0.015, 8, -0.015, 1, 8.089, -0.015, 8.178, 0.008, 8.267, 0.008, 1, 8.311, 0.008, 8.356, 0.002, 8.4, 0.002]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.278, 0, 0.522, -2.071, 0.767, -2.071, 1, 0.811, -2.071, 0.856, -1.994, 0.9, -1.994, 1, 1, -1.994, 1.1, -2.205, 1.2, -2.205, 1, 1.322, -2.205, 1.444, -2.084, 1.567, -2.084, 1, 1.633, -2.084, 1.7, -2.101, 1.767, -2.101, 1, 2.3, -2.101, 2.833, 1.45, 3.367, 1.45, 1, 3.422, 1.45, 3.478, 1.137, 3.533, 1.137, 1, 3.633, 1.137, 3.733, 1.991, 3.833, 1.991, 1, 3.956, 1.991, 4.078, 1.394, 4.2, 1.394, 1, 4.256, 1.394, 4.311, 1.437, 4.367, 1.437, 1, 5.167, 1.437, 5.967, -0.037, 6.767, -0.037, 1, 6.811, -0.037, 6.856, -0.028, 6.9, -0.028, 1, 7, -0.028, 7.1, -0.079, 7.2, -0.079, 1, 7.311, -0.079, 7.422, -0.056, 7.533, -0.056, 1, 7.633, -0.056, 7.733, -0.066, 7.833, -0.066, 1, 7.933, -0.066, 8.033, -0.061, 8.133, -0.061, 1, 8.144, -0.061, 8.156, -0.061, 8.167, -0.061, 1, 8.244, -0.061, 8.322, -0.063, 8.4, -0.063]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 0.583, 0.267, 0.583, 1, 0.367, 0.583, 0.467, -0.569, 0.567, -0.569, 1, 0.711, -0.569, 0.856, 0.217, 1, 0.217, 1, 1.122, 0.217, 1.244, -0.211, 1.367, -0.211, 1, 1.478, -0.211, 1.589, 0.141, 1.7, 0.141, 1, 1.8, 0.141, 1.9, -0.093, 2, -0.093, 1, 2.111, -0.093, 2.222, 0.044, 2.333, 0.044, 1, 2.478, 0.044, 2.622, -0.676, 2.767, -0.676, 1, 2.867, -0.676, 2.967, 1.006, 3.067, 1.006, 1, 3.167, 1.006, 3.267, -0.414, 3.367, -0.414, 1, 3.422, -0.414, 3.478, -0.034, 3.533, -0.034, 1, 3.589, -0.034, 3.644, -0.423, 3.7, -0.423, 1, 3.8, -0.423, 3.9, 0.608, 4, 0.608, 1, 4.1, 0.608, 4.2, -0.474, 4.3, -0.474, 1, 4.411, -0.474, 4.522, 0.303, 4.633, 0.303, 1, 4.744, 0.303, 4.856, -0.187, 4.967, -0.187, 1, 5.078, -0.187, 5.189, 0.094, 5.3, 0.094, 1, 5.411, 0.094, 5.522, -0.061, 5.633, -0.061, 1, 5.733, -0.061, 5.833, 0.02, 5.933, 0.02, 1, 6.056, 0.02, 6.178, -0.021, 6.3, -0.021, 1, 6.4, -0.021, 6.5, 0, 6.6, 0, 1, 6.689, 0, 6.778, -0.008, 6.867, -0.008, 1, 6.944, -0.008, 7.022, 0.032, 7.1, 0.032, 1, 7.189, 0.032, 7.278, -0.036, 7.367, -0.036, 1, 7.478, -0.036, 7.589, 0.027, 7.7, 0.027, 1, 7.811, 0.027, 7.922, -0.017, 8.033, -0.017, 1, 8.133, -0.017, 8.233, 0.01, 8.333, 0.01, 1, 8.356, 0.01, 8.378, 0.009, 8.4, 0.009]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.278, 0, 0.522, -2.876, 0.767, -2.876, 1, 0.811, -2.876, 0.856, -2.77, 0.9, -2.77, 1, 1, -2.77, 1.1, -3.063, 1.2, -3.063, 1, 1.322, -3.063, 1.444, -2.895, 1.567, -2.895, 1, 1.633, -2.895, 1.7, -2.918, 1.767, -2.918, 1, 2.3, -2.918, 2.833, 2.014, 3.367, 2.014, 1, 3.422, 2.014, 3.478, 1.579, 3.533, 1.579, 1, 3.633, 1.579, 3.733, 2.766, 3.833, 2.766, 1, 3.956, 2.766, 4.078, 1.937, 4.2, 1.937, 1, 4.256, 1.937, 4.311, 1.996, 4.367, 1.996, 1, 5.167, 1.996, 5.967, -0.051, 6.767, -0.051, 1, 6.811, -0.051, 6.856, -0.039, 6.9, -0.039, 1, 7, -0.039, 7.1, -0.109, 7.2, -0.109, 1, 7.311, -0.109, 7.422, -0.077, 7.533, -0.077, 1, 7.633, -0.077, 7.733, -0.092, 7.833, -0.092, 1, 7.933, -0.092, 8.033, -0.085, 8.133, -0.085, 1, 8.144, -0.085, 8.156, -0.085, 8.167, -0.085, 1, 8.244, -0.085, 8.322, -0.088, 8.4, -0.088]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.278, 0, 0.522, -2.876, 0.767, -2.876, 1, 0.811, -2.876, 0.856, -2.77, 0.9, -2.77, 1, 1, -2.77, 1.1, -3.063, 1.2, -3.063, 1, 1.322, -3.063, 1.444, -2.895, 1.567, -2.895, 1, 1.633, -2.895, 1.7, -2.918, 1.767, -2.918, 1, 2.3, -2.918, 2.833, 2.014, 3.367, 2.014, 1, 3.422, 2.014, 3.478, 1.579, 3.533, 1.579, 1, 3.633, 1.579, 3.733, 2.766, 3.833, 2.766, 1, 3.956, 2.766, 4.078, 1.937, 4.2, 1.937, 1, 4.256, 1.937, 4.311, 1.996, 4.367, 1.996, 1, 5.167, 1.996, 5.967, -0.051, 6.767, -0.051, 1, 6.811, -0.051, 6.856, -0.039, 6.9, -0.039, 1, 7, -0.039, 7.1, -0.109, 7.2, -0.109, 1, 7.311, -0.109, 7.422, -0.077, 7.533, -0.077, 1, 7.633, -0.077, 7.733, -0.092, 7.833, -0.092, 1, 7.933, -0.092, 8.033, -0.085, 8.133, -0.085, 1, 8.144, -0.085, 8.156, -0.085, 8.167, -0.085, 1, 8.244, -0.085, 8.322, -0.088, 8.4, -0.088]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.278, 0, 0.522, -2.876, 0.767, -2.876, 1, 0.811, -2.876, 0.856, -2.77, 0.9, -2.77, 1, 1, -2.77, 1.1, -3.063, 1.2, -3.063, 1, 1.322, -3.063, 1.444, -2.895, 1.567, -2.895, 1, 1.633, -2.895, 1.7, -2.918, 1.767, -2.918, 1, 2.3, -2.918, 2.833, 2.014, 3.367, 2.014, 1, 3.422, 2.014, 3.478, 1.579, 3.533, 1.579, 1, 3.633, 1.579, 3.733, 2.766, 3.833, 2.766, 1, 3.956, 2.766, 4.078, 1.937, 4.2, 1.937, 1, 4.256, 1.937, 4.311, 1.996, 4.367, 1.996, 1, 5.167, 1.996, 5.967, -0.051, 6.767, -0.051, 1, 6.811, -0.051, 6.856, -0.039, 6.9, -0.039, 1, 7, -0.039, 7.1, -0.109, 7.2, -0.109, 1, 7.311, -0.109, 7.422, -0.077, 7.533, -0.077, 1, 7.633, -0.077, 7.733, -0.092, 7.833, -0.092, 1, 7.933, -0.092, 8.033, -0.085, 8.133, -0.085, 1, 8.144, -0.085, 8.156, -0.085, 8.167, -0.085, 1, 8.244, -0.085, 8.322, -0.088, 8.4, -0.088]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.278, 0, 0.522, -2.071, 0.767, -2.071, 1, 0.811, -2.071, 0.856, -1.994, 0.9, -1.994, 1, 1, -1.994, 1.1, -2.205, 1.2, -2.205, 1, 1.322, -2.205, 1.444, -2.084, 1.567, -2.084, 1, 1.633, -2.084, 1.7, -2.101, 1.767, -2.101, 1, 2.3, -2.101, 2.833, 1.45, 3.367, 1.45, 1, 3.422, 1.45, 3.478, 1.137, 3.533, 1.137, 1, 3.633, 1.137, 3.733, 1.991, 3.833, 1.991, 1, 3.956, 1.991, 4.078, 1.394, 4.2, 1.394, 1, 4.256, 1.394, 4.311, 1.437, 4.367, 1.437, 1, 5.167, 1.437, 5.967, -0.037, 6.767, -0.037, 1, 6.811, -0.037, 6.856, -0.028, 6.9, -0.028, 1, 7, -0.028, 7.1, -0.079, 7.2, -0.079, 1, 7.311, -0.079, 7.422, -0.056, 7.533, -0.056, 1, 7.633, -0.056, 7.733, -0.066, 7.833, -0.066, 1, 7.933, -0.066, 8.033, -0.061, 8.133, -0.061, 1, 8.144, -0.061, 8.156, -0.061, 8.167, -0.061, 1, 8.244, -0.061, 8.322, -0.063, 8.4, -0.063]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 0.583, 0.267, 0.583, 1, 0.367, 0.583, 0.467, -0.569, 0.567, -0.569, 1, 0.711, -0.569, 0.856, 0.217, 1, 0.217, 1, 1.122, 0.217, 1.244, -0.211, 1.367, -0.211, 1, 1.478, -0.211, 1.589, 0.141, 1.7, 0.141, 1, 1.8, 0.141, 1.9, -0.093, 2, -0.093, 1, 2.111, -0.093, 2.222, 0.044, 2.333, 0.044, 1, 2.478, 0.044, 2.622, -0.676, 2.767, -0.676, 1, 2.867, -0.676, 2.967, 1.006, 3.067, 1.006, 1, 3.167, 1.006, 3.267, -0.414, 3.367, -0.414, 1, 3.422, -0.414, 3.478, -0.034, 3.533, -0.034, 1, 3.589, -0.034, 3.644, -0.423, 3.7, -0.423, 1, 3.8, -0.423, 3.9, 0.608, 4, 0.608, 1, 4.1, 0.608, 4.2, -0.474, 4.3, -0.474, 1, 4.411, -0.474, 4.522, 0.303, 4.633, 0.303, 1, 4.744, 0.303, 4.856, -0.187, 4.967, -0.187, 1, 5.078, -0.187, 5.189, 0.094, 5.3, 0.094, 1, 5.411, 0.094, 5.522, -0.061, 5.633, -0.061, 1, 5.733, -0.061, 5.833, 0.02, 5.933, 0.02, 1, 6.056, 0.02, 6.178, -0.021, 6.3, -0.021, 1, 6.4, -0.021, 6.5, 0, 6.6, 0, 1, 6.689, 0, 6.778, -0.008, 6.867, -0.008, 1, 6.944, -0.008, 7.022, 0.032, 7.1, 0.032, 1, 7.189, 0.032, 7.278, -0.036, 7.367, -0.036, 1, 7.478, -0.036, 7.589, 0.027, 7.7, 0.027, 1, 7.811, 0.027, 7.922, -0.017, 8.033, -0.017, 1, 8.133, -0.017, 8.233, 0.01, 8.333, 0.01, 1, 8.356, 0.01, 8.378, 0.009, 8.4, 0.009]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.278, 0, 0.522, -2.876, 0.767, -2.876, 1, 0.811, -2.876, 0.856, -2.77, 0.9, -2.77, 1, 1, -2.77, 1.1, -3.063, 1.2, -3.063, 1, 1.322, -3.063, 1.444, -2.895, 1.567, -2.895, 1, 1.633, -2.895, 1.7, -2.918, 1.767, -2.918, 1, 2.3, -2.918, 2.833, 2.014, 3.367, 2.014, 1, 3.422, 2.014, 3.478, 1.579, 3.533, 1.579, 1, 3.633, 1.579, 3.733, 2.766, 3.833, 2.766, 1, 3.956, 2.766, 4.078, 1.937, 4.2, 1.937, 1, 4.256, 1.937, 4.311, 1.996, 4.367, 1.996, 1, 5.167, 1.996, 5.967, -0.051, 6.767, -0.051, 1, 6.811, -0.051, 6.856, -0.039, 6.9, -0.039, 1, 7, -0.039, 7.1, -0.109, 7.2, -0.109, 1, 7.311, -0.109, 7.422, -0.077, 7.533, -0.077, 1, 7.633, -0.077, 7.733, -0.092, 7.833, -0.092, 1, 7.933, -0.092, 8.033, -0.085, 8.133, -0.085, 1, 8.144, -0.085, 8.156, -0.085, 8.167, -0.085, 1, 8.244, -0.085, 8.322, -0.088, 8.4, -0.088]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.256, 0, 0.478, -1.366, 0.7, -1.366, 1, 0.8, -1.366, 0.9, -1.148, 1, -1.148, 1, 1.111, -1.148, 1.222, -1.229, 1.333, -1.229, 1, 1.967, -1.229, 2.6, 1.384, 3.233, 1.384, 1, 3.356, 1.384, 3.478, 0.658, 3.6, 0.658, 1, 3.711, 0.658, 3.822, 0.962, 3.933, 0.962, 1, 4.822, 0.962, 5.711, -0.047, 6.6, -0.047, 1, 6.722, -0.047, 6.844, -0.019, 6.967, -0.019, 1, 7.089, -0.019, 7.211, -0.039, 7.333, -0.039, 1, 7.456, -0.039, 7.578, -0.034, 7.7, -0.034, 1, 7.833, -0.034, 7.967, -0.035, 8.1, -0.035, 1, 8.2, -0.035, 8.3, -0.035, 8.4, -0.035]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.958, 0.3, 0.958, 1, 0.489, 0.958, 0.678, -0.451, 0.867, -0.451, 1, 0.978, -0.451, 1.089, 0.271, 1.2, 0.271, 1, 1.311, 0.271, 1.422, -0.126, 1.533, -0.126, 1, 1.656, -0.126, 1.778, 0.022, 1.9, 0.022, 1, 2.044, 0.022, 2.189, -0.04, 2.333, -0.04, 1, 2.411, -0.04, 2.489, -0.033, 2.567, -0.033, 1, 2.656, -0.033, 2.744, -1.515, 2.833, -1.515, 1, 3.056, -1.515, 3.278, 0.824, 3.5, 0.824, 1, 3.6, 0.824, 3.7, -0.75, 3.8, -0.75, 1, 3.911, -0.75, 4.022, 0.435, 4.133, 0.435, 1, 4.256, 0.435, 4.378, -0.072, 4.5, -0.072, 1, 4.633, -0.072, 4.767, 0.113, 4.9, 0.113, 1, 5.011, 0.113, 5.122, 0.047, 5.233, 0.047, 1, 5.256, 0.047, 5.278, 0.049, 5.3, 0.049, 1, 5.311, 0.049, 5.322, 0.049, 5.333, 0.049, 1, 5.4, 0.049, 5.467, 0.057, 5.533, 0.057, 1, 5.978, 0.057, 6.422, -0.031, 6.867, -0.031, 1, 6.967, -0.031, 7.067, 0.035, 7.167, 0.035, 1, 7.289, 0.035, 7.411, -0.017, 7.533, -0.017, 1, 7.656, -0.017, 7.778, 0.007, 7.9, 0.007, 1, 8.022, 0.007, 8.144, -0.002, 8.267, -0.002, 1, 8.311, -0.002, 8.356, -0.001, 8.4, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.244, 0, 0.356, 0.976, 0.467, 0.976, 1, 0.611, 0.976, 0.756, -0.665, 0.9, -0.665, 1, 1.044, -0.665, 1.189, 0.417, 1.333, 0.417, 1, 1.456, 0.417, 1.578, -0.23, 1.7, -0.23, 1, 1.822, -0.23, 1.944, 0.072, 2.067, 0.072, 1, 2.2, 0.072, 2.333, -0.064, 2.467, -0.064, 1, 2.5, -0.064, 2.533, -0.053, 2.567, -0.053, 1, 2.7, -0.053, 2.833, -1.584, 2.967, -1.584, 1, 3.122, -1.584, 3.278, 1.047, 3.433, 1.047, 1, 3.589, 1.047, 3.744, -0.941, 3.9, -0.941, 1, 4.033, -0.941, 4.167, 0.668, 4.3, 0.668, 1, 4.422, 0.668, 4.544, -0.217, 4.667, -0.217, 1, 4.789, -0.217, 4.911, 0.18, 5.033, 0.18, 1, 5.167, 0.18, 5.3, 0.018, 5.433, 0.018, 1, 5.544, 0.018, 5.656, 0.061, 5.767, 0.061, 1, 6.167, 0.061, 6.567, -0.031, 6.967, -0.031, 1, 7.078, -0.031, 7.189, 0.041, 7.3, 0.041, 1, 7.422, 0.041, 7.544, -0.027, 7.667, -0.027, 1, 7.789, -0.027, 7.911, 0.013, 8.033, 0.013, 1, 8.156, 0.013, 8.278, -0.005, 8.4, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.278, 0, 0.422, 1.033, 0.567, 1.033, 1, 0.722, 1.033, 0.878, -0.938, 1.033, -0.938, 1, 1.167, -0.938, 1.3, 0.635, 1.433, 0.635, 1, 1.567, 0.635, 1.7, -0.384, 1.833, -0.384, 1, 1.956, -0.384, 2.078, 0.159, 2.2, 0.159, 1, 2.333, 0.159, 2.467, -0.111, 2.6, -0.111, 1, 2.611, -0.111, 2.622, -0.11, 2.633, -0.11, 1, 2.789, -0.11, 2.944, -1.67, 3.1, -1.67, 1, 3.244, -1.67, 3.389, 1.526, 3.533, 1.526, 1, 3.7, 1.526, 3.867, -1.242, 4.033, -1.242, 1, 4.167, -1.242, 4.3, 0.975, 4.433, 0.975, 1, 4.556, 0.975, 4.678, -0.435, 4.8, -0.435, 1, 4.922, -0.435, 5.044, 0.299, 5.167, 0.299, 1, 5.3, 0.299, 5.433, -0.04, 5.567, -0.04, 1, 5.689, -0.04, 5.811, 0.081, 5.933, 0.081, 1, 6.089, 0.081, 6.244, 0.005, 6.4, 0.005, 1, 6.444, 0.005, 6.489, 0.007, 6.533, 0.007, 1, 6.7, 0.007, 6.867, -0.034, 7.033, -0.034, 1, 7.167, -0.034, 7.3, 0.049, 7.433, 0.049, 1, 7.556, 0.049, 7.678, -0.038, 7.8, -0.038, 1, 7.933, -0.038, 8.067, 0.022, 8.2, 0.022, 1, 8.267, 0.022, 8.333, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.256, 0, 0.478, -3.416, 0.7, -3.416, 1, 0.8, -3.416, 0.9, -2.869, 1, -2.869, 1, 1.111, -2.869, 1.222, -3.071, 1.333, -3.071, 1, 1.967, -3.071, 2.6, 3.459, 3.233, 3.459, 1, 3.356, 3.459, 3.478, 1.644, 3.6, 1.644, 1, 3.711, 1.644, 3.822, 2.405, 3.933, 2.405, 1, 4.822, 2.405, 5.711, -0.116, 6.6, -0.116, 1, 6.722, -0.116, 6.844, -0.047, 6.967, -0.047, 1, 7.089, -0.047, 7.211, -0.097, 7.333, -0.097, 1, 7.456, -0.097, 7.578, -0.085, 7.7, -0.085, 1, 7.833, -0.085, 7.967, -0.088, 8.1, -0.088, 1, 8.2, -0.088, 8.3, -0.087, 8.4, -0.087]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.958, 0.3, 0.958, 1, 0.489, 0.958, 0.678, -0.451, 0.867, -0.451, 1, 0.978, -0.451, 1.089, 0.271, 1.2, 0.271, 1, 1.311, 0.271, 1.422, -0.126, 1.533, -0.126, 1, 1.656, -0.126, 1.778, 0.022, 1.9, 0.022, 1, 2.044, 0.022, 2.189, -0.04, 2.333, -0.04, 1, 2.411, -0.04, 2.489, -0.033, 2.567, -0.033, 1, 2.656, -0.033, 2.744, -1.515, 2.833, -1.515, 1, 3.056, -1.515, 3.278, 0.824, 3.5, 0.824, 1, 3.6, 0.824, 3.7, -0.75, 3.8, -0.75, 1, 3.911, -0.75, 4.022, 0.435, 4.133, 0.435, 1, 4.256, 0.435, 4.378, -0.072, 4.5, -0.072, 1, 4.633, -0.072, 4.767, 0.113, 4.9, 0.113, 1, 5.011, 0.113, 5.122, 0.047, 5.233, 0.047, 1, 5.256, 0.047, 5.278, 0.049, 5.3, 0.049, 1, 5.311, 0.049, 5.322, 0.049, 5.333, 0.049, 1, 5.4, 0.049, 5.467, 0.057, 5.533, 0.057, 1, 5.978, 0.057, 6.422, -0.031, 6.867, -0.031, 1, 6.967, -0.031, 7.067, 0.035, 7.167, 0.035, 1, 7.289, 0.035, 7.411, -0.017, 7.533, -0.017, 1, 7.656, -0.017, 7.778, 0.007, 7.9, 0.007, 1, 8.022, 0.007, 8.144, -0.002, 8.267, -0.002, 1, 8.311, -0.002, 8.356, -0.001, 8.4, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.244, 0, 0.356, 0.976, 0.467, 0.976, 1, 0.611, 0.976, 0.756, -0.665, 0.9, -0.665, 1, 1.044, -0.665, 1.189, 0.417, 1.333, 0.417, 1, 1.456, 0.417, 1.578, -0.23, 1.7, -0.23, 1, 1.822, -0.23, 1.944, 0.072, 2.067, 0.072, 1, 2.2, 0.072, 2.333, -0.064, 2.467, -0.064, 1, 2.5, -0.064, 2.533, -0.053, 2.567, -0.053, 1, 2.7, -0.053, 2.833, -1.584, 2.967, -1.584, 1, 3.122, -1.584, 3.278, 1.047, 3.433, 1.047, 1, 3.589, 1.047, 3.744, -0.941, 3.9, -0.941, 1, 4.033, -0.941, 4.167, 0.668, 4.3, 0.668, 1, 4.422, 0.668, 4.544, -0.217, 4.667, -0.217, 1, 4.789, -0.217, 4.911, 0.18, 5.033, 0.18, 1, 5.167, 0.18, 5.3, 0.018, 5.433, 0.018, 1, 5.544, 0.018, 5.656, 0.061, 5.767, 0.061, 1, 6.167, 0.061, 6.567, -0.031, 6.967, -0.031, 1, 7.078, -0.031, 7.189, 0.041, 7.3, 0.041, 1, 7.422, 0.041, 7.544, -0.027, 7.667, -0.027, 1, 7.789, -0.027, 7.911, 0.013, 8.033, 0.013, 1, 8.156, 0.013, 8.278, -0.005, 8.4, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.256, 0, 0.478, -1.366, 0.7, -1.366, 1, 0.8, -1.366, 0.9, -1.148, 1, -1.148, 1, 1.111, -1.148, 1.222, -1.229, 1.333, -1.229, 1, 1.967, -1.229, 2.6, 1.384, 3.233, 1.384, 1, 3.356, 1.384, 3.478, 0.658, 3.6, 0.658, 1, 3.711, 0.658, 3.822, 0.962, 3.933, 0.962, 1, 4.822, 0.962, 5.711, -0.047, 6.6, -0.047, 1, 6.722, -0.047, 6.844, -0.019, 6.967, -0.019, 1, 7.089, -0.019, 7.211, -0.039, 7.333, -0.039, 1, 7.456, -0.039, 7.578, -0.034, 7.7, -0.034, 1, 7.833, -0.034, 7.967, -0.035, 8.1, -0.035, 1, 8.2, -0.035, 8.3, -0.035, 8.4, -0.035]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.958, 0.3, 0.958, 1, 0.489, 0.958, 0.678, -0.451, 0.867, -0.451, 1, 0.978, -0.451, 1.089, 0.271, 1.2, 0.271, 1, 1.311, 0.271, 1.422, -0.126, 1.533, -0.126, 1, 1.656, -0.126, 1.778, 0.022, 1.9, 0.022, 1, 2.044, 0.022, 2.189, -0.04, 2.333, -0.04, 1, 2.411, -0.04, 2.489, -0.033, 2.567, -0.033, 1, 2.656, -0.033, 2.744, -1.515, 2.833, -1.515, 1, 3.056, -1.515, 3.278, 0.824, 3.5, 0.824, 1, 3.6, 0.824, 3.7, -0.75, 3.8, -0.75, 1, 3.911, -0.75, 4.022, 0.435, 4.133, 0.435, 1, 4.256, 0.435, 4.378, -0.072, 4.5, -0.072, 1, 4.633, -0.072, 4.767, 0.113, 4.9, 0.113, 1, 5.011, 0.113, 5.122, 0.047, 5.233, 0.047, 1, 5.256, 0.047, 5.278, 0.049, 5.3, 0.049, 1, 5.311, 0.049, 5.322, 0.049, 5.333, 0.049, 1, 5.4, 0.049, 5.467, 0.057, 5.533, 0.057, 1, 5.978, 0.057, 6.422, -0.031, 6.867, -0.031, 1, 6.967, -0.031, 7.067, 0.035, 7.167, 0.035, 1, 7.289, 0.035, 7.411, -0.017, 7.533, -0.017, 1, 7.656, -0.017, 7.778, 0.007, 7.9, 0.007, 1, 8.022, 0.007, 8.144, -0.002, 8.267, -0.002, 1, 8.311, -0.002, 8.356, -0.001, 8.4, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.244, 0, 0.356, 0.976, 0.467, 0.976, 1, 0.611, 0.976, 0.756, -0.665, 0.9, -0.665, 1, 1.044, -0.665, 1.189, 0.417, 1.333, 0.417, 1, 1.456, 0.417, 1.578, -0.23, 1.7, -0.23, 1, 1.822, -0.23, 1.944, 0.072, 2.067, 0.072, 1, 2.2, 0.072, 2.333, -0.064, 2.467, -0.064, 1, 2.5, -0.064, 2.533, -0.053, 2.567, -0.053, 1, 2.7, -0.053, 2.833, -1.584, 2.967, -1.584, 1, 3.122, -1.584, 3.278, 1.047, 3.433, 1.047, 1, 3.589, 1.047, 3.744, -0.941, 3.9, -0.941, 1, 4.033, -0.941, 4.167, 0.668, 4.3, 0.668, 1, 4.422, 0.668, 4.544, -0.217, 4.667, -0.217, 1, 4.789, -0.217, 4.911, 0.18, 5.033, 0.18, 1, 5.167, 0.18, 5.3, 0.018, 5.433, 0.018, 1, 5.544, 0.018, 5.656, 0.061, 5.767, 0.061, 1, 6.167, 0.061, 6.567, -0.031, 6.967, -0.031, 1, 7.078, -0.031, 7.189, 0.041, 7.3, 0.041, 1, 7.422, 0.041, 7.544, -0.027, 7.667, -0.027, 1, 7.789, -0.027, 7.911, 0.013, 8.033, 0.013, 1, 8.156, 0.013, 8.278, -0.005, 8.4, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.278, 0, 0.422, 1.033, 0.567, 1.033, 1, 0.722, 1.033, 0.878, -0.938, 1.033, -0.938, 1, 1.167, -0.938, 1.3, 0.635, 1.433, 0.635, 1, 1.567, 0.635, 1.7, -0.384, 1.833, -0.384, 1, 1.956, -0.384, 2.078, 0.159, 2.2, 0.159, 1, 2.333, 0.159, 2.467, -0.111, 2.6, -0.111, 1, 2.611, -0.111, 2.622, -0.11, 2.633, -0.11, 1, 2.789, -0.11, 2.944, -1.67, 3.1, -1.67, 1, 3.244, -1.67, 3.389, 1.526, 3.533, 1.526, 1, 3.7, 1.526, 3.867, -1.242, 4.033, -1.242, 1, 4.167, -1.242, 4.3, 0.975, 4.433, 0.975, 1, 4.556, 0.975, 4.678, -0.435, 4.8, -0.435, 1, 4.922, -0.435, 5.044, 0.299, 5.167, 0.299, 1, 5.3, 0.299, 5.433, -0.04, 5.567, -0.04, 1, 5.689, -0.04, 5.811, 0.081, 5.933, 0.081, 1, 6.089, 0.081, 6.244, 0.005, 6.4, 0.005, 1, 6.444, 0.005, 6.489, 0.007, 6.533, 0.007, 1, 6.7, 0.007, 6.867, -0.034, 7.033, -0.034, 1, 7.167, -0.034, 7.3, 0.049, 7.433, 0.049, 1, 7.556, 0.049, 7.678, -0.038, 7.8, -0.038, 1, 7.933, -0.038, 8.067, 0.022, 8.2, 0.022, 1, 8.267, 0.022, 8.333, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.256, 0, 0.478, -3.416, 0.7, -3.416, 1, 0.8, -3.416, 0.9, -2.869, 1, -2.869, 1, 1.111, -2.869, 1.222, -3.071, 1.333, -3.071, 1, 1.967, -3.071, 2.6, 3.459, 3.233, 3.459, 1, 3.356, 3.459, 3.478, 1.644, 3.6, 1.644, 1, 3.711, 1.644, 3.822, 2.405, 3.933, 2.405, 1, 4.822, 2.405, 5.711, -0.116, 6.6, -0.116, 1, 6.722, -0.116, 6.844, -0.047, 6.967, -0.047, 1, 7.089, -0.047, 7.211, -0.097, 7.333, -0.097, 1, 7.456, -0.097, 7.578, -0.085, 7.7, -0.085, 1, 7.833, -0.085, 7.967, -0.088, 8.1, -0.088, 1, 8.2, -0.088, 8.3, -0.087, 8.4, -0.087]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.958, 0.3, 0.958, 1, 0.489, 0.958, 0.678, -0.451, 0.867, -0.451, 1, 0.978, -0.451, 1.089, 0.271, 1.2, 0.271, 1, 1.311, 0.271, 1.422, -0.126, 1.533, -0.126, 1, 1.656, -0.126, 1.778, 0.022, 1.9, 0.022, 1, 2.044, 0.022, 2.189, -0.04, 2.333, -0.04, 1, 2.411, -0.04, 2.489, -0.033, 2.567, -0.033, 1, 2.656, -0.033, 2.744, -1.515, 2.833, -1.515, 1, 3.056, -1.515, 3.278, 0.824, 3.5, 0.824, 1, 3.6, 0.824, 3.7, -0.75, 3.8, -0.75, 1, 3.911, -0.75, 4.022, 0.435, 4.133, 0.435, 1, 4.256, 0.435, 4.378, -0.072, 4.5, -0.072, 1, 4.633, -0.072, 4.767, 0.113, 4.9, 0.113, 1, 5.011, 0.113, 5.122, 0.047, 5.233, 0.047, 1, 5.256, 0.047, 5.278, 0.049, 5.3, 0.049, 1, 5.311, 0.049, 5.322, 0.049, 5.333, 0.049, 1, 5.4, 0.049, 5.467, 0.057, 5.533, 0.057, 1, 5.978, 0.057, 6.422, -0.031, 6.867, -0.031, 1, 6.967, -0.031, 7.067, 0.035, 7.167, 0.035, 1, 7.289, 0.035, 7.411, -0.017, 7.533, -0.017, 1, 7.656, -0.017, 7.778, 0.007, 7.9, 0.007, 1, 8.022, 0.007, 8.144, -0.002, 8.267, -0.002, 1, 8.311, -0.002, 8.356, -0.001, 8.4, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.244, 0, 0.356, 0.976, 0.467, 0.976, 1, 0.611, 0.976, 0.756, -0.665, 0.9, -0.665, 1, 1.044, -0.665, 1.189, 0.417, 1.333, 0.417, 1, 1.456, 0.417, 1.578, -0.23, 1.7, -0.23, 1, 1.822, -0.23, 1.944, 0.072, 2.067, 0.072, 1, 2.2, 0.072, 2.333, -0.064, 2.467, -0.064, 1, 2.5, -0.064, 2.533, -0.053, 2.567, -0.053, 1, 2.7, -0.053, 2.833, -1.584, 2.967, -1.584, 1, 3.122, -1.584, 3.278, 1.047, 3.433, 1.047, 1, 3.589, 1.047, 3.744, -0.941, 3.9, -0.941, 1, 4.033, -0.941, 4.167, 0.668, 4.3, 0.668, 1, 4.422, 0.668, 4.544, -0.217, 4.667, -0.217, 1, 4.789, -0.217, 4.911, 0.18, 5.033, 0.18, 1, 5.167, 0.18, 5.3, 0.018, 5.433, 0.018, 1, 5.544, 0.018, 5.656, 0.061, 5.767, 0.061, 1, 6.167, 0.061, 6.567, -0.031, 6.967, -0.031, 1, 7.078, -0.031, 7.189, 0.041, 7.3, 0.041, 1, 7.422, 0.041, 7.544, -0.027, 7.667, -0.027, 1, 7.789, -0.027, 7.911, 0.013, 8.033, 0.013, 1, 8.156, 0.013, 8.278, -0.005, 8.4, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.256, 0, 0.478, -3.416, 0.7, -3.416, 1, 0.8, -3.416, 0.9, -2.869, 1, -2.869, 1, 1.111, -2.869, 1.222, -3.071, 1.333, -3.071, 1, 1.967, -3.071, 2.6, 3.459, 3.233, 3.459, 1, 3.356, 3.459, 3.478, 1.644, 3.6, 1.644, 1, 3.711, 1.644, 3.822, 2.405, 3.933, 2.405, 1, 4.822, 2.405, 5.711, -0.116, 6.6, -0.116, 1, 6.722, -0.116, 6.844, -0.047, 6.967, -0.047, 1, 7.089, -0.047, 7.211, -0.097, 7.333, -0.097, 1, 7.456, -0.097, 7.578, -0.085, 7.7, -0.085, 1, 7.833, -0.085, 7.967, -0.088, 8.1, -0.088, 1, 8.2, -0.088, 8.3, -0.087, 8.4, -0.087]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.958, 0.3, 0.958, 1, 0.489, 0.958, 0.678, -0.451, 0.867, -0.451, 1, 0.978, -0.451, 1.089, 0.271, 1.2, 0.271, 1, 1.311, 0.271, 1.422, -0.126, 1.533, -0.126, 1, 1.656, -0.126, 1.778, 0.022, 1.9, 0.022, 1, 2.044, 0.022, 2.189, -0.04, 2.333, -0.04, 1, 2.411, -0.04, 2.489, -0.033, 2.567, -0.033, 1, 2.656, -0.033, 2.744, -1.515, 2.833, -1.515, 1, 3.056, -1.515, 3.278, 0.824, 3.5, 0.824, 1, 3.6, 0.824, 3.7, -0.75, 3.8, -0.75, 1, 3.911, -0.75, 4.022, 0.435, 4.133, 0.435, 1, 4.256, 0.435, 4.378, -0.072, 4.5, -0.072, 1, 4.633, -0.072, 4.767, 0.113, 4.9, 0.113, 1, 5.011, 0.113, 5.122, 0.047, 5.233, 0.047, 1, 5.256, 0.047, 5.278, 0.049, 5.3, 0.049, 1, 5.311, 0.049, 5.322, 0.049, 5.333, 0.049, 1, 5.4, 0.049, 5.467, 0.057, 5.533, 0.057, 1, 5.978, 0.057, 6.422, -0.031, 6.867, -0.031, 1, 6.967, -0.031, 7.067, 0.035, 7.167, 0.035, 1, 7.289, 0.035, 7.411, -0.017, 7.533, -0.017, 1, 7.656, -0.017, 7.778, 0.007, 7.9, 0.007, 1, 8.022, 0.007, 8.144, -0.002, 8.267, -0.002, 1, 8.311, -0.002, 8.356, -0.001, 8.4, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.244, 0, 0.356, 0.976, 0.467, 0.976, 1, 0.611, 0.976, 0.756, -0.665, 0.9, -0.665, 1, 1.044, -0.665, 1.189, 0.417, 1.333, 0.417, 1, 1.456, 0.417, 1.578, -0.23, 1.7, -0.23, 1, 1.822, -0.23, 1.944, 0.072, 2.067, 0.072, 1, 2.2, 0.072, 2.333, -0.064, 2.467, -0.064, 1, 2.5, -0.064, 2.533, -0.053, 2.567, -0.053, 1, 2.7, -0.053, 2.833, -1.584, 2.967, -1.584, 1, 3.122, -1.584, 3.278, 1.047, 3.433, 1.047, 1, 3.589, 1.047, 3.744, -0.941, 3.9, -0.941, 1, 4.033, -0.941, 4.167, 0.668, 4.3, 0.668, 1, 4.422, 0.668, 4.544, -0.217, 4.667, -0.217, 1, 4.789, -0.217, 4.911, 0.18, 5.033, 0.18, 1, 5.167, 0.18, 5.3, 0.018, 5.433, 0.018, 1, 5.544, 0.018, 5.656, 0.061, 5.767, 0.061, 1, 6.167, 0.061, 6.567, -0.031, 6.967, -0.031, 1, 7.078, -0.031, 7.189, 0.041, 7.3, 0.041, 1, 7.422, 0.041, 7.544, -0.027, 7.667, -0.027, 1, 7.789, -0.027, 7.911, 0.013, 8.033, 0.013, 1, 8.156, 0.013, 8.278, -0.005, 8.4, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.256, 0, 0.478, -3.416, 0.7, -3.416, 1, 0.8, -3.416, 0.9, -2.869, 1, -2.869, 1, 1.111, -2.869, 1.222, -3.071, 1.333, -3.071, 1, 1.967, -3.071, 2.6, 3.459, 3.233, 3.459, 1, 3.356, 3.459, 3.478, 1.644, 3.6, 1.644, 1, 3.711, 1.644, 3.822, 2.405, 3.933, 2.405, 1, 4.822, 2.405, 5.711, -0.116, 6.6, -0.116, 1, 6.722, -0.116, 6.844, -0.047, 6.967, -0.047, 1, 7.089, -0.047, 7.211, -0.097, 7.333, -0.097, 1, 7.456, -0.097, 7.578, -0.085, 7.7, -0.085, 1, 7.833, -0.085, 7.967, -0.088, 8.1, -0.088, 1, 8.2, -0.088, 8.3, -0.087, 8.4, -0.087]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.958, 0.3, 0.958, 1, 0.489, 0.958, 0.678, -0.451, 0.867, -0.451, 1, 0.978, -0.451, 1.089, 0.271, 1.2, 0.271, 1, 1.311, 0.271, 1.422, -0.126, 1.533, -0.126, 1, 1.656, -0.126, 1.778, 0.022, 1.9, 0.022, 1, 2.044, 0.022, 2.189, -0.04, 2.333, -0.04, 1, 2.411, -0.04, 2.489, -0.033, 2.567, -0.033, 1, 2.656, -0.033, 2.744, -1.515, 2.833, -1.515, 1, 3.056, -1.515, 3.278, 0.824, 3.5, 0.824, 1, 3.6, 0.824, 3.7, -0.75, 3.8, -0.75, 1, 3.911, -0.75, 4.022, 0.435, 4.133, 0.435, 1, 4.256, 0.435, 4.378, -0.072, 4.5, -0.072, 1, 4.633, -0.072, 4.767, 0.113, 4.9, 0.113, 1, 5.011, 0.113, 5.122, 0.047, 5.233, 0.047, 1, 5.256, 0.047, 5.278, 0.049, 5.3, 0.049, 1, 5.311, 0.049, 5.322, 0.049, 5.333, 0.049, 1, 5.4, 0.049, 5.467, 0.057, 5.533, 0.057, 1, 5.978, 0.057, 6.422, -0.031, 6.867, -0.031, 1, 6.967, -0.031, 7.067, 0.035, 7.167, 0.035, 1, 7.289, 0.035, 7.411, -0.017, 7.533, -0.017, 1, 7.656, -0.017, 7.778, 0.007, 7.9, 0.007, 1, 8.022, 0.007, 8.144, -0.002, 8.267, -0.002, 1, 8.311, -0.002, 8.356, -0.001, 8.4, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.244, 0, 0.356, 0.976, 0.467, 0.976, 1, 0.611, 0.976, 0.756, -0.665, 0.9, -0.665, 1, 1.044, -0.665, 1.189, 0.417, 1.333, 0.417, 1, 1.456, 0.417, 1.578, -0.23, 1.7, -0.23, 1, 1.822, -0.23, 1.944, 0.072, 2.067, 0.072, 1, 2.2, 0.072, 2.333, -0.064, 2.467, -0.064, 1, 2.5, -0.064, 2.533, -0.053, 2.567, -0.053, 1, 2.7, -0.053, 2.833, -1.584, 2.967, -1.584, 1, 3.122, -1.584, 3.278, 1.047, 3.433, 1.047, 1, 3.589, 1.047, 3.744, -0.941, 3.9, -0.941, 1, 4.033, -0.941, 4.167, 0.668, 4.3, 0.668, 1, 4.422, 0.668, 4.544, -0.217, 4.667, -0.217, 1, 4.789, -0.217, 4.911, 0.18, 5.033, 0.18, 1, 5.167, 0.18, 5.3, 0.018, 5.433, 0.018, 1, 5.544, 0.018, 5.656, 0.061, 5.767, 0.061, 1, 6.167, 0.061, 6.567, -0.031, 6.967, -0.031, 1, 7.078, -0.031, 7.189, 0.041, 7.3, 0.041, 1, 7.422, 0.041, 7.544, -0.027, 7.667, -0.027, 1, 7.789, -0.027, 7.911, 0.013, 8.033, 0.013, 1, 8.156, 0.013, 8.278, -0.005, 8.4, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0.014, 1, 0.056, 0.06, 0.111, 0.196, 0.167, 0.196, 1, 0.4, 0.196, 0.633, -3.822, 0.867, -3.822, 1, 0.989, -3.822, 1.111, -3.074, 1.233, -3.074, 1, 1.344, -3.074, 1.456, -3.247, 1.567, -3.247, 1, 2.178, -3.247, 2.789, 2.699, 3.4, 2.699, 1, 3.5, 2.699, 3.6, 2.127, 3.7, 2.127, 1, 3.767, 2.127, 3.833, 2.178, 3.9, 2.178, 1, 4.811, 2.178, 5.722, -0.358, 6.633, -0.358, 1, 6.733, -0.358, 6.833, -0.308, 6.933, -0.308, 1, 7.056, -0.308, 7.178, -0.361, 7.3, -0.361, 1, 7.422, -0.361, 7.544, -0.346, 7.667, -0.346, 1, 7.789, -0.346, 7.911, -0.35, 8.033, -0.35, 1, 8.156, -0.35, 8.278, -0.349, 8.4, -0.349]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, -0.001, 1, 0.044, -0.001, 0.089, -0.159, 0.133, -0.159, 1, 0.244, -0.159, 0.356, 0.821, 0.467, 0.821, 1, 0.644, 0.821, 0.822, -0.487, 1, -0.487, 1, 1.122, -0.487, 1.244, 0.31, 1.367, 0.31, 1, 1.489, 0.31, 1.611, -0.137, 1.733, -0.137, 1, 1.844, -0.137, 1.956, 0.039, 2.067, 0.039, 1, 2.2, 0.039, 2.333, -0.044, 2.467, -0.044, 1, 2.5, -0.044, 2.533, -0.04, 2.567, -0.04, 1, 2.656, -0.04, 2.744, -0.966, 2.833, -0.966, 1, 3.067, -0.966, 3.3, 0.526, 3.533, 0.526, 1, 3.633, 0.526, 3.733, -0.238, 3.833, -0.238, 1, 3.956, -0.238, 4.078, 0.221, 4.2, 0.221, 1, 4.311, 0.221, 4.422, 0.04, 4.533, 0.04, 1, 4.656, 0.04, 4.778, 0.122, 4.9, 0.122, 1, 5.011, 0.122, 5.122, 0.085, 5.233, 0.085, 1, 5.256, 0.085, 5.278, 0.092, 5.3, 0.092, 1, 5.344, 0.092, 5.389, 0.087, 5.433, 0.087, 1, 5.478, 0.087, 5.522, 0.104, 5.567, 0.104, 1, 5.6, 0.104, 5.633, -0.043, 5.667, -0.043, 1, 5.767, -0.043, 5.867, 0.124, 5.967, 0.124, 1, 6.078, 0.124, 6.189, -0.002, 6.3, -0.002, 1, 6.378, -0.002, 6.456, 0.018, 6.533, 0.018, 1, 6.656, 0.018, 6.778, -0.037, 6.9, -0.037, 1, 6.978, -0.037, 7.056, 0.036, 7.133, 0.036, 1, 7.244, 0.036, 7.356, -0.021, 7.467, -0.021, 1, 7.589, -0.021, 7.711, 0.009, 7.833, 0.009, 1, 7.944, 0.009, 8.056, -0.004, 8.167, -0.004, 1, 8.244, -0.004, 8.322, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0.125, 1, 0.022, 0.123, 0.044, 0.12, 0.067, 0.12, 1, 0.078, 0.12, 0.089, 0.127, 0.1, 0.127, 1, 0.156, 0.127, 0.211, -0.061, 0.267, -0.061, 1, 0.378, -0.061, 0.489, 0.637, 0.6, 0.637, 1, 0.756, 0.637, 0.911, -0.374, 1.067, -0.374, 1, 1.2, -0.374, 1.333, 0.321, 1.467, 0.321, 1, 1.589, 0.321, 1.711, -0.171, 1.833, -0.171, 1, 1.944, -0.171, 2.056, 0.082, 2.167, 0.082, 1, 2.289, 0.082, 2.411, -0.048, 2.533, -0.048, 1, 2.544, -0.048, 2.556, -0.046, 2.567, -0.046, 1, 2.7, -0.046, 2.833, -0.865, 2.967, -0.865, 1, 3.189, -0.865, 3.411, 0.386, 3.633, 0.386, 1, 3.744, 0.386, 3.856, -0.245, 3.967, -0.245, 1, 4.078, -0.245, 4.189, 0.267, 4.3, 0.267, 1, 4.411, 0.267, 4.522, 0.001, 4.633, 0.001, 1, 4.744, 0.001, 4.856, 0.137, 4.967, 0.137, 1, 5.044, 0.137, 5.122, 0.089, 5.2, 0.089, 1, 5.244, 0.089, 5.289, 0.128, 5.333, 0.128, 1, 5.411, 0.128, 5.489, 0.054, 5.567, 0.054, 1, 5.589, 0.054, 5.611, 0.097, 5.633, 0.097, 1, 5.711, 0.097, 5.789, 0.023, 5.867, 0.023, 1, 5.944, 0.023, 6.022, 0.107, 6.1, 0.107, 1, 6.211, 0.107, 6.322, -0.01, 6.433, -0.01, 1, 6.522, -0.01, 6.611, 0.019, 6.7, 0.019, 1, 6.8, 0.019, 6.9, -0.032, 7, -0.032, 1, 7.089, -0.032, 7.178, 0.034, 7.267, 0.034, 1, 7.378, 0.034, 7.489, -0.025, 7.6, -0.025, 1, 7.711, -0.025, 7.822, 0.014, 7.933, 0.014, 1, 8.044, 0.014, 8.156, -0.007, 8.267, -0.007, 1, 8.311, -0.007, 8.356, -0.003, 8.4, -0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, -0.126, 1, 0.033, -0.126, 0.067, -0.161, 0.1, -0.161, 1, 0.278, -0.161, 0.456, 0.678, 0.633, 0.678, 1, 0.778, 0.678, 0.922, -0.524, 1.067, -0.524, 1, 1.211, -0.524, 1.356, 0.349, 1.5, 0.349, 1, 1.622, 0.349, 1.744, -0.247, 1.867, -0.247, 1, 1.978, -0.247, 2.089, 0.107, 2.2, 0.107, 1, 2.322, 0.107, 2.444, -0.088, 2.567, -0.088, 1, 2.589, -0.088, 2.611, -0.084, 2.633, -0.084, 1, 2.756, -0.084, 2.878, -0.844, 3, -0.844, 1, 3.222, -0.844, 3.444, 0.315, 3.667, 0.315, 1, 3.778, 0.315, 3.889, -0.279, 4, -0.279, 1, 4.111, -0.279, 4.222, 0.284, 4.333, 0.284, 1, 4.456, 0.284, 4.578, -0.065, 4.7, -0.065, 1, 4.811, -0.065, 4.922, 0.127, 5.033, 0.127, 1, 5.122, 0.127, 5.211, -0.054, 5.3, -0.054, 1, 5.378, -0.054, 5.456, 0.105, 5.533, 0.105, 1, 5.656, 0.105, 5.778, 0.004, 5.9, 0.004, 1, 5.989, 0.004, 6.078, 0.083, 6.167, 0.083, 1, 6.278, 0.083, 6.389, -0.033, 6.5, -0.033, 1, 6.589, -0.033, 6.678, 0.025, 6.767, 0.025, 1, 6.867, 0.025, 6.967, -0.029, 7.067, -0.029, 1, 7.167, -0.029, 7.267, 0.036, 7.367, 0.036, 1, 7.467, 0.036, 7.567, -0.032, 7.667, -0.032, 1, 7.778, -0.032, 7.889, 0.02, 8, 0.02, 1, 8.1, 0.02, 8.2, -0.011, 8.3, -0.011, 1, 8.333, -0.011, 8.367, -0.007, 8.4, -0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0.073, 1, 0.011, 0.084, 0.022, 0.095, 0.033, 0.095, 1, 0.089, 0.095, 0.144, -0.338, 0.2, -0.338, 1, 0.378, -0.338, 0.556, 0.623, 0.733, 0.623, 1, 0.867, 0.623, 1, -0.716, 1.133, -0.716, 1, 1.278, -0.716, 1.422, 0.474, 1.567, 0.474, 1, 1.689, 0.474, 1.811, -0.364, 1.933, -0.364, 1, 2.044, -0.364, 2.156, 0.207, 2.267, 0.207, 1, 2.378, 0.207, 2.489, -0.137, 2.6, -0.137, 1, 2.656, -0.137, 2.711, -0.078, 2.767, -0.078, 1, 2.878, -0.078, 2.989, -0.809, 3.1, -0.809, 1, 3.222, -0.809, 3.344, 0.364, 3.467, 0.364, 1, 3.533, 0.364, 3.6, 0.279, 3.667, 0.279, 1, 3.689, 0.279, 3.711, 0.392, 3.733, 0.392, 1, 3.856, 0.392, 3.978, -0.546, 4.1, -0.546, 1, 4.2, -0.546, 4.3, 0.465, 4.4, 0.465, 1, 4.5, 0.465, 4.6, -0.179, 4.7, -0.179, 1, 4.822, -0.179, 4.944, 0.159, 5.067, 0.159, 1, 5.189, 0.159, 5.311, -0.069, 5.433, -0.069, 1, 5.511, -0.069, 5.589, 0.11, 5.667, 0.11, 1, 5.767, 0.11, 5.867, -0.04, 5.967, -0.04, 1, 6.067, -0.04, 6.167, 0.092, 6.267, 0.092, 1, 6.367, 0.092, 6.467, -0.069, 6.567, -0.069, 1, 6.667, -0.069, 6.767, 0.043, 6.867, 0.043, 1, 6.956, 0.043, 7.044, -0.043, 7.133, -0.043, 1, 7.233, -0.043, 7.333, 0.053, 7.433, 0.053, 1, 7.533, 0.053, 7.633, -0.05, 7.733, -0.05, 1, 7.844, -0.05, 7.956, 0.036, 8.067, 0.036, 1, 8.167, 0.036, 8.267, -0.021, 8.367, -0.021, 1, 8.378, -0.021, 8.389, -0.02, 8.4, -0.02]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0.014, 1, 0.056, 0.06, 0.111, 0.196, 0.167, 0.196, 1, 0.4, 0.196, 0.633, -3.822, 0.867, -3.822, 1, 0.989, -3.822, 1.111, -3.074, 1.233, -3.074, 1, 1.344, -3.074, 1.456, -3.247, 1.567, -3.247, 1, 2.178, -3.247, 2.789, 2.699, 3.4, 2.699, 1, 3.5, 2.699, 3.6, 2.127, 3.7, 2.127, 1, 3.767, 2.127, 3.833, 2.178, 3.9, 2.178, 1, 4.811, 2.178, 5.722, -0.358, 6.633, -0.358, 1, 6.733, -0.358, 6.833, -0.308, 6.933, -0.308, 1, 7.056, -0.308, 7.178, -0.361, 7.3, -0.361, 1, 7.422, -0.361, 7.544, -0.346, 7.667, -0.346, 1, 7.789, -0.346, 7.911, -0.35, 8.033, -0.35, 1, 8.156, -0.35, 8.278, -0.349, 8.4, -0.349]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, -0.001, 1, 0.044, -0.001, 0.089, -0.159, 0.133, -0.159, 1, 0.244, -0.159, 0.356, 0.821, 0.467, 0.821, 1, 0.644, 0.821, 0.822, -0.487, 1, -0.487, 1, 1.122, -0.487, 1.244, 0.31, 1.367, 0.31, 1, 1.489, 0.31, 1.611, -0.137, 1.733, -0.137, 1, 1.844, -0.137, 1.956, 0.039, 2.067, 0.039, 1, 2.2, 0.039, 2.333, -0.044, 2.467, -0.044, 1, 2.5, -0.044, 2.533, -0.04, 2.567, -0.04, 1, 2.656, -0.04, 2.744, -0.966, 2.833, -0.966, 1, 3.067, -0.966, 3.3, 0.526, 3.533, 0.526, 1, 3.633, 0.526, 3.733, -0.238, 3.833, -0.238, 1, 3.956, -0.238, 4.078, 0.221, 4.2, 0.221, 1, 4.311, 0.221, 4.422, 0.04, 4.533, 0.04, 1, 4.656, 0.04, 4.778, 0.122, 4.9, 0.122, 1, 5.011, 0.122, 5.122, 0.085, 5.233, 0.085, 1, 5.256, 0.085, 5.278, 0.092, 5.3, 0.092, 1, 5.344, 0.092, 5.389, 0.087, 5.433, 0.087, 1, 5.478, 0.087, 5.522, 0.104, 5.567, 0.104, 1, 5.6, 0.104, 5.633, -0.043, 5.667, -0.043, 1, 5.767, -0.043, 5.867, 0.124, 5.967, 0.124, 1, 6.078, 0.124, 6.189, -0.002, 6.3, -0.002, 1, 6.378, -0.002, 6.456, 0.018, 6.533, 0.018, 1, 6.656, 0.018, 6.778, -0.037, 6.9, -0.037, 1, 6.978, -0.037, 7.056, 0.036, 7.133, 0.036, 1, 7.244, 0.036, 7.356, -0.021, 7.467, -0.021, 1, 7.589, -0.021, 7.711, 0.009, 7.833, 0.009, 1, 7.944, 0.009, 8.056, -0.004, 8.167, -0.004, 1, 8.244, -0.004, 8.322, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0.125, 1, 0.022, 0.123, 0.044, 0.12, 0.067, 0.12, 1, 0.078, 0.12, 0.089, 0.127, 0.1, 0.127, 1, 0.156, 0.127, 0.211, -0.061, 0.267, -0.061, 1, 0.378, -0.061, 0.489, 0.637, 0.6, 0.637, 1, 0.756, 0.637, 0.911, -0.374, 1.067, -0.374, 1, 1.2, -0.374, 1.333, 0.321, 1.467, 0.321, 1, 1.589, 0.321, 1.711, -0.171, 1.833, -0.171, 1, 1.944, -0.171, 2.056, 0.082, 2.167, 0.082, 1, 2.289, 0.082, 2.411, -0.048, 2.533, -0.048, 1, 2.544, -0.048, 2.556, -0.046, 2.567, -0.046, 1, 2.7, -0.046, 2.833, -0.865, 2.967, -0.865, 1, 3.189, -0.865, 3.411, 0.386, 3.633, 0.386, 1, 3.744, 0.386, 3.856, -0.245, 3.967, -0.245, 1, 4.078, -0.245, 4.189, 0.267, 4.3, 0.267, 1, 4.411, 0.267, 4.522, 0.001, 4.633, 0.001, 1, 4.744, 0.001, 4.856, 0.137, 4.967, 0.137, 1, 5.044, 0.137, 5.122, 0.089, 5.2, 0.089, 1, 5.244, 0.089, 5.289, 0.128, 5.333, 0.128, 1, 5.411, 0.128, 5.489, 0.054, 5.567, 0.054, 1, 5.589, 0.054, 5.611, 0.097, 5.633, 0.097, 1, 5.711, 0.097, 5.789, 0.023, 5.867, 0.023, 1, 5.944, 0.023, 6.022, 0.107, 6.1, 0.107, 1, 6.211, 0.107, 6.322, -0.01, 6.433, -0.01, 1, 6.522, -0.01, 6.611, 0.019, 6.7, 0.019, 1, 6.8, 0.019, 6.9, -0.032, 7, -0.032, 1, 7.089, -0.032, 7.178, 0.034, 7.267, 0.034, 1, 7.378, 0.034, 7.489, -0.025, 7.6, -0.025, 1, 7.711, -0.025, 7.822, 0.014, 7.933, 0.014, 1, 8.044, 0.014, 8.156, -0.007, 8.267, -0.007, 1, 8.311, -0.007, 8.356, -0.003, 8.4, -0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, -0.126, 1, 0.033, -0.126, 0.067, -0.161, 0.1, -0.161, 1, 0.278, -0.161, 0.456, 0.678, 0.633, 0.678, 1, 0.778, 0.678, 0.922, -0.524, 1.067, -0.524, 1, 1.211, -0.524, 1.356, 0.349, 1.5, 0.349, 1, 1.622, 0.349, 1.744, -0.247, 1.867, -0.247, 1, 1.978, -0.247, 2.089, 0.107, 2.2, 0.107, 1, 2.322, 0.107, 2.444, -0.088, 2.567, -0.088, 1, 2.589, -0.088, 2.611, -0.084, 2.633, -0.084, 1, 2.756, -0.084, 2.878, -0.844, 3, -0.844, 1, 3.222, -0.844, 3.444, 0.315, 3.667, 0.315, 1, 3.778, 0.315, 3.889, -0.279, 4, -0.279, 1, 4.111, -0.279, 4.222, 0.284, 4.333, 0.284, 1, 4.456, 0.284, 4.578, -0.065, 4.7, -0.065, 1, 4.811, -0.065, 4.922, 0.127, 5.033, 0.127, 1, 5.122, 0.127, 5.211, -0.054, 5.3, -0.054, 1, 5.378, -0.054, 5.456, 0.105, 5.533, 0.105, 1, 5.656, 0.105, 5.778, 0.004, 5.9, 0.004, 1, 5.989, 0.004, 6.078, 0.083, 6.167, 0.083, 1, 6.278, 0.083, 6.389, -0.033, 6.5, -0.033, 1, 6.589, -0.033, 6.678, 0.025, 6.767, 0.025, 1, 6.867, 0.025, 6.967, -0.029, 7.067, -0.029, 1, 7.167, -0.029, 7.267, 0.036, 7.367, 0.036, 1, 7.467, 0.036, 7.567, -0.032, 7.667, -0.032, 1, 7.778, -0.032, 7.889, 0.02, 8, 0.02, 1, 8.1, 0.02, 8.2, -0.011, 8.3, -0.011, 1, 8.333, -0.011, 8.367, -0.007, 8.4, -0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0.073, 1, 0.011, 0.084, 0.022, 0.095, 0.033, 0.095, 1, 0.089, 0.095, 0.144, -0.338, 0.2, -0.338, 1, 0.378, -0.338, 0.556, 0.623, 0.733, 0.623, 1, 0.867, 0.623, 1, -0.716, 1.133, -0.716, 1, 1.278, -0.716, 1.422, 0.474, 1.567, 0.474, 1, 1.689, 0.474, 1.811, -0.364, 1.933, -0.364, 1, 2.044, -0.364, 2.156, 0.207, 2.267, 0.207, 1, 2.378, 0.207, 2.489, -0.137, 2.6, -0.137, 1, 2.656, -0.137, 2.711, -0.078, 2.767, -0.078, 1, 2.878, -0.078, 2.989, -0.809, 3.1, -0.809, 1, 3.222, -0.809, 3.344, 0.364, 3.467, 0.364, 1, 3.533, 0.364, 3.6, 0.279, 3.667, 0.279, 1, 3.689, 0.279, 3.711, 0.392, 3.733, 0.392, 1, 3.856, 0.392, 3.978, -0.546, 4.1, -0.546, 1, 4.2, -0.546, 4.3, 0.465, 4.4, 0.465, 1, 4.5, 0.465, 4.6, -0.179, 4.7, -0.179, 1, 4.822, -0.179, 4.944, 0.159, 5.067, 0.159, 1, 5.189, 0.159, 5.311, -0.069, 5.433, -0.069, 1, 5.511, -0.069, 5.589, 0.11, 5.667, 0.11, 1, 5.767, 0.11, 5.867, -0.04, 5.967, -0.04, 1, 6.067, -0.04, 6.167, 0.092, 6.267, 0.092, 1, 6.367, 0.092, 6.467, -0.069, 6.567, -0.069, 1, 6.667, -0.069, 6.767, 0.043, 6.867, 0.043, 1, 6.956, 0.043, 7.044, -0.043, 7.133, -0.043, 1, 7.233, -0.043, 7.333, 0.053, 7.433, 0.053, 1, 7.533, 0.053, 7.633, -0.05, 7.733, -0.05, 1, 7.844, -0.05, 7.956, 0.036, 8.067, 0.036, 1, 8.167, 0.036, 8.267, -0.021, 8.367, -0.021, 1, 8.378, -0.021, 8.389, -0.02, 8.4, -0.02]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.222, 0, 0.344, 0.536, 0.467, 0.536, 1, 0.633, 0.536, 0.8, -0.232, 0.967, -0.232, 1, 1.089, -0.232, 1.211, 0.082, 1.333, 0.082, 1, 1.467, 0.082, 1.6, -0.034, 1.733, -0.034, 1, 1.833, -0.034, 1.933, -0.01, 2.033, -0.01, 1, 2.367, -0.01, 2.7, -0.748, 3.033, -0.748, 1, 3.233, -0.748, 3.433, 0.313, 3.633, 0.313, 1, 3.744, 0.313, 3.856, -0.071, 3.967, -0.071, 1, 4.1, -0.071, 4.233, 0.088, 4.367, 0.088, 1, 4.467, 0.088, 4.567, 0.055, 4.667, 0.055, 1, 4.8, 0.055, 4.933, 0.069, 5.067, 0.069, 1, 5.411, 0.069, 5.756, 0.044, 6.1, 0.044, 1, 6.133, 0.044, 6.167, 0.057, 6.2, 0.057, 1, 6.467, 0.057, 6.733, -0.011, 7, -0.011, 1, 7.122, -0.011, 7.244, 0.004, 7.367, 0.004, 1, 7.489, 0.004, 7.611, -0.001, 7.733, -0.001, 1, 7.856, -0.001, 7.978, 0, 8.1, 0, 1, 8.2, 0, 8.3, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.256, 0, 0.411, 0.586, 0.567, 0.586, 1, 0.733, 0.586, 0.9, -0.304, 1.067, -0.304, 1, 1.2, -0.304, 1.333, 0.136, 1.467, 0.136, 1, 1.589, 0.136, 1.711, -0.062, 1.833, -0.062, 1, 1.956, -0.062, 2.078, -0.002, 2.2, -0.002, 1, 2.511, -0.002, 2.822, -0.812, 3.133, -0.812, 1, 3.322, -0.812, 3.511, 0.389, 3.7, 0.389, 1, 3.833, 0.389, 3.967, -0.134, 4.1, -0.134, 1, 4.222, -0.134, 4.344, 0.123, 4.467, 0.123, 1, 4.589, 0.123, 4.711, 0.043, 4.833, 0.043, 1, 4.956, 0.043, 5.078, 0.074, 5.2, 0.074, 1, 5.533, 0.074, 5.867, 0.013, 6.2, 0.013, 1, 6.278, 0.013, 6.356, 0.04, 6.433, 0.04, 1, 6.656, 0.04, 6.878, -0.012, 7.1, -0.012, 1, 7.233, -0.012, 7.367, 0.006, 7.5, 0.006, 1, 7.622, 0.006, 7.744, -0.003, 7.867, -0.003, 1, 7.989, -0.003, 8.111, 0.001, 8.233, 0.001, 1, 8.289, 0.001, 8.344, 0, 8.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.289, 0, 0.478, 0.632, 0.667, 0.632, 1, 0.833, 0.632, 1, -0.398, 1.167, -0.398, 1, 1.3, -0.398, 1.433, 0.204, 1.567, 0.204, 1, 1.7, 0.204, 1.833, -0.102, 1.967, -0.102, 1, 2.089, -0.102, 2.211, 0.014, 2.333, 0.014, 1, 2.633, 0.014, 2.933, -0.872, 3.233, -0.872, 1, 3.422, -0.872, 3.611, 0.494, 3.8, 0.494, 1, 3.933, 0.494, 4.067, -0.21, 4.2, -0.21, 1, 4.333, -0.21, 4.467, 0.173, 4.6, 0.173, 1, 4.722, 0.173, 4.844, 0.023, 4.967, 0.023, 1, 5.089, 0.023, 5.211, 0.082, 5.333, 0.082, 1, 5.589, 0.082, 5.844, 0.051, 6.1, 0.051, 1, 6.122, 0.051, 6.144, 0.056, 6.167, 0.056, 1, 6.244, 0.056, 6.322, 0.022, 6.4, 0.022, 1, 6.467, 0.022, 6.533, 0.033, 6.6, 0.033, 1, 6.778, 0.033, 6.956, -0.015, 7.133, -0.015, 1, 7.289, -0.015, 7.444, 0.009, 7.6, 0.009, 1, 7.722, 0.009, 7.844, -0.004, 7.967, -0.004, 1, 8.1, -0.004, 8.233, 0.002, 8.367, 0.002, 1, 8.378, 0.002, 8.389, 0.002, 8.4, 0.002]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "<PERSON><PERSON>_Skinning", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 8.4, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 8.4, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 8.4, 1]}]}