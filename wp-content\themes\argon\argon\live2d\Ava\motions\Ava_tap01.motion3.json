{"Version": 3, "Meta": {"Duration": 8.1, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 99, "TotalSegmentCount": 1190, "TotalPointCount": 3467, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0, 0.533, 0, 1, 0.611, 0, 0.689, 0, 0.767, 0, 1, 0.9, 0, 1.033, 0, 1.167, 0, 1, 1.433, 0, 1.7, 1, 1.967, 1, 1, 2.244, 1, 2.522, 1, 2.8, 1, 1, 2.967, 1, 3.133, 1, 3.3, 1, 1, 3.467, 1, 3.633, -2, 3.8, -2, 1, 4.067, -2, 4.333, -2, 4.6, -2, 1, 4.733, -2, 4.867, 0, 5, 0, 1, 5.156, 0, 5.311, 0, 5.467, 0, 1, 5.7, 0, 5.933, 0, 6.167, 0, 1, 6.411, 0, 6.656, 0, 6.9, 0, 1, 7.156, 0, 7.411, 0, 7.667, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -2, 1, 0.178, -2, 0.356, 12, 0.533, 12, 1, 0.611, 12, 0.689, 12, 0.767, 12, 1, 0.9, 12, 1.033, 12, 1.167, 12, 1, 1.433, 12, 1.7, -15, 1.967, -15, 1, 2.244, -15, 2.522, -15, 2.8, -15, 1, 2.967, -15, 3.133, 1, 3.3, 1, 1, 3.467, 1, 3.633, -14, 3.8, -14, 1, 4.067, -14, 4.333, -14, 4.6, -14, 1, 4.733, -14, 4.867, 11, 5, 11, 1, 5.156, 11, 5.311, -8, 5.467, -8, 1, 5.7, -8, 5.933, 16, 6.167, 16, 1, 6.411, 16, 6.656, -14, 6.9, -14, 1, 7.156, -14, 7.411, -1, 7.667, -1, 0, 8.1, -1]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.256, 0, 0.511, -5, 0.767, -5, 1, 0.9, -5, 1.033, -5, 1.167, -5, 1, 1.433, -5, 1.7, 7, 1.967, 7, 1, 2.233, 7, 2.5, 7, 2.767, 7, 1, 3.111, 7, 3.456, -5, 3.8, -5, 0, 8.1, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0, 0.533, 0, 1, 0.611, 0, 0.689, 0, 0.767, 0, 1, 0.9, 0, 1.033, 0, 1.167, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 4, 1, 0.178, 4, 0.356, 0, 0.533, 0, 1, 0.611, 0, 0.689, 0, 0.767, 0, 1, 0.9, 0, 1.033, 0, 1.167, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.256, 0, 0.511, -9, 0.767, -9, 1, 0.9, -9, 1.033, -9, 1.167, -9, 1, 1.433, -9, 1.7, 10, 1.967, 10, 1, 2.278, 10, 2.589, 10.292, 2.9, 8, 1, 3.2, 5.789, 3.5, -8, 3.8, -8, 1, 4.067, -8, 4.333, -8, 4.6, -8, 1, 4.9, -8, 5.2, 8, 5.5, 8, 1, 5.8, 8, 6.1, 8, 6.4, 8, 1, 6.678, 8, 6.956, 3, 7.233, 3, 0, 8.1, 3]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 1.567, 0, 3.133, 0, 4.7, 0, 1, 4.989, 0, 5.278, 1, 5.567, 1, 0, 8.1, 1]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.211, 0, 0.389, 0, 0.567, 0, 1, 0.589, 0, 0.611, 30, 0.633, 30, 1, 0.656, 30, 0.678, 30, 0.7, 30, 1, 0.733, 30, 0.767, -30, 0.8, -30, 1, 0.822, -30, 0.844, -30, 0.867, -30, 1, 0.911, -30, 0.956, 30, 1, 30, 1, 1.078, 30, 1.156, -11.706, 1.233, -11.706, 1, 1.311, -11.706, 1.389, 4.593, 1.467, 4.593, 1, 1.544, 4.593, 1.622, -1.816, 1.7, -1.816, 1, 1.778, -1.816, 1.856, 0.716, 1.933, 0.716, 1, 2.011, 0.716, 2.089, -0.282, 2.167, -0.282, 1, 2.233, -0.282, 2.3, 0.111, 2.367, 0.111, 1, 2.444, 0.111, 2.522, -0.045, 2.6, -0.045, 1, 2.644, -0.045, 2.689, 30, 2.733, 30, 1, 2.756, 30, 2.778, 30, 2.8, 30, 1, 2.833, 30, 2.867, -30, 2.9, -30, 1, 2.922, -30, 2.944, -30, 2.967, -30, 1, 3.011, -30, 3.056, 30, 3.1, 30, 1, 3.178, 30, 3.256, -11.706, 3.333, -11.706, 1, 3.411, -11.706, 3.489, 4.593, 3.567, 4.593, 1, 3.644, 4.593, 3.722, -1.816, 3.8, -1.816, 1, 3.878, -1.816, 3.956, 0.716, 4.033, 0.716, 1, 4.111, 0.716, 4.189, -0.282, 4.267, -0.282, 1, 4.367, -0.282, 4.467, 30, 4.567, 30, 1, 4.589, 30, 4.611, 30, 4.633, 30, 1, 4.678, 30, 4.722, -30, 4.767, -30, 1, 4.789, -30, 4.811, -30, 4.833, -30, 1, 4.9, -30, 4.967, 13.53, 5.033, 13.53, 1, 5.111, 13.53, 5.189, -5.287, 5.267, -5.287, 1, 5.344, -5.287, 5.422, 2.089, 5.5, 2.089, 1, 5.578, 2.089, 5.656, -0.824, 5.733, -0.824, 1, 5.811, -0.824, 5.889, 0.324, 5.967, 0.324, 1, 6.033, 0.324, 6.1, -0.128, 6.167, -0.128, 1, 6.244, -0.128, 6.322, 0.051, 6.4, 0.051, 1, 6.478, 0.051, 6.556, -0.021, 6.633, -0.021, 1, 6.711, -0.021, 6.789, 0.008, 6.867, 0.008, 1, 6.944, 0.008, 7.022, -0.003, 7.1, -0.003, 1, 7.133, -0.003, 7.167, -0.001, 7.2, -0.001, 1, 7.267, -0.001, 7.333, -18.6, 7.4, -18.6, 1, 7.522, -18.6, 7.644, 15.859, 7.767, 15.859, 1, 7.811, 15.859, 7.856, -1.469, 7.9, -1.469, 0, 8.1, -1.469]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.178, 1, 0.356, 1, 0.533, 1, 1, 0.589, 1, 0.644, 0, 0.7, 0, 1, 0.744, 0, 0.789, 1, 0.833, 1, 1, 1.433, 1, 2.033, 1, 2.633, 1, 1, 2.689, 1, 2.744, 0, 2.8, 0, 1, 2.844, 0, 2.889, 1, 2.933, 1, 1, 3.411, 1, 3.889, 1, 4.367, 1, 1, 4.378, 1, 4.389, 1, 4.4, 1, 1, 4.5, 1, 4.6, 0, 4.7, 0, 1, 5.522, 0, 6.344, 0, 7.167, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 1.411, 0, 2.822, 0, 4.233, 0, 1, 4.389, 0, 4.544, 1, 4.7, 1, 0, 8.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.178, 1, 0.356, 1, 0.533, 1, 1, 0.589, 1, 0.644, 0, 0.7, 0, 1, 0.744, 0, 0.789, 1, 0.833, 1, 1, 1.433, 1, 2.033, 1, 2.633, 1, 1, 2.689, 1, 2.744, 0, 2.8, 0, 1, 2.844, 0, 2.889, 1, 2.933, 1, 1, 3.422, 1, 3.911, 1, 4.4, 1, 1, 4.5, 1, 4.6, 0, 4.7, 0, 1, 5.522, 0, 6.344, 0, 7.167, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 1.411, 0, 2.822, 0, 4.233, 0, 1, 4.278, 0, 4.322, 1, 4.367, 1, 1, 4.478, 1, 4.589, 1, 4.7, 1, 0, 8.1, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.8, 1, 0.356, -0.8, 0.711, -0.8, 1.067, -0.8, 1, 1.144, -0.8, 1.222, -0.1, 1.3, -0.1, 1, 1.422, -0.1, 1.544, -0.29, 1.667, -0.4, 1, 1.778, -0.5, 1.889, -0.5, 2, -0.5, 1, 2.167, -0.5, 2.333, 0.1, 2.5, 0.1, 1, 2.578, 0.1, 2.656, 0.1, 2.733, 0.1, 1, 2.833, 0.1, 2.933, -0.2, 3.033, -0.2, 1, 3.122, -0.2, 3.211, -0.2, 3.3, -0.2, 1, 3.411, -0.2, 3.522, 0.1, 3.633, 0.1, 1, 3.733, 0.1, 3.833, 0.1, 3.933, 0.1, 1, 4.022, 0.1, 4.111, -0.2, 4.2, -0.2, 1, 4.289, -0.2, 4.378, -0.2, 4.467, -0.2, 1, 4.511, -0.2, 4.556, -0.4, 4.6, -0.4, 1, 4.667, -0.4, 4.733, -0.4, 4.8, -0.4, 1, 4.889, -0.4, 4.978, -0.4, 5.067, -0.4, 1, 5.144, -0.4, 5.222, 0, 5.3, 0, 1, 5.389, 0, 5.478, 0, 5.567, 0, 1, 5.656, 0, 5.744, -0.033, 5.833, -0.1, 1, 5.922, -0.167, 6.011, -0.2, 6.1, -0.2, 1, 6.2, -0.2, 6.3, -0.2, 6.4, -0.2, 1, 6.467, -0.2, 6.533, 0.1, 6.6, 0.1, 0, 8.1, 0.1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.356, 0, 0.711, 0, 1.067, 0, 1, 1.144, 0, 1.222, 0.24, 1.3, 0.4, 1, 1.422, 0.651, 1.544, 0.7, 1.667, 0.7, 1, 1.778, 0.7, 1.889, 0.5, 2, 0.5, 1, 2.167, 0.5, 2.333, 0.5, 2.5, 0.5, 1, 2.578, 0.5, 2.656, 0.5, 2.733, 0.5, 1, 2.833, 0.5, 2.933, 0.3, 3.033, 0.3, 1, 3.122, 0.3, 3.211, 0.3, 3.3, 0.3, 1, 3.411, 0.3, 3.522, 0.5, 3.633, 0.5, 1, 3.733, 0.5, 3.833, 0.5, 3.933, 0.5, 1, 4.022, 0.5, 4.111, 0.3, 4.2, 0.3, 1, 4.289, 0.3, 4.378, 0.3, 4.467, 0.3, 1, 4.511, 0.3, 4.556, 0, 4.6, 0, 1, 4.667, 0, 4.733, 0.5, 4.8, 0.5, 1, 4.889, 0.5, 4.978, 0.5, 5.067, 0.5, 1, 5.144, 0.5, 5.222, 0.4, 5.3, 0.4, 1, 5.389, 0.4, 5.478, 1, 5.567, 1, 1, 5.656, 1, 5.744, 0.514, 5.833, 0.4, 1, 5.922, 0.286, 6.011, 0.3, 6.1, 0.3, 1, 6.2, 0.3, 6.3, 0.3, 6.4, 0.3, 1, 6.467, 0.3, 6.533, 0, 6.6, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, -7, 1, 0.2, -7, 0.4, -10, 0.6, -10, 1, 0.656, -10, 0.711, -10, 0.767, -10, 0, 8.1, -10]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.656, 0, 0.711, 0, 0.767, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -2, 1, 0.2, -2, 0.4, -5, 0.6, -5, 1, 0.656, -5, 0.711, -5, 0.767, -5, 0, 8.1, -5]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.656, 0, 0.711, 0, 0.767, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -1, 1, 0.367, -1, 0.733, 13, 1.1, 13, 1, 1.267, 13, 1.433, -22, 1.6, -22, 1, 1.922, -22, 2.244, -22, 2.567, -22, 1, 2.9, -22, 3.233, 15, 3.567, 15, 1, 3.733, 15, 3.9, -20.346, 4.067, -21, 1, 4.333, -22.047, 4.6, -22, 4.867, -22, 1, 5.056, -22, 5.244, 3, 5.433, 3, 1, 5.611, 3, 5.789, -21, 5.967, -21, 1, 6.189, -21, 6.411, 3, 6.633, 3, 1, 6.811, 3, 6.989, -21, 7.167, -21, 0, 8.1, -21]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 1, 0.167, 0, 0.333, -1, 0.5, -1, 1, 0.667, -1, 0.833, 30, 1, 30, 1, 1.6, 30, 2.2, 30, 2.8, 30, 1, 2.944, 30, 3.089, -13.054, 3.233, -16, 1, 3.644, -24.383, 4.056, -26, 4.467, -26, 1, 4.611, -26, 4.756, 30, 4.9, 30, 1, 5.367, 30, 5.833, 12, 6.3, 12, 0, 8.1, 12]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, -1, 1, 0.278, -1, 0.556, 15, 0.833, 15, 1, 1.467, 15, 2.1, 15, 2.733, 15, 1, 2.878, 15, 3.022, -15, 3.167, -15, 1, 3.589, -15, 4.011, -14.899, 4.433, -14, 1, 4.589, -13.669, 4.744, 6, 4.9, 6, 0, 8.1, 6]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -30, 1, 0.2, -30, 0.4, 11, 0.6, 11, 1, 0.744, 11, 0.889, 11, 1.033, 11, 0, 8.1, 11]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -30, 1, 0.333, -30, 0.667, 30, 1, 30, 1, 1.356, 30, 1.711, -30, 2.067, -30, 1, 2.422, -30, 2.778, 30, 3.133, 30, 1, 3.478, 30, 3.822, -30, 4.167, -30, 1, 4.444, -30, 4.722, 30, 5, 30, 1, 5.278, 30, 5.556, -30, 5.833, -30, 1, 6.133, -30, 6.433, 30, 6.733, 30, 1, 7.011, 30, 7.289, -30, 7.567, -30, 0, 8.1, -30]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, -30, 0, 4.733, -30, 1, 5.256, -30, 5.778, 30, 6.3, 30, 0, 8.1, 30]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, -30, 0, 4.7, -30, 1, 5.078, -30, 5.456, 30, 5.833, 30, 1, 5.978, 30, 6.122, -30, 6.267, -30, 0, 8.1, -30]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.311, 0, 0.589, -2.352, 0.867, -2.352, 1, 0.967, -2.352, 1.067, -2.079, 1.167, -2.079, 1, 1.2, -2.079, 1.233, -2.097, 1.267, -2.097, 1, 1.522, -2.097, 1.778, 2.757, 2.033, 2.757, 1, 2.144, 2.757, 2.256, 2.271, 2.367, 2.271, 1, 2.422, 2.271, 2.478, 2.319, 2.533, 2.319, 1, 2.989, 2.319, 3.444, -2.42, 3.9, -2.42, 1, 4.011, -2.42, 4.122, -1.763, 4.233, -1.763, 1, 4.333, -1.763, 4.433, -1.935, 4.533, -1.935, 1, 4.567, -1.935, 4.6, -1.923, 4.633, -1.923, 1, 4.689, -1.923, 4.744, -2.26, 4.8, -2.26, 1, 5, -2.26, 5.2, 2.006, 5.4, 2.006, 1, 5.522, 2.006, 5.644, 1.626, 5.767, 1.626, 1, 5.989, 1.626, 6.211, 2.176, 6.433, 2.176, 1, 6.7, 2.176, 6.967, 0.541, 7.233, 0.541, 1, 7.411, 0.541, 7.589, 0.762, 7.767, 0.762, 1, 7.811, 0.762, 7.856, 0.729, 7.9, 0.729, 0, 8.1, 0.729]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.311, 0, 0.589, -2.352, 0.867, -2.352, 1, 0.967, -2.352, 1.067, -2.079, 1.167, -2.079, 1, 1.2, -2.079, 1.233, -2.097, 1.267, -2.097, 1, 1.522, -2.097, 1.778, 2.757, 2.033, 2.757, 1, 2.144, 2.757, 2.256, 2.271, 2.367, 2.271, 1, 2.422, 2.271, 2.478, 2.319, 2.533, 2.319, 1, 2.989, 2.319, 3.444, -2.42, 3.9, -2.42, 1, 4.011, -2.42, 4.122, -1.763, 4.233, -1.763, 1, 4.333, -1.763, 4.433, -1.935, 4.533, -1.935, 1, 4.567, -1.935, 4.6, -1.923, 4.633, -1.923, 1, 4.689, -1.923, 4.744, -2.26, 4.8, -2.26, 1, 5, -2.26, 5.2, 2.006, 5.4, 2.006, 1, 5.522, 2.006, 5.644, 1.626, 5.767, 1.626, 1, 5.989, 1.626, 6.211, 2.176, 6.433, 2.176, 1, 6.7, 2.176, 6.967, 0.541, 7.233, 0.541, 1, 7.411, 0.541, 7.589, 0.762, 7.767, 0.762, 1, 7.811, 0.762, 7.856, 0.729, 7.9, 0.729, 0, 8.1, 0.729]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.356, 0, 0.578, -5.014, 0.8, -5.014, 1, 0.889, -5.014, 0.978, -4.576, 1.067, -4.576, 1, 1.122, -4.576, 1.178, -4.698, 1.233, -4.698, 1, 1.489, -4.698, 1.744, 5.871, 2, 5.871, 1, 2.089, 5.871, 2.178, 4.952, 2.267, 4.952, 1, 2.344, 4.952, 2.422, 5.249, 2.5, 5.249, 1, 2.944, 5.249, 3.389, -4.586, 3.833, -4.586, 1, 3.922, -4.586, 4.011, -4.012, 4.1, -4.012, 1, 4.2, -4.012, 4.3, -4.267, 4.4, -4.267, 1, 4.778, -4.267, 5.156, 4.636, 5.533, 4.636, 1, 5.622, 4.636, 5.711, 3.991, 5.8, 3.991, 1, 5.9, 3.991, 6, 4.277, 6.1, 4.277, 1, 6.189, 4.277, 6.278, 4.15, 6.367, 4.15, 1, 6.4, 4.15, 6.433, 4.162, 6.467, 4.162, 1, 6.733, 4.162, 7, 1.416, 7.267, 1.416, 1, 7.356, 1.416, 7.444, 1.64, 7.533, 1.64, 1, 7.633, 1.64, 7.733, 1.54, 7.833, 1.54, 1, 7.856, 1.54, 7.878, 1.55, 7.9, 1.55, 0, 8.1, 1.55]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0.476, 0.367, 0.476, 1, 0.467, 0.476, 0.567, -0.353, 0.667, -0.353, 1, 0.822, -0.353, 0.978, 0.147, 1.133, 0.147, 1, 1.256, 0.147, 1.378, -0.838, 1.5, -0.838, 1, 1.611, -0.838, 1.722, 0.498, 1.833, 0.498, 1, 2.011, 0.498, 2.189, -0.315, 2.367, -0.315, 1, 2.467, -0.315, 2.567, 0.284, 2.667, 0.284, 1, 2.756, 0.284, 2.844, -0.081, 2.933, -0.081, 1, 3.033, -0.081, 3.133, 0.442, 3.233, 0.442, 1, 3.344, 0.442, 3.456, -0.193, 3.567, -0.193, 1, 3.633, -0.193, 3.7, -0.086, 3.767, -0.086, 1, 3.822, -0.086, 3.878, -0.214, 3.933, -0.214, 1, 4.033, -0.214, 4.133, 0.225, 4.233, 0.225, 1, 4.322, 0.225, 4.411, -0.16, 4.5, -0.16, 1, 4.567, -0.16, 4.633, 0.024, 4.7, 0.024, 1, 4.789, 0.024, 4.878, -0.535, 4.967, -0.535, 1, 5.078, -0.535, 5.189, 0.268, 5.3, 0.268, 1, 5.367, 0.268, 5.433, 0.098, 5.5, 0.098, 1, 5.544, 0.098, 5.589, 0.212, 5.633, 0.212, 1, 5.733, 0.212, 5.833, -0.243, 5.933, -0.243, 1, 6.022, -0.243, 6.111, 0.174, 6.2, 0.174, 1, 6.289, 0.174, 6.378, -0.102, 6.467, -0.102, 1, 6.567, -0.102, 6.667, 0.243, 6.767, 0.243, 1, 6.867, 0.243, 6.967, -0.137, 7.067, -0.137, 1, 7.256, -0.137, 7.444, 0.072, 7.633, 0.072, 1, 7.722, 0.072, 7.811, -0.052, 7.9, -0.052, 0, 8.1, -0.052]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.211, 0, 0.289, 0.714, 0.367, 0.714, 1, 0.467, 0.714, 0.567, -0.529, 0.667, -0.529, 1, 0.822, -0.529, 0.978, 0.221, 1.133, 0.221, 1, 1.256, 0.221, 1.378, -1.256, 1.5, -1.256, 1, 1.611, -1.256, 1.722, 0.748, 1.833, 0.748, 1, 2.011, 0.748, 2.189, -0.473, 2.367, -0.473, 1, 2.467, -0.473, 2.567, 0.427, 2.667, 0.427, 1, 2.756, 0.427, 2.844, -0.121, 2.933, -0.121, 1, 3.033, -0.121, 3.133, 0.663, 3.233, 0.663, 1, 3.344, 0.663, 3.456, -0.289, 3.567, -0.289, 1, 3.633, -0.289, 3.7, -0.128, 3.767, -0.128, 1, 3.822, -0.128, 3.878, -0.321, 3.933, -0.321, 1, 4.033, -0.321, 4.133, 0.338, 4.233, 0.338, 1, 4.322, 0.338, 4.411, -0.24, 4.5, -0.24, 1, 4.567, -0.24, 4.633, 0.036, 4.7, 0.036, 1, 4.789, 0.036, 4.878, -0.803, 4.967, -0.803, 1, 5.078, -0.803, 5.189, 0.402, 5.3, 0.402, 1, 5.367, 0.402, 5.433, 0.146, 5.5, 0.146, 1, 5.544, 0.146, 5.589, 0.318, 5.633, 0.318, 1, 5.733, 0.318, 5.833, -0.364, 5.933, -0.364, 1, 6.022, -0.364, 6.111, 0.262, 6.2, 0.262, 1, 6.289, 0.262, 6.378, -0.152, 6.467, -0.152, 1, 6.567, -0.152, 6.667, 0.365, 6.767, 0.365, 1, 6.867, 0.365, 6.967, -0.205, 7.067, -0.205, 1, 7.256, -0.205, 7.444, 0.108, 7.633, 0.108, 1, 7.722, 0.108, 7.811, -0.078, 7.9, -0.078, 0, 8.1, -0.078]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, -1.582, 0.733, -1.582, 1, 0.844, -1.582, 0.956, -1.382, 1.067, -1.382, 1, 1.133, -1.382, 1.2, -1.438, 1.267, -1.438, 1, 1.5, -1.438, 1.733, 1.993, 1.967, 1.993, 1, 2.078, 1.993, 2.189, 1.561, 2.3, 1.561, 1, 2.389, 1.561, 2.478, 1.716, 2.567, 1.716, 1, 3, 1.716, 3.433, -1.739, 3.867, -1.739, 1, 3.978, -1.739, 4.089, -1.127, 4.2, -1.127, 1, 4.3, -1.127, 4.4, -1.408, 4.5, -1.408, 1, 4.556, -1.408, 4.611, -1.344, 4.667, -1.344, 1, 4.7, -1.344, 4.733, -1.378, 4.767, -1.378, 1, 5.044, -1.378, 5.322, 0.756, 5.6, 0.756, 1, 5.7, 0.756, 5.8, 0.661, 5.9, 0.661, 1, 6.011, 0.661, 6.122, 0.705, 6.233, 0.705, 1, 6.556, 0.705, 6.878, 0, 7.2, 0, 1, 7.433, 0, 7.667, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0.37, 0.4, 0.37, 1, 0.522, 0.37, 0.644, -0.414, 0.767, -0.414, 1, 0.9, -0.414, 1.033, 0.264, 1.167, 0.264, 1, 1.289, 0.264, 1.411, -0.72, 1.533, -0.72, 1, 1.667, -0.72, 1.8, 0.668, 1.933, 0.668, 1, 2.078, 0.668, 2.222, -0.425, 2.367, -0.425, 1, 2.489, -0.425, 2.611, 0.328, 2.733, 0.328, 1, 2.833, 0.328, 2.933, -0.066, 3.033, -0.066, 1, 3.122, -0.066, 3.211, 0.278, 3.3, 0.278, 1, 3.389, 0.278, 3.478, -0.197, 3.567, -0.197, 1, 3.644, -0.197, 3.722, 0.115, 3.8, 0.115, 1, 3.878, 0.115, 3.956, -0.515, 4.033, -0.515, 1, 4.144, -0.515, 4.256, 0.513, 4.367, 0.513, 1, 4.467, 0.513, 4.567, -0.35, 4.667, -0.35, 1, 4.722, -0.35, 4.778, -0.131, 4.833, -0.131, 1, 4.9, -0.131, 4.967, -0.405, 5.033, -0.405, 1, 5.133, -0.405, 5.233, 0.565, 5.333, 0.565, 1, 5.444, 0.565, 5.556, -0.202, 5.667, -0.202, 1, 5.756, -0.202, 5.844, 0.066, 5.933, 0.066, 1, 6.033, 0.066, 6.133, -0.023, 6.233, -0.023, 1, 6.422, -0.023, 6.611, 0.091, 6.8, 0.091, 1, 6.922, 0.091, 7.044, -0.081, 7.167, -0.081, 1, 7.178, -0.081, 7.189, -0.046, 7.2, -0.046, 1, 7.211, -0.046, 7.222, -0.053, 7.233, -0.053, 1, 7.333, -0.053, 7.433, 0.135, 7.533, 0.135, 1, 7.644, 0.135, 7.756, 0.029, 7.867, 0.029, 1, 7.878, 0.029, 7.889, 0.029, 7.9, 0.029, 0, 8.1, 0.029]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, -2.197, 0.733, -2.197, 1, 0.844, -2.197, 0.956, -1.919, 1.067, -1.919, 1, 1.133, -1.919, 1.2, -1.997, 1.267, -1.997, 1, 1.5, -1.997, 1.733, 2.768, 1.967, 2.768, 1, 2.078, 2.768, 2.189, 2.169, 2.3, 2.169, 1, 2.389, 2.169, 2.478, 2.384, 2.567, 2.384, 1, 3, 2.384, 3.433, -2.415, 3.867, -2.415, 1, 3.978, -2.415, 4.089, -1.566, 4.2, -1.566, 1, 4.3, -1.566, 4.4, -1.956, 4.5, -1.956, 1, 4.556, -1.956, 4.611, -1.866, 4.667, -1.866, 1, 4.7, -1.866, 4.733, -1.914, 4.767, -1.914, 1, 5.044, -1.914, 5.322, 1.051, 5.6, 1.051, 1, 5.7, 1.051, 5.8, 0.919, 5.9, 0.919, 1, 6.011, 0.919, 6.122, 0.979, 6.233, 0.979, 1, 6.556, 0.979, 6.878, 0, 7.2, 0, 1, 7.433, 0, 7.667, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, -2.197, 0.733, -2.197, 1, 0.844, -2.197, 0.956, -1.919, 1.067, -1.919, 1, 1.133, -1.919, 1.2, -1.997, 1.267, -1.997, 1, 1.5, -1.997, 1.733, 2.768, 1.967, 2.768, 1, 2.078, 2.768, 2.189, 2.169, 2.3, 2.169, 1, 2.389, 2.169, 2.478, 2.384, 2.567, 2.384, 1, 3, 2.384, 3.433, -2.415, 3.867, -2.415, 1, 3.978, -2.415, 4.089, -1.566, 4.2, -1.566, 1, 4.3, -1.566, 4.4, -1.956, 4.5, -1.956, 1, 4.556, -1.956, 4.611, -1.866, 4.667, -1.866, 1, 4.7, -1.866, 4.733, -1.914, 4.767, -1.914, 1, 5.044, -1.914, 5.322, 1.051, 5.6, 1.051, 1, 5.7, 1.051, 5.8, 0.919, 5.9, 0.919, 1, 6.011, 0.919, 6.122, 0.979, 6.233, 0.979, 1, 6.556, 0.979, 6.878, 0, 7.2, 0, 1, 7.433, 0, 7.667, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, -2.197, 0.733, -2.197, 1, 0.844, -2.197, 0.956, -1.919, 1.067, -1.919, 1, 1.133, -1.919, 1.2, -1.997, 1.267, -1.997, 1, 1.5, -1.997, 1.733, 2.768, 1.967, 2.768, 1, 2.078, 2.768, 2.189, 2.169, 2.3, 2.169, 1, 2.389, 2.169, 2.478, 2.384, 2.567, 2.384, 1, 3, 2.384, 3.433, -2.415, 3.867, -2.415, 1, 3.978, -2.415, 4.089, -1.566, 4.2, -1.566, 1, 4.3, -1.566, 4.4, -1.956, 4.5, -1.956, 1, 4.556, -1.956, 4.611, -1.866, 4.667, -1.866, 1, 4.7, -1.866, 4.733, -1.914, 4.767, -1.914, 1, 5.044, -1.914, 5.322, 1.051, 5.6, 1.051, 1, 5.7, 1.051, 5.8, 0.919, 5.9, 0.919, 1, 6.011, 0.919, 6.122, 0.979, 6.233, 0.979, 1, 6.556, 0.979, 6.878, 0, 7.2, 0, 1, 7.433, 0, 7.667, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, -1.582, 0.733, -1.582, 1, 0.844, -1.582, 0.956, -1.382, 1.067, -1.382, 1, 1.133, -1.382, 1.2, -1.438, 1.267, -1.438, 1, 1.5, -1.438, 1.733, 1.993, 1.967, 1.993, 1, 2.078, 1.993, 2.189, 1.561, 2.3, 1.561, 1, 2.389, 1.561, 2.478, 1.716, 2.567, 1.716, 1, 3, 1.716, 3.433, -1.739, 3.867, -1.739, 1, 3.978, -1.739, 4.089, -1.127, 4.2, -1.127, 1, 4.3, -1.127, 4.4, -1.408, 4.5, -1.408, 1, 4.556, -1.408, 4.611, -1.344, 4.667, -1.344, 1, 4.7, -1.344, 4.733, -1.378, 4.767, -1.378, 1, 5.044, -1.378, 5.322, 0.756, 5.6, 0.756, 1, 5.7, 0.756, 5.8, 0.661, 5.9, 0.661, 1, 6.011, 0.661, 6.122, 0.705, 6.233, 0.705, 1, 6.556, 0.705, 6.878, 0, 7.2, 0, 1, 7.433, 0, 7.667, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0.37, 0.4, 0.37, 1, 0.522, 0.37, 0.644, -0.414, 0.767, -0.414, 1, 0.9, -0.414, 1.033, 0.264, 1.167, 0.264, 1, 1.289, 0.264, 1.411, -0.72, 1.533, -0.72, 1, 1.667, -0.72, 1.8, 0.668, 1.933, 0.668, 1, 2.078, 0.668, 2.222, -0.425, 2.367, -0.425, 1, 2.489, -0.425, 2.611, 0.328, 2.733, 0.328, 1, 2.833, 0.328, 2.933, -0.066, 3.033, -0.066, 1, 3.122, -0.066, 3.211, 0.278, 3.3, 0.278, 1, 3.389, 0.278, 3.478, -0.197, 3.567, -0.197, 1, 3.644, -0.197, 3.722, 0.115, 3.8, 0.115, 1, 3.878, 0.115, 3.956, -0.515, 4.033, -0.515, 1, 4.144, -0.515, 4.256, 0.513, 4.367, 0.513, 1, 4.467, 0.513, 4.567, -0.35, 4.667, -0.35, 1, 4.722, -0.35, 4.778, -0.131, 4.833, -0.131, 1, 4.9, -0.131, 4.967, -0.405, 5.033, -0.405, 1, 5.133, -0.405, 5.233, 0.565, 5.333, 0.565, 1, 5.444, 0.565, 5.556, -0.202, 5.667, -0.202, 1, 5.756, -0.202, 5.844, 0.066, 5.933, 0.066, 1, 6.033, 0.066, 6.133, -0.023, 6.233, -0.023, 1, 6.422, -0.023, 6.611, 0.091, 6.8, 0.091, 1, 6.922, 0.091, 7.044, -0.081, 7.167, -0.081, 1, 7.178, -0.081, 7.189, -0.046, 7.2, -0.046, 1, 7.211, -0.046, 7.222, -0.053, 7.233, -0.053, 1, 7.333, -0.053, 7.433, 0.135, 7.533, 0.135, 1, 7.644, 0.135, 7.756, 0.029, 7.867, 0.029, 1, 7.878, 0.029, 7.889, 0.029, 7.9, 0.029, 0, 8.1, 0.029]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, -2.197, 0.733, -2.197, 1, 0.844, -2.197, 0.956, -1.919, 1.067, -1.919, 1, 1.133, -1.919, 1.2, -1.997, 1.267, -1.997, 1, 1.5, -1.997, 1.733, 2.768, 1.967, 2.768, 1, 2.078, 2.768, 2.189, 2.169, 2.3, 2.169, 1, 2.389, 2.169, 2.478, 2.384, 2.567, 2.384, 1, 3, 2.384, 3.433, -2.415, 3.867, -2.415, 1, 3.978, -2.415, 4.089, -1.566, 4.2, -1.566, 1, 4.3, -1.566, 4.4, -1.956, 4.5, -1.956, 1, 4.556, -1.956, 4.611, -1.866, 4.667, -1.866, 1, 4.7, -1.866, 4.733, -1.914, 4.767, -1.914, 1, 5.044, -1.914, 5.322, 1.051, 5.6, 1.051, 1, 5.7, 1.051, 5.8, 0.919, 5.9, 0.919, 1, 6.011, 0.919, 6.122, 0.979, 6.233, 0.979, 1, 6.556, 0.979, 6.878, 0, 7.2, 0, 1, 7.433, 0, 7.667, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.422, 0, 0.644, -2.73, 0.867, -2.73, 1, 1.267, -2.73, 1.667, 3.706, 2.067, 3.706, 1, 2.278, 3.706, 2.489, 1.875, 2.7, 1.875, 1, 2.756, 1.875, 2.811, 1.918, 2.867, 1.918, 1, 3.233, 1.918, 3.6, -2.609, 3.967, -2.609, 1, 4.156, -2.609, 4.344, -1.606, 4.533, -1.606, 1, 4.644, -1.606, 4.756, -1.99, 4.867, -1.99, 1, 5.089, -1.99, 5.311, 1.874, 5.533, 1.874, 1, 5.722, 1.874, 5.911, 0.693, 6.1, 0.693, 1, 6.256, 0.693, 6.411, 0.987, 6.567, 0.987, 1, 6.811, 0.987, 7.056, -0.194, 7.3, -0.194, 1, 7.5, -0.194, 7.7, 0.149, 7.9, 0.149, 0, 8.1, 0.149]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.178, 0, 0.222, -0.128, 0.267, -0.128, 1, 0.4, -0.128, 0.533, 0.104, 0.667, 0.104, 1, 0.7, 0.104, 0.733, 0.1, 0.767, 0.1, 1, 0.878, 0.1, 0.989, 0.235, 1.1, 0.235, 1, 1.256, 0.235, 1.411, -0.317, 1.567, -0.317, 1, 1.656, -0.317, 1.744, -0.12, 1.833, -0.12, 1, 1.978, -0.12, 2.122, -0.455, 2.267, -0.455, 1, 2.433, -0.455, 2.6, 0.267, 2.767, 0.267, 1, 2.867, 0.267, 2.967, 0.089, 3.067, 0.089, 1, 3.156, 0.089, 3.244, 0.157, 3.333, 0.157, 1, 3.433, 0.157, 3.533, -0.249, 3.633, -0.249, 1, 3.756, -0.249, 3.878, 0.365, 4, 0.365, 1, 4.211, 0.365, 4.422, -0.095, 4.633, -0.095, 1, 4.722, -0.095, 4.811, 0.247, 4.9, 0.247, 1, 4.978, 0.247, 5.056, -0.129, 5.133, -0.129, 1, 5.189, -0.129, 5.244, -0.076, 5.3, -0.076, 1, 5.433, -0.076, 5.567, -0.315, 5.7, -0.315, 1, 5.878, -0.315, 6.056, 0.152, 6.233, 0.152, 1, 6.367, 0.152, 6.5, -0.007, 6.633, -0.007, 1, 6.922, -0.007, 7.211, 0.089, 7.5, 0.089, 1, 7.511, 0.089, 7.522, 0.088, 7.533, 0.088, 1, 7.556, 0.088, 7.578, 0.105, 7.6, 0.105, 1, 7.7, 0.105, 7.8, -0.035, 7.9, -0.035, 0, 8.1, -0.035]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.256, 0, 0.378, 0.299, 0.5, 0.299, 1, 0.656, 0.299, 0.811, -0.507, 0.967, -0.507, 1, 1.089, -0.507, 1.211, 0.34, 1.333, 0.34, 1, 1.444, 0.34, 1.556, -0.937, 1.667, -0.937, 1, 1.811, -0.937, 1.956, 0.986, 2.1, 0.986, 1, 2.233, 0.986, 2.367, -0.636, 2.5, -0.636, 1, 2.633, -0.636, 2.767, 0.381, 2.9, 0.381, 1, 2.989, 0.381, 3.078, 0.14, 3.167, 0.14, 1, 3.244, 0.14, 3.322, 0.304, 3.4, 0.304, 1, 3.522, 0.304, 3.644, -0.337, 3.767, -0.337, 1, 3.889, -0.337, 4.011, -0.001, 4.133, -0.001, 1, 4.178, -0.001, 4.222, -0.018, 4.267, -0.018, 1, 4.378, -0.018, 4.489, 0.111, 4.6, 0.111, 1, 4.789, 0.111, 4.978, -0.541, 5.167, -0.541, 1, 5.3, -0.541, 5.433, 0.644, 5.567, 0.644, 1, 5.7, 0.644, 5.833, -0.368, 5.967, -0.368, 1, 6.1, -0.368, 6.233, 0.164, 6.367, 0.164, 1, 6.467, 0.164, 6.567, -0.006, 6.667, -0.006, 1, 6.756, -0.006, 6.844, 0.144, 6.933, 0.144, 1, 7.078, 0.144, 7.222, -0.185, 7.367, -0.185, 1, 7.511, -0.185, 7.656, 0.142, 7.8, 0.142, 1, 7.833, 0.142, 7.867, 0.089, 7.9, 0.089, 0, 8.1, 0.089]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, -0.075, 1, 0.033, -0.075, 0.067, -0.069, 0.1, -0.069, 1, 0.122, -0.069, 0.144, -0.08, 0.167, -0.08, 1, 0.3, -0.08, 0.433, 0.416, 0.567, 0.416, 1, 0.722, 0.416, 0.878, -0.742, 1.033, -0.742, 1, 1.167, -0.742, 1.3, 0.692, 1.433, 0.692, 1, 1.556, 0.692, 1.678, -1.438, 1.8, -1.438, 1, 1.933, -1.438, 2.067, 1.822, 2.2, 1.822, 1, 2.333, 1.822, 2.467, -1.472, 2.6, -1.472, 1, 2.722, -1.472, 2.844, 1.067, 2.967, 1.067, 1, 3.078, 1.067, 3.189, -0.24, 3.3, -0.24, 1, 3.389, -0.24, 3.478, 0.376, 3.567, 0.376, 1, 3.689, 0.376, 3.811, -0.597, 3.933, -0.597, 1, 4.044, -0.597, 4.156, 0.262, 4.267, 0.262, 1, 4.367, 0.262, 4.467, -0.045, 4.567, -0.045, 1, 4.644, -0.045, 4.722, 0.103, 4.8, 0.103, 1, 4.944, 0.103, 5.089, -0.724, 5.233, -0.724, 1, 5.367, -0.724, 5.5, 1.09, 5.633, 1.09, 1, 5.778, 1.09, 5.922, -0.871, 6.067, -0.871, 1, 6.189, -0.871, 6.311, 0.548, 6.433, 0.548, 1, 6.544, 0.548, 6.656, -0.232, 6.767, -0.232, 1, 6.878, -0.232, 6.989, 0.275, 7.1, 0.275, 1, 7.222, 0.275, 7.344, -0.362, 7.467, -0.362, 1, 7.6, -0.362, 7.733, 0.312, 7.867, 0.312, 1, 7.878, 0.312, 7.889, 0.294, 7.9, 0.294, 0, 8.1, 0.294]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.422, 0, 0.644, -2.73, 0.867, -2.73, 1, 1.267, -2.73, 1.667, 3.706, 2.067, 3.706, 1, 2.278, 3.706, 2.489, 1.875, 2.7, 1.875, 1, 2.756, 1.875, 2.811, 1.918, 2.867, 1.918, 1, 3.233, 1.918, 3.6, -2.609, 3.967, -2.609, 1, 4.156, -2.609, 4.344, -1.606, 4.533, -1.606, 1, 4.644, -1.606, 4.756, -1.99, 4.867, -1.99, 1, 5.089, -1.99, 5.311, 1.874, 5.533, 1.874, 1, 5.722, 1.874, 5.911, 0.693, 6.1, 0.693, 1, 6.256, 0.693, 6.411, 0.987, 6.567, 0.987, 1, 6.811, 0.987, 7.056, -0.194, 7.3, -0.194, 1, 7.5, -0.194, 7.7, 0.149, 7.9, 0.149, 0, 8.1, 0.149]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.178, 0, 0.222, -0.128, 0.267, -0.128, 1, 0.4, -0.128, 0.533, 0.104, 0.667, 0.104, 1, 0.7, 0.104, 0.733, 0.1, 0.767, 0.1, 1, 0.878, 0.1, 0.989, 0.235, 1.1, 0.235, 1, 1.256, 0.235, 1.411, -0.317, 1.567, -0.317, 1, 1.656, -0.317, 1.744, -0.12, 1.833, -0.12, 1, 1.978, -0.12, 2.122, -0.455, 2.267, -0.455, 1, 2.433, -0.455, 2.6, 0.267, 2.767, 0.267, 1, 2.867, 0.267, 2.967, 0.089, 3.067, 0.089, 1, 3.156, 0.089, 3.244, 0.157, 3.333, 0.157, 1, 3.433, 0.157, 3.533, -0.249, 3.633, -0.249, 1, 3.756, -0.249, 3.878, 0.365, 4, 0.365, 1, 4.211, 0.365, 4.422, -0.095, 4.633, -0.095, 1, 4.722, -0.095, 4.811, 0.247, 4.9, 0.247, 1, 4.978, 0.247, 5.056, -0.129, 5.133, -0.129, 1, 5.189, -0.129, 5.244, -0.076, 5.3, -0.076, 1, 5.433, -0.076, 5.567, -0.315, 5.7, -0.315, 1, 5.878, -0.315, 6.056, 0.152, 6.233, 0.152, 1, 6.367, 0.152, 6.5, -0.007, 6.633, -0.007, 1, 6.922, -0.007, 7.211, 0.089, 7.5, 0.089, 1, 7.511, 0.089, 7.522, 0.088, 7.533, 0.088, 1, 7.556, 0.088, 7.578, 0.105, 7.6, 0.105, 1, 7.7, 0.105, 7.8, -0.035, 7.9, -0.035, 0, 8.1, -0.035]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.256, 0, 0.378, 0.299, 0.5, 0.299, 1, 0.656, 0.299, 0.811, -0.507, 0.967, -0.507, 1, 1.089, -0.507, 1.211, 0.34, 1.333, 0.34, 1, 1.444, 0.34, 1.556, -0.937, 1.667, -0.937, 1, 1.811, -0.937, 1.956, 0.986, 2.1, 0.986, 1, 2.233, 0.986, 2.367, -0.636, 2.5, -0.636, 1, 2.633, -0.636, 2.767, 0.381, 2.9, 0.381, 1, 2.989, 0.381, 3.078, 0.14, 3.167, 0.14, 1, 3.244, 0.14, 3.322, 0.304, 3.4, 0.304, 1, 3.522, 0.304, 3.644, -0.337, 3.767, -0.337, 1, 3.889, -0.337, 4.011, -0.001, 4.133, -0.001, 1, 4.178, -0.001, 4.222, -0.018, 4.267, -0.018, 1, 4.378, -0.018, 4.489, 0.111, 4.6, 0.111, 1, 4.789, 0.111, 4.978, -0.541, 5.167, -0.541, 1, 5.3, -0.541, 5.433, 0.644, 5.567, 0.644, 1, 5.7, 0.644, 5.833, -0.368, 5.967, -0.368, 1, 6.1, -0.368, 6.233, 0.164, 6.367, 0.164, 1, 6.467, 0.164, 6.567, -0.006, 6.667, -0.006, 1, 6.756, -0.006, 6.844, 0.144, 6.933, 0.144, 1, 7.078, 0.144, 7.222, -0.185, 7.367, -0.185, 1, 7.511, -0.185, 7.656, 0.142, 7.8, 0.142, 1, 7.833, 0.142, 7.867, 0.089, 7.9, 0.089, 0, 8.1, 0.089]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.422, 0, 0.644, -2.73, 0.867, -2.73, 1, 1.267, -2.73, 1.667, 3.706, 2.067, 3.706, 1, 2.278, 3.706, 2.489, 1.875, 2.7, 1.875, 1, 2.756, 1.875, 2.811, 1.918, 2.867, 1.918, 1, 3.233, 1.918, 3.6, -2.609, 3.967, -2.609, 1, 4.156, -2.609, 4.344, -1.606, 4.533, -1.606, 1, 4.644, -1.606, 4.756, -1.99, 4.867, -1.99, 1, 5.089, -1.99, 5.311, 1.874, 5.533, 1.874, 1, 5.722, 1.874, 5.911, 0.693, 6.1, 0.693, 1, 6.256, 0.693, 6.411, 0.987, 6.567, 0.987, 1, 6.811, 0.987, 7.056, -0.194, 7.3, -0.194, 1, 7.5, -0.194, 7.7, 0.149, 7.9, 0.149, 0, 8.1, 0.149]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.178, 0, 0.222, -0.128, 0.267, -0.128, 1, 0.4, -0.128, 0.533, 0.104, 0.667, 0.104, 1, 0.7, 0.104, 0.733, 0.1, 0.767, 0.1, 1, 0.878, 0.1, 0.989, 0.235, 1.1, 0.235, 1, 1.256, 0.235, 1.411, -0.317, 1.567, -0.317, 1, 1.656, -0.317, 1.744, -0.12, 1.833, -0.12, 1, 1.978, -0.12, 2.122, -0.455, 2.267, -0.455, 1, 2.433, -0.455, 2.6, 0.267, 2.767, 0.267, 1, 2.867, 0.267, 2.967, 0.089, 3.067, 0.089, 1, 3.156, 0.089, 3.244, 0.157, 3.333, 0.157, 1, 3.433, 0.157, 3.533, -0.249, 3.633, -0.249, 1, 3.756, -0.249, 3.878, 0.365, 4, 0.365, 1, 4.211, 0.365, 4.422, -0.095, 4.633, -0.095, 1, 4.722, -0.095, 4.811, 0.247, 4.9, 0.247, 1, 4.978, 0.247, 5.056, -0.129, 5.133, -0.129, 1, 5.189, -0.129, 5.244, -0.076, 5.3, -0.076, 1, 5.433, -0.076, 5.567, -0.315, 5.7, -0.315, 1, 5.878, -0.315, 6.056, 0.152, 6.233, 0.152, 1, 6.367, 0.152, 6.5, -0.007, 6.633, -0.007, 1, 6.922, -0.007, 7.211, 0.089, 7.5, 0.089, 1, 7.511, 0.089, 7.522, 0.088, 7.533, 0.088, 1, 7.556, 0.088, 7.578, 0.105, 7.6, 0.105, 1, 7.7, 0.105, 7.8, -0.035, 7.9, -0.035, 0, 8.1, -0.035]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.256, 0, 0.378, 0.299, 0.5, 0.299, 1, 0.656, 0.299, 0.811, -0.507, 0.967, -0.507, 1, 1.089, -0.507, 1.211, 0.34, 1.333, 0.34, 1, 1.444, 0.34, 1.556, -0.937, 1.667, -0.937, 1, 1.811, -0.937, 1.956, 0.986, 2.1, 0.986, 1, 2.233, 0.986, 2.367, -0.636, 2.5, -0.636, 1, 2.633, -0.636, 2.767, 0.381, 2.9, 0.381, 1, 2.989, 0.381, 3.078, 0.14, 3.167, 0.14, 1, 3.244, 0.14, 3.322, 0.304, 3.4, 0.304, 1, 3.522, 0.304, 3.644, -0.337, 3.767, -0.337, 1, 3.889, -0.337, 4.011, -0.001, 4.133, -0.001, 1, 4.178, -0.001, 4.222, -0.018, 4.267, -0.018, 1, 4.378, -0.018, 4.489, 0.111, 4.6, 0.111, 1, 4.789, 0.111, 4.978, -0.541, 5.167, -0.541, 1, 5.3, -0.541, 5.433, 0.644, 5.567, 0.644, 1, 5.7, 0.644, 5.833, -0.368, 5.967, -0.368, 1, 6.1, -0.368, 6.233, 0.164, 6.367, 0.164, 1, 6.467, 0.164, 6.567, -0.006, 6.667, -0.006, 1, 6.756, -0.006, 6.844, 0.144, 6.933, 0.144, 1, 7.078, 0.144, 7.222, -0.185, 7.367, -0.185, 1, 7.511, -0.185, 7.656, 0.142, 7.8, 0.142, 1, 7.833, 0.142, 7.867, 0.089, 7.9, 0.089, 0, 8.1, 0.089]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, -0.075, 1, 0.033, -0.075, 0.067, -0.069, 0.1, -0.069, 1, 0.122, -0.069, 0.144, -0.08, 0.167, -0.08, 1, 0.3, -0.08, 0.433, 0.416, 0.567, 0.416, 1, 0.722, 0.416, 0.878, -0.742, 1.033, -0.742, 1, 1.167, -0.742, 1.3, 0.692, 1.433, 0.692, 1, 1.556, 0.692, 1.678, -1.438, 1.8, -1.438, 1, 1.933, -1.438, 2.067, 1.822, 2.2, 1.822, 1, 2.333, 1.822, 2.467, -1.472, 2.6, -1.472, 1, 2.722, -1.472, 2.844, 1.067, 2.967, 1.067, 1, 3.078, 1.067, 3.189, -0.24, 3.3, -0.24, 1, 3.389, -0.24, 3.478, 0.376, 3.567, 0.376, 1, 3.689, 0.376, 3.811, -0.597, 3.933, -0.597, 1, 4.044, -0.597, 4.156, 0.262, 4.267, 0.262, 1, 4.367, 0.262, 4.467, -0.045, 4.567, -0.045, 1, 4.644, -0.045, 4.722, 0.103, 4.8, 0.103, 1, 4.944, 0.103, 5.089, -0.724, 5.233, -0.724, 1, 5.367, -0.724, 5.5, 1.09, 5.633, 1.09, 1, 5.778, 1.09, 5.922, -0.871, 6.067, -0.871, 1, 6.189, -0.871, 6.311, 0.548, 6.433, 0.548, 1, 6.544, 0.548, 6.656, -0.232, 6.767, -0.232, 1, 6.878, -0.232, 6.989, 0.275, 7.1, 0.275, 1, 7.222, 0.275, 7.344, -0.362, 7.467, -0.362, 1, 7.6, -0.362, 7.733, 0.312, 7.867, 0.312, 1, 7.878, 0.312, 7.889, 0.294, 7.9, 0.294, 0, 8.1, 0.294]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.422, 0, 0.644, -2.73, 0.867, -2.73, 1, 1.267, -2.73, 1.667, 3.706, 2.067, 3.706, 1, 2.278, 3.706, 2.489, 1.875, 2.7, 1.875, 1, 2.756, 1.875, 2.811, 1.918, 2.867, 1.918, 1, 3.233, 1.918, 3.6, -2.609, 3.967, -2.609, 1, 4.156, -2.609, 4.344, -1.606, 4.533, -1.606, 1, 4.644, -1.606, 4.756, -1.99, 4.867, -1.99, 1, 5.089, -1.99, 5.311, 1.874, 5.533, 1.874, 1, 5.722, 1.874, 5.911, 0.693, 6.1, 0.693, 1, 6.256, 0.693, 6.411, 0.987, 6.567, 0.987, 1, 6.811, 0.987, 7.056, -0.194, 7.3, -0.194, 1, 7.5, -0.194, 7.7, 0.149, 7.9, 0.149, 0, 8.1, 0.149]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.178, 0, 0.222, -0.128, 0.267, -0.128, 1, 0.4, -0.128, 0.533, 0.104, 0.667, 0.104, 1, 0.7, 0.104, 0.733, 0.1, 0.767, 0.1, 1, 0.878, 0.1, 0.989, 0.235, 1.1, 0.235, 1, 1.256, 0.235, 1.411, -0.317, 1.567, -0.317, 1, 1.656, -0.317, 1.744, -0.12, 1.833, -0.12, 1, 1.978, -0.12, 2.122, -0.455, 2.267, -0.455, 1, 2.433, -0.455, 2.6, 0.267, 2.767, 0.267, 1, 2.867, 0.267, 2.967, 0.089, 3.067, 0.089, 1, 3.156, 0.089, 3.244, 0.157, 3.333, 0.157, 1, 3.433, 0.157, 3.533, -0.249, 3.633, -0.249, 1, 3.756, -0.249, 3.878, 0.365, 4, 0.365, 1, 4.211, 0.365, 4.422, -0.095, 4.633, -0.095, 1, 4.722, -0.095, 4.811, 0.247, 4.9, 0.247, 1, 4.978, 0.247, 5.056, -0.129, 5.133, -0.129, 1, 5.189, -0.129, 5.244, -0.076, 5.3, -0.076, 1, 5.433, -0.076, 5.567, -0.315, 5.7, -0.315, 1, 5.878, -0.315, 6.056, 0.152, 6.233, 0.152, 1, 6.367, 0.152, 6.5, -0.007, 6.633, -0.007, 1, 6.922, -0.007, 7.211, 0.089, 7.5, 0.089, 1, 7.511, 0.089, 7.522, 0.088, 7.533, 0.088, 1, 7.556, 0.088, 7.578, 0.105, 7.6, 0.105, 1, 7.7, 0.105, 7.8, -0.035, 7.9, -0.035, 0, 8.1, -0.035]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.256, 0, 0.378, 0.299, 0.5, 0.299, 1, 0.656, 0.299, 0.811, -0.507, 0.967, -0.507, 1, 1.089, -0.507, 1.211, 0.34, 1.333, 0.34, 1, 1.444, 0.34, 1.556, -0.937, 1.667, -0.937, 1, 1.811, -0.937, 1.956, 0.986, 2.1, 0.986, 1, 2.233, 0.986, 2.367, -0.636, 2.5, -0.636, 1, 2.633, -0.636, 2.767, 0.381, 2.9, 0.381, 1, 2.989, 0.381, 3.078, 0.14, 3.167, 0.14, 1, 3.244, 0.14, 3.322, 0.304, 3.4, 0.304, 1, 3.522, 0.304, 3.644, -0.337, 3.767, -0.337, 1, 3.889, -0.337, 4.011, -0.001, 4.133, -0.001, 1, 4.178, -0.001, 4.222, -0.018, 4.267, -0.018, 1, 4.378, -0.018, 4.489, 0.111, 4.6, 0.111, 1, 4.789, 0.111, 4.978, -0.541, 5.167, -0.541, 1, 5.3, -0.541, 5.433, 0.644, 5.567, 0.644, 1, 5.7, 0.644, 5.833, -0.368, 5.967, -0.368, 1, 6.1, -0.368, 6.233, 0.164, 6.367, 0.164, 1, 6.467, 0.164, 6.567, -0.006, 6.667, -0.006, 1, 6.756, -0.006, 6.844, 0.144, 6.933, 0.144, 1, 7.078, 0.144, 7.222, -0.185, 7.367, -0.185, 1, 7.511, -0.185, 7.656, 0.142, 7.8, 0.142, 1, 7.833, 0.142, 7.867, 0.089, 7.9, 0.089, 0, 8.1, 0.089]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.422, 0, 0.644, -2.73, 0.867, -2.73, 1, 1.267, -2.73, 1.667, 3.706, 2.067, 3.706, 1, 2.278, 3.706, 2.489, 1.875, 2.7, 1.875, 1, 2.756, 1.875, 2.811, 1.918, 2.867, 1.918, 1, 3.233, 1.918, 3.6, -2.609, 3.967, -2.609, 1, 4.156, -2.609, 4.344, -1.606, 4.533, -1.606, 1, 4.644, -1.606, 4.756, -1.99, 4.867, -1.99, 1, 5.089, -1.99, 5.311, 1.874, 5.533, 1.874, 1, 5.722, 1.874, 5.911, 0.693, 6.1, 0.693, 1, 6.256, 0.693, 6.411, 0.987, 6.567, 0.987, 1, 6.811, 0.987, 7.056, -0.194, 7.3, -0.194, 1, 7.5, -0.194, 7.7, 0.149, 7.9, 0.149, 0, 8.1, 0.149]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.178, 0, 0.222, -0.128, 0.267, -0.128, 1, 0.4, -0.128, 0.533, 0.104, 0.667, 0.104, 1, 0.7, 0.104, 0.733, 0.1, 0.767, 0.1, 1, 0.878, 0.1, 0.989, 0.235, 1.1, 0.235, 1, 1.256, 0.235, 1.411, -0.317, 1.567, -0.317, 1, 1.656, -0.317, 1.744, -0.12, 1.833, -0.12, 1, 1.978, -0.12, 2.122, -0.455, 2.267, -0.455, 1, 2.433, -0.455, 2.6, 0.267, 2.767, 0.267, 1, 2.867, 0.267, 2.967, 0.089, 3.067, 0.089, 1, 3.156, 0.089, 3.244, 0.157, 3.333, 0.157, 1, 3.433, 0.157, 3.533, -0.249, 3.633, -0.249, 1, 3.756, -0.249, 3.878, 0.365, 4, 0.365, 1, 4.211, 0.365, 4.422, -0.095, 4.633, -0.095, 1, 4.722, -0.095, 4.811, 0.247, 4.9, 0.247, 1, 4.978, 0.247, 5.056, -0.129, 5.133, -0.129, 1, 5.189, -0.129, 5.244, -0.076, 5.3, -0.076, 1, 5.433, -0.076, 5.567, -0.315, 5.7, -0.315, 1, 5.878, -0.315, 6.056, 0.152, 6.233, 0.152, 1, 6.367, 0.152, 6.5, -0.007, 6.633, -0.007, 1, 6.922, -0.007, 7.211, 0.089, 7.5, 0.089, 1, 7.511, 0.089, 7.522, 0.088, 7.533, 0.088, 1, 7.556, 0.088, 7.578, 0.105, 7.6, 0.105, 1, 7.7, 0.105, 7.8, -0.035, 7.9, -0.035, 0, 8.1, -0.035]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.256, 0, 0.378, 0.299, 0.5, 0.299, 1, 0.656, 0.299, 0.811, -0.507, 0.967, -0.507, 1, 1.089, -0.507, 1.211, 0.34, 1.333, 0.34, 1, 1.444, 0.34, 1.556, -0.937, 1.667, -0.937, 1, 1.811, -0.937, 1.956, 0.986, 2.1, 0.986, 1, 2.233, 0.986, 2.367, -0.636, 2.5, -0.636, 1, 2.633, -0.636, 2.767, 0.381, 2.9, 0.381, 1, 2.989, 0.381, 3.078, 0.14, 3.167, 0.14, 1, 3.244, 0.14, 3.322, 0.304, 3.4, 0.304, 1, 3.522, 0.304, 3.644, -0.337, 3.767, -0.337, 1, 3.889, -0.337, 4.011, -0.001, 4.133, -0.001, 1, 4.178, -0.001, 4.222, -0.018, 4.267, -0.018, 1, 4.378, -0.018, 4.489, 0.111, 4.6, 0.111, 1, 4.789, 0.111, 4.978, -0.541, 5.167, -0.541, 1, 5.3, -0.541, 5.433, 0.644, 5.567, 0.644, 1, 5.7, 0.644, 5.833, -0.368, 5.967, -0.368, 1, 6.1, -0.368, 6.233, 0.164, 6.367, 0.164, 1, 6.467, 0.164, 6.567, -0.006, 6.667, -0.006, 1, 6.756, -0.006, 6.844, 0.144, 6.933, 0.144, 1, 7.078, 0.144, 7.222, -0.185, 7.367, -0.185, 1, 7.511, -0.185, 7.656, 0.142, 7.8, 0.142, 1, 7.833, 0.142, 7.867, 0.089, 7.9, 0.089, 0, 8.1, 0.089]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.422, 0, 0.644, -2.73, 0.867, -2.73, 1, 1.267, -2.73, 1.667, 3.706, 2.067, 3.706, 1, 2.278, 3.706, 2.489, 1.875, 2.7, 1.875, 1, 2.756, 1.875, 2.811, 1.918, 2.867, 1.918, 1, 3.233, 1.918, 3.6, -2.609, 3.967, -2.609, 1, 4.156, -2.609, 4.344, -1.606, 4.533, -1.606, 1, 4.644, -1.606, 4.756, -1.99, 4.867, -1.99, 1, 5.089, -1.99, 5.311, 1.874, 5.533, 1.874, 1, 5.722, 1.874, 5.911, 0.693, 6.1, 0.693, 1, 6.256, 0.693, 6.411, 0.987, 6.567, 0.987, 1, 6.811, 0.987, 7.056, -0.194, 7.3, -0.194, 1, 7.5, -0.194, 7.7, 0.149, 7.9, 0.149, 0, 8.1, 0.149]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.178, 0, 0.222, -0.128, 0.267, -0.128, 1, 0.4, -0.128, 0.533, 0.104, 0.667, 0.104, 1, 0.7, 0.104, 0.733, 0.1, 0.767, 0.1, 1, 0.878, 0.1, 0.989, 0.235, 1.1, 0.235, 1, 1.256, 0.235, 1.411, -0.317, 1.567, -0.317, 1, 1.656, -0.317, 1.744, -0.12, 1.833, -0.12, 1, 1.978, -0.12, 2.122, -0.455, 2.267, -0.455, 1, 2.433, -0.455, 2.6, 0.267, 2.767, 0.267, 1, 2.867, 0.267, 2.967, 0.089, 3.067, 0.089, 1, 3.156, 0.089, 3.244, 0.157, 3.333, 0.157, 1, 3.433, 0.157, 3.533, -0.249, 3.633, -0.249, 1, 3.756, -0.249, 3.878, 0.365, 4, 0.365, 1, 4.211, 0.365, 4.422, -0.095, 4.633, -0.095, 1, 4.722, -0.095, 4.811, 0.247, 4.9, 0.247, 1, 4.978, 0.247, 5.056, -0.129, 5.133, -0.129, 1, 5.189, -0.129, 5.244, -0.076, 5.3, -0.076, 1, 5.433, -0.076, 5.567, -0.315, 5.7, -0.315, 1, 5.878, -0.315, 6.056, 0.152, 6.233, 0.152, 1, 6.367, 0.152, 6.5, -0.007, 6.633, -0.007, 1, 6.922, -0.007, 7.211, 0.089, 7.5, 0.089, 1, 7.511, 0.089, 7.522, 0.088, 7.533, 0.088, 1, 7.556, 0.088, 7.578, 0.105, 7.6, 0.105, 1, 7.7, 0.105, 7.8, -0.035, 7.9, -0.035, 0, 8.1, -0.035]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.256, 0, 0.378, 0.299, 0.5, 0.299, 1, 0.656, 0.299, 0.811, -0.507, 0.967, -0.507, 1, 1.089, -0.507, 1.211, 0.34, 1.333, 0.34, 1, 1.444, 0.34, 1.556, -0.937, 1.667, -0.937, 1, 1.811, -0.937, 1.956, 0.986, 2.1, 0.986, 1, 2.233, 0.986, 2.367, -0.636, 2.5, -0.636, 1, 2.633, -0.636, 2.767, 0.381, 2.9, 0.381, 1, 2.989, 0.381, 3.078, 0.14, 3.167, 0.14, 1, 3.244, 0.14, 3.322, 0.304, 3.4, 0.304, 1, 3.522, 0.304, 3.644, -0.337, 3.767, -0.337, 1, 3.889, -0.337, 4.011, -0.001, 4.133, -0.001, 1, 4.178, -0.001, 4.222, -0.018, 4.267, -0.018, 1, 4.378, -0.018, 4.489, 0.111, 4.6, 0.111, 1, 4.789, 0.111, 4.978, -0.541, 5.167, -0.541, 1, 5.3, -0.541, 5.433, 0.644, 5.567, 0.644, 1, 5.7, 0.644, 5.833, -0.368, 5.967, -0.368, 1, 6.1, -0.368, 6.233, 0.164, 6.367, 0.164, 1, 6.467, 0.164, 6.567, -0.006, 6.667, -0.006, 1, 6.756, -0.006, 6.844, 0.144, 6.933, 0.144, 1, 7.078, 0.144, 7.222, -0.185, 7.367, -0.185, 1, 7.511, -0.185, 7.656, 0.142, 7.8, 0.142, 1, 7.833, 0.142, 7.867, 0.089, 7.9, 0.089, 0, 8.1, 0.089]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.311, 0, 0.589, -2.491, 0.867, -2.491, 1, 0.967, -2.491, 1.067, -2.202, 1.167, -2.202, 1, 1.2, -2.202, 1.233, -2.22, 1.267, -2.22, 1, 1.522, -2.22, 1.778, 2.92, 2.033, 2.92, 1, 2.144, 2.92, 2.256, 2.404, 2.367, 2.404, 1, 2.422, 2.404, 2.478, 2.456, 2.533, 2.456, 1, 2.989, 2.456, 3.444, -2.562, 3.9, -2.562, 1, 4.011, -2.562, 4.122, -1.867, 4.233, -1.867, 1, 4.333, -1.867, 4.433, -2.049, 4.533, -2.049, 1, 4.567, -2.049, 4.6, -2.036, 4.633, -2.036, 1, 4.689, -2.036, 4.744, -2.393, 4.8, -2.393, 1, 5, -2.393, 5.2, 2.124, 5.4, 2.124, 1, 5.522, 2.124, 5.644, 1.721, 5.767, 1.721, 1, 5.989, 1.721, 6.211, 2.305, 6.433, 2.305, 1, 6.7, 2.305, 6.967, 0.573, 7.233, 0.573, 1, 7.411, 0.573, 7.589, 0.807, 7.767, 0.807, 1, 7.811, 0.807, 7.856, 0.772, 7.9, 0.772, 0, 8.1, 0.772]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, 0.387, 0.4, 0.387, 1, 0.6, 0.387, 0.8, -0.229, 1, -0.229, 1, 1.089, -0.229, 1.178, 0.087, 1.267, 0.087, 1, 1.389, 0.087, 1.511, -0.995, 1.633, -0.995, 1, 1.811, -0.995, 1.989, 0.426, 2.167, 0.426, 1, 2.267, 0.426, 2.367, -0.182, 2.467, -0.182, 1, 2.578, -0.182, 2.689, 0.165, 2.8, 0.165, 1, 2.833, 0.165, 2.867, 0.145, 2.9, 0.145, 1, 3.033, 0.145, 3.167, 0.396, 3.3, 0.396, 1, 3.367, 0.396, 3.433, 0.27, 3.5, 0.27, 1, 3.578, 0.27, 3.656, 0.52, 3.733, 0.52, 1, 3.844, 0.52, 3.956, -0.566, 4.067, -0.566, 1, 4.167, -0.566, 4.267, 0.267, 4.367, 0.267, 1, 4.456, 0.267, 4.544, -0.077, 4.633, -0.077, 1, 4.678, -0.077, 4.722, 0.178, 4.767, 0.178, 1, 4.867, 0.178, 4.967, -1.445, 5.067, -1.445, 1, 5.189, -1.445, 5.311, 0.514, 5.433, 0.514, 1, 5.578, 0.514, 5.722, -0.197, 5.867, -0.197, 1, 5.978, -0.197, 6.089, -0.05, 6.2, -0.05, 1, 6.233, -0.05, 6.267, -0.065, 6.3, -0.065, 1, 6.467, -0.065, 6.633, 0.322, 6.8, 0.322, 1, 6.978, 0.322, 7.156, -0.132, 7.333, -0.132, 1, 7.511, -0.132, 7.689, 0.053, 7.867, 0.053, 1, 7.878, 0.053, 7.889, 0.033, 7.9, 0.033, 0, 8.1, 0.033]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.2, 0, 0.367, 0.768, 0.533, 0.768, 1, 0.733, 0.768, 0.933, -0.408, 1.133, -0.408, 1, 1.211, -0.408, 1.289, 0.004, 1.367, 0.004, 1, 1.5, 0.004, 1.633, -1.96, 1.767, -1.96, 1, 1.933, -1.96, 2.1, 0.909, 2.267, 0.909, 1, 2.378, 0.909, 2.489, -0.376, 2.6, -0.376, 1, 2.889, -0.376, 3.178, 0.844, 3.467, 0.844, 1, 3.511, 0.844, 3.556, 0.807, 3.6, 0.807, 1, 3.667, 0.807, 3.733, 0.966, 3.8, 0.966, 1, 3.933, 0.966, 4.067, -0.982, 4.2, -0.982, 1, 4.311, -0.982, 4.422, 0.522, 4.533, 0.522, 1, 4.756, 0.522, 4.978, -2.386, 5.2, -2.386, 1, 5.322, -2.386, 5.444, 1.194, 5.567, 1.194, 1, 5.711, 1.194, 5.856, -0.565, 6, -0.565, 1, 6.3, -0.565, 6.6, 0.662, 6.9, 0.662, 1, 7.078, 0.662, 7.256, -0.333, 7.433, -0.333, 1, 7.589, -0.333, 7.744, 0.119, 7.9, 0.119, 0, 8.1, 0.119]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -0.004, 0.167, -0.004, 1, 0.333, -0.004, 0.5, 0.677, 0.667, 0.677, 1, 0.867, 0.677, 1.067, -0.442, 1.267, -0.442, 1, 1.356, -0.442, 1.444, 0.147, 1.533, 0.147, 1, 1.656, 0.147, 1.778, -1.845, 1.9, -1.845, 1, 2.056, -1.845, 2.211, 1.13, 2.367, 1.13, 1, 2.5, 1.13, 2.633, -0.559, 2.767, -0.559, 1, 2.889, -0.559, 3.011, 0.458, 3.133, 0.458, 1, 3.2, 0.458, 3.267, 0.401, 3.333, 0.401, 1, 3.4, 0.401, 3.467, 0.491, 3.533, 0.491, 1, 3.589, 0.491, 3.644, 0.457, 3.7, 0.457, 1, 3.789, 0.457, 3.878, 0.855, 3.967, 0.855, 1, 4.089, 0.855, 4.211, -1.095, 4.333, -1.095, 1, 4.456, -1.095, 4.578, 0.684, 4.7, 0.684, 1, 4.911, 0.684, 5.122, -2.433, 5.333, -2.433, 1, 5.456, -2.433, 5.578, 1.622, 5.7, 1.622, 1, 5.844, 1.622, 5.989, -0.746, 6.133, -0.746, 1, 6.256, -0.746, 6.378, 0.033, 6.5, 0.033, 1, 6.544, 0.033, 6.589, 0.02, 6.633, 0.02, 1, 6.778, 0.02, 6.922, 0.635, 7.067, 0.635, 1, 7.222, 0.635, 7.378, -0.367, 7.533, -0.367, 1, 7.656, -0.367, 7.778, 0.019, 7.9, 0.019, 0, 8.1, 0.019]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0.233, 1, 0.011, 0.233, 0.022, 0.233, 0.033, 0.233, 1, 0.044, 0.233, 0.056, 0.239, 0.067, 0.239, 1, 0.156, 0.239, 0.244, -0.006, 0.333, -0.006, 1, 0.478, -0.006, 0.622, 0.811, 0.767, 0.811, 1, 0.956, 0.811, 1.144, -0.548, 1.333, -0.548, 1, 1.433, -0.548, 1.533, 0.11, 1.633, 0.11, 1, 1.756, 0.11, 1.878, -2.179, 2, -2.179, 1, 2.156, -2.179, 2.311, 1.563, 2.467, 1.563, 1, 2.6, 1.563, 2.733, -0.835, 2.867, -0.835, 1, 3, -0.835, 3.133, 0.689, 3.267, 0.689, 1, 3.367, 0.689, 3.467, 0.488, 3.567, 0.488, 1, 3.733, 0.488, 3.9, 0.906, 4.067, 0.906, 1, 4.2, 0.906, 4.333, -1.304, 4.467, -1.304, 1, 4.589, -1.304, 4.711, 0.965, 4.833, 0.965, 1, 5.033, 0.965, 5.233, -2.781, 5.433, -2.781, 1, 5.567, -2.781, 5.7, 2.236, 5.833, 2.236, 1, 5.967, 2.236, 6.1, -1.191, 6.233, -1.191, 1, 6.367, -1.191, 6.5, 0.213, 6.633, 0.213, 1, 6.689, 0.213, 6.744, 0.145, 6.8, 0.145, 1, 6.922, 0.145, 7.044, 0.739, 7.167, 0.739, 1, 7.322, 0.739, 7.478, -0.513, 7.633, -0.513, 1, 7.722, -0.513, 7.811, -0.054, 7.9, -0.054, 0, 8.1, -0.054]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.311, 0, 0.589, -2.491, 0.867, -2.491, 1, 0.967, -2.491, 1.067, -2.202, 1.167, -2.202, 1, 1.2, -2.202, 1.233, -2.22, 1.267, -2.22, 1, 1.522, -2.22, 1.778, 2.92, 2.033, 2.92, 1, 2.144, 2.92, 2.256, 2.404, 2.367, 2.404, 1, 2.422, 2.404, 2.478, 2.456, 2.533, 2.456, 1, 2.989, 2.456, 3.444, -2.562, 3.9, -2.562, 1, 4.011, -2.562, 4.122, -1.867, 4.233, -1.867, 1, 4.333, -1.867, 4.433, -2.049, 4.533, -2.049, 1, 4.567, -2.049, 4.6, -2.036, 4.633, -2.036, 1, 4.689, -2.036, 4.744, -2.393, 4.8, -2.393, 1, 5, -2.393, 5.2, 2.124, 5.4, 2.124, 1, 5.522, 2.124, 5.644, 1.721, 5.767, 1.721, 1, 5.989, 1.721, 6.211, 2.305, 6.433, 2.305, 1, 6.7, 2.305, 6.967, 0.573, 7.233, 0.573, 1, 7.411, 0.573, 7.589, 0.807, 7.767, 0.807, 1, 7.811, 0.807, 7.856, 0.772, 7.9, 0.772, 0, 8.1, 0.772]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, 0.387, 0.4, 0.387, 1, 0.6, 0.387, 0.8, -0.229, 1, -0.229, 1, 1.089, -0.229, 1.178, 0.087, 1.267, 0.087, 1, 1.389, 0.087, 1.511, -0.995, 1.633, -0.995, 1, 1.811, -0.995, 1.989, 0.426, 2.167, 0.426, 1, 2.267, 0.426, 2.367, -0.182, 2.467, -0.182, 1, 2.578, -0.182, 2.689, 0.165, 2.8, 0.165, 1, 2.833, 0.165, 2.867, 0.145, 2.9, 0.145, 1, 3.033, 0.145, 3.167, 0.396, 3.3, 0.396, 1, 3.367, 0.396, 3.433, 0.27, 3.5, 0.27, 1, 3.578, 0.27, 3.656, 0.52, 3.733, 0.52, 1, 3.844, 0.52, 3.956, -0.566, 4.067, -0.566, 1, 4.167, -0.566, 4.267, 0.267, 4.367, 0.267, 1, 4.456, 0.267, 4.544, -0.077, 4.633, -0.077, 1, 4.678, -0.077, 4.722, 0.178, 4.767, 0.178, 1, 4.867, 0.178, 4.967, -1.445, 5.067, -1.445, 1, 5.189, -1.445, 5.311, 0.514, 5.433, 0.514, 1, 5.578, 0.514, 5.722, -0.197, 5.867, -0.197, 1, 5.978, -0.197, 6.089, -0.05, 6.2, -0.05, 1, 6.233, -0.05, 6.267, -0.065, 6.3, -0.065, 1, 6.467, -0.065, 6.633, 0.322, 6.8, 0.322, 1, 6.978, 0.322, 7.156, -0.132, 7.333, -0.132, 1, 7.511, -0.132, 7.689, 0.053, 7.867, 0.053, 1, 7.878, 0.053, 7.889, 0.033, 7.9, 0.033, 0, 8.1, 0.033]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.2, 0, 0.367, 0.768, 0.533, 0.768, 1, 0.733, 0.768, 0.933, -0.408, 1.133, -0.408, 1, 1.211, -0.408, 1.289, 0.004, 1.367, 0.004, 1, 1.5, 0.004, 1.633, -1.96, 1.767, -1.96, 1, 1.933, -1.96, 2.1, 0.909, 2.267, 0.909, 1, 2.378, 0.909, 2.489, -0.376, 2.6, -0.376, 1, 2.889, -0.376, 3.178, 0.844, 3.467, 0.844, 1, 3.511, 0.844, 3.556, 0.807, 3.6, 0.807, 1, 3.667, 0.807, 3.733, 0.966, 3.8, 0.966, 1, 3.933, 0.966, 4.067, -0.982, 4.2, -0.982, 1, 4.311, -0.982, 4.422, 0.522, 4.533, 0.522, 1, 4.756, 0.522, 4.978, -2.386, 5.2, -2.386, 1, 5.322, -2.386, 5.444, 1.194, 5.567, 1.194, 1, 5.711, 1.194, 5.856, -0.565, 6, -0.565, 1, 6.3, -0.565, 6.6, 0.662, 6.9, 0.662, 1, 7.078, 0.662, 7.256, -0.333, 7.433, -0.333, 1, 7.589, -0.333, 7.744, 0.119, 7.9, 0.119, 0, 8.1, 0.119]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -0.004, 0.167, -0.004, 1, 0.333, -0.004, 0.5, 0.677, 0.667, 0.677, 1, 0.867, 0.677, 1.067, -0.442, 1.267, -0.442, 1, 1.356, -0.442, 1.444, 0.147, 1.533, 0.147, 1, 1.656, 0.147, 1.778, -1.845, 1.9, -1.845, 1, 2.056, -1.845, 2.211, 1.13, 2.367, 1.13, 1, 2.5, 1.13, 2.633, -0.559, 2.767, -0.559, 1, 2.889, -0.559, 3.011, 0.458, 3.133, 0.458, 1, 3.2, 0.458, 3.267, 0.401, 3.333, 0.401, 1, 3.4, 0.401, 3.467, 0.491, 3.533, 0.491, 1, 3.589, 0.491, 3.644, 0.457, 3.7, 0.457, 1, 3.789, 0.457, 3.878, 0.855, 3.967, 0.855, 1, 4.089, 0.855, 4.211, -1.095, 4.333, -1.095, 1, 4.456, -1.095, 4.578, 0.684, 4.7, 0.684, 1, 4.911, 0.684, 5.122, -2.433, 5.333, -2.433, 1, 5.456, -2.433, 5.578, 1.622, 5.7, 1.622, 1, 5.844, 1.622, 5.989, -0.746, 6.133, -0.746, 1, 6.256, -0.746, 6.378, 0.033, 6.5, 0.033, 1, 6.544, 0.033, 6.589, 0.02, 6.633, 0.02, 1, 6.778, 0.02, 6.922, 0.635, 7.067, 0.635, 1, 7.222, 0.635, 7.378, -0.367, 7.533, -0.367, 1, 7.656, -0.367, 7.778, 0.019, 7.9, 0.019, 0, 8.1, 0.019]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0.233, 1, 0.011, 0.233, 0.022, 0.233, 0.033, 0.233, 1, 0.044, 0.233, 0.056, 0.239, 0.067, 0.239, 1, 0.156, 0.239, 0.244, -0.006, 0.333, -0.006, 1, 0.478, -0.006, 0.622, 0.811, 0.767, 0.811, 1, 0.956, 0.811, 1.144, -0.548, 1.333, -0.548, 1, 1.433, -0.548, 1.533, 0.11, 1.633, 0.11, 1, 1.756, 0.11, 1.878, -2.179, 2, -2.179, 1, 2.156, -2.179, 2.311, 1.563, 2.467, 1.563, 1, 2.6, 1.563, 2.733, -0.835, 2.867, -0.835, 1, 3, -0.835, 3.133, 0.689, 3.267, 0.689, 1, 3.367, 0.689, 3.467, 0.488, 3.567, 0.488, 1, 3.733, 0.488, 3.9, 0.906, 4.067, 0.906, 1, 4.2, 0.906, 4.333, -1.304, 4.467, -1.304, 1, 4.589, -1.304, 4.711, 0.965, 4.833, 0.965, 1, 5.033, 0.965, 5.233, -2.781, 5.433, -2.781, 1, 5.567, -2.781, 5.7, 2.236, 5.833, 2.236, 1, 5.967, 2.236, 6.1, -1.191, 6.233, -1.191, 1, 6.367, -1.191, 6.5, 0.213, 6.633, 0.213, 1, 6.689, 0.213, 6.744, 0.145, 6.8, 0.145, 1, 6.922, 0.145, 7.044, 0.739, 7.167, 0.739, 1, 7.322, 0.739, 7.478, -0.513, 7.633, -0.513, 1, 7.722, -0.513, 7.811, -0.054, 7.9, -0.054, 0, 8.1, -0.054]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 2.633, 0, 5.267, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, 0, 1, 2.633, 0, 5.267, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, 0, 1, 2.633, 0, 5.267, 0, 7.9, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 0, 0, 8.1, 0]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 8.1, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 8.1, 1]}]}