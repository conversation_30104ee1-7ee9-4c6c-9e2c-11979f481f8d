{"Version": 3, "Meta": {"Duration": 4.07, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 105, "TotalSegmentCount": 705, "TotalPointCount": 2098, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 14.791, 0.5, 16, 1, 0.667, 17.209, 0.833, 17, 1, 17, 1, 1.267, 17, 1.533, 4.814, 1.8, -2, 1, 2.2, -12.221, 2.6, -14, 3, -14, 0, 4.067, -14]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.167, -1, 0.333, -2.717, 0.5, -5, 1, 0.667, -7.283, 0.833, -8, 1, -8, 1, 1.267, -8, 1.533, 15, 1.8, 15, 1, 2.2, 15, 2.6, -18, 3, -18, 0, 4.067, -18]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.333, 0, 0.667, -9, 1, -9, 1, 1.456, -9, 1.911, -7.516, 2.367, 0, 1, 2.578, 3.483, 2.789, 13, 3, 13, 0, 4.067, 13]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 5, 0.5, 5, 1, 0.956, 5, 1.411, 5, 1.867, 5, 1, 2.256, 5, 2.644, -5, 3.033, -5, 0, 4.067, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.167, 0, 0.333, -3, 0.5, -3, 1, 0.667, -3, 0.833, -3, 1, -3, 1, 1.267, -3, 1.533, 0, 1.8, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 0.389, 0, 0.411, 30, 0.433, 30, 1, 0.456, 30, 0.478, 30, 0.5, 30, 1, 0.533, 30, 0.567, -30, 0.6, -30, 1, 0.633, -30, 0.667, -30, 0.7, -30, 1, 0.733, -30, 0.767, 30, 0.8, 30, 1, 0.811, 30, 0.822, 30, 0.833, 30, 1, 0.911, 30, 0.989, -12.111, 1.067, -12.111, 1, 1.144, -12.111, 1.222, 4.71, 1.3, 4.71, 1, 1.378, 4.71, 1.456, -1.846, 1.533, -1.846, 1, 1.6, -1.846, 1.667, 0.737, 1.733, 0.737, 1, 1.811, 0.737, 1.889, -0.296, 1.967, -0.296, 1, 2.056, -0.296, 2.144, 30, 2.233, 30, 1, 2.256, 30, 2.278, 30, 2.3, 30, 1, 2.333, 30, 2.367, -30, 2.4, -30, 1, 2.433, -30, 2.467, -30, 2.5, -30, 1, 2.533, -30, 2.567, 30, 2.6, 30, 1, 2.611, 30, 2.622, 30, 2.633, 30, 1, 2.711, 30, 2.789, -12.111, 2.867, -12.111, 1, 2.944, -12.111, 3.022, 4.71, 3.1, 4.71, 1, 3.178, 4.71, 3.256, -1.846, 3.333, -1.846, 1, 3.4, -1.846, 3.467, 0.737, 3.533, 0.737, 1, 3.611, 0.737, 3.689, -0.296, 3.767, -0.296, 1, 3.844, -0.296, 3.922, 0.118, 4, 0.118, 1, 4.022, 0.118, 4.044, 0.105, 4.067, 0.085]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.389, 1, 0.444, 0, 0.5, 0, 1, 0.556, 0, 0.611, 1, 0.667, 1, 1, 1.156, 1, 1.644, 1, 2.133, 1, 1, 2.189, 1, 2.244, 0, 2.3, 0, 1, 2.356, 0, 2.411, 1, 2.467, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.389, 1, 0.444, 0, 0.5, 0, 1, 0.556, 0, 0.611, 1, 0.667, 1, 1, 1.156, 1, 1.644, 1, 2.133, 1, 1, 2.189, 1, 2.244, 0, 2.3, 0, 1, 2.356, 0, 2.411, 1, 2.467, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.6, 1, 0.244, -0.6, 0.489, 0.1, 0.733, 0.1, 1, 0.822, 0.1, 0.911, -0.2, 1, -0.2, 1, 1.011, -0.2, 1.022, 0, 1.033, 0, 1, 1.1, 0, 1.167, -0.5, 1.233, -0.5, 1, 1.311, -0.5, 1.389, 0.4, 1.467, 0.4, 1, 1.544, 0.4, 1.622, -0.5, 1.7, -0.5, 1, 1.778, -0.5, 1.856, 0.4, 1.933, 0.4, 1, 2.022, 0.4, 2.111, 0.4, 2.2, 0.4, 1, 2.367, 0.4, 2.533, 0.4, 2.7, 0.4, 1, 2.811, 0.4, 2.922, 1, 3.033, 1, 0, 4.067, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.244, 0, 0.489, 1, 0.733, 1, 1, 0.822, 1, 0.911, 0, 1, 0, 1, 1.011, 0, 1.022, 0.387, 1.033, 0.4, 1, 1.1, 0.477, 1.167, 0.501, 1.233, 0.6, 1, 1.311, 0.716, 1.389, 0.9, 1.467, 0.9, 1, 1.544, 0.9, 1.622, 0.511, 1.7, 0.4, 1, 1.778, 0.289, 1.856, 0.3, 1.933, 0.3, 1, 2.022, 0.3, 2.111, 1, 2.2, 1, 1, 2.367, 1, 2.533, 1, 2.7, 1, 1, 2.811, 1, 2.922, 0, 3.033, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.167, 0, 0.333, -7, 0.5, -7, 1, 0.667, -7, 0.833, -7, 1, -7, 1, 1.144, -7, 1.289, -7, 1.433, -7, 1, 1.722, -7, 2.011, 0, 2.3, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -2, 1, 0.167, -2, 0.333, -13, 0.5, -13, 1, 0.667, -13, 0.833, -13, 1, -13, 1, 1.144, -13, 1.289, -13, 1.433, -13, 1, 1.722, -13, 2.011, -9, 2.3, -9, 0, 4.067, -9]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, -1, 0, 2.333, -1, 1, 2.422, -1, 2.511, 10, 2.6, 10, 1, 2.7, 10, 2.8, -10, 2.9, -10, 1, 3.011, -10, 3.122, 2, 3.233, 2, 1, 3.333, 2, 3.433, -6, 3.533, -6, 1, 3.633, -6, 3.733, -1, 3.833, -1, 0, 4.067, -1]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, -30, 1, 0.211, -30, 0.422, 30, 0.633, 30, 0, 4.067, 30]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, -30, 0, 4.067, -30]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 30, 0, 4.067, 30]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, -15, 0, 0.667, -15, 1, 0.822, -15, 0.978, 7, 1.133, 7, 1, 1.267, 7, 1.4, -15, 1.533, -15, 1, 1.689, -15, 1.844, 7, 2, 7, 1, 2.2, 7, 2.4, -15, 2.6, -15, 1, 2.756, -15, 2.911, 7, 3.067, 7, 1, 3.211, 7, 3.356, -15, 3.5, -15, 1, 3.656, -15, 3.811, 7, 3.967, 7, 0, 4.067, 7]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -0.961, 0.3, -0.961, 1, 0.411, -0.961, 0.522, 0.747, 0.633, 0.747, 1, 0.767, 0.747, 0.9, -0.169, 1.033, -0.169, 1, 1.156, -0.169, 1.278, 0.423, 1.4, 0.423, 1, 1.833, 0.423, 2.267, -0.306, 2.7, -0.306, 1, 2.833, -0.306, 2.967, 0.152, 3.1, 0.152, 1, 3.211, 0.152, 3.322, -0.04, 3.433, -0.04, 1, 3.544, -0.04, 3.656, 0.011, 3.767, 0.011, 1, 3.867, 0.011, 3.967, -0.003, 4.067, -0.003]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -0.961, 0.3, -0.961, 1, 0.411, -0.961, 0.522, 0.747, 0.633, 0.747, 1, 0.767, 0.747, 0.9, -0.169, 1.033, -0.169, 1, 1.156, -0.169, 1.278, 0.423, 1.4, 0.423, 1, 1.833, 0.423, 2.267, -0.306, 2.7, -0.306, 1, 2.833, -0.306, 2.967, 0.152, 3.1, 0.152, 1, 3.211, 0.152, 3.322, -0.04, 3.433, -0.04, 1, 3.544, -0.04, 3.656, 0.011, 3.767, 0.011, 1, 3.867, 0.011, 3.967, -0.003, 4.067, -0.003]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -4.065, 0.267, -4.065, 1, 0.367, -4.065, 0.467, 4.527, 0.567, 4.527, 1, 0.667, 4.527, 0.767, -2.017, 0.867, -2.017, 1, 0.956, -2.017, 1.044, 0.88, 1.133, 0.88, 1, 1.233, 0.88, 1.333, -0.396, 1.433, -0.396, 1, 1.522, -0.396, 1.611, 0.172, 1.7, 0.172, 1, 1.767, 0.172, 1.833, -0.022, 1.9, -0.022, 1, 1.989, -0.022, 2.078, 2.157, 2.167, 2.157, 1, 2.3, 2.157, 2.433, 0.443, 2.567, 0.443, 1, 2.578, 0.443, 2.589, 0.449, 2.6, 0.449, 1, 2.767, 0.449, 2.933, -1.451, 3.1, -1.451, 1, 3.189, -1.451, 3.278, 0.643, 3.367, 0.643, 1, 3.467, 0.643, 3.567, -0.285, 3.667, -0.285, 1, 3.756, -0.285, 3.844, 0.126, 3.933, 0.126, 1, 3.978, 0.126, 4.022, 0.09, 4.067, 0.05]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, 1.172, 0.167, 1.172, 1, 0.256, 1.172, 0.344, -2.45, 0.433, -2.45, 1, 0.522, -2.45, 0.611, 3.543, 0.7, 3.543, 1, 0.8, 3.543, 0.9, -3.019, 1, -3.019, 1, 1.089, -3.019, 1.178, 2.004, 1.267, 2.004, 1, 1.356, 2.004, 1.444, -1.163, 1.533, -1.163, 1, 1.633, -1.163, 1.733, 0.654, 1.833, 0.654, 1, 1.911, 0.654, 1.989, -0.804, 2.067, -0.804, 1, 2.156, -0.804, 2.244, 0.886, 2.333, 0.886, 1, 2.422, 0.886, 2.511, -0.58, 2.6, -0.58, 1, 2.689, -0.58, 2.778, 0.421, 2.867, 0.421, 1, 2.989, 0.421, 3.111, -0.783, 3.233, -0.783, 1, 3.322, -0.783, 3.411, 0.819, 3.5, 0.819, 1, 3.589, 0.819, 3.678, -0.573, 3.767, -0.573, 1, 3.867, -0.573, 3.967, 0.349, 4.067, 0.349]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, 1.759, 0.167, 1.759, 1, 0.256, 1.759, 0.344, -3.674, 0.433, -3.674, 1, 0.522, -3.674, 0.611, 5.314, 0.7, 5.314, 1, 0.8, 5.314, 0.9, -4.529, 1, -4.529, 1, 1.089, -4.529, 1.178, 3.006, 1.267, 3.006, 1, 1.356, 3.006, 1.444, -1.744, 1.533, -1.744, 1, 1.633, -1.744, 1.733, 0.981, 1.833, 0.981, 1, 1.911, 0.981, 1.989, -1.205, 2.067, -1.205, 1, 2.156, -1.205, 2.244, 1.33, 2.333, 1.33, 1, 2.422, 1.33, 2.511, -0.87, 2.6, -0.87, 1, 2.689, -0.87, 2.778, 0.632, 2.867, 0.632, 1, 2.989, 0.632, 3.111, -1.174, 3.233, -1.174, 1, 3.322, -1.174, 3.411, 1.229, 3.5, 1.229, 1, 3.589, 1.229, 3.678, -0.86, 3.767, -0.86, 1, 3.867, -0.86, 3.967, 0.523, 4.067, 0.523]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -2.28, 0.267, -2.28, 1, 0.378, -2.28, 0.489, 2.007, 0.6, 2.007, 1, 0.711, 2.007, 0.822, -1.596, 0.933, -1.596, 1, 1.044, -1.596, 1.156, 0.459, 1.267, 0.459, 1, 1.378, 0.459, 1.489, -0.532, 1.6, -0.532, 1, 1.822, -0.532, 2.044, 0.296, 2.267, 0.296, 1, 2.322, 0.296, 2.378, 0.234, 2.433, 0.234, 1, 2.589, 0.234, 2.744, 0.723, 2.9, 0.723, 1, 2.967, 0.723, 3.033, 0.537, 3.1, 0.537, 1, 3.211, 0.537, 3.322, 0.947, 3.433, 0.947, 1, 3.533, 0.947, 3.633, 0.757, 3.733, 0.757, 1, 3.844, 0.757, 3.956, 0.844, 4.067, 0.844]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, -0.057, 1, 0.011, -0.061, 0.022, -0.066, 0.033, -0.066, 1, 0.089, -0.066, 0.144, 0.978, 0.2, 0.978, 1, 0.289, 0.978, 0.378, -2.774, 0.467, -2.774, 1, 0.567, -2.774, 0.667, 4.081, 0.767, 4.081, 1, 0.878, 4.081, 0.989, -3.429, 1.1, -3.429, 1, 1.211, -3.429, 1.322, 2.614, 1.433, 2.614, 1, 1.544, 2.614, 1.656, -1.528, 1.767, -1.528, 1, 1.878, -1.528, 1.989, 0.596, 2.1, 0.596, 1, 2.267, 0.596, 2.433, -0.204, 2.6, -0.204, 1, 2.733, -0.204, 2.867, 0.222, 3, 0.222, 1, 3.1, 0.222, 3.2, -0.385, 3.3, -0.385, 1, 3.4, -0.385, 3.5, 0.35, 3.6, 0.35, 1, 3.711, 0.35, 3.822, -0.242, 3.933, -0.242, 1, 3.978, -0.242, 4.022, -0.165, 4.067, -0.08]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -3.166, 0.267, -3.166, 1, 0.378, -3.166, 0.489, 2.788, 0.6, 2.788, 1, 0.711, 2.788, 0.822, -2.216, 0.933, -2.216, 1, 1.044, -2.216, 1.156, 0.637, 1.267, 0.637, 1, 1.378, 0.637, 1.489, -0.738, 1.6, -0.738, 1, 1.822, -0.738, 2.044, 0.412, 2.267, 0.412, 1, 2.322, 0.412, 2.378, 0.325, 2.433, 0.325, 1, 2.589, 0.325, 2.744, 1.005, 2.9, 1.005, 1, 2.967, 1.005, 3.033, 0.746, 3.1, 0.746, 1, 3.211, 0.746, 3.322, 1.315, 3.433, 1.315, 1, 3.533, 1.315, 3.633, 1.052, 3.733, 1.052, 1, 3.844, 1.052, 3.956, 1.172, 4.067, 1.172]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -3.166, 0.267, -3.166, 1, 0.378, -3.166, 0.489, 2.788, 0.6, 2.788, 1, 0.711, 2.788, 0.822, -2.216, 0.933, -2.216, 1, 1.044, -2.216, 1.156, 0.637, 1.267, 0.637, 1, 1.378, 0.637, 1.489, -0.738, 1.6, -0.738, 1, 1.822, -0.738, 2.044, 0.412, 2.267, 0.412, 1, 2.322, 0.412, 2.378, 0.325, 2.433, 0.325, 1, 2.589, 0.325, 2.744, 1.005, 2.9, 1.005, 1, 2.967, 1.005, 3.033, 0.746, 3.1, 0.746, 1, 3.211, 0.746, 3.322, 1.315, 3.433, 1.315, 1, 3.533, 1.315, 3.633, 1.052, 3.733, 1.052, 1, 3.844, 1.052, 3.956, 1.172, 4.067, 1.172]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -3.166, 0.267, -3.166, 1, 0.378, -3.166, 0.489, 2.788, 0.6, 2.788, 1, 0.711, 2.788, 0.822, -2.216, 0.933, -2.216, 1, 1.044, -2.216, 1.156, 0.637, 1.267, 0.637, 1, 1.378, 0.637, 1.489, -0.738, 1.6, -0.738, 1, 1.822, -0.738, 2.044, 0.412, 2.267, 0.412, 1, 2.322, 0.412, 2.378, 0.325, 2.433, 0.325, 1, 2.589, 0.325, 2.744, 1.005, 2.9, 1.005, 1, 2.967, 1.005, 3.033, 0.746, 3.1, 0.746, 1, 3.211, 0.746, 3.322, 1.315, 3.433, 1.315, 1, 3.533, 1.315, 3.633, 1.052, 3.733, 1.052, 1, 3.844, 1.052, 3.956, 1.172, 4.067, 1.172]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -2.28, 0.267, -2.28, 1, 0.378, -2.28, 0.489, 2.007, 0.6, 2.007, 1, 0.711, 2.007, 0.822, -1.596, 0.933, -1.596, 1, 1.044, -1.596, 1.156, 0.459, 1.267, 0.459, 1, 1.378, 0.459, 1.489, -0.532, 1.6, -0.532, 1, 1.822, -0.532, 2.044, 0.296, 2.267, 0.296, 1, 2.322, 0.296, 2.378, 0.234, 2.433, 0.234, 1, 2.589, 0.234, 2.744, 0.723, 2.9, 0.723, 1, 2.967, 0.723, 3.033, 0.537, 3.1, 0.537, 1, 3.211, 0.537, 3.322, 0.947, 3.433, 0.947, 1, 3.533, 0.947, 3.633, 0.757, 3.733, 0.757, 1, 3.844, 0.757, 3.956, 0.844, 4.067, 0.844]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, -0.057, 1, 0.011, -0.061, 0.022, -0.066, 0.033, -0.066, 1, 0.089, -0.066, 0.144, 0.978, 0.2, 0.978, 1, 0.289, 0.978, 0.378, -2.774, 0.467, -2.774, 1, 0.567, -2.774, 0.667, 4.081, 0.767, 4.081, 1, 0.878, 4.081, 0.989, -3.429, 1.1, -3.429, 1, 1.211, -3.429, 1.322, 2.614, 1.433, 2.614, 1, 1.544, 2.614, 1.656, -1.528, 1.767, -1.528, 1, 1.878, -1.528, 1.989, 0.596, 2.1, 0.596, 1, 2.267, 0.596, 2.433, -0.204, 2.6, -0.204, 1, 2.733, -0.204, 2.867, 0.222, 3, 0.222, 1, 3.1, 0.222, 3.2, -0.385, 3.3, -0.385, 1, 3.4, -0.385, 3.5, 0.35, 3.6, 0.35, 1, 3.711, 0.35, 3.822, -0.242, 3.933, -0.242, 1, 3.978, -0.242, 4.022, -0.165, 4.067, -0.08]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, -3.166, 0.267, -3.166, 1, 0.378, -3.166, 0.489, 2.788, 0.6, 2.788, 1, 0.711, 2.788, 0.822, -2.216, 0.933, -2.216, 1, 1.044, -2.216, 1.156, 0.637, 1.267, 0.637, 1, 1.378, 0.637, 1.489, -0.738, 1.6, -0.738, 1, 1.822, -0.738, 2.044, 0.412, 2.267, 0.412, 1, 2.322, 0.412, 2.378, 0.325, 2.433, 0.325, 1, 2.589, 0.325, 2.744, 1.005, 2.9, 1.005, 1, 2.967, 1.005, 3.033, 0.746, 3.1, 0.746, 1, 3.211, 0.746, 3.322, 1.315, 3.433, 1.315, 1, 3.533, 1.315, 3.633, 1.052, 3.733, 1.052, 1, 3.844, 1.052, 3.956, 1.172, 4.067, 1.172]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, -6.28, 0.4, -6.28, 1, 0.556, -6.28, 0.711, 1.97, 0.867, 1.97, 1, 0.989, 1.97, 1.111, 0.058, 1.233, 0.058, 1, 1.389, 0.058, 1.544, 1.233, 1.7, 1.233, 1, 1.8, 1.233, 1.9, 0.644, 2, 0.644, 1, 2.2, 0.644, 2.4, 2.152, 2.6, 2.152, 1, 2.822, 2.152, 3.044, 0.401, 3.267, 0.401, 1, 3.456, 0.401, 3.644, 1.35, 3.833, 1.35, 1, 3.911, 1.35, 3.989, 1.308, 4.067, 1.256]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 4.14, 0.367, 4.14, 1, 0.489, 4.14, 0.611, -0.619, 0.733, -0.619, 1, 0.789, -0.619, 0.844, -0.447, 0.9, -0.447, 1, 1.033, -0.447, 1.167, -1.483, 1.3, -1.483, 1, 1.4, -1.483, 1.5, -1.035, 1.6, -1.035, 1, 1.678, -1.035, 1.756, -1.179, 1.833, -1.179, 1, 1.867, -1.179, 1.9, -1.102, 1.933, -1.102, 1, 2.067, -1.102, 2.2, -1.377, 2.333, -1.377, 1, 2.344, -1.377, 2.356, -1.375, 2.367, -1.375, 1, 2.378, -1.375, 2.389, -1.377, 2.4, -1.377, 1, 2.411, -1.377, 2.422, -1.371, 2.433, -1.371, 1, 2.444, -1.371, 2.456, -1.371, 2.467, -1.371, 1, 2.711, -1.371, 2.956, 0.444, 3.2, 0.444, 1, 3.478, 0.444, 3.756, -0.107, 4.033, -0.107, 1, 4.044, -0.107, 4.056, -0.106, 4.067, -0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.049, 1, 0.111, 0.095, 0.222, 0.889, 0.333, 0.889, 1, 0.444, 0.889, 0.556, -2.015, 0.667, -2.015, 1, 0.789, -2.015, 0.911, 2.451, 1.033, 2.451, 1, 1.156, 2.451, 1.278, -2.048, 1.4, -2.048, 1, 1.522, -2.048, 1.644, 1.361, 1.767, 1.361, 1, 1.889, 1.361, 2.011, -0.809, 2.133, -0.809, 1, 2.267, -0.809, 2.4, 0.417, 2.533, 0.417, 1, 2.656, 0.417, 2.778, -0.232, 2.9, -0.232, 1, 3, -0.232, 3.1, 0.294, 3.2, 0.294, 1, 3.311, 0.294, 3.422, -0.386, 3.533, -0.386, 1, 3.656, -0.386, 3.778, 0.346, 3.9, 0.346, 1, 3.956, 0.346, 4.011, 0.23, 4.067, 0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, -0.507, 1, 0.144, -0.507, 0.289, 1.238, 0.433, 1.238, 1, 0.556, 1.238, 0.678, -2.992, 0.8, -2.992, 1, 0.922, -2.992, 1.044, 4.294, 1.167, 4.294, 1, 1.278, 4.294, 1.389, -4.388, 1.5, -4.388, 1, 1.622, -4.388, 1.744, 3.63, 1.867, 3.63, 1, 1.989, 3.63, 2.111, -2.732, 2.233, -2.732, 1, 2.356, -2.732, 2.478, 1.671, 2.6, 1.671, 1, 2.722, 1.671, 2.844, -1.002, 2.967, -1.002, 1, 3.089, -1.002, 3.211, 0.795, 3.333, 0.795, 1, 3.444, 0.795, 3.556, -0.832, 3.667, -0.832, 1, 3.778, -0.832, 3.889, 0.8, 4, 0.8, 1, 4.022, 0.8, 4.044, 0.753, 4.067, 0.676]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, -6.28, 0.4, -6.28, 1, 0.556, -6.28, 0.711, 1.97, 0.867, 1.97, 1, 0.989, 1.97, 1.111, 0.058, 1.233, 0.058, 1, 1.389, 0.058, 1.544, 1.233, 1.7, 1.233, 1, 1.8, 1.233, 1.9, 0.644, 2, 0.644, 1, 2.2, 0.644, 2.4, 2.152, 2.6, 2.152, 1, 2.822, 2.152, 3.044, 0.401, 3.267, 0.401, 1, 3.456, 0.401, 3.644, 1.35, 3.833, 1.35, 1, 3.911, 1.35, 3.989, 1.308, 4.067, 1.256]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 4.14, 0.367, 4.14, 1, 0.489, 4.14, 0.611, -0.619, 0.733, -0.619, 1, 0.789, -0.619, 0.844, -0.447, 0.9, -0.447, 1, 1.033, -0.447, 1.167, -1.483, 1.3, -1.483, 1, 1.4, -1.483, 1.5, -1.035, 1.6, -1.035, 1, 1.678, -1.035, 1.756, -1.179, 1.833, -1.179, 1, 1.867, -1.179, 1.9, -1.102, 1.933, -1.102, 1, 2.067, -1.102, 2.2, -1.377, 2.333, -1.377, 1, 2.344, -1.377, 2.356, -1.375, 2.367, -1.375, 1, 2.378, -1.375, 2.389, -1.377, 2.4, -1.377, 1, 2.411, -1.377, 2.422, -1.371, 2.433, -1.371, 1, 2.444, -1.371, 2.456, -1.371, 2.467, -1.371, 1, 2.711, -1.371, 2.956, 0.444, 3.2, 0.444, 1, 3.478, 0.444, 3.756, -0.107, 4.033, -0.107, 1, 4.044, -0.107, 4.056, -0.106, 4.067, -0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.049, 1, 0.111, 0.095, 0.222, 0.889, 0.333, 0.889, 1, 0.444, 0.889, 0.556, -2.015, 0.667, -2.015, 1, 0.789, -2.015, 0.911, 2.451, 1.033, 2.451, 1, 1.156, 2.451, 1.278, -2.048, 1.4, -2.048, 1, 1.522, -2.048, 1.644, 1.361, 1.767, 1.361, 1, 1.889, 1.361, 2.011, -0.809, 2.133, -0.809, 1, 2.267, -0.809, 2.4, 0.417, 2.533, 0.417, 1, 2.656, 0.417, 2.778, -0.232, 2.9, -0.232, 1, 3, -0.232, 3.1, 0.294, 3.2, 0.294, 1, 3.311, 0.294, 3.422, -0.386, 3.533, -0.386, 1, 3.656, -0.386, 3.778, 0.346, 3.9, 0.346, 1, 3.956, 0.346, 4.011, 0.23, 4.067, 0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, -6.28, 0.4, -6.28, 1, 0.556, -6.28, 0.711, 1.97, 0.867, 1.97, 1, 0.989, 1.97, 1.111, 0.058, 1.233, 0.058, 1, 1.389, 0.058, 1.544, 1.233, 1.7, 1.233, 1, 1.8, 1.233, 1.9, 0.644, 2, 0.644, 1, 2.2, 0.644, 2.4, 2.152, 2.6, 2.152, 1, 2.822, 2.152, 3.044, 0.401, 3.267, 0.401, 1, 3.456, 0.401, 3.644, 1.35, 3.833, 1.35, 1, 3.911, 1.35, 3.989, 1.308, 4.067, 1.256]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 4.14, 0.367, 4.14, 1, 0.489, 4.14, 0.611, -0.619, 0.733, -0.619, 1, 0.789, -0.619, 0.844, -0.447, 0.9, -0.447, 1, 1.033, -0.447, 1.167, -1.483, 1.3, -1.483, 1, 1.4, -1.483, 1.5, -1.035, 1.6, -1.035, 1, 1.678, -1.035, 1.756, -1.179, 1.833, -1.179, 1, 1.867, -1.179, 1.9, -1.102, 1.933, -1.102, 1, 2.067, -1.102, 2.2, -1.377, 2.333, -1.377, 1, 2.344, -1.377, 2.356, -1.375, 2.367, -1.375, 1, 2.378, -1.375, 2.389, -1.377, 2.4, -1.377, 1, 2.411, -1.377, 2.422, -1.371, 2.433, -1.371, 1, 2.444, -1.371, 2.456, -1.371, 2.467, -1.371, 1, 2.711, -1.371, 2.956, 0.444, 3.2, 0.444, 1, 3.478, 0.444, 3.756, -0.107, 4.033, -0.107, 1, 4.044, -0.107, 4.056, -0.106, 4.067, -0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, -0.049, 1, 0.111, 0.095, 0.222, 0.889, 0.333, 0.889, 1, 0.444, 0.889, 0.556, -2.015, 0.667, -2.015, 1, 0.789, -2.015, 0.911, 2.451, 1.033, 2.451, 1, 1.156, 2.451, 1.278, -2.048, 1.4, -2.048, 1, 1.522, -2.048, 1.644, 1.361, 1.767, 1.361, 1, 1.889, 1.361, 2.011, -0.809, 2.133, -0.809, 1, 2.267, -0.809, 2.4, 0.417, 2.533, 0.417, 1, 2.656, 0.417, 2.778, -0.232, 2.9, -0.232, 1, 3, -0.232, 3.1, 0.294, 3.2, 0.294, 1, 3.311, 0.294, 3.422, -0.386, 3.533, -0.386, 1, 3.656, -0.386, 3.778, 0.346, 3.9, 0.346, 1, 3.956, 0.346, 4.011, 0.23, 4.067, 0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, -0.507, 1, 0.144, -0.507, 0.289, 1.238, 0.433, 1.238, 1, 0.556, 1.238, 0.678, -2.992, 0.8, -2.992, 1, 0.922, -2.992, 1.044, 4.294, 1.167, 4.294, 1, 1.278, 4.294, 1.389, -4.388, 1.5, -4.388, 1, 1.622, -4.388, 1.744, 3.63, 1.867, 3.63, 1, 1.989, 3.63, 2.111, -2.732, 2.233, -2.732, 1, 2.356, -2.732, 2.478, 1.671, 2.6, 1.671, 1, 2.722, 1.671, 2.844, -1.002, 2.967, -1.002, 1, 3.089, -1.002, 3.211, 0.795, 3.333, 0.795, 1, 3.444, 0.795, 3.556, -0.832, 3.667, -0.832, 1, 3.778, -0.832, 3.889, 0.8, 4, 0.8, 1, 4.022, 0.8, 4.044, 0.753, 4.067, 0.676]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, -6.28, 0.4, -6.28, 1, 0.556, -6.28, 0.711, 1.97, 0.867, 1.97, 1, 0.989, 1.97, 1.111, 0.058, 1.233, 0.058, 1, 1.389, 0.058, 1.544, 1.233, 1.7, 1.233, 1, 1.8, 1.233, 1.9, 0.644, 2, 0.644, 1, 2.2, 0.644, 2.4, 2.152, 2.6, 2.152, 1, 2.822, 2.152, 3.044, 0.401, 3.267, 0.401, 1, 3.456, 0.401, 3.644, 1.35, 3.833, 1.35, 1, 3.911, 1.35, 3.989, 1.308, 4.067, 1.256]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 4.14, 0.367, 4.14, 1, 0.489, 4.14, 0.611, -0.619, 0.733, -0.619, 1, 0.789, -0.619, 0.844, -0.447, 0.9, -0.447, 1, 1.033, -0.447, 1.167, -1.483, 1.3, -1.483, 1, 1.4, -1.483, 1.5, -1.035, 1.6, -1.035, 1, 1.678, -1.035, 1.756, -1.179, 1.833, -1.179, 1, 1.867, -1.179, 1.9, -1.102, 1.933, -1.102, 1, 2.067, -1.102, 2.2, -1.377, 2.333, -1.377, 1, 2.344, -1.377, 2.356, -1.375, 2.367, -1.375, 1, 2.378, -1.375, 2.389, -1.377, 2.4, -1.377, 1, 2.411, -1.377, 2.422, -1.371, 2.433, -1.371, 1, 2.444, -1.371, 2.456, -1.371, 2.467, -1.371, 1, 2.711, -1.371, 2.956, 0.444, 3.2, 0.444, 1, 3.478, 0.444, 3.756, -0.107, 4.033, -0.107, 1, 4.044, -0.107, 4.056, -0.106, 4.067, -0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, -0.049, 1, 0.111, 0.095, 0.222, 0.889, 0.333, 0.889, 1, 0.444, 0.889, 0.556, -2.015, 0.667, -2.015, 1, 0.789, -2.015, 0.911, 2.451, 1.033, 2.451, 1, 1.156, 2.451, 1.278, -2.048, 1.4, -2.048, 1, 1.522, -2.048, 1.644, 1.361, 1.767, 1.361, 1, 1.889, 1.361, 2.011, -0.809, 2.133, -0.809, 1, 2.267, -0.809, 2.4, 0.417, 2.533, 0.417, 1, 2.656, 0.417, 2.778, -0.232, 2.9, -0.232, 1, 3, -0.232, 3.1, 0.294, 3.2, 0.294, 1, 3.311, 0.294, 3.422, -0.386, 3.533, -0.386, 1, 3.656, -0.386, 3.778, 0.346, 3.9, 0.346, 1, 3.956, 0.346, 4.011, 0.23, 4.067, 0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, -6.28, 0.4, -6.28, 1, 0.556, -6.28, 0.711, 1.97, 0.867, 1.97, 1, 0.989, 1.97, 1.111, 0.058, 1.233, 0.058, 1, 1.389, 0.058, 1.544, 1.233, 1.7, 1.233, 1, 1.8, 1.233, 1.9, 0.644, 2, 0.644, 1, 2.2, 0.644, 2.4, 2.152, 2.6, 2.152, 1, 2.822, 2.152, 3.044, 0.401, 3.267, 0.401, 1, 3.456, 0.401, 3.644, 1.35, 3.833, 1.35, 1, 3.911, 1.35, 3.989, 1.308, 4.067, 1.256]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 4.14, 0.367, 4.14, 1, 0.489, 4.14, 0.611, -0.619, 0.733, -0.619, 1, 0.789, -0.619, 0.844, -0.447, 0.9, -0.447, 1, 1.033, -0.447, 1.167, -1.483, 1.3, -1.483, 1, 1.4, -1.483, 1.5, -1.035, 1.6, -1.035, 1, 1.678, -1.035, 1.756, -1.179, 1.833, -1.179, 1, 1.867, -1.179, 1.9, -1.102, 1.933, -1.102, 1, 2.067, -1.102, 2.2, -1.377, 2.333, -1.377, 1, 2.344, -1.377, 2.356, -1.375, 2.367, -1.375, 1, 2.378, -1.375, 2.389, -1.377, 2.4, -1.377, 1, 2.411, -1.377, 2.422, -1.371, 2.433, -1.371, 1, 2.444, -1.371, 2.456, -1.371, 2.467, -1.371, 1, 2.711, -1.371, 2.956, 0.444, 3.2, 0.444, 1, 3.478, 0.444, 3.756, -0.107, 4.033, -0.107, 1, 4.044, -0.107, 4.056, -0.106, 4.067, -0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, -0.049, 1, 0.111, 0.095, 0.222, 0.889, 0.333, 0.889, 1, 0.444, 0.889, 0.556, -2.015, 0.667, -2.015, 1, 0.789, -2.015, 0.911, 2.451, 1.033, 2.451, 1, 1.156, 2.451, 1.278, -2.048, 1.4, -2.048, 1, 1.522, -2.048, 1.644, 1.361, 1.767, 1.361, 1, 1.889, 1.361, 2.011, -0.809, 2.133, -0.809, 1, 2.267, -0.809, 2.4, 0.417, 2.533, 0.417, 1, 2.656, 0.417, 2.778, -0.232, 2.9, -0.232, 1, 3, -0.232, 3.1, 0.294, 3.2, 0.294, 1, 3.311, 0.294, 3.422, -0.386, 3.533, -0.386, 1, 3.656, -0.386, 3.778, 0.346, 3.9, 0.346, 1, 3.956, 0.346, 4.011, 0.23, 4.067, 0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.156, 0, 0.278, -6.28, 0.4, -6.28, 1, 0.556, -6.28, 0.711, 1.97, 0.867, 1.97, 1, 0.989, 1.97, 1.111, 0.058, 1.233, 0.058, 1, 1.389, 0.058, 1.544, 1.233, 1.7, 1.233, 1, 1.8, 1.233, 1.9, 0.644, 2, 0.644, 1, 2.2, 0.644, 2.4, 2.152, 2.6, 2.152, 1, 2.822, 2.152, 3.044, 0.401, 3.267, 0.401, 1, 3.456, 0.401, 3.644, 1.35, 3.833, 1.35, 1, 3.911, 1.35, 3.989, 1.308, 4.067, 1.256]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 4.14, 0.367, 4.14, 1, 0.489, 4.14, 0.611, -0.619, 0.733, -0.619, 1, 0.789, -0.619, 0.844, -0.447, 0.9, -0.447, 1, 1.033, -0.447, 1.167, -1.483, 1.3, -1.483, 1, 1.4, -1.483, 1.5, -1.035, 1.6, -1.035, 1, 1.678, -1.035, 1.756, -1.179, 1.833, -1.179, 1, 1.867, -1.179, 1.9, -1.102, 1.933, -1.102, 1, 2.067, -1.102, 2.2, -1.377, 2.333, -1.377, 1, 2.344, -1.377, 2.356, -1.375, 2.367, -1.375, 1, 2.378, -1.375, 2.389, -1.377, 2.4, -1.377, 1, 2.411, -1.377, 2.422, -1.371, 2.433, -1.371, 1, 2.444, -1.371, 2.456, -1.371, 2.467, -1.371, 1, 2.711, -1.371, 2.956, 0.444, 3.2, 0.444, 1, 3.478, 0.444, 3.756, -0.107, 4.033, -0.107, 1, 4.044, -0.107, 4.056, -0.106, 4.067, -0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, -0.049, 1, 0.111, 0.095, 0.222, 0.889, 0.333, 0.889, 1, 0.444, 0.889, 0.556, -2.015, 0.667, -2.015, 1, 0.789, -2.015, 0.911, 2.451, 1.033, 2.451, 1, 1.156, 2.451, 1.278, -2.048, 1.4, -2.048, 1, 1.522, -2.048, 1.644, 1.361, 1.767, 1.361, 1, 1.889, 1.361, 2.011, -0.809, 2.133, -0.809, 1, 2.267, -0.809, 2.4, 0.417, 2.533, 0.417, 1, 2.656, 0.417, 2.778, -0.232, 2.9, -0.232, 1, 3, -0.232, 3.1, 0.294, 3.2, 0.294, 1, 3.311, 0.294, 3.422, -0.386, 3.533, -0.386, 1, 3.656, -0.386, 3.778, 0.346, 3.9, 0.346, 1, 3.956, 0.346, 4.011, 0.23, 4.067, 0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -1.017, 0.3, -1.017, 1, 0.411, -1.017, 0.522, 0.791, 0.633, 0.791, 1, 0.767, 0.791, 0.9, -0.179, 1.033, -0.179, 1, 1.156, -0.179, 1.278, 0.448, 1.4, 0.448, 1, 1.833, 0.448, 2.267, -0.324, 2.7, -0.324, 1, 2.833, -0.324, 2.967, 0.161, 3.1, 0.161, 1, 3.211, 0.161, 3.322, -0.043, 3.433, -0.043, 1, 3.544, -0.043, 3.656, 0.011, 3.767, 0.011, 1, 3.867, 0.011, 3.967, -0.003, 4.067, -0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, 0.03, 1, 0.067, 0.143, 0.133, 0.54, 0.2, 0.54, 1, 0.311, 0.54, 0.422, -1.016, 0.533, -1.016, 1, 0.622, -1.016, 0.711, 0.798, 0.8, 0.798, 1, 0.933, 0.798, 1.067, -0.428, 1.2, -0.428, 1, 1.311, -0.428, 1.422, 0.237, 1.533, 0.237, 1, 1.633, 0.237, 1.733, 0.018, 1.833, 0.018, 1, 1.922, 0.018, 2.011, 0.081, 2.1, 0.081, 1, 2.2, 0.081, 2.3, 0.051, 2.4, 0.051, 1, 2.456, 0.051, 2.511, 0.127, 2.567, 0.127, 1, 2.678, 0.127, 2.789, -0.202, 2.9, -0.202, 1, 3.022, -0.202, 3.144, 0.175, 3.267, 0.175, 1, 3.378, 0.175, 3.489, -0.081, 3.6, -0.081, 1, 3.7, -0.081, 3.8, 0.029, 3.9, 0.029, 1, 3.956, 0.029, 4.011, 0.02, 4.067, 0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.44, 0.3, 0.44, 1, 0.411, 0.44, 0.522, -1.624, 0.633, -1.624, 1, 0.744, -1.624, 0.856, 1.45, 0.967, 1.45, 1, 1.078, 1.45, 1.189, -0.841, 1.3, -0.841, 1, 1.422, -0.841, 1.544, 0.63, 1.667, 0.63, 1, 1.789, 0.63, 1.911, 0.085, 2.033, 0.085, 1, 2.122, 0.085, 2.211, 0.183, 2.3, 0.183, 1, 2.378, 0.183, 2.456, 0.129, 2.533, 0.129, 1, 2.556, 0.129, 2.578, 0.134, 2.6, 0.134, 1, 2.744, 0.134, 2.889, -0.419, 3.033, -0.419, 1, 3.156, -0.419, 3.278, 0.321, 3.4, 0.321, 1, 3.522, 0.321, 3.644, -0.16, 3.767, -0.16, 1, 3.867, -0.16, 3.967, 0.021, 4.067, 0.057]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, 0.011, 1, 0.144, 0.105, 0.289, 0.761, 0.433, 0.761, 1, 0.544, 0.761, 0.656, -1.804, 0.767, -1.804, 1, 0.878, -1.804, 0.989, 1.673, 1.1, 1.673, 1, 1.222, 1.673, 1.344, -1.254, 1.467, -1.254, 1, 1.589, -1.254, 1.711, 0.648, 1.833, 0.648, 1, 1.944, 0.648, 2.056, -0.152, 2.167, -0.152, 1, 2.356, -0.152, 2.544, 0.213, 2.733, 0.213, 1, 2.878, 0.213, 3.022, -0.444, 3.167, -0.444, 1, 3.3, -0.444, 3.433, 0.385, 3.567, 0.385, 1, 3.678, 0.385, 3.789, -0.221, 3.9, -0.221, 1, 3.956, -0.221, 4.011, -0.142, 4.067, -0.062]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0.005, 1, 0.178, 0.088, 0.356, 0.796, 0.533, 0.796, 1, 0.644, 0.796, 0.756, -2.027, 0.867, -2.027, 1, 0.989, -2.027, 1.111, 2.092, 1.233, 2.092, 1, 1.356, 2.092, 1.478, -1.681, 1.6, -1.681, 1, 1.722, -1.681, 1.844, 0.97, 1.967, 0.97, 1, 2.078, 0.97, 2.189, -0.295, 2.3, -0.295, 1, 2.444, -0.295, 2.589, 0.285, 2.733, 0.285, 1, 2.911, 0.285, 3.089, -0.531, 3.267, -0.531, 1, 3.4, -0.531, 3.533, 0.501, 3.667, 0.501, 1, 3.789, 0.501, 3.911, -0.312, 4.033, -0.312, 1, 4.044, -0.312, 4.056, -0.308, 4.067, -0.299]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -1.017, 0.3, -1.017, 1, 0.411, -1.017, 0.522, 0.791, 0.633, 0.791, 1, 0.767, 0.791, 0.9, -0.179, 1.033, -0.179, 1, 1.156, -0.179, 1.278, 0.448, 1.4, 0.448, 1, 1.833, 0.448, 2.267, -0.324, 2.7, -0.324, 1, 2.833, -0.324, 2.967, 0.161, 3.1, 0.161, 1, 3.211, 0.161, 3.322, -0.043, 3.433, -0.043, 1, 3.544, -0.043, 3.656, 0.011, 3.767, 0.011, 1, 3.867, 0.011, 3.967, -0.003, 4.067, -0.003]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, 0.03, 1, 0.067, 0.143, 0.133, 0.54, 0.2, 0.54, 1, 0.311, 0.54, 0.422, -1.016, 0.533, -1.016, 1, 0.622, -1.016, 0.711, 0.798, 0.8, 0.798, 1, 0.933, 0.798, 1.067, -0.428, 1.2, -0.428, 1, 1.311, -0.428, 1.422, 0.237, 1.533, 0.237, 1, 1.633, 0.237, 1.733, 0.018, 1.833, 0.018, 1, 1.922, 0.018, 2.011, 0.081, 2.1, 0.081, 1, 2.2, 0.081, 2.3, 0.051, 2.4, 0.051, 1, 2.456, 0.051, 2.511, 0.127, 2.567, 0.127, 1, 2.678, 0.127, 2.789, -0.202, 2.9, -0.202, 1, 3.022, -0.202, 3.144, 0.175, 3.267, 0.175, 1, 3.378, 0.175, 3.489, -0.081, 3.6, -0.081, 1, 3.7, -0.081, 3.8, 0.029, 3.9, 0.029, 1, 3.956, 0.029, 4.011, 0.02, 4.067, 0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.44, 0.3, 0.44, 1, 0.411, 0.44, 0.522, -1.624, 0.633, -1.624, 1, 0.744, -1.624, 0.856, 1.45, 0.967, 1.45, 1, 1.078, 1.45, 1.189, -0.841, 1.3, -0.841, 1, 1.422, -0.841, 1.544, 0.63, 1.667, 0.63, 1, 1.789, 0.63, 1.911, 0.085, 2.033, 0.085, 1, 2.122, 0.085, 2.211, 0.183, 2.3, 0.183, 1, 2.378, 0.183, 2.456, 0.129, 2.533, 0.129, 1, 2.556, 0.129, 2.578, 0.134, 2.6, 0.134, 1, 2.744, 0.134, 2.889, -0.419, 3.033, -0.419, 1, 3.156, -0.419, 3.278, 0.321, 3.4, 0.321, 1, 3.522, 0.321, 3.644, -0.16, 3.767, -0.16, 1, 3.867, -0.16, 3.967, 0.021, 4.067, 0.057]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, 0.011, 1, 0.144, 0.105, 0.289, 0.761, 0.433, 0.761, 1, 0.544, 0.761, 0.656, -1.804, 0.767, -1.804, 1, 0.878, -1.804, 0.989, 1.673, 1.1, 1.673, 1, 1.222, 1.673, 1.344, -1.254, 1.467, -1.254, 1, 1.589, -1.254, 1.711, 0.648, 1.833, 0.648, 1, 1.944, 0.648, 2.056, -0.152, 2.167, -0.152, 1, 2.356, -0.152, 2.544, 0.213, 2.733, 0.213, 1, 2.878, 0.213, 3.022, -0.444, 3.167, -0.444, 1, 3.3, -0.444, 3.433, 0.385, 3.567, 0.385, 1, 3.678, 0.385, 3.789, -0.221, 3.9, -0.221, 1, 3.956, -0.221, 4.011, -0.142, 4.067, -0.062]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0.005, 1, 0.178, 0.088, 0.356, 0.796, 0.533, 0.796, 1, 0.644, 0.796, 0.756, -2.027, 0.867, -2.027, 1, 0.989, -2.027, 1.111, 2.092, 1.233, 2.092, 1, 1.356, 2.092, 1.478, -1.681, 1.6, -1.681, 1, 1.722, -1.681, 1.844, 0.97, 1.967, 0.97, 1, 2.078, 0.97, 2.189, -0.295, 2.3, -0.295, 1, 2.444, -0.295, 2.589, 0.285, 2.733, 0.285, 1, 2.911, 0.285, 3.089, -0.531, 3.267, -0.531, 1, 3.4, -0.531, 3.533, 0.501, 3.667, 0.501, 1, 3.789, 0.501, 3.911, -0.312, 4.033, -0.312, 1, 4.044, -0.312, 4.056, -0.308, 4.067, -0.299]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 1.356, 0, 2.711, 0, 4.067, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, -30, 1, 1.356, -30, 2.711, -30, 4.067, -30]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, -30, 1, 1.356, -30, 2.711, -30, 4.067, -30]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 2, 0.5, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 0, 0, 4.07, 0]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 4.07, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 4.07, 1]}]}