{"Version": 3, "Meta": {"Duration": 3.23, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 114, "TotalSegmentCount": 553, "TotalPointCount": 1643, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -2, 1, 0.133, -2, 0.267, -21, 0.4, -21, 1, 0.544, -21, 0.689, 18, 0.833, 18, 1, 0.978, 18, 1.122, -21, 1.267, -21, 1, 1.411, -21, 1.556, 18, 1.7, 18, 1, 1.867, 18, 2.033, -12, 2.2, -12, 1, 2.4, -12, 2.6, 7, 2.8, 7, 0, 3.233, 7]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 18, 0, 3.233, 18]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.233, 0, 0.467, -4, 0.7, -4, 1, 0.844, -4, 0.989, 3, 1.133, 3, 1, 1.278, 3, 1.422, -3, 1.567, -3, 1, 1.711, -3, 1.856, 4, 2, 4, 1, 2.167, 4, 2.333, -4, 2.5, -4, 1, 2.7, -4, 2.9, 4, 3.1, 4, 0, 3.233, 4]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 1.078, 0, 2.156, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 3.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 3.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.144, 0, 0.289, -0.5, 0.433, -0.5, 0, 3.233, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.3, 0, 3.233, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.3, 0, 3.233, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.6, 1, 0.122, -0.6, 0.244, -1, 0.367, -1, 1, 0.489, -1, 0.611, -0.4, 0.733, -0.4, 1, 0.878, -0.4, 1.022, -0.411, 1.167, -0.5, 1, 1.311, -0.589, 1.456, -0.7, 1.6, -0.7, 1, 1.667, -0.7, 1.733, -0.7, 1.8, -0.7, 0, 3.233, -0.7]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0.439, 0.367, 0.5, 1, 0.489, 0.561, 0.611, 0.546, 0.733, 0.6, 1, 0.878, 0.664, 1.022, 0.8, 1.167, 0.8, 1, 1.311, 0.8, 1.456, 0.782, 1.6, 0.6, 1, 1.667, 0.516, 1.733, 0, 1.8, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 3.233, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.144, 0, 0.256, 1.635, 0.367, 1.635, 1, 0.578, 1.635, 0.789, -5.019, 1, -5.019, 1, 1.133, -5.019, 1.267, 5.542, 1.4, 5.542, 1, 1.544, 5.542, 1.689, -6.094, 1.833, -6.094, 1, 1.978, -6.094, 2.122, 5.998, 2.267, 5.998, 1, 2.433, 5.998, 2.6, -4.674, 2.767, -4.674, 1, 2.922, -4.674, 3.078, 1.563, 3.233, 1.563]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, -0.004, 1, 0.089, -0.004, 0.178, 7.035, 0.267, 7.035, 1, 0.378, 7.035, 0.489, -15.261, 0.6, -15.261, 1, 0.733, -15.261, 0.867, 15.883, 1, 15.883, 1, 1.144, 15.883, 1.289, -15.584, 1.433, -15.584, 1, 1.578, -15.584, 1.722, 12.209, 1.867, 12.209, 1, 2.022, 12.209, 2.178, -5.513, 2.333, -5.513, 1, 2.533, -5.513, 2.733, 2.199, 2.933, 2.199, 1, 2.989, 2.199, 3.044, 1.288, 3.1, 1.288, 1, 3.122, 1.288, 3.144, 1.313, 3.167, 1.313, 1, 3.189, 1.313, 3.211, 1.218, 3.233, 1.071]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0.083, 1, 0.1, 0.565, 0.2, 2.974, 0.3, 2.974, 1, 0.422, 2.974, 0.544, -8.793, 0.667, -8.793, 1, 0.811, -8.793, 0.956, 10.968, 1.1, 10.968, 1, 1.233, 10.968, 1.367, -11.36, 1.5, -11.36, 1, 1.644, -11.36, 1.789, 8.987, 1.933, 8.987, 1, 2.078, 8.987, 2.222, -5.043, 2.367, -5.043, 1, 2.544, -5.043, 2.722, 1.596, 2.9, 1.596, 1, 3.011, 1.596, 3.122, -0.101, 3.233, -0.41]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.083, 1, 0.1, 0.565, 0.2, 2.974, 0.3, 2.974, 1, 0.422, 2.974, 0.544, -8.793, 0.667, -8.793, 1, 0.811, -8.793, 0.956, 10.968, 1.1, 10.968, 1, 1.233, 10.968, 1.367, -11.36, 1.5, -11.36, 1, 1.644, -11.36, 1.789, 8.987, 1.933, 8.987, 1, 2.078, 8.987, 2.222, -5.043, 2.367, -5.043, 1, 2.544, -5.043, 2.722, 1.596, 2.9, 1.596, 1, 3.011, 1.596, 3.122, -0.101, 3.233, -0.41]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 1.986, 0.267, 1.986, 1, 0.489, 1.986, 0.711, -6.662, 0.933, -6.662, 1, 1.044, -6.662, 1.156, 10.083, 1.267, 10.083, 1, 1.411, 10.083, 1.556, -9.231, 1.7, -9.231, 1, 1.844, -9.231, 1.989, 9.482, 2.133, 9.482, 1, 2.289, 9.482, 2.444, -6.861, 2.6, -6.861, 1, 2.789, -6.861, 2.978, 3.639, 3.167, 3.639, 1, 3.189, 3.639, 3.211, 3.381, 3.233, 2.98]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -0.289, 0.167, -0.289, 1, 0.256, -0.289, 0.344, 0.556, 0.433, 0.556, 1, 0.522, 0.556, 0.611, -0.34, 0.7, -0.34, 1, 0.756, -0.34, 0.811, 0.844, 0.867, 0.844, 1, 0.956, 0.844, 1.044, -2.334, 1.133, -2.334, 1, 1.233, -2.334, 1.333, 3.527, 1.433, 3.527, 1, 1.544, 3.527, 1.656, -2.431, 1.767, -2.431, 1, 1.922, -2.431, 2.078, 2.077, 2.233, 2.077, 1, 2.344, 2.077, 2.456, -1.172, 2.567, -1.172, 1, 2.711, -1.172, 2.856, 0.628, 3, 0.628, 1, 3.056, 0.628, 3.111, -0.094, 3.167, -0.094, 1, 3.189, -0.094, 3.211, 0.039, 3.233, 0.2]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -0.289, 0.167, -0.289, 1, 0.256, -0.289, 0.344, 0.556, 0.433, 0.556, 1, 0.522, 0.556, 0.611, -0.34, 0.7, -0.34, 1, 0.756, -0.34, 0.811, 0.844, 0.867, 0.844, 1, 0.956, 0.844, 1.044, -2.334, 1.133, -2.334, 1, 1.233, -2.334, 1.333, 3.527, 1.433, 3.527, 1, 1.544, 3.527, 1.656, -2.431, 1.767, -2.431, 1, 1.922, -2.431, 2.078, 2.077, 2.233, 2.077, 1, 2.344, 2.077, 2.456, -1.172, 2.567, -1.172, 1, 2.711, -1.172, 2.856, 0.628, 3, 0.628, 1, 3.056, 0.628, 3.111, -0.094, 3.167, -0.094, 1, 3.189, -0.094, 3.211, 0.039, 3.233, 0.2]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -0.001, 1, 0.011, -0.001, 0.022, -0.001, 0.033, -0.001, 1, 0.111, -0.001, 0.189, 2.593, 0.267, 2.593, 1, 0.378, 2.593, 0.489, -5.632, 0.6, -5.632, 1, 0.733, -5.632, 0.867, 5.933, 1, 5.933, 1, 1.156, 5.933, 1.311, -5.852, 1.467, -5.852, 1, 1.6, -5.852, 1.733, 4.607, 1.867, 4.607, 1, 2.022, 4.607, 2.178, -2.095, 2.333, -2.095, 1, 2.533, -2.095, 2.733, 0.801, 2.933, 0.801, 1, 2.989, 0.801, 3.044, 0.494, 3.1, 0.494, 1, 3.122, 0.494, 3.144, 0.499, 3.167, 0.499, 1, 3.189, 0.499, 3.211, 0.463, 3.233, 0.406]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0.028, 1, 0.011, 0.03, 0.022, 0.032, 0.033, 0.032, 1, 0.078, 0.032, 0.122, -1.132, 0.167, -1.132, 1, 0.267, -1.132, 0.367, 3.935, 0.467, 3.935, 1, 0.578, 3.935, 0.689, -7.899, 0.8, -7.899, 1, 0.922, -7.899, 1.044, 8.885, 1.167, 8.885, 1, 1.3, 8.885, 1.433, -8.338, 1.567, -8.338, 1, 1.7, -8.338, 1.833, 7.455, 1.967, 7.455, 1, 2.1, 7.455, 2.233, -3.92, 2.367, -3.92, 1, 2.478, -3.92, 2.589, 1.383, 2.7, 1.383, 1, 2.778, 1.383, 2.856, -0.255, 2.933, -0.255, 1, 3, -0.255, 3.067, 0.32, 3.133, 0.32, 1, 3.144, 0.32, 3.156, 0.299, 3.167, 0.299, 1, 3.189, 0.299, 3.211, 0.315, 3.233, 0.331]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, -0.001, 1, 0.011, -0.002, 0.022, -0.002, 0.033, -0.002, 1, 0.111, -0.002, 0.189, 3.602, 0.267, 3.602, 1, 0.378, 3.602, 0.489, -7.823, 0.6, -7.823, 1, 0.733, -7.823, 0.867, 8.24, 1, 8.24, 1, 1.156, 8.24, 1.311, -8.127, 1.467, -8.127, 1, 1.6, -8.127, 1.733, 6.398, 1.867, 6.398, 1, 2.022, 6.398, 2.178, -2.91, 2.333, -2.91, 1, 2.533, -2.91, 2.733, 1.113, 2.933, 1.113, 1, 2.989, 1.113, 3.044, 0.686, 3.1, 0.686, 1, 3.122, 0.686, 3.144, 0.692, 3.167, 0.692, 1, 3.189, 0.692, 3.211, 0.642, 3.233, 0.564]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, -0.001, 1, 0.011, -0.002, 0.022, -0.002, 0.033, -0.002, 1, 0.111, -0.002, 0.189, 3.602, 0.267, 3.602, 1, 0.378, 3.602, 0.489, -7.823, 0.6, -7.823, 1, 0.733, -7.823, 0.867, 8.24, 1, 8.24, 1, 1.156, 8.24, 1.311, -8.127, 1.467, -8.127, 1, 1.6, -8.127, 1.733, 6.398, 1.867, 6.398, 1, 2.022, 6.398, 2.178, -2.91, 2.333, -2.91, 1, 2.533, -2.91, 2.733, 1.113, 2.933, 1.113, 1, 2.989, 1.113, 3.044, 0.686, 3.1, 0.686, 1, 3.122, 0.686, 3.144, 0.692, 3.167, 0.692, 1, 3.189, 0.692, 3.211, 0.642, 3.233, 0.564]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, -0.001, 1, 0.011, -0.002, 0.022, -0.002, 0.033, -0.002, 1, 0.111, -0.002, 0.189, 3.602, 0.267, 3.602, 1, 0.378, 3.602, 0.489, -7.823, 0.6, -7.823, 1, 0.733, -7.823, 0.867, 8.24, 1, 8.24, 1, 1.156, 8.24, 1.311, -8.127, 1.467, -8.127, 1, 1.6, -8.127, 1.733, 6.398, 1.867, 6.398, 1, 2.022, 6.398, 2.178, -2.91, 2.333, -2.91, 1, 2.533, -2.91, 2.733, 1.113, 2.933, 1.113, 1, 2.989, 1.113, 3.044, 0.686, 3.1, 0.686, 1, 3.122, 0.686, 3.144, 0.692, 3.167, 0.692, 1, 3.189, 0.692, 3.211, 0.642, 3.233, 0.564]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -0.001, 1, 0.011, -0.001, 0.022, -0.001, 0.033, -0.001, 1, 0.111, -0.001, 0.189, 2.593, 0.267, 2.593, 1, 0.378, 2.593, 0.489, -5.632, 0.6, -5.632, 1, 0.733, -5.632, 0.867, 5.933, 1, 5.933, 1, 1.156, 5.933, 1.311, -5.852, 1.467, -5.852, 1, 1.6, -5.852, 1.733, 4.607, 1.867, 4.607, 1, 2.022, 4.607, 2.178, -2.095, 2.333, -2.095, 1, 2.533, -2.095, 2.733, 0.801, 2.933, 0.801, 1, 2.989, 0.801, 3.044, 0.494, 3.1, 0.494, 1, 3.122, 0.494, 3.144, 0.499, 3.167, 0.499, 1, 3.189, 0.499, 3.211, 0.463, 3.233, 0.406]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0.028, 1, 0.011, 0.03, 0.022, 0.032, 0.033, 0.032, 1, 0.078, 0.032, 0.122, -1.132, 0.167, -1.132, 1, 0.267, -1.132, 0.367, 3.935, 0.467, 3.935, 1, 0.578, 3.935, 0.689, -7.899, 0.8, -7.899, 1, 0.922, -7.899, 1.044, 8.885, 1.167, 8.885, 1, 1.3, 8.885, 1.433, -8.338, 1.567, -8.338, 1, 1.7, -8.338, 1.833, 7.455, 1.967, 7.455, 1, 2.1, 7.455, 2.233, -3.92, 2.367, -3.92, 1, 2.478, -3.92, 2.589, 1.383, 2.7, 1.383, 1, 2.778, 1.383, 2.856, -0.255, 2.933, -0.255, 1, 3, -0.255, 3.067, 0.32, 3.133, 0.32, 1, 3.144, 0.32, 3.156, 0.299, 3.167, 0.299, 1, 3.189, 0.299, 3.211, 0.315, 3.233, 0.331]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -0.001, 1, 0.011, -0.002, 0.022, -0.002, 0.033, -0.002, 1, 0.111, -0.002, 0.189, 3.602, 0.267, 3.602, 1, 0.378, 3.602, 0.489, -7.823, 0.6, -7.823, 1, 0.733, -7.823, 0.867, 8.24, 1, 8.24, 1, 1.156, 8.24, 1.311, -8.127, 1.467, -8.127, 1, 1.6, -8.127, 1.733, 6.398, 1.867, 6.398, 1, 2.022, 6.398, 2.178, -2.91, 2.333, -2.91, 1, 2.533, -2.91, 2.733, 1.113, 2.933, 1.113, 1, 2.989, 1.113, 3.044, 0.686, 3.1, 0.686, 1, 3.122, 0.686, 3.144, 0.692, 3.167, 0.692, 1, 3.189, 0.692, 3.211, 0.642, 3.233, 0.564]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 1.796, 0.3, 1.796, 1, 0.433, 1.796, 0.567, -3.347, 0.7, -3.347, 1, 0.844, -3.347, 0.989, 3.337, 1.133, 3.337, 1, 1.278, 3.337, 1.422, -3.434, 1.567, -3.434, 1, 1.722, -3.434, 1.878, 2.465, 2.033, 2.465, 1, 2.233, 2.465, 2.433, -1.511, 2.633, -1.511, 1, 2.822, -1.511, 3.011, 0.34, 3.2, 0.34, 1, 3.211, 0.34, 3.222, 0.336, 3.233, 0.33]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, -0.096, 1, 0.078, -0.525, 0.156, -2.242, 0.233, -2.242, 1, 0.344, -2.242, 0.456, 5.891, 0.567, 5.891, 1, 0.689, 5.891, 0.811, -7.801, 0.933, -7.801, 1, 1.078, -7.801, 1.222, 8.384, 1.367, 8.384, 1, 1.511, 8.384, 1.656, -8.014, 1.8, -8.014, 1, 1.944, -8.014, 2.089, 4.797, 2.233, 4.797, 1, 2.4, 4.797, 2.567, -1.731, 2.733, -1.731, 1, 2.867, -1.731, 3, 0.446, 3.133, 0.446, 1, 3.144, 0.446, 3.156, 0.438, 3.167, 0.438, 1, 3.189, 0.438, 3.211, 0.47, 3.233, 0.508]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.041, 1, 0.111, -0.334, 0.222, -1.945, 0.333, -1.945, 1, 0.444, -1.945, 0.556, 6.074, 0.667, 6.074, 1, 0.8, 6.074, 0.933, -9.325, 1.067, -9.325, 1, 1.2, -9.325, 1.333, 10.496, 1.467, 10.496, 1, 1.611, 10.496, 1.756, -10.379, 1.9, -10.379, 1, 2.044, -10.379, 2.189, 7.354, 2.333, 7.354, 1, 2.478, 7.354, 2.622, -3.34, 2.767, -3.34, 1, 2.922, -3.34, 3.078, 0.063, 3.233, 0.913]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0.005, 1, 0.022, 0.008, 0.044, 0.012, 0.067, 0.012, 1, 0.189, 0.012, 0.311, -1.817, 0.433, -1.817, 1, 0.556, -1.817, 0.678, 6.553, 0.8, 6.553, 1, 0.922, 6.553, 1.044, -10.945, 1.167, -10.945, 1, 1.311, -10.945, 1.456, 13.007, 1.6, 13.007, 1, 1.733, 13.007, 1.867, -13.19, 2, -13.19, 1, 2.144, -13.19, 2.289, 10.352, 2.433, 10.352, 1, 2.589, 10.352, 2.744, -5.64, 2.9, -5.64, 1, 3.011, -5.64, 3.122, -1.688, 3.233, 0.57]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 4.489, 0.3, 4.489, 1, 0.433, 4.489, 0.567, -8.366, 0.7, -8.366, 1, 0.844, -8.366, 0.989, 8.343, 1.133, 8.343, 1, 1.278, 8.343, 1.422, -8.584, 1.567, -8.584, 1, 1.722, -8.584, 1.878, 6.162, 2.033, 6.162, 1, 2.233, 6.162, 2.433, -3.778, 2.633, -3.778, 1, 2.822, -3.778, 3.011, 0.85, 3.2, 0.85, 1, 3.211, 0.85, 3.222, 0.841, 3.233, 0.825]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, -0.096, 1, 0.078, -0.525, 0.156, -2.242, 0.233, -2.242, 1, 0.344, -2.242, 0.456, 5.891, 0.567, 5.891, 1, 0.689, 5.891, 0.811, -7.801, 0.933, -7.801, 1, 1.078, -7.801, 1.222, 8.384, 1.367, 8.384, 1, 1.511, 8.384, 1.656, -8.014, 1.8, -8.014, 1, 1.944, -8.014, 2.089, 4.797, 2.233, 4.797, 1, 2.4, 4.797, 2.567, -1.731, 2.733, -1.731, 1, 2.867, -1.731, 3, 0.446, 3.133, 0.446, 1, 3.144, 0.446, 3.156, 0.438, 3.167, 0.438, 1, 3.189, 0.438, 3.211, 0.47, 3.233, 0.508]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.041, 1, 0.111, -0.334, 0.222, -1.945, 0.333, -1.945, 1, 0.444, -1.945, 0.556, 6.074, 0.667, 6.074, 1, 0.8, 6.074, 0.933, -9.325, 1.067, -9.325, 1, 1.2, -9.325, 1.333, 10.496, 1.467, 10.496, 1, 1.611, 10.496, 1.756, -10.379, 1.9, -10.379, 1, 2.044, -10.379, 2.189, 7.354, 2.333, 7.354, 1, 2.478, 7.354, 2.622, -3.34, 2.767, -3.34, 1, 2.922, -3.34, 3.078, 0.063, 3.233, 0.913]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 1.796, 0.3, 1.796, 1, 0.433, 1.796, 0.567, -3.347, 0.7, -3.347, 1, 0.844, -3.347, 0.989, 3.337, 1.133, 3.337, 1, 1.278, 3.337, 1.422, -3.434, 1.567, -3.434, 1, 1.722, -3.434, 1.878, 2.465, 2.033, 2.465, 1, 2.233, 2.465, 2.433, -1.511, 2.633, -1.511, 1, 2.822, -1.511, 3.011, 0.34, 3.2, 0.34, 1, 3.211, 0.34, 3.222, 0.336, 3.233, 0.33]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, -0.096, 1, 0.078, -0.525, 0.156, -2.242, 0.233, -2.242, 1, 0.344, -2.242, 0.456, 5.891, 0.567, 5.891, 1, 0.689, 5.891, 0.811, -7.801, 0.933, -7.801, 1, 1.078, -7.801, 1.222, 8.384, 1.367, 8.384, 1, 1.511, 8.384, 1.656, -8.014, 1.8, -8.014, 1, 1.944, -8.014, 2.089, 4.797, 2.233, 4.797, 1, 2.4, 4.797, 2.567, -1.731, 2.733, -1.731, 1, 2.867, -1.731, 3, 0.446, 3.133, 0.446, 1, 3.144, 0.446, 3.156, 0.438, 3.167, 0.438, 1, 3.189, 0.438, 3.211, 0.47, 3.233, 0.508]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, -0.041, 1, 0.111, -0.334, 0.222, -1.945, 0.333, -1.945, 1, 0.444, -1.945, 0.556, 6.074, 0.667, 6.074, 1, 0.8, 6.074, 0.933, -9.325, 1.067, -9.325, 1, 1.2, -9.325, 1.333, 10.496, 1.467, 10.496, 1, 1.611, 10.496, 1.756, -10.379, 1.9, -10.379, 1, 2.044, -10.379, 2.189, 7.354, 2.333, 7.354, 1, 2.478, 7.354, 2.622, -3.34, 2.767, -3.34, 1, 2.922, -3.34, 3.078, 0.063, 3.233, 0.913]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, 0.005, 1, 0.022, 0.008, 0.044, 0.012, 0.067, 0.012, 1, 0.189, 0.012, 0.311, -1.817, 0.433, -1.817, 1, 0.556, -1.817, 0.678, 6.553, 0.8, 6.553, 1, 0.922, 6.553, 1.044, -10.945, 1.167, -10.945, 1, 1.311, -10.945, 1.456, 13.007, 1.6, 13.007, 1, 1.733, 13.007, 1.867, -13.19, 2, -13.19, 1, 2.144, -13.19, 2.289, 10.352, 2.433, 10.352, 1, 2.589, 10.352, 2.744, -5.64, 2.9, -5.64, 1, 3.011, -5.64, 3.122, -1.688, 3.233, 0.57]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 4.489, 0.3, 4.489, 1, 0.433, 4.489, 0.567, -8.366, 0.7, -8.366, 1, 0.844, -8.366, 0.989, 8.343, 1.133, 8.343, 1, 1.278, 8.343, 1.422, -8.584, 1.567, -8.584, 1, 1.722, -8.584, 1.878, 6.162, 2.033, 6.162, 1, 2.233, 6.162, 2.433, -3.778, 2.633, -3.778, 1, 2.822, -3.778, 3.011, 0.85, 3.2, 0.85, 1, 3.211, 0.85, 3.222, 0.841, 3.233, 0.825]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, -0.096, 1, 0.078, -0.525, 0.156, -2.242, 0.233, -2.242, 1, 0.344, -2.242, 0.456, 5.891, 0.567, 5.891, 1, 0.689, 5.891, 0.811, -7.801, 0.933, -7.801, 1, 1.078, -7.801, 1.222, 8.384, 1.367, 8.384, 1, 1.511, 8.384, 1.656, -8.014, 1.8, -8.014, 1, 1.944, -8.014, 2.089, 4.797, 2.233, 4.797, 1, 2.4, 4.797, 2.567, -1.731, 2.733, -1.731, 1, 2.867, -1.731, 3, 0.446, 3.133, 0.446, 1, 3.144, 0.446, 3.156, 0.438, 3.167, 0.438, 1, 3.189, 0.438, 3.211, 0.47, 3.233, 0.508]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, -0.041, 1, 0.111, -0.334, 0.222, -1.945, 0.333, -1.945, 1, 0.444, -1.945, 0.556, 6.074, 0.667, 6.074, 1, 0.8, 6.074, 0.933, -9.325, 1.067, -9.325, 1, 1.2, -9.325, 1.333, 10.496, 1.467, 10.496, 1, 1.611, 10.496, 1.756, -10.379, 1.9, -10.379, 1, 2.044, -10.379, 2.189, 7.354, 2.333, 7.354, 1, 2.478, 7.354, 2.622, -3.34, 2.767, -3.34, 1, 2.922, -3.34, 3.078, 0.063, 3.233, 0.913]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 4.489, 0.3, 4.489, 1, 0.433, 4.489, 0.567, -8.366, 0.7, -8.366, 1, 0.844, -8.366, 0.989, 8.343, 1.133, 8.343, 1, 1.278, 8.343, 1.422, -8.584, 1.567, -8.584, 1, 1.722, -8.584, 1.878, 6.162, 2.033, 6.162, 1, 2.233, 6.162, 2.433, -3.778, 2.633, -3.778, 1, 2.822, -3.778, 3.011, 0.85, 3.2, 0.85, 1, 3.211, 0.85, 3.222, 0.841, 3.233, 0.825]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, -0.096, 1, 0.078, -0.525, 0.156, -2.242, 0.233, -2.242, 1, 0.344, -2.242, 0.456, 5.891, 0.567, 5.891, 1, 0.689, 5.891, 0.811, -7.801, 0.933, -7.801, 1, 1.078, -7.801, 1.222, 8.384, 1.367, 8.384, 1, 1.511, 8.384, 1.656, -8.014, 1.8, -8.014, 1, 1.944, -8.014, 2.089, 4.797, 2.233, 4.797, 1, 2.4, 4.797, 2.567, -1.731, 2.733, -1.731, 1, 2.867, -1.731, 3, 0.446, 3.133, 0.446, 1, 3.144, 0.446, 3.156, 0.438, 3.167, 0.438, 1, 3.189, 0.438, 3.211, 0.47, 3.233, 0.508]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, -0.041, 1, 0.111, -0.334, 0.222, -1.945, 0.333, -1.945, 1, 0.444, -1.945, 0.556, 6.074, 0.667, 6.074, 1, 0.8, 6.074, 0.933, -9.325, 1.067, -9.325, 1, 1.2, -9.325, 1.333, 10.496, 1.467, 10.496, 1, 1.611, 10.496, 1.756, -10.379, 1.9, -10.379, 1, 2.044, -10.379, 2.189, 7.354, 2.333, 7.354, 1, 2.478, 7.354, 2.622, -3.34, 2.767, -3.34, 1, 2.922, -3.34, 3.078, 0.063, 3.233, 0.913]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 4.489, 0.3, 4.489, 1, 0.433, 4.489, 0.567, -8.366, 0.7, -8.366, 1, 0.844, -8.366, 0.989, 8.343, 1.133, 8.343, 1, 1.278, 8.343, 1.422, -8.584, 1.567, -8.584, 1, 1.722, -8.584, 1.878, 6.162, 2.033, 6.162, 1, 2.233, 6.162, 2.433, -3.778, 2.633, -3.778, 1, 2.822, -3.778, 3.011, 0.85, 3.2, 0.85, 1, 3.211, 0.85, 3.222, 0.841, 3.233, 0.825]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, -0.096, 1, 0.078, -0.525, 0.156, -2.242, 0.233, -2.242, 1, 0.344, -2.242, 0.456, 5.891, 0.567, 5.891, 1, 0.689, 5.891, 0.811, -7.801, 0.933, -7.801, 1, 1.078, -7.801, 1.222, 8.384, 1.367, 8.384, 1, 1.511, 8.384, 1.656, -8.014, 1.8, -8.014, 1, 1.944, -8.014, 2.089, 4.797, 2.233, 4.797, 1, 2.4, 4.797, 2.567, -1.731, 2.733, -1.731, 1, 2.867, -1.731, 3, 0.446, 3.133, 0.446, 1, 3.144, 0.446, 3.156, 0.438, 3.167, 0.438, 1, 3.189, 0.438, 3.211, 0.47, 3.233, 0.508]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, -0.041, 1, 0.111, -0.334, 0.222, -1.945, 0.333, -1.945, 1, 0.444, -1.945, 0.556, 6.074, 0.667, 6.074, 1, 0.8, 6.074, 0.933, -9.325, 1.067, -9.325, 1, 1.2, -9.325, 1.333, 10.496, 1.467, 10.496, 1, 1.611, 10.496, 1.756, -10.379, 1.9, -10.379, 1, 2.044, -10.379, 2.189, 7.354, 2.333, 7.354, 1, 2.478, 7.354, 2.622, -3.34, 2.767, -3.34, 1, 2.922, -3.34, 3.078, 0.063, 3.233, 0.913]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0.069, 1, 0.1, 0.471, 0.2, 2.478, 0.3, 2.478, 1, 0.422, 2.478, 0.544, -7.328, 0.667, -7.328, 1, 0.811, -7.328, 0.956, 9.14, 1.1, 9.14, 1, 1.233, 9.14, 1.367, -9.467, 1.5, -9.467, 1, 1.644, -9.467, 1.789, 7.489, 1.933, 7.489, 1, 2.078, 7.489, 2.222, -4.203, 2.367, -4.203, 1, 2.544, -4.203, 2.722, 1.33, 2.9, 1.33, 1, 3.011, 1.33, 3.122, -0.085, 3.233, -0.342]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, -0.001, 1, 0.011, -0.001, 0.022, -0.001, 0.033, -0.001, 1, 0.1, -0.001, 0.167, -1.537, 0.233, -1.537, 1, 0.344, -1.537, 0.456, 4.934, 0.567, 4.934, 1, 0.689, 4.934, 0.811, -8.186, 0.933, -8.186, 1, 1.056, -8.186, 1.178, 9.106, 1.3, 9.106, 1, 1.444, 9.106, 1.589, -9.055, 1.733, -9.055, 1, 1.856, -9.055, 1.978, 6.666, 2.1, 6.666, 1, 2.233, 6.666, 2.367, -3.629, 2.5, -3.629, 1, 2.667, -3.629, 2.833, 1.184, 3, 1.184, 1, 3.078, 1.184, 3.156, 0.541, 3.233, 0.006]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, -0.031, 1, 0.111, -0.216, 0.222, -1.237, 0.333, -1.237, 1, 0.444, -1.237, 0.556, 4.403, 0.667, 4.403, 1, 0.789, 4.403, 0.911, -7.522, 1.033, -7.522, 1, 1.156, -7.522, 1.278, 8.766, 1.4, 8.766, 1, 1.533, 8.766, 1.667, -8.546, 1.8, -8.546, 1, 1.933, -8.546, 2.067, 6.634, 2.2, 6.634, 1, 2.333, 6.634, 2.467, -3.761, 2.6, -3.761, 1, 2.722, -3.761, 2.844, 1.469, 2.967, 1.469, 1, 3.056, 1.469, 3.144, 0.86, 3.233, 0.292]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, -0.017, 1, 0.122, -0.11, 0.244, -0.673, 0.367, -0.673, 1, 0.489, -0.673, 0.611, 3.053, 0.733, 3.053, 1, 0.844, 3.053, 0.956, -6.489, 1.067, -6.489, 1, 1.189, -6.489, 1.311, 8.622, 1.433, 8.622, 1, 1.567, 8.622, 1.7, -8.852, 1.833, -8.852, 1, 1.967, -8.852, 2.1, 7.729, 2.233, 7.729, 1, 2.367, 7.729, 2.5, -5.045, 2.633, -5.045, 1, 2.756, -5.045, 2.878, 2.449, 3, 2.449, 1, 3.078, 2.449, 3.156, 1.508, 3.233, 0.639]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, -0.009, 1, 0.022, -0.011, 0.044, -0.014, 0.067, -0.014, 1, 0.078, -0.014, 0.089, -0.014, 0.1, -0.014, 1, 0.222, -0.014, 0.344, -0.717, 0.467, -0.717, 1, 0.578, -0.717, 0.689, 3.601, 0.8, 3.601, 1, 0.911, 3.601, 1.022, -8.024, 1.133, -8.024, 1, 1.256, -8.024, 1.378, 10.987, 1.5, 10.987, 1, 1.622, 10.987, 1.744, -11.219, 1.867, -11.219, 1, 2.011, -11.219, 2.156, 10.044, 2.3, 10.044, 1, 2.422, 10.044, 2.544, -7.192, 2.667, -7.192, 1, 2.8, -7.192, 2.933, 3.979, 3.067, 3.979, 1, 3.122, 3.979, 3.178, 2.852, 3.233, 1.624]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0.069, 1, 0.1, 0.471, 0.2, 2.478, 0.3, 2.478, 1, 0.422, 2.478, 0.544, -7.328, 0.667, -7.328, 1, 0.811, -7.328, 0.956, 9.14, 1.1, 9.14, 1, 1.233, 9.14, 1.367, -9.467, 1.5, -9.467, 1, 1.644, -9.467, 1.789, 7.489, 1.933, 7.489, 1, 2.078, 7.489, 2.222, -4.203, 2.367, -4.203, 1, 2.544, -4.203, 2.722, 1.33, 2.9, 1.33, 1, 3.011, 1.33, 3.122, -0.085, 3.233, -0.342]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, -0.001, 1, 0.011, -0.001, 0.022, -0.001, 0.033, -0.001, 1, 0.1, -0.001, 0.167, -1.537, 0.233, -1.537, 1, 0.344, -1.537, 0.456, 4.934, 0.567, 4.934, 1, 0.689, 4.934, 0.811, -8.186, 0.933, -8.186, 1, 1.056, -8.186, 1.178, 9.106, 1.3, 9.106, 1, 1.444, 9.106, 1.589, -9.055, 1.733, -9.055, 1, 1.856, -9.055, 1.978, 6.666, 2.1, 6.666, 1, 2.233, 6.666, 2.367, -3.629, 2.5, -3.629, 1, 2.667, -3.629, 2.833, 1.184, 3, 1.184, 1, 3.078, 1.184, 3.156, 0.541, 3.233, 0.006]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, -0.031, 1, 0.111, -0.216, 0.222, -1.237, 0.333, -1.237, 1, 0.444, -1.237, 0.556, 4.403, 0.667, 4.403, 1, 0.789, 4.403, 0.911, -7.522, 1.033, -7.522, 1, 1.156, -7.522, 1.278, 8.766, 1.4, 8.766, 1, 1.533, 8.766, 1.667, -8.546, 1.8, -8.546, 1, 1.933, -8.546, 2.067, 6.634, 2.2, 6.634, 1, 2.333, 6.634, 2.467, -3.761, 2.6, -3.761, 1, 2.722, -3.761, 2.844, 1.469, 2.967, 1.469, 1, 3.056, 1.469, 3.144, 0.86, 3.233, 0.292]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, -0.017, 1, 0.122, -0.11, 0.244, -0.673, 0.367, -0.673, 1, 0.489, -0.673, 0.611, 3.053, 0.733, 3.053, 1, 0.844, 3.053, 0.956, -6.489, 1.067, -6.489, 1, 1.189, -6.489, 1.311, 8.622, 1.433, 8.622, 1, 1.567, 8.622, 1.7, -8.852, 1.833, -8.852, 1, 1.967, -8.852, 2.1, 7.729, 2.233, 7.729, 1, 2.367, 7.729, 2.5, -5.045, 2.633, -5.045, 1, 2.756, -5.045, 2.878, 2.449, 3, 2.449, 1, 3.078, 2.449, 3.156, 1.508, 3.233, 0.639]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, -0.009, 1, 0.022, -0.011, 0.044, -0.014, 0.067, -0.014, 1, 0.078, -0.014, 0.089, -0.014, 0.1, -0.014, 1, 0.222, -0.014, 0.344, -0.717, 0.467, -0.717, 1, 0.578, -0.717, 0.689, 3.601, 0.8, 3.601, 1, 0.911, 3.601, 1.022, -8.024, 1.133, -8.024, 1, 1.256, -8.024, 1.378, 10.987, 1.5, 10.987, 1, 1.622, 10.987, 1.744, -11.219, 1.867, -11.219, 1, 2.011, -11.219, 2.156, 10.044, 2.3, 10.044, 1, 2.422, 10.044, 2.544, -7.192, 2.667, -7.192, 1, 2.8, -7.192, 2.933, 3.979, 3.067, 3.979, 1, 3.122, 3.979, 3.178, 2.852, 3.233, 1.624]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, -0.674, 0.233, -0.674, 1, 0.367, -0.674, 0.5, 0.723, 0.633, 0.723, 1, 0.667, 0.723, 0.7, 0.678, 0.733, 0.678, 1, 0.789, 0.678, 0.844, 2.007, 0.9, 2.007, 1, 1.011, 2.007, 1.122, -4.502, 1.233, -4.502, 1, 1.367, -4.502, 1.5, 4.715, 1.633, 4.715, 1, 1.778, 4.715, 1.922, -5.087, 2.067, -5.087, 1, 2.211, -5.087, 2.356, 4.006, 2.5, 4.006, 1, 2.644, 4.006, 2.789, -2.275, 2.933, -2.275, 1, 3.033, -2.275, 3.133, -0.69, 3.233, 0.442]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.178, 0, 0.256, -0.634, 0.333, -0.634, 1, 0.544, -0.634, 0.756, 1.694, 0.967, 1.694, 1, 1.089, 1.694, 1.211, -4.691, 1.333, -4.691, 1, 1.467, -4.691, 1.6, 5.731, 1.733, 5.731, 1, 1.878, 5.731, 2.022, -6.132, 2.167, -6.132, 1, 2.311, -6.132, 2.456, 5.28, 2.6, 5.28, 1, 2.744, 5.28, 2.889, -3.239, 3.033, -3.239, 1, 3.1, -3.239, 3.167, -2.41, 3.233, -1.415]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.267, 0, 0.367, -0.599, 0.467, -0.599, 1, 0.667, -0.599, 0.867, 1.634, 1.067, 1.634, 1, 1.2, 1.634, 1.333, -4.905, 1.467, -4.905, 1, 1.589, -4.905, 1.711, 6.704, 1.833, 6.704, 1, 1.978, 6.704, 2.122, -7.313, 2.267, -7.313, 1, 2.411, -7.313, 2.556, 6.601, 2.7, 6.601, 1, 2.844, 6.601, 2.989, -4.39, 3.133, -4.39, 1, 3.167, -4.39, 3.2, -4.114, 3.233, -3.671]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "<PERSON><PERSON>_Skinning", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 0, 0, 3.23, 0]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 3.23, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 3.23, 1]}]}