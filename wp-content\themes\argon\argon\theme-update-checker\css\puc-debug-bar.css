.puc-debug-bar-panel-v4 pre {
	margin-top: 0;
}

/* Style the debug data table to match "widefat" table style used by WordPress. */
table.puc-debug-data {
    width: 100%;
    clear: both;
    margin: 0;

    border-spacing: 0;
    background-color: #f9f9f9;

    border-radius: 3px;
    border: 1px solid #dfdfdf;
    border-collapse: separate;
}

table.puc-debug-data * {
    word-wrap: break-word;
}

table.puc-debug-data th {
	width: 11em;
    padding: 7px 7px 8px;
    text-align: left;

    font-family: "Georgia", "Times New Roman", "Bitstream Charter", "Times", serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.3em;
    text-shadow: rgba(255, 255, 255, 0.804) 0 1px 0;
}

table.puc-debug-data td, table.puc-debug-data th {
    border-width: 1px 0;
    border-style: solid;

    border-top-color: #fff;
    border-bottom-color: #dfdfdf;

    text-transform: none;
}

table.puc-debug-data td {
    color: #555;
    font-size: 12px;
    padding: 4px 7px 2px;
    vertical-align: top;
}

.puc-ajax-response {
	border: 1px solid #dfdfdf;
	border-radius: 3px;
	padding: 0.5em;
	margin: 5px 0;
	background-color: white;
}

.puc-ajax-nonce {
	display: none;
}

.puc-ajax-response dt {
	margin: 0;
}

.puc-ajax-response dd {
	margin: 0 0 1em;
}
