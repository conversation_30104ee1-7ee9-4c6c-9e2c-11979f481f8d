{"Version": 3, "Meta": {"Duration": 3.27, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 115, "TotalSegmentCount": 683, "TotalPointCount": 2032, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.133, 0, 0.267, 21, 0.4, 21, 1, 0.767, 21, 1.133, 21, 1.5, 21, 1, 1.667, 21, 1.833, -7, 2, -7, 0, 3.267, -7]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.133, -1, 0.267, 21, 0.4, 21, 1, 0.767, 21, 1.133, 21, 1.5, 21, 1, 1.667, 21, 1.833, -19, 2, -19, 0, 3.267, -19]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -0.152, 1, 0.133, -1.331, 0.267, -9, 0.4, -9, 1, 0.767, -9, 1.133, -9, 1.5, -9, 0, 3.267, -9]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.133, 0, 0.267, -10, 0.4, -10, 1, 0.767, -10, 1.133, -10, 1.5, -10, 1, 1.667, -10, 1.833, 0, 2, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.133, 0, 0.267, -6, 0.4, -6, 1, 0.767, -6, 1.133, -6, 1.5, -6, 1, 1.667, -6, 1.833, 9, 2, 9, 0, 3.267, 9]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.456, 0, 0.478, 30, 0.5, 30, 1, 0.522, 30, 0.544, 30, 0.567, 30, 1, 0.589, 30, 0.611, -30, 0.633, -30, 1, 0.656, -30, 0.678, -30, 0.7, -30, 1, 0.733, -30, 0.767, 30, 0.8, 30, 1, 0.822, 30, 0.844, 30, 0.867, 30, 1, 0.933, 30, 1, -12.224, 1.067, -12.224, 1, 1.144, -12.224, 1.222, 4.773, 1.3, 4.773, 1, 1.378, 4.773, 1.456, -1.879, 1.533, -1.879, 1, 1.611, -1.879, 1.689, 0.738, 1.767, 0.738, 1, 1.833, 0.738, 1.9, -0.293, 1.967, -0.293, 1, 2.022, -0.293, 2.078, 30, 2.133, 30, 1, 2.156, 30, 2.178, 30, 2.2, 30, 1, 2.222, 30, 2.244, -30, 2.267, -30, 1, 2.289, -30, 2.311, -30, 2.333, -30, 1, 2.367, -30, 2.4, 30, 2.433, 30, 1, 2.456, 30, 2.478, 30, 2.5, 30, 1, 2.567, 30, 2.633, -12.227, 2.7, -12.227, 1, 2.778, -12.227, 2.856, 4.774, 2.933, 4.774, 1, 3.011, 4.774, 3.089, -1.879, 3.167, -1.879, 1, 3.2, -1.879, 3.233, -1.399, 3.267, -0.849]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.133, 1, 0.267, 1, 0.4, 1, 1, 0.444, 1, 0.489, 0, 0.533, 0, 1, 0.578, 0, 0.622, 1, 0.667, 1, 1, 1.122, 1, 1.578, 1, 2.033, 1, 1, 2.078, 1, 2.122, 0, 2.167, 0, 1, 2.211, 0, 2.256, 1, 2.3, 1, 0, 3.267, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.133, 1, 0.267, 1, 0.4, 1, 1, 0.444, 1, 0.489, 0, 0.533, 0, 1, 0.578, 0, 0.622, 1, 0.667, 1, 1, 1.122, 1, 1.578, 1, 2.033, 1, 1, 2.078, 1, 2.122, 0, 2.167, 0, 1, 2.211, 0, 2.256, 1, 2.3, 1, 0, 3.267, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.3, 0, 3.267, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.3, 0, 3.267, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.8, 1, 0.133, -0.8, 0.267, 0.2, 0.4, 0.2, 1, 0.756, 0.2, 1.111, 0.2, 1.467, 0.2, 1, 1.6, 0.2, 1.733, 0.4, 1.867, 0.4, 0, 3.267, 0.4]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.756, 0, 1.111, 0, 1.467, 0, 1, 1.6, 0, 1.733, 0.5, 1.867, 0.5, 0, 3.267, 0.5]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.156, 0, 0.311, 1, 0.467, 1, 1, 0.878, 1, 1.289, 1, 1.7, 1, 1, 2.044, 1, 2.389, 1, 2.733, 1, 0, 3.267, 1]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.156, 0, 0.311, -8, 0.467, -8, 1, 0.878, -8, 1.289, -8, 1.7, -8, 1, 2.044, -8, 2.389, -6, 2.733, -6, 0, 3.267, -6]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 3.267, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 6.224, 0.267, 6.224, 1, 0.378, 6.224, 0.489, -5.156, 0.6, -5.156, 1, 0.711, -5.156, 0.822, -1.219, 0.933, -1.219, 1, 1.056, -1.219, 1.178, -2.022, 1.3, -2.022, 1, 1.378, -2.022, 1.456, -1.887, 1.533, -1.887, 1, 1.6, -1.887, 1.667, -7.618, 1.733, -7.618, 1, 1.844, -7.618, 1.956, 6.82, 2.067, 6.82, 1, 2.178, 6.82, 2.289, 2.023, 2.4, 2.023, 1, 2.522, 2.023, 2.644, 2.994, 2.767, 2.994, 1, 2.889, 2.994, 3.011, 2.793, 3.133, 2.793, 1, 3.178, 2.793, 3.222, 2.799, 3.267, 2.806]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.178, 0, 0.322, -4.748, 0.467, -4.748, 1, 0.567, -4.748, 0.667, -3.165, 0.767, -3.165, 1, 0.867, -3.165, 0.967, -3.894, 1.067, -3.894, 1, 1.167, -3.894, 1.267, -3.561, 1.367, -3.561, 1, 1.422, -3.561, 1.478, -3.648, 1.533, -3.648, 1, 1.667, -3.648, 1.8, 3.37, 1.933, 3.37, 1, 2.044, 3.37, 2.156, 0.749, 2.267, 0.749, 1, 2.367, 0.749, 2.467, 1.949, 2.567, 1.949, 1, 2.667, 1.949, 2.767, 1.398, 2.867, 1.398, 1, 2.967, 1.398, 3.067, 1.649, 3.167, 1.649, 1, 3.2, 1.649, 3.233, 1.639, 3.267, 1.624]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, -30, 0, 0.467, -30, 1, 0.633, -30, 0.8, 30, 0.967, 30, 1, 1.167, 30, 1.367, -30, 1.567, -30, 1, 1.733, -30, 1.9, 30, 2.067, 30, 1, 2.267, 30, 2.467, -30, 2.667, -30, 1, 2.833, -30, 3, 30, 3.167, 30, 1, 3.2, 30, 3.233, 27.6, 3.267, 23.76]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0.034, 1, 0.011, 0.051, 0.022, 0.068, 0.033, 0.068, 1, 0.133, 0.068, 0.233, -5.86, 0.333, -5.86, 1, 0.433, -5.86, 0.533, -0.659, 0.633, -0.659, 1, 0.756, -0.659, 0.878, -3.046, 1, -3.046, 1, 1.122, -3.046, 1.244, -2.36, 1.367, -2.36, 1, 1.422, -2.36, 1.478, -2.474, 1.533, -2.474, 1, 1.633, -2.474, 1.733, 9.045, 1.833, 9.045, 1, 1.933, 9.045, 2.033, 1.262, 2.133, 1.262, 1, 2.256, 1.262, 2.378, 4.49, 2.5, 4.49, 1, 2.622, 4.49, 2.744, 3.563, 2.867, 3.563, 1, 2.989, 3.563, 3.111, 3.829, 3.233, 3.829, 1, 3.244, 3.829, 3.256, 3.829, 3.267, 3.828]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.034, 1, 0.011, 0.051, 0.022, 0.068, 0.033, 0.068, 1, 0.133, 0.068, 0.233, -5.86, 0.333, -5.86, 1, 0.433, -5.86, 0.533, -0.659, 0.633, -0.659, 1, 0.756, -0.659, 0.878, -3.046, 1, -3.046, 1, 1.122, -3.046, 1.244, -2.36, 1.367, -2.36, 1, 1.422, -2.36, 1.478, -2.474, 1.533, -2.474, 1, 1.633, -2.474, 1.733, 9.045, 1.833, 9.045, 1, 1.933, 9.045, 2.033, 1.262, 2.133, 1.262, 1, 2.256, 1.262, 2.378, 4.49, 2.5, 4.49, 1, 2.622, 4.49, 2.744, 3.563, 2.867, 3.563, 1, 2.989, 3.563, 3.111, 3.829, 3.233, 3.829, 1, 3.244, 3.829, 3.256, 3.829, 3.267, 3.828]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, 9.188, 0.233, 9.188, 1, 0.322, 9.188, 0.411, -16.267, 0.5, -16.267, 1, 0.6, -16.267, 0.7, 2.449, 0.8, 2.449, 1, 0.889, 2.449, 0.978, -5.583, 1.067, -5.583, 1, 1.167, -5.583, 1.267, -2.053, 1.367, -2.053, 1, 1.478, -2.053, 1.589, -12.627, 1.7, -12.627, 1, 1.789, -12.627, 1.878, 20.022, 1.967, 20.022, 1, 2.067, 20.022, 2.167, -1.794, 2.267, -1.794, 1, 2.367, -1.794, 2.467, 7.537, 2.567, 7.537, 1, 2.656, 7.537, 2.744, 3.452, 2.833, 3.452, 1, 2.933, 3.452, 3.033, 5.267, 3.133, 5.267, 1, 3.178, 5.267, 3.222, 5.067, 3.267, 4.866]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -1.698, 0.167, -1.698, 1, 0.244, -1.698, 0.322, 4.563, 0.4, 4.563, 1, 0.489, 4.563, 0.578, -6.349, 0.667, -6.349, 1, 0.756, -6.349, 0.844, 4.872, 0.933, 4.872, 1, 1.022, 4.872, 1.111, -3.116, 1.2, -3.116, 1, 1.3, -3.116, 1.4, 1.812, 1.5, 1.812, 1, 1.522, 1.812, 1.544, 1.538, 1.567, 1.538, 1, 1.589, 1.538, 1.611, 1.895, 1.633, 1.895, 1, 1.711, 1.895, 1.789, -5.709, 1.867, -5.709, 1, 1.956, -5.709, 2.044, 7.75, 2.133, 7.75, 1, 2.222, 7.75, 2.311, -5.652, 2.4, -5.652, 1, 2.489, -5.652, 2.578, 3.519, 2.667, 3.519, 1, 2.767, 3.519, 2.867, -2.089, 2.967, -2.089, 1, 3.056, -2.089, 3.144, 1.162, 3.233, 1.162, 1, 3.244, 1.162, 3.256, 1.14, 3.267, 1.101]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -1.698, 0.167, -1.698, 1, 0.244, -1.698, 0.322, 4.563, 0.4, 4.563, 1, 0.489, 4.563, 0.578, -6.349, 0.667, -6.349, 1, 0.756, -6.349, 0.844, 4.872, 0.933, 4.872, 1, 1.022, 4.872, 1.111, -3.116, 1.2, -3.116, 1, 1.3, -3.116, 1.4, 1.812, 1.5, 1.812, 1, 1.522, 1.812, 1.544, 1.538, 1.567, 1.538, 1, 1.589, 1.538, 1.611, 1.895, 1.633, 1.895, 1, 1.711, 1.895, 1.789, -5.709, 1.867, -5.709, 1, 1.956, -5.709, 2.044, 7.75, 2.133, 7.75, 1, 2.222, 7.75, 2.311, -5.652, 2.4, -5.652, 1, 2.489, -5.652, 2.578, 3.519, 2.667, 3.519, 1, 2.767, 3.519, 2.867, -2.089, 2.967, -2.089, 1, 3.056, -2.089, 3.144, 1.162, 3.233, 1.162, 1, 3.244, 1.162, 3.256, 1.14, 3.267, 1.101]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.178, 0, 0.322, -1.72, 0.467, -1.72, 1, 0.567, -1.72, 0.667, -1.134, 0.767, -1.134, 1, 0.867, -1.134, 0.967, -1.404, 1.067, -1.404, 1, 1.178, -1.404, 1.289, -1.281, 1.4, -1.281, 1, 1.444, -1.281, 1.489, -1.309, 1.533, -1.309, 1, 1.667, -1.309, 1.8, 1.223, 1.933, 1.223, 1, 2.044, 1.223, 2.156, 0.261, 2.267, 0.261, 1, 2.367, 0.261, 2.467, 0.705, 2.567, 0.705, 1, 2.678, 0.705, 2.789, 0.502, 2.9, 0.502, 1, 3, 0.502, 3.1, 0.595, 3.2, 0.595, 1, 3.222, 0.595, 3.244, 0.593, 3.267, 0.59]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.533, 0.3, 0.533, 1, 0.4, 0.533, 0.5, -0.883, 0.6, -0.883, 1, 0.711, -0.883, 0.822, 0.685, 0.933, 0.685, 1, 1.044, 0.685, 1.156, -0.437, 1.267, -0.437, 1, 1.367, -0.437, 1.467, 0.235, 1.567, 0.235, 1, 1.644, 0.235, 1.722, -0.87, 1.8, -0.87, 1, 1.9, -0.87, 2, 1.372, 2.1, 1.372, 1, 2.211, 1.372, 2.322, -1.097, 2.433, -1.097, 1, 2.544, -1.097, 2.656, 0.698, 2.767, 0.698, 1, 2.867, 0.698, 2.967, -0.405, 3.067, -0.405, 1, 3.133, -0.405, 3.2, -0.178, 3.267, 0.003]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.178, 0, 0.322, -2.389, 0.467, -2.389, 1, 0.567, -2.389, 0.667, -1.575, 0.767, -1.575, 1, 0.867, -1.575, 0.967, -1.95, 1.067, -1.95, 1, 1.178, -1.95, 1.289, -1.779, 1.4, -1.779, 1, 1.444, -1.779, 1.489, -1.817, 1.533, -1.817, 1, 1.667, -1.817, 1.8, 1.699, 1.933, 1.699, 1, 2.044, 1.699, 2.156, 0.362, 2.267, 0.362, 1, 2.367, 0.362, 2.467, 0.979, 2.567, 0.979, 1, 2.678, 0.979, 2.789, 0.697, 2.9, 0.697, 1, 3, 0.697, 3.1, 0.826, 3.2, 0.826, 1, 3.222, 0.826, 3.244, 0.823, 3.267, 0.819]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.178, 0, 0.322, -2.389, 0.467, -2.389, 1, 0.567, -2.389, 0.667, -1.575, 0.767, -1.575, 1, 0.867, -1.575, 0.967, -1.95, 1.067, -1.95, 1, 1.178, -1.95, 1.289, -1.779, 1.4, -1.779, 1, 1.444, -1.779, 1.489, -1.817, 1.533, -1.817, 1, 1.667, -1.817, 1.8, 1.699, 1.933, 1.699, 1, 2.044, 1.699, 2.156, 0.362, 2.267, 0.362, 1, 2.367, 0.362, 2.467, 0.979, 2.567, 0.979, 1, 2.678, 0.979, 2.789, 0.697, 2.9, 0.697, 1, 3, 0.697, 3.1, 0.826, 3.2, 0.826, 1, 3.222, 0.826, 3.244, 0.823, 3.267, 0.819]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.178, 0, 0.322, -2.389, 0.467, -2.389, 1, 0.567, -2.389, 0.667, -1.575, 0.767, -1.575, 1, 0.867, -1.575, 0.967, -1.95, 1.067, -1.95, 1, 1.178, -1.95, 1.289, -1.779, 1.4, -1.779, 1, 1.444, -1.779, 1.489, -1.817, 1.533, -1.817, 1, 1.667, -1.817, 1.8, 1.699, 1.933, 1.699, 1, 2.044, 1.699, 2.156, 0.362, 2.267, 0.362, 1, 2.367, 0.362, 2.467, 0.979, 2.567, 0.979, 1, 2.678, 0.979, 2.789, 0.697, 2.9, 0.697, 1, 3, 0.697, 3.1, 0.826, 3.2, 0.826, 1, 3.222, 0.826, 3.244, 0.823, 3.267, 0.819]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.178, 0, 0.322, -1.72, 0.467, -1.72, 1, 0.567, -1.72, 0.667, -1.134, 0.767, -1.134, 1, 0.867, -1.134, 0.967, -1.404, 1.067, -1.404, 1, 1.178, -1.404, 1.289, -1.281, 1.4, -1.281, 1, 1.444, -1.281, 1.489, -1.309, 1.533, -1.309, 1, 1.667, -1.309, 1.8, 1.223, 1.933, 1.223, 1, 2.044, 1.223, 2.156, 0.261, 2.267, 0.261, 1, 2.367, 0.261, 2.467, 0.705, 2.567, 0.705, 1, 2.678, 0.705, 2.789, 0.502, 2.9, 0.502, 1, 3, 0.502, 3.1, 0.595, 3.2, 0.595, 1, 3.222, 0.595, 3.244, 0.593, 3.267, 0.59]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 0.533, 0.3, 0.533, 1, 0.4, 0.533, 0.5, -0.883, 0.6, -0.883, 1, 0.711, -0.883, 0.822, 0.685, 0.933, 0.685, 1, 1.044, 0.685, 1.156, -0.437, 1.267, -0.437, 1, 1.367, -0.437, 1.467, 0.235, 1.567, 0.235, 1, 1.644, 0.235, 1.722, -0.87, 1.8, -0.87, 1, 1.9, -0.87, 2, 1.372, 2.1, 1.372, 1, 2.211, 1.372, 2.322, -1.097, 2.433, -1.097, 1, 2.544, -1.097, 2.656, 0.698, 2.767, 0.698, 1, 2.867, 0.698, 2.967, -0.405, 3.067, -0.405, 1, 3.133, -0.405, 3.2, -0.178, 3.267, 0.003]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.178, 0, 0.322, -2.389, 0.467, -2.389, 1, 0.567, -2.389, 0.667, -1.575, 0.767, -1.575, 1, 0.867, -1.575, 0.967, -1.95, 1.067, -1.95, 1, 1.178, -1.95, 1.289, -1.779, 1.4, -1.779, 1, 1.444, -1.779, 1.489, -1.817, 1.533, -1.817, 1, 1.667, -1.817, 1.8, 1.699, 1.933, 1.699, 1, 2.044, 1.699, 2.156, 0.362, 2.267, 0.362, 1, 2.367, 0.362, 2.467, 0.979, 2.567, 0.979, 1, 2.678, 0.979, 2.789, 0.697, 2.9, 0.697, 1, 3, 0.697, 3.1, 0.826, 3.2, 0.826, 1, 3.222, 0.826, 3.244, 0.823, 3.267, 0.819]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.189, 0, 0.344, -0.904, 0.5, -0.904, 1, 0.622, -0.904, 0.744, -0.691, 0.867, -0.691, 1, 0.989, -0.691, 1.111, -0.744, 1.233, -0.744, 1, 1.478, -0.744, 1.722, 0.583, 1.967, 0.583, 1, 2.1, 0.583, 2.233, 0.248, 2.367, 0.248, 1, 2.489, 0.248, 2.611, 0.331, 2.733, 0.331, 1, 2.856, 0.331, 2.978, 0.31, 3.1, 0.31, 1, 3.156, 0.31, 3.211, 0.311, 3.267, 0.312]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0.794, 0.333, 0.794, 1, 0.444, 0.794, 0.556, -0.592, 0.667, -0.592, 1, 0.789, -0.592, 0.911, 0.245, 1.033, 0.245, 1, 1.156, 0.245, 1.278, -0.085, 1.4, -0.085, 1, 1.444, -0.085, 1.489, -0.046, 1.533, -0.046, 1, 1.633, -0.046, 1.733, -1.202, 1.833, -1.202, 1, 1.944, -1.202, 2.056, 0.896, 2.167, 0.896, 1, 2.289, 0.896, 2.411, -0.376, 2.533, -0.376, 1, 2.656, -0.376, 2.778, 0.132, 2.9, 0.132, 1, 3.022, 0.132, 3.144, -0.042, 3.267, -0.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.189, 0, 0.311, 0.796, 0.433, 0.796, 1, 0.556, 0.796, 0.678, -0.769, 0.8, -0.769, 1, 0.922, -0.769, 1.044, 0.423, 1.167, 0.423, 1, 1.3, 0.423, 1.433, -0.189, 1.567, -0.189, 1, 1.578, -0.189, 1.589, -0.187, 1.6, -0.187, 1, 1.711, -0.187, 1.822, -1.106, 1.933, -1.106, 1, 2.056, -1.106, 2.178, 1.14, 2.3, 1.14, 1, 2.422, 1.14, 2.544, -0.642, 2.667, -0.642, 1, 2.789, -0.642, 2.911, 0.283, 3.033, 0.283, 1, 3.111, 0.283, 3.189, 0.149, 3.267, 0.037]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.233, 0, 0.4, 0.822, 0.567, 0.822, 1, 0.689, 0.822, 0.811, -0.969, 0.933, -0.969, 1, 1.067, -0.969, 1.2, 0.657, 1.333, 0.657, 1, 1.578, 0.657, 1.822, -1.052, 2.067, -1.052, 1, 2.189, -1.052, 2.311, 1.396, 2.433, 1.396, 1, 2.567, 1.396, 2.7, -0.968, 2.833, -0.968, 1, 2.956, -0.968, 3.078, 0.519, 3.2, 0.519, 1, 3.222, 0.519, 3.244, 0.494, 3.267, 0.453]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.189, 0, 0.344, -2.26, 0.5, -2.26, 1, 0.622, -2.26, 0.744, -1.726, 0.867, -1.726, 1, 0.989, -1.726, 1.111, -1.859, 1.233, -1.859, 1, 1.478, -1.859, 1.722, 1.457, 1.967, 1.457, 1, 2.1, 1.457, 2.233, 0.619, 2.367, 0.619, 1, 2.489, 0.619, 2.611, 0.827, 2.733, 0.827, 1, 2.856, 0.827, 2.978, 0.775, 3.1, 0.775, 1, 3.156, 0.775, 3.211, 0.777, 3.267, 0.78]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0.794, 0.333, 0.794, 1, 0.444, 0.794, 0.556, -0.592, 0.667, -0.592, 1, 0.789, -0.592, 0.911, 0.245, 1.033, 0.245, 1, 1.156, 0.245, 1.278, -0.085, 1.4, -0.085, 1, 1.444, -0.085, 1.489, -0.046, 1.533, -0.046, 1, 1.633, -0.046, 1.733, -1.202, 1.833, -1.202, 1, 1.944, -1.202, 2.056, 0.896, 2.167, 0.896, 1, 2.289, 0.896, 2.411, -0.376, 2.533, -0.376, 1, 2.656, -0.376, 2.778, 0.132, 2.9, 0.132, 1, 3.022, 0.132, 3.144, -0.042, 3.267, -0.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.189, 0, 0.311, 0.796, 0.433, 0.796, 1, 0.556, 0.796, 0.678, -0.769, 0.8, -0.769, 1, 0.922, -0.769, 1.044, 0.423, 1.167, 0.423, 1, 1.3, 0.423, 1.433, -0.189, 1.567, -0.189, 1, 1.578, -0.189, 1.589, -0.187, 1.6, -0.187, 1, 1.711, -0.187, 1.822, -1.106, 1.933, -1.106, 1, 2.056, -1.106, 2.178, 1.14, 2.3, 1.14, 1, 2.422, 1.14, 2.544, -0.642, 2.667, -0.642, 1, 2.789, -0.642, 2.911, 0.283, 3.033, 0.283, 1, 3.111, 0.283, 3.189, 0.149, 3.267, 0.037]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.189, 0, 0.344, -0.904, 0.5, -0.904, 1, 0.622, -0.904, 0.744, -0.691, 0.867, -0.691, 1, 0.989, -0.691, 1.111, -0.744, 1.233, -0.744, 1, 1.478, -0.744, 1.722, 0.583, 1.967, 0.583, 1, 2.1, 0.583, 2.233, 0.248, 2.367, 0.248, 1, 2.489, 0.248, 2.611, 0.331, 2.733, 0.331, 1, 2.856, 0.331, 2.978, 0.31, 3.1, 0.31, 1, 3.156, 0.31, 3.211, 0.311, 3.267, 0.312]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0.794, 0.333, 0.794, 1, 0.444, 0.794, 0.556, -0.592, 0.667, -0.592, 1, 0.789, -0.592, 0.911, 0.245, 1.033, 0.245, 1, 1.156, 0.245, 1.278, -0.085, 1.4, -0.085, 1, 1.444, -0.085, 1.489, -0.046, 1.533, -0.046, 1, 1.633, -0.046, 1.733, -1.202, 1.833, -1.202, 1, 1.944, -1.202, 2.056, 0.896, 2.167, 0.896, 1, 2.289, 0.896, 2.411, -0.376, 2.533, -0.376, 1, 2.656, -0.376, 2.778, 0.132, 2.9, 0.132, 1, 3.022, 0.132, 3.144, -0.042, 3.267, -0.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.189, 0, 0.311, 0.796, 0.433, 0.796, 1, 0.556, 0.796, 0.678, -0.769, 0.8, -0.769, 1, 0.922, -0.769, 1.044, 0.423, 1.167, 0.423, 1, 1.3, 0.423, 1.433, -0.189, 1.567, -0.189, 1, 1.578, -0.189, 1.589, -0.187, 1.6, -0.187, 1, 1.711, -0.187, 1.822, -1.106, 1.933, -1.106, 1, 2.056, -1.106, 2.178, 1.14, 2.3, 1.14, 1, 2.422, 1.14, 2.544, -0.642, 2.667, -0.642, 1, 2.789, -0.642, 2.911, 0.283, 3.033, 0.283, 1, 3.111, 0.283, 3.189, 0.149, 3.267, 0.037]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.233, 0, 0.4, 0.822, 0.567, 0.822, 1, 0.689, 0.822, 0.811, -0.969, 0.933, -0.969, 1, 1.067, -0.969, 1.2, 0.657, 1.333, 0.657, 1, 1.578, 0.657, 1.822, -1.052, 2.067, -1.052, 1, 2.189, -1.052, 2.311, 1.396, 2.433, 1.396, 1, 2.567, 1.396, 2.7, -0.968, 2.833, -0.968, 1, 2.956, -0.968, 3.078, 0.519, 3.2, 0.519, 1, 3.222, 0.519, 3.244, 0.494, 3.267, 0.453]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.189, 0, 0.344, -2.26, 0.5, -2.26, 1, 0.622, -2.26, 0.744, -1.726, 0.867, -1.726, 1, 0.989, -1.726, 1.111, -1.859, 1.233, -1.859, 1, 1.478, -1.859, 1.722, 1.457, 1.967, 1.457, 1, 2.1, 1.457, 2.233, 0.619, 2.367, 0.619, 1, 2.489, 0.619, 2.611, 0.827, 2.733, 0.827, 1, 2.856, 0.827, 2.978, 0.775, 3.1, 0.775, 1, 3.156, 0.775, 3.211, 0.777, 3.267, 0.78]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0.794, 0.333, 0.794, 1, 0.444, 0.794, 0.556, -0.592, 0.667, -0.592, 1, 0.789, -0.592, 0.911, 0.245, 1.033, 0.245, 1, 1.156, 0.245, 1.278, -0.085, 1.4, -0.085, 1, 1.444, -0.085, 1.489, -0.046, 1.533, -0.046, 1, 1.633, -0.046, 1.733, -1.202, 1.833, -1.202, 1, 1.944, -1.202, 2.056, 0.896, 2.167, 0.896, 1, 2.289, 0.896, 2.411, -0.376, 2.533, -0.376, 1, 2.656, -0.376, 2.778, 0.132, 2.9, 0.132, 1, 3.022, 0.132, 3.144, -0.042, 3.267, -0.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.189, 0, 0.311, 0.796, 0.433, 0.796, 1, 0.556, 0.796, 0.678, -0.769, 0.8, -0.769, 1, 0.922, -0.769, 1.044, 0.423, 1.167, 0.423, 1, 1.3, 0.423, 1.433, -0.189, 1.567, -0.189, 1, 1.578, -0.189, 1.589, -0.187, 1.6, -0.187, 1, 1.711, -0.187, 1.822, -1.106, 1.933, -1.106, 1, 2.056, -1.106, 2.178, 1.14, 2.3, 1.14, 1, 2.422, 1.14, 2.544, -0.642, 2.667, -0.642, 1, 2.789, -0.642, 2.911, 0.283, 3.033, 0.283, 1, 3.111, 0.283, 3.189, 0.149, 3.267, 0.037]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.189, 0, 0.344, -2.26, 0.5, -2.26, 1, 0.622, -2.26, 0.744, -1.726, 0.867, -1.726, 1, 0.989, -1.726, 1.111, -1.859, 1.233, -1.859, 1, 1.478, -1.859, 1.722, 1.457, 1.967, 1.457, 1, 2.1, 1.457, 2.233, 0.619, 2.367, 0.619, 1, 2.489, 0.619, 2.611, 0.827, 2.733, 0.827, 1, 2.856, 0.827, 2.978, 0.775, 3.1, 0.775, 1, 3.156, 0.775, 3.211, 0.777, 3.267, 0.78]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0.794, 0.333, 0.794, 1, 0.444, 0.794, 0.556, -0.592, 0.667, -0.592, 1, 0.789, -0.592, 0.911, 0.245, 1.033, 0.245, 1, 1.156, 0.245, 1.278, -0.085, 1.4, -0.085, 1, 1.444, -0.085, 1.489, -0.046, 1.533, -0.046, 1, 1.633, -0.046, 1.733, -1.202, 1.833, -1.202, 1, 1.944, -1.202, 2.056, 0.896, 2.167, 0.896, 1, 2.289, 0.896, 2.411, -0.376, 2.533, -0.376, 1, 2.656, -0.376, 2.778, 0.132, 2.9, 0.132, 1, 3.022, 0.132, 3.144, -0.042, 3.267, -0.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.189, 0, 0.311, 0.796, 0.433, 0.796, 1, 0.556, 0.796, 0.678, -0.769, 0.8, -0.769, 1, 0.922, -0.769, 1.044, 0.423, 1.167, 0.423, 1, 1.3, 0.423, 1.433, -0.189, 1.567, -0.189, 1, 1.578, -0.189, 1.589, -0.187, 1.6, -0.187, 1, 1.711, -0.187, 1.822, -1.106, 1.933, -1.106, 1, 2.056, -1.106, 2.178, 1.14, 2.3, 1.14, 1, 2.422, 1.14, 2.544, -0.642, 2.667, -0.642, 1, 2.789, -0.642, 2.911, 0.283, 3.033, 0.283, 1, 3.111, 0.283, 3.189, 0.149, 3.267, 0.037]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.189, 0, 0.344, -2.26, 0.5, -2.26, 1, 0.622, -2.26, 0.744, -1.726, 0.867, -1.726, 1, 0.989, -1.726, 1.111, -1.859, 1.233, -1.859, 1, 1.478, -1.859, 1.722, 1.457, 1.967, 1.457, 1, 2.1, 1.457, 2.233, 0.619, 2.367, 0.619, 1, 2.489, 0.619, 2.611, 0.827, 2.733, 0.827, 1, 2.856, 0.827, 2.978, 0.775, 3.1, 0.775, 1, 3.156, 0.775, 3.211, 0.777, 3.267, 0.78]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0.794, 0.333, 0.794, 1, 0.444, 0.794, 0.556, -0.592, 0.667, -0.592, 1, 0.789, -0.592, 0.911, 0.245, 1.033, 0.245, 1, 1.156, 0.245, 1.278, -0.085, 1.4, -0.085, 1, 1.444, -0.085, 1.489, -0.046, 1.533, -0.046, 1, 1.633, -0.046, 1.733, -1.202, 1.833, -1.202, 1, 1.944, -1.202, 2.056, 0.896, 2.167, 0.896, 1, 2.289, 0.896, 2.411, -0.376, 2.533, -0.376, 1, 2.656, -0.376, 2.778, 0.132, 2.9, 0.132, 1, 3.022, 0.132, 3.144, -0.042, 3.267, -0.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.189, 0, 0.311, 0.796, 0.433, 0.796, 1, 0.556, 0.796, 0.678, -0.769, 0.8, -0.769, 1, 0.922, -0.769, 1.044, 0.423, 1.167, 0.423, 1, 1.3, 0.423, 1.433, -0.189, 1.567, -0.189, 1, 1.578, -0.189, 1.589, -0.187, 1.6, -0.187, 1, 1.711, -0.187, 1.822, -1.106, 1.933, -1.106, 1, 2.056, -1.106, 2.178, 1.14, 2.3, 1.14, 1, 2.422, 1.14, 2.544, -0.642, 2.667, -0.642, 1, 2.789, -0.642, 2.911, 0.283, 3.033, 0.283, 1, 3.111, 0.283, 3.189, 0.149, 3.267, 0.037]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0.028, 1, 0.011, 0.043, 0.022, 0.057, 0.033, 0.057, 1, 0.133, 0.057, 0.233, -4.884, 0.333, -4.884, 1, 0.433, -4.884, 0.533, -0.549, 0.633, -0.549, 1, 0.756, -0.549, 0.878, -2.538, 1, -2.538, 1, 1.122, -2.538, 1.244, -1.967, 1.367, -1.967, 1, 1.422, -1.967, 1.478, -2.062, 1.533, -2.062, 1, 1.633, -2.062, 1.733, 7.537, 1.833, 7.537, 1, 1.933, 7.537, 2.033, 1.052, 2.133, 1.052, 1, 2.256, 1.052, 2.378, 3.741, 2.5, 3.741, 1, 2.622, 3.741, 2.744, 2.969, 2.867, 2.969, 1, 2.989, 2.969, 3.111, 3.191, 3.233, 3.191, 1, 3.244, 3.191, 3.256, 3.191, 3.267, 3.19]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.062, 0.033, -0.062, 1, 0.1, -0.062, 0.167, 2.733, 0.233, 2.733, 1, 0.333, 2.733, 0.433, -3.539, 0.533, -3.539, 1, 0.633, -3.539, 0.733, 2.224, 0.833, 2.224, 1, 0.944, 2.224, 1.056, -1.062, 1.167, -1.062, 1, 1.278, -1.062, 1.389, 0.435, 1.5, 0.435, 1, 1.578, 0.435, 1.656, -5.148, 1.733, -5.148, 1, 1.822, -5.148, 1.911, 5.779, 2, 5.779, 1, 2.111, 5.779, 2.222, -3.269, 2.333, -3.269, 1, 2.444, -3.269, 2.556, 1.511, 2.667, 1.511, 1, 2.778, 1.511, 2.889, -0.638, 3, -0.638, 1, 3.089, -0.638, 3.178, -0.178, 3.267, 0.073]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0.122, 1, 0.011, 0.121, 0.022, 0.119, 0.033, 0.119, 1, 0.133, 0.119, 0.233, 2.101, 0.333, 2.101, 1, 0.433, 2.101, 0.533, -3.001, 0.633, -3.001, 1, 0.744, -3.001, 0.856, 2.384, 0.967, 2.384, 1, 1.067, 2.384, 1.167, -1.439, 1.267, -1.439, 1, 1.367, -1.439, 1.467, 0.628, 1.567, 0.628, 1, 1.656, 0.628, 1.744, -4.091, 1.833, -4.091, 1, 1.933, -4.091, 2.033, 5.04, 2.133, 5.04, 1, 2.233, 5.04, 2.333, -3.679, 2.433, -3.679, 1, 2.544, -3.679, 2.656, 2.139, 2.767, 2.139, 1, 2.878, 2.139, 2.989, -1.042, 3.1, -1.042, 1, 3.156, -1.042, 3.211, -0.667, 3.267, -0.292]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, -0.125, 1, 0.022, -0.125, 0.044, -0.141, 0.067, -0.141, 1, 0.178, -0.141, 0.289, 1.58, 0.4, 1.58, 1, 0.5, 1.58, 0.6, -2.821, 0.7, -2.821, 1, 0.811, -2.821, 0.922, 2.823, 1.033, 2.823, 1, 1.133, 2.823, 1.233, -2.016, 1.333, -2.016, 1, 1.433, -2.016, 1.533, 0.897, 1.633, 0.897, 1, 1.722, 0.897, 1.811, -3.465, 1.9, -3.465, 1, 2, -3.465, 2.1, 5.02, 2.2, 5.02, 1, 2.311, 5.02, 2.422, -4.506, 2.533, -4.506, 1, 2.633, -4.506, 2.733, 3.066, 2.833, 3.066, 1, 2.944, 3.066, 3.056, -1.661, 3.167, -1.661, 1, 3.2, -1.661, 3.233, -1.441, 3.267, -1.132]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.1, 0, 0.133, -0.062, 0.167, -0.062, 1, 0.267, -0.062, 0.367, 1.689, 0.467, 1.689, 1, 0.578, 1.689, 0.689, -3.552, 0.8, -3.552, 1, 0.9, -3.552, 1, 4.104, 1.1, 4.104, 1, 1.2, 4.104, 1.3, -3.291, 1.4, -3.291, 1, 1.5, -3.291, 1.6, 1.978, 1.7, 1.978, 1, 1.8, 1.978, 1.9, -4.181, 2, -4.181, 1, 2.1, -4.181, 2.2, 6.608, 2.3, 6.608, 1, 2.4, 6.608, 2.5, -6.774, 2.6, -6.774, 1, 2.7, -6.774, 2.8, 5.118, 2.9, 5.118, 1, 3.011, 5.118, 3.122, -3.164, 3.233, -3.164, 1, 3.244, -3.164, 3.256, -3.104, 3.267, -2.998]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0.028, 1, 0.011, 0.043, 0.022, 0.057, 0.033, 0.057, 1, 0.133, 0.057, 0.233, -4.884, 0.333, -4.884, 1, 0.433, -4.884, 0.533, -0.549, 0.633, -0.549, 1, 0.756, -0.549, 0.878, -2.538, 1, -2.538, 1, 1.122, -2.538, 1.244, -1.967, 1.367, -1.967, 1, 1.422, -1.967, 1.478, -2.062, 1.533, -2.062, 1, 1.633, -2.062, 1.733, 7.537, 1.833, 7.537, 1, 1.933, 7.537, 2.033, 1.052, 2.133, 1.052, 1, 2.256, 1.052, 2.378, 3.741, 2.5, 3.741, 1, 2.622, 3.741, 2.744, 2.969, 2.867, 2.969, 1, 2.989, 2.969, 3.111, 3.191, 3.233, 3.191, 1, 3.244, 3.191, 3.256, 3.191, 3.267, 3.19]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.062, 0.033, -0.062, 1, 0.1, -0.062, 0.167, 2.733, 0.233, 2.733, 1, 0.333, 2.733, 0.433, -3.539, 0.533, -3.539, 1, 0.633, -3.539, 0.733, 2.224, 0.833, 2.224, 1, 0.944, 2.224, 1.056, -1.062, 1.167, -1.062, 1, 1.278, -1.062, 1.389, 0.435, 1.5, 0.435, 1, 1.578, 0.435, 1.656, -5.148, 1.733, -5.148, 1, 1.822, -5.148, 1.911, 5.779, 2, 5.779, 1, 2.111, 5.779, 2.222, -3.269, 2.333, -3.269, 1, 2.444, -3.269, 2.556, 1.511, 2.667, 1.511, 1, 2.778, 1.511, 2.889, -0.638, 3, -0.638, 1, 3.089, -0.638, 3.178, -0.178, 3.267, 0.073]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0.122, 1, 0.011, 0.121, 0.022, 0.119, 0.033, 0.119, 1, 0.133, 0.119, 0.233, 2.101, 0.333, 2.101, 1, 0.433, 2.101, 0.533, -3.001, 0.633, -3.001, 1, 0.744, -3.001, 0.856, 2.384, 0.967, 2.384, 1, 1.067, 2.384, 1.167, -1.439, 1.267, -1.439, 1, 1.367, -1.439, 1.467, 0.628, 1.567, 0.628, 1, 1.656, 0.628, 1.744, -4.091, 1.833, -4.091, 1, 1.933, -4.091, 2.033, 5.04, 2.133, 5.04, 1, 2.233, 5.04, 2.333, -3.679, 2.433, -3.679, 1, 2.544, -3.679, 2.656, 2.139, 2.767, 2.139, 1, 2.878, 2.139, 2.989, -1.042, 3.1, -1.042, 1, 3.156, -1.042, 3.211, -0.667, 3.267, -0.292]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, -0.125, 1, 0.022, -0.125, 0.044, -0.141, 0.067, -0.141, 1, 0.178, -0.141, 0.289, 1.58, 0.4, 1.58, 1, 0.5, 1.58, 0.6, -2.821, 0.7, -2.821, 1, 0.811, -2.821, 0.922, 2.823, 1.033, 2.823, 1, 1.133, 2.823, 1.233, -2.016, 1.333, -2.016, 1, 1.433, -2.016, 1.533, 0.897, 1.633, 0.897, 1, 1.722, 0.897, 1.811, -3.465, 1.9, -3.465, 1, 2, -3.465, 2.1, 5.02, 2.2, 5.02, 1, 2.311, 5.02, 2.422, -4.506, 2.533, -4.506, 1, 2.633, -4.506, 2.733, 3.066, 2.833, 3.066, 1, 2.944, 3.066, 3.056, -1.661, 3.167, -1.661, 1, 3.2, -1.661, 3.233, -1.441, 3.267, -1.132]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.1, 0, 0.133, -0.062, 0.167, -0.062, 1, 0.267, -0.062, 0.367, 1.689, 0.467, 1.689, 1, 0.578, 1.689, 0.689, -3.552, 0.8, -3.552, 1, 0.9, -3.552, 1, 4.104, 1.1, 4.104, 1, 1.2, 4.104, 1.3, -3.291, 1.4, -3.291, 1, 1.5, -3.291, 1.6, 1.978, 1.7, 1.978, 1, 1.8, 1.978, 1.9, -4.181, 2, -4.181, 1, 2.1, -4.181, 2.2, 6.608, 2.3, 6.608, 1, 2.4, 6.608, 2.5, -6.774, 2.6, -6.774, 1, 2.7, -6.774, 2.8, 5.118, 2.9, 5.118, 1, 3.011, 5.118, 3.122, -3.164, 3.233, -3.164, 1, 3.244, -3.164, 3.256, -3.104, 3.267, -2.998]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -3.081, 0.2, -3.081, 1, 0.289, -3.081, 0.378, 6.421, 0.467, 6.421, 1, 0.567, 6.421, 0.667, -3.627, 0.767, -3.627, 1, 0.889, -3.627, 1.011, 1.302, 1.133, 1.302, 1, 1.244, 1.302, 1.356, -0.389, 1.467, -0.389, 1, 1.544, -0.389, 1.622, 3.243, 1.7, 3.243, 1, 1.778, 3.243, 1.856, -7.543, 1.933, -7.543, 1, 2.033, -7.543, 2.133, 4.365, 2.233, 4.365, 1, 2.356, 4.365, 2.478, -1.576, 2.6, -1.576, 1, 2.711, -1.576, 2.822, 0.47, 2.933, 0.47, 1, 3.044, 0.47, 3.156, -0.023, 3.267, -0.113]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -2.589, 0.3, -2.589, 1, 0.4, -2.589, 0.5, 5.951, 0.6, 5.951, 1, 0.711, 5.951, 0.822, -4.418, 0.933, -4.418, 1, 1.044, -4.418, 1.156, 2.075, 1.267, 2.075, 1, 1.367, 2.075, 1.467, -0.635, 1.567, -0.635, 1, 1.644, -0.635, 1.722, 2.275, 1.8, 2.275, 1, 1.889, 2.275, 1.978, -6.733, 2.067, -6.733, 1, 2.178, -6.733, 2.289, 5.227, 2.4, 5.227, 1, 2.511, 5.227, 2.622, -2.48, 2.733, -2.48, 1, 2.856, -2.48, 2.978, 0.929, 3.1, 0.929, 1, 3.156, 0.929, 3.211, 0.62, 3.267, 0.312]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.178, 0, 0.289, -2.377, 0.4, -2.377, 1, 0.5, -2.377, 0.6, 5.934, 0.7, 5.934, 1, 0.822, 5.934, 0.944, -5.277, 1.067, -5.277, 1, 1.178, -5.277, 1.289, 2.976, 1.4, 2.976, 1, 1.5, 2.976, 1.6, -0.615, 1.7, -0.615, 1, 1.767, -0.615, 1.833, 1.481, 1.9, 1.481, 1, 1.989, 1.481, 2.078, -6.332, 2.167, -6.332, 1, 2.289, -6.332, 2.411, 6.093, 2.533, 6.093, 1, 2.644, 6.093, 2.756, -3.507, 2.867, -3.507, 1, 2.989, -3.507, 3.111, 1.577, 3.233, 1.577, 1, 3.244, 1.577, 3.256, 1.559, 3.267, 1.527]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "<PERSON><PERSON>_Skinning", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 3.27, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 3.27, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 3.27, 1]}]}