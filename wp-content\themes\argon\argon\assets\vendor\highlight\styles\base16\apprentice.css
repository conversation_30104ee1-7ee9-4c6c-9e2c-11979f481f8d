/*!
  Theme: Apprentice
  Author: romainl
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme apprentice
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #262626  Default Background
base01  #303030  Lighter Background (Used for status bars, line number and folding marks)
base02  #333333  Selection Background
base03  #6C6C6C  Comments, Invisibles, Line Highlighting
base04  #787878  Dark Foreground (Used for status bars)
base05  #BCBCBC  Default Foreground, Caret, Delimiters, Operators
base06  #C9C9C9  Light Foreground (Not often used)
base07  #FFFFFF  Light Background (Not often used)
base08  #5F8787  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #FF8700  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #5F8787  Classes, Markup Bold, Search Text Background
base0B  #87AF87  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #5F875F  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #FFFFAF  Functions, Methods, Attribute IDs, Headings
base0E  #87AFD7  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #5F87AF  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #BCBCBC;
  background: #262626;
}

.hljs::selection,
.hljs ::selection {
  background-color: #333333;
  color: #BCBCBC;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #6C6C6C -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #6C6C6C;
}

/* base04 - #787878 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #787878;
}

/* base05 - #BCBCBC -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #BCBCBC;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #5F8787;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #FF8700;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #5F8787;
}

.hljs-strong {
  font-weight:bold;
  color: #5F8787;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #87AF87;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #5F875F;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #FFFFAF;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #87AFD7;
}
.hljs-emphasis {
  color: #87AFD7;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #5F87AF;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
