{"name": "yahnis-elsts/plugin-update-checker", "type": "library", "description": "A custom update checker for WordPress plugins and themes. Useful if you can't host your plugin in the official WP repository but still want it to support automatic updates.", "keywords": ["wordpress", "plugin updates", "automatic updates", "theme updates"], "homepage": "https://github.com/YahnisElsts/plugin-update-checker/", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://w-shadow.com/", "role": "Developer"}], "require": {"php": ">=5.2.0"}, "autoload": {"files": ["load-v4p8.php"]}}