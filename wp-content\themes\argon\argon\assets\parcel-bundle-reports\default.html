
          <html>
            <head>
              <meta charset="utf-8">
              <title>📦Parcel Bundle Analyzer | default</title>
              <style>
                body {
                  margin: 0;
                }

                .tooltip {
                  background-color: rgba(255, 255, 255, 0.7);
                  left: 0;
                  padding: 20px;
                  pointer-events: none;
                  position: absolute;
                  top: 0;
                  transform: translate3d(0, 0, 0);
                }

                .tooltip-content {
                  font-family: monospace;
                }

                .tooltip-content dl div {
                  display: flex;
                }

                .tooltip-title {
                  font-size: 18px;
                }
              </style>
              <script>
                /**
 * Carrot Search FoamTree HTML5 (demo variant)
 * v3.4.9, bugfix/3.4.x/b84d33dd, build FOAMTREE-SOFTWARE4-DIST-70, Sep 10, 2019
 * 
 * Carrot Search confidential.
 * Copyright 2002-2019, Carrot Search s.c, All Rights Reserved.
 */
(function() {var v=function(){var a=window.navigator.userAgent,m;try{window.localStorage.setItem("ftap5caavc","ftap5caavc"),window.localStorage.removeItem("ftap5caavc"),m=!0}catch(k){m=!1}return{nf:function(){return/webkit/i.test(a)},mi:function(){return/Mac/.test(a)},lf:function(){return/iPad|iPod|iPhone/.test(a)},hf:function(){return/Android/.test(a)},ii:function(){return"ontouchstart"in window||!!window.DocumentTouch&&document instanceof window.DocumentTouch},hi:function(){return m},gi:function(){var a=document.createElement("canvas");
return!(!a.getContext||!a.getContext("2d"))},Dd:function(a,c){return[].forEach&&v.gi()?a&&a():c&&c()}}}();var ba=function(){function a(){return window.performance&&(window.performance.now||window.performance.mozNow||window.performance.msNow||window.performance.oNow||window.performance.webkitNow)||Date.now}var m=a();return{create:function(){return{now:function(){var k=a();return function(){return k.call(window.performance)}}()}},now:function(){return m.call(window.performance)}}}();function da(){function a(){if(!d)throw"AF0";var a=ba.now();0!==g&&(k.Kd=a-g);g=a;c=c.filter(function(a){return null!==a});k.frames++;for(var e=0;e<c.length;e++){var b=c[e];null!==b&&(!0===b.ye.call(b.Xg)?c[e]=null:D.Sc(b.repeat)&&(b.repeat=b.repeat-1,0>=b.repeat&&(c[e]=null)))}c=c.filter(function(a){return null!==a});d=!1;m();a=ba.now()-a;0!==a&&(k.Jd=a);k.totalTime+=a;k.Oe=1E3*k.frames/k.totalTime;g=0===c.length?0:ba.now()}function m(){0<c.length&&!d&&(d=!0,f(a))}var k=this.qg={frames:0,totalTime:0,
Jd:0,Kd:0,Oe:0};fa=k;var f=function(){return v.lf()?function(a){window.setTimeout(a,0)}:window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(){var a=ba.create();return function(e){var b=0;window.setTimeout(function(){var c=a.now();e();b=a.now()-c},16>b?16-b:0)}}()}(),c=[],d=!1,g=0;this.repeat=function(a,e,b){this.cancel(a);c.push({ye:a,Xg:b,repeat:e});m()};this.d=function(a,e){this.repeat(a,
1,e)};this.cancel=function(a){for(var e=0;e<c.length;e++){var b=c[e];null!==b&&b.ye===a&&(c[e]=null)}};this.k=function(){c=[]}}var fa;var ga=v.Dd(function(){function a(){this.buffer=[];this.oa=0;this.Gc=D.extend({},g)}function m(a){return function(){var e,b=this.buffer,c=this.oa;b[c++]="call";b[c++]=a;b[c++]=arguments.length;for(e=0;e<arguments.length;e++)b[c++]=arguments[e];this.oa=c}}function k(a){return function(){return c[a].apply(c,arguments)}}var f=document.createElement("canvas");f.width=1;f.height=1;var c=f.getContext("2d"),f=["font"],d="fillStyle globalAlpha globalCompositeOperation lineCap lineDashOffset lineJoin lineWidth miterLimit shadowBlur shadowColor shadowOffsetX shadowOffsetY strokeStyle textAlign textBaseline".split(" "),
g={};d.concat(f).forEach(function(a){g[a]=c[a]});a.prototype.clear=function(){this.oa=0};a.prototype.Na=function(){return 0===this.oa};a.prototype.Ta=function(a){function e(a,b,e){for(var c=0,d=a.oa,f=a.buffer;c<e;)f[d++]=b[c++];a.oa=d}function b(a,b,e,c){for(var d=0;d<e;)switch(b[d++]){case "set":a[b[d++]]=b[d++];break;case "setGlobalAlpha":a[b[d++]]=b[d++]*c;break;case "call":var f=b[d++];switch(b[d++]){case 0:a[f]();break;case 1:a[f](b[d++]);break;case 2:a[f](b[d++],b[d++]);break;case 3:a[f](b[d++],
b[d++],b[d++]);break;case 4:a[f](b[d++],b[d++],b[d++],b[d++]);break;case 5:a[f](b[d++],b[d++],b[d++],b[d++],b[d++]);break;case 6:a[f](b[d++],b[d++],b[d++],b[d++],b[d++],b[d++]);break;case 7:a[f](b[d++],b[d++],b[d++],b[d++],b[d++],b[d++],b[d++]);break;case 8:a[f](b[d++],b[d++],b[d++],b[d++],b[d++],b[d++],b[d++],b[d++]);break;case 9:a[f](b[d++],b[d++],b[d++],b[d++],b[d++],b[d++],b[d++],b[d++],b[d++]);break;default:throw"CB0";}}}a instanceof ga?e(a,this.buffer,this.oa):b(a,this.buffer,this.oa,D.B(a.globalAlpha,
1))};a.prototype.replay=a.prototype.Ta;a.prototype.d=function(){return new a};a.prototype.scratch=a.prototype.d;"arc arcTo beginPath bezierCurveTo clearRect clip closePath drawImage fill fillRect fillText lineTo moveTo putImageData quadraticCurveTo rect rotate scale setLineDash setTransform stroke strokeRect strokeText transform translate".split(" ").forEach(function(d){a.prototype[d]=m(d)});["measureText","createLinearGradient","createRadialGradient","createPattern","getLineDash"].forEach(function(d){a.prototype[d]=
k(d)});["save","restore"].forEach(function(d){a.prototype[d]=function(a,b){return function(){a.apply(this,arguments);b.apply(this,arguments)}}(m(d),k(d))});f.forEach(function(d){Object.defineProperty(a.prototype,d,{set:function(a){c[d]=a;this.Gc[d]=a;var b=this.buffer;b[this.oa++]="set";b[this.oa++]=d;b[this.oa++]=a},get:function(){return this.Gc[d]}})});d.forEach(function(d){Object.defineProperty(a.prototype,d,{set:function(a){this.Gc[d]=a;var b=this.buffer;b[this.oa++]="globalAlpha"===d?"setGlobalAlpha":
"set";b[this.oa++]=d;b[this.oa++]=a},get:function(){return this.Gc[d]}})});a.prototype.roundRect=function(a,d,b,c,f){this.beginPath();this.moveTo(a+f,d);this.lineTo(a+b-f,d);this.quadraticCurveTo(a+b,d,a+b,d+f);this.lineTo(a+b,d+c-f);this.quadraticCurveTo(a+b,d+c,a+b-f,d+c);this.lineTo(a+f,d+c);this.quadraticCurveTo(a,d+c,a,d+c-f);this.lineTo(a,d+f);this.quadraticCurveTo(a,d,a+f,d);this.closePath()};a.prototype.fillPolygonWithText=function(a,d,b,c,f){f||(f={});var k={rb:D.B(f.maxFontSize,G.Ea.rb),
Zc:D.B(f.minFontSize,G.Ea.Zc),lineHeight:D.B(f.lineHeight,G.Ea.lineHeight),ob:D.B(f.horizontalPadding,G.Ea.ob),cb:D.B(f.verticalPadding,G.Ea.cb),sb:D.B(f.maxTotalTextHeight,G.Ea.sb),fontFamily:D.B(f.fontFamily,G.Ea.fontFamily),fontStyle:D.B(f.fontStyle,G.Ea.fontStyle),fontVariant:D.B(f.fontVariant,G.Ea.fontVariant),fontWeight:D.B(f.fontWeight,G.Ea.fontWeight),verticalAlign:D.B(f.verticalAlign,G.Ea.verticalAlign)},g=f.cache;if(g&&D.N(f,"area")){g.jd||(g.jd=new ga);var r=f.area,s=D.B(f.cacheInvalidationThreshold,
0.05);a=G.xe(k,this,c,a,M.q(a,{}),{x:d,y:b},f.allowForcedSplit||!1,f.allowEllipsis||!1,g,r,s,f.invalidateCache)}else a=G.Le(k,this,c,a,M.q(a,{}),{x:d,y:b},f.allowForcedSplit||!1,f.allowEllipsis||!1);return a.la?{fit:!0,lineCount:a.mc,fontSize:a.fontSize,box:{x:a.ea.x,y:a.ea.y,w:a.ea.f,h:a.ea.i},ellipsis:a.ec}:{fit:!1}};return a});var ha=v.Dd(function(){function a(a){this.Q=a;this.d=[];this.Ib=[void 0];this.Nc=["#SIZE#px sans-serif"];this.Ld=[0];this.Md=[1];this.ie=[0];this.je=[0];this.ke=[0];this.Qd=[10];this.hc=[10];this.Sb=[this.Ib,this.Nc,this.hc,this.Ld,this.Md,this.ie,this.Qd,this.je,this.ke];this.ga=[1,0,0,1,0,0]}function m(a){var d=a.Q,c=a.Sb[0].length-1;a.Ib[c]&&(d.setLineDash(a.Ib[c]),d.Vj=a.Ld[c]);d.miterLimit=a.Qd[c];d.lineWidth=a.Md[c];d.shadowBlur=a.ie[c];d.shadowOffsetX=a.je[c];d.shadowOffsetY=a.ke[c];d.font=
a.Nc[c].replace("#SIZE#",a.hc[c].toString())}function k(a){return function(){return this.Q[a].apply(this.Q,arguments)}}function f(a){return function(c,e){var f=this.ga;return this.Q[a].call(this.Q,d(c,e,f),g(c,e,f))}}function c(a){return function(c,e,f,k){var r=this.ga;return this.Q[a].call(this.Q,d(c,e,r),g(c,e,r),f*r[0],k*r[3])}}function d(a,d,c){return a*c[0]+d*c[2]+c[4]}function g(a,d,c){return a*c[1]+d*c[3]+c[5]}function l(a,d){for(var c=0;c<a.length;c++)a[c]*=d[0];return a}a.prototype.save=
function(){this.d.push(this.ga.slice(0));for(var a=0;a<this.Sb.length;a++){var d=this.Sb[a];d.push(d[d.length-1])}this.Q.save()};a.prototype.restore=function(){this.ga=this.d.pop();for(var a=0;a<this.Sb.length;a++)this.Sb[a].pop();this.Q.restore();m(this)};a.prototype.scale=function(a,d){var c=this.ga;c[0]*=a;c[1]*=a;c[2]*=d;c[3]*=d;var c=this.ga,e=this.Sb,f=e[0].length-1,k=this.Ib[f];k&&l(k,c);for(k=2;k<e.length;k++){var g=e[k];g[f]*=c[0]}m(this)};a.prototype.translate=function(a,d){var c=this.ga;
c[4]+=c[0]*a+c[2]*d;c[5]+=c[1]*a+c[3]*d};["moveTo","lineTo"].forEach(function(b){a.prototype[b]=f(b)});["clearRect","fillRect","strokeRect","rect"].forEach(function(b){a.prototype[b]=c(b)});"fill stroke beginPath closePath clip createImageData createPattern getImageData putImageData getLineDash setLineDash".split(" ").forEach(function(b){a.prototype[b]=k(b)});[{ub:"lineDashOffset",zb:function(a){return a.Ld}},{ub:"lineWidth",zb:function(a){return a.Md}},{ub:"miterLimit",zb:function(a){return a.Qd}},
{ub:"shadowBlur",zb:function(a){return a.ie}},{ub:"shadowOffsetX",zb:function(a){return a.je}},{ub:"shadowOffsetY",zb:function(a){return a.ke}}].forEach(function(b){Object.defineProperty(a.prototype,b.ub,{set:function(a){var d=b.zb(this);a*=this.ga[0];d[d.length-1]=a;this.Q[b.ub]=a}})});var e=/(\d+(?:\.\d+)?)px/;Object.defineProperty(a.prototype,"font",{set:function(a){var d=e.exec(a);if(1<d.length){var c=this.hc.length-1;this.hc[c]=parseFloat(d[1]);this.Nc[c]=a.replace(e,"#SIZE#px");this.Q.font=
this.Nc[c].replace("#SIZE#",(this.hc[c]*this.ga[0]).toString())}}});"fillStyle globalAlpha globalCompositeOperation lineCap lineJoin shadowColor strokeStyle textAlign textBaseline".split(" ").forEach(function(b){Object.defineProperty(a.prototype,b,{set:function(a){this.Q[b]=a}})});a.prototype.arc=function(a,c,e,f,k,r){var s=this.ga;this.Q.arc(d(a,c,s),g(a,c,s),e*s[0],f,k,r)};a.prototype.arcTo=function(a,c,e,f,k){var r=this.ga;this.Q.arc(d(a,c,r),g(a,c,r),d(e,f,r),g(e,f,r),k*r[0])};a.prototype.bezierCurveTo=
function(a,c,e,f,k,r){var s=this.ga;this.Q.bezierCurveTo(d(a,c,s),g(a,c,s),d(e,f,s),g(e,f,s),d(k,r,s),g(k,r,s))};a.prototype.drawImage=function(a,c,e,f,k,r,s,l,m){function y(c,e,f,k){A.push(d(c,e,x));A.push(g(c,e,x));f=D.V(f)?a.width:f;k=D.V(k)?a.height:k;A.push(f*x[0]);A.push(k*x[3])}var x=this.ga,A=[a];D.V(r)?y(c,e,f,k):y(r,s,l,m);this.Q.drawImage.apply(this.Q,A)};a.prototype.quadraticCurveTo=function(a,c,e,f){var k=this.ga;this.Q.quadraticCurveTo(d(a,c,k),g(a,c,k),d(e,f,k),g(e,f,k))};a.prototype.fillText=
function(a,c,e,f){var k=this.ga;this.Q.fillText(a,d(c,e,k),g(c,e,k),D.Sc(f)?f*k[0]:1E20)};a.prototype.setLineDash=function(a){a=l(a.slice(0),this.ga);this.Ib[this.Ib.length-1]=a;this.Q.setLineDash(a)};return a});var ja=function(){var a=!v.nf()||v.lf()||v.hf()?1:7;return{eh:function(){function m(a){a.beginPath();ia.le(a,l)}var k=document.createElement("canvas");k.width=800;k.height=600;var f=k.getContext("2d"),c=k.width,k=k.height,d,g=0,l=[{x:0,y:100}];for(d=1;6>=d;d++)g=2*d*Math.PI/6,l.push({x:0+100*Math.sin(g),y:0+100*Math.cos(g)});d={polygonPlainFill:[m,function(a){a.fillStyle="rgb(255, 0, 0)";a.fill()}],polygonPlainStroke:[m,function(a){a.strokeStyle="rgb(128, 0, 0)";a.lineWidth=2;a.closePath();a.stroke()}],
polygonGradientFill:[m,function(a){var b=a.createRadialGradient(0,0,10,0,0,60);b.addColorStop(0,"rgb(255, 0, 0)");b.addColorStop(1,"rgb(255, 255, 0)");a.fillStyle=b;a.fill()}],polygonGradientStroke:[m,function(a){var b=a.createLinearGradient(-100,-100,100,100);b.addColorStop(0,"rgb(224, 0, 0)");b.addColorStop(1,"rgb(32, 0, 0)");a.strokeStyle=b;a.lineWidth=2;a.closePath();a.stroke()}],polygonExposureShadow:[m,function(a){a.shadowBlur=50;a.shadowColor="rgba(0, 0, 0, 1)";a.fillStyle="rgba(0, 0, 0, 1)";
a.globalCompositeOperation="source-over";a.fill();a.shadowBlur=0;a.shadowColor="transparent";a.globalCompositeOperation="destination-out";a.fill()}],labelPlainFill:[function(a){a.fillStyle="#000";a.font="24px sans-serif";a.textAlign="center"},function(a){a.fillText("Some text",0,-16);a.fillText("for testing purposes",0,16)}]};var g=100/Object.keys(d).length,e=ba.now(),b={},h;for(h in d){var n=d[h],q=ba.now(),p,r=0;do{f.save();f.translate(Math.random()*c,Math.random()*k);p=3*Math.random()+0.5;f.scale(p,
p);for(p=0;p<n.length;p++)n[p](f);f.restore();r++;p=ba.now()}while(p-q<g);b[h]=a*(p-q)/r}b.total=ba.now()-e;return b}}}();var ia={le:function(a,m){var k=m[0];a.moveTo(k.x,k.y);for(var f=m.length-1;0<f;f--)k=m[f],a.lineTo(k.x,k.y)},tj:function(a,m,k,f){var c,d,g,l=[],e=0,b=m.length;for(g=0;g<b;g++)c=m[g],d=m[(g+1)%b],c=M.d(c,d),c=Math.sqrt(c),l.push(c),e+=c;k=f*(k+0.5*f*e/b);var h,n;f={};var e={},q={},p=0;for(g=0;g<b;g++)c=m[g],d=m[(g+1)%b],h=m[(g+2)%b],n=l[(g+1)%b],n=Math.min(0.5,k/n),M.Aa(1-n,d,h,e),M.Aa(n,d,h,q),p++,0==g&&(h=Math.min(0.5,k/l[0]),M.Aa(h,c,d,f),p++,a.moveTo(f.x,f.y)),a.quadraticCurveTo(d.x,d.y,e.x,e.y),
a.lineTo(q.x,q.y);return!0}};function ka(a){function m(){return"embedded"===c.getAttribute("data-foamtree")}function k(a){n[a]&&(n[a].style.opacity=p*q[a])}function f(a){a.width=Math.round(g*a.n);a.height=Math.round(l*a.n)}var c,d,g,l,e,b,h=[],n={},q={},p=0;this.H=function(f){c=f;0!==c.clientWidth&&0!==c.clientHeight||la.Pa("element has zero dimensions: "+c.clientWidth+" x "+c.clientHeight+".");c.innerHTML="";g=c.clientWidth;l=c.clientHeight;e=0!==g?g:void 0;b=0!==l?l:void 0;m()&&la.Pa("visualization already embedded in the element.");
c.setAttribute("data-foamtree","embedded");d=document.createElement("div");d.style.width="100%";d.style.height="100%";d.style.position="relative";c.appendChild(d);a.c.p("stage:initialized",this,d,g,l)};this.kb=function(){m()&&(c.removeAttribute("data-foamtree"),h=[],n={},c.removeChild(d),a.c.p("stage:disposed",this,d))};this.k=function(){g=c.clientWidth;l=c.clientHeight;if(0!==g&&0!==l&&(g!==e||l!==b)){for(var d=h.length-1;0<=d;d--)f(h[d]);a.c.p("stage:resized",e,b,g,l);e=g;b=l}};this.hj=function(a,
b){a.n=b;f(a)};this.oc=function(b,c,e){var g=document.createElement("canvas");g.setAttribute("style","position: absolute; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;");g.n=c;f(g);h.push(g);n[b]=g;q[b]=1;k(b);e||d.appendChild(g);a.c.p("stage:newLayer",b,g);return g};this.kc=function(a,b){D.V(b)||(q[a]=b,k(a));return q[a]};this.d=function(a){D.V(a)||(p=a,D.Ga(n,
function(a,b){k(b)}));return p}};function oa(a){function m(a,b,e){y=!0;q.x=0;q.y=0;p.x=0;p.y=0;c=h;d.x=n.x;d.y=n.y;b();g*=a;l=e?g/c:a;l=Math.max(0.25/c,l);return!0}function k(a,b){b.x=a.x/h+n.x;b.y=a.y/h+n.y;return b}function f(a,b,d,c,e,f,k,g,h){var s=(a-d)*(f-g)-(b-c)*(e-k);if(1E-5>Math.abs(s))return!1;h.x=((a*c-b*d)*(e-k)-(a-d)*(e*g-f*k))/s;h.y=((a*c-b*d)*(f-g)-(b-c)*(e*g-f*k))/s;return!0}var c=1,d={x:0,y:0},g=1,l=1,e=1,b={x:0,y:0},h=1,n={x:0,y:0},q={x:0,y:0},p={x:0,y:0},r,s,w={x:0,y:0,f:0,i:0},u={x:0,y:0,f:0,i:0,scale:1},y=!0;
a.c.j("stage:initialized",function(a,b,d,c){r=d;s=c;w.x=0;w.y=0;w.f=d;w.i=c;u.x=0;u.y=0;u.f=d;u.i=c;u.scale=1});a.c.j("stage:resized",function(a,c,e,f){function k(a){a.x*=h;a.y*=l}function g(a){k(a);a.f*=h;a.i*=l}r=e;s=f;var h=e/a,l=f/c;k(d);k(n);k(b);k(q);k(p);g(w);g(u)});this.Yb=function(a,d){return m(d,function(){k(a,b)},!0)};this.Z=function(a,d){if(1===Math.round(1E4*d)/1E4){var c=w.x-n.x,e=w.y-n.y;m(1,function(){},!0);return this.d(-c,-e)}return m(d,function(){for(var d=!1;!d;)var d=Math.random(),
c=Math.random(),e=Math.random(),k=Math.random(),d=f(a.x+d*a.f,a.y+c*a.i,w.x+d*w.f,w.y+c*w.i,a.x+e*a.f,a.y+k*a.i,w.x+e*w.f,w.y+k*w.i,b)},!0)};this.sc=function(a,d){var c,e,k,g;c=a.f/a.i;e=r/s;c<e?(k=a.i*e,g=a.i,c=a.x-0.5*(k-a.f),e=a.y):c>e?(k=a.f,g=a.f*s/r,c=a.x,e=a.y-0.5*(g-a.i)):(c=a.x,e=a.y,k=a.f,g=a.i);c-=k*d;e-=g*d;k*=1+2*d;if(f(c,e,n.x,n.y,c+k,e,n.x+r/h,n.y,b))return m(r/h/k,D.ta,!1);y=!1;return this.d(h*(n.x-c),h*(n.y-e))};this.d=function(a,b){var c=Math.round(1E4*a)/1E4,d=Math.round(1E4*b)/
1E4;p.x+=c/h;p.y+=d/h;return 0!==c||0!==d};this.reset=function(a){a&&this.content(0,0,r,s);return this.Z({x:w.x+n.x,y:w.y+n.y,f:w.f/h,i:w.i/h},e/g)};this.Qb=function(a){e=Math.min(1,Math.round(1E4*(a||g))/1E4)};this.k=function(){return n.x<w.x?(w.x-n.x)*h:n.x+r/h>w.x+w.f?-(n.x+r/h-w.x-w.f)*h:0};this.A=function(){return n.y<w.y?(w.y-n.y)*h:n.y+s/h>w.y+w.i?-(n.y+s/h-w.y-w.i)*h:0};this.update=function(a){var e=Math.abs(Math.log(l));6>e?e=2:(e/=4,e+=3*e*(1<l?a:1-a));e=1<l?Math.pow(a,e):1-Math.pow(1-a,
e);e=(y?e:1)*(l-1)+1;h=c*e;n.x=b.x-(b.x-d.x)/e;n.y=b.y-(b.y-d.y)/e;n.x-=q.x*(1-a)+p.x*a;n.y-=q.y*(1-a)+p.y*a;1===a&&(q.x=p.x,q.y=p.y);u.x=n.x;u.y=n.y;u.f=r/h;u.i=s/h;u.scale=h};this.S=function(a){a.x=u.x;a.y=u.y;a.scale=u.scale;return a};this.absolute=function(a,b){return k(a,b||{})};this.nd=function(a,b){var c=b||{};c.x=(a.x-n.x)*h;c.y=(a.y-n.y)*h;return c};this.Hc=function(a){return this.scale()<e/a};this.Rd=function(){return D.Fd(h,1)};this.scale=function(){return Math.round(1E4*h)/1E4};this.content=
function(a,b,c,d){w.x=a;w.y=b;w.f=c;w.i=d};this.Jc=function(a,b){var c;for(c=a.length-1;0<=c;c--){var d=a[c];d.save();d.scale(h,h);d.translate(-n.x,-n.y)}b(u);for(c=a.length-1;0<=c;c--)d=a[c],d.restore()}};var S=new function(){function a(a){if("hsl"==a.model||"hsla"==a.model)return a;var f=a.r/=255,c=a.g/=255,d=a.b/=255,g=Math.max(f,c,d),l=Math.min(f,c,d),e,b=(g+l)/2;if(g==l)e=l=0;else{var h=g-l,l=0.5<b?h/(2-g-l):h/(g+l);switch(g){case f:e=(c-d)/h+(c<d?6:0);break;case c:e=(d-f)/h+2;break;case d:e=(f-c)/h+4}e/=6}a.h=360*e;a.s=100*l;a.l=100*b;a.model="hsl";return a}var m={h:0,s:0,l:0,a:1,model:"hsla"};this.Ba=function(k){return D.Tc(k)?a(S.Gg(k)):D.jc(k)?a(k):m};this.Gg=function(a){var f;return(f=/rgba\(\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*\)/.exec(a))&&
5==f.length?{r:parseFloat(f[1]),g:parseFloat(f[2]),b:parseFloat(f[3]),a:parseFloat(f[4]),model:"rgba"}:(f=/hsla\(\s*([^,\s]+)\s*,\s*([^,%\s]+)%\s*,\s*([^,\s%]+)%\s*,\s*([^,\s]+)\s*\)/.exec(a))&&5==f.length?{h:parseFloat(f[1]),s:parseFloat(f[2]),l:parseFloat(f[3]),a:parseFloat(f[4]),model:"hsla"}:(f=/rgb\(\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*\)/.exec(a))&&4==f.length?{r:parseFloat(f[1]),g:parseFloat(f[2]),b:parseFloat(f[3]),a:1,model:"rgb"}:(f=/hsl\(\s*([^,\s]+)\s*,\s*([^,\s%]+)%\s*,\s*([^,\s%]+)%\s*\)/.exec(a))&&
4==f.length?{h:parseFloat(f[1]),s:parseFloat(f[2]),l:parseFloat(f[3]),a:1,model:"hsl"}:(f=/#([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})/.exec(a))&&4==f.length?{r:parseInt(f[1],16),g:parseInt(f[2],16),b:parseInt(f[3],16),a:1,model:"rgb"}:(f=/#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])/.exec(a))&&4==f.length?{r:17*parseInt(f[1],16),g:17*parseInt(f[2],16),b:17*parseInt(f[3],16),a:1,model:"rgb"}:m};this.Bg=function(a){function f(a,b,c){0>c&&(c+=1);1<c&&(c-=1);return c<1/6?a+6*(b-a)*c:0.5>c?b:c<2/3?
a+(b-a)*(2/3-c)*6:a}if("rgb"==a.model||"rgba"==a.model)return Math.sqrt(a.r*a.r*0.241+a.g*a.g*0.691+a.b*a.b*0.068)/255;var c,d;c=a.l/100;var g=a.s/100;d=a.h/360;if(0==a.Yj)c=a=d=c;else{var g=0.5>c?c*(1+g):c+g-c*g,l=2*c-g;c=f(l,g,d+1/3);a=f(l,g,d);d=f(l,g,d-1/3)}return Math.sqrt(65025*c*c*0.241+65025*a*a*0.691+65025*d*d*0.068)/255};this.Mg=function(a){if(D.Tc(a))return a;if(D.jc(a))switch(a.model){case "hsla":return S.Hg(a);case "hsl":return S.Ac(a);case "rgba":return S.Kg(a);case "rgb":return S.Jg(a);
default:return"#000"}else return"#000"};this.Kg=function(a){return"rgba("+(0.5+a.r|0)+","+(0.5+a.g|0)+","+(0.5+a.b|0)+","+a.a+")"};this.Jg=function(a){return"rgba("+(0.5+a.r|0)+","+(0.5+a.g|0)+","+(0.5+a.b|0)+")"};this.Hg=function(a){return"hsla("+(0.5+a.h|0)+","+(0.5+a.s|0)+"%,"+(0.5+a.l|0)+"%,"+a.a+")"};this.Ac=function(a){return"hsl("+(0.5+a.h|0)+","+(0.5+a.s|0)+"%,"+(0.5+a.l|0)+"%)"};this.Z=function(a,f,c){return"hsl("+(0.5+a|0)+","+(0.5+f|0)+"%,"+(0.5+c|0)+"%)"}};function V(){var a=!1,m,k=[],f=this,c=new function(){this.P=function(c){c&&(a?c.apply(f,m):k.push(c));return this};this.ih=function(a){f=a;return{then:this.P}}};this.J=function(){m=arguments;for(var c=0;c<k.length;c++)k[c].apply(f,m);a=!0;return this};this.M=function(){return c}}function pa(a){var m=new V,k=a.length;if(0<a.length)for(var f=a.length-1;0<=f;f--)a[f].P(function(){0===--k&&m.J()});else m.J();return m.M()}
function qa(a){var m=0;this.d=function(){m++};this.k=function(){m--;0===m&&a()};this.clear=function(){m=0};this.A=function(){return 0===m}};var ra={Ie:function(a,m,k,f){f=f||{};var c;try{c=a.getBoundingClientRect()}catch(d){if(!ra.ki){ra.ki=!0;window.console.log("getBoundingClientRect() failed.");for(window.console.log("Element",a);null!==a.parentElement;)a=a.parentElement;window.console.log("Attached to DOM",a===document.body.parentElement)}c={left:0,top:0}}f.x=m-c.left;f.y=k-c.top;return f}};function sa(){var a=document,m={};this.addEventListener=function(k,f,c){var d=m[k];d||(d=[],m[k]=d);d.push(f);a.addEventListener(k,f,c)};this.d=function(){D.Ga(m,function(k,f){for(var c=k.length-1;0<=c;c--)a.removeEventListener(f,k[c])})}};function ta(a){function m(a){return function(b){k(b)&&a.apply(this,arguments)}}function k(b){for(b=b.target;b;){if(b===a)return!0;b=b.parentElement}return!1}function f(a,b,d){d=d||{};c(a,d);for(var e=0;e<b.length;e++)b[e].call(a.target,d);c(a,d);(void 0===d.Mb&&d.Bi||"prevent"===d.Mb)&&a.preventDefault();return d}function c(b,c){ra.Ie(a,b.clientX,b.clientY,c);c.altKey=b.altKey;c.metaKey=b.metaKey;c.ctrlKey=b.ctrlKey;c.shiftKey=b.shiftKey;c.wb=3===b.which;return c}var d=new sa,g=[],l=[],e=[],b=[],
h=[],n=[],q=[],p=[],r=[],s=[],w=[];this.d=function(a){g.push(a)};this.k=function(a){h.push(a)};this.ya=function(a){l.push(a)};this.Ba=function(a){e.push(a)};this.Pa=function(a){b.push(a)};this.Aa=function(a){w.push(a)};this.za=function(a){n.push(a)};this.Ja=function(a){q.push(a)};this.Z=function(a){p.push(a)};this.A=function(a){r.push(a)};this.S=function(a){s.push(a)};this.kb=function(){d.d()};var u,y,x,A,C={x:0,y:0},K={x:0,y:0},B=!1,H=!1;d.addEventListener("mousedown",m(function(b){if(b.target!==
a){var c=f(b,e);K.x=c.x;K.y=c.y;C.x=c.x;C.y=c.y;B=!0;f(b,p);y=!1;u=window.setTimeout(function(){100>M.d(C,c)&&(window.clearTimeout(A),f(b,l),y=!0)},400)}}));d.addEventListener("mouseup",function(a){var c=f(a,b);B&&(H&&f(a,s),window.clearTimeout(u),y||H||!k(a)||(c={x:c.x,y:c.y},x&&100>M.d(c,x)?f(a,h):f(a,g),x=c,A=window.setTimeout(function(){x=null},350)),H=B=!1)});d.addEventListener("mousemove",function(a){var b=c(a,{});k(a)&&f(a,n,{type:"move"});C.x=b.x;C.y=b.y;B&&!H&&100<M.d(K,C)&&(H=!0);H&&f(a,
r,b)});d.addEventListener("mouseout",m(function(a){f(a,q,{type:"out"})}));d.addEventListener("wheel",m(function(){return function(a){var b;"deltaY"in a?b=a.deltaY:(b=0,"detail"in a&&(b=a.detail),"wheelDelta"in a&&(b=-a.wheelDelta/120),"wheelDeltaY"in a&&(b=-a.wheelDeltaY/120),"axis"in a&&a.axis===a.HORIZONTAL_AXIS&&(b=0),b*=10);b&&a.deltaMode&&(b=1===a.deltaMode?67*b:800*b);f(a,w,{wd:-b/200,Bi:!0})}}()),{passive:!1});d.addEventListener("contextmenu",m(function(a){a.preventDefault()}))};var X=function(){function a(a){return function(d){return Math.pow(d,a)}}function m(a){return function(d){return 1-Math.pow(1-d,a)}}function k(a){return function(d){return 1>(d*=2)?0.5*Math.pow(d,a):1-0.5*Math.abs(Math.pow(2-d,a))}}function f(a){return function(d){for(var f=0;f<a.length;f++)d=(0,a[f])(d);return d}}return{pa:function(a){switch(a){case "linear":return X.Jb;case "bounce":return X.Ug;case "squareIn":return X.ng;case "squareOut":return X.Rb;case "squareInOut":return X.og;case "cubicIn":return X.Yg;
case "cubicOut":return X.ze;case "cubicInOut":return X.Zg;case "quadIn":return X.Ti;case "quadOut":return X.Vi;case "quadInOut":return X.Ui;default:return X.Jb}},Jb:function(a){return a},Ug:f([k(2),function(a){return 0===a?0:1===a?1:a*(a*(a*(a*(25.9425*a-85.88)+105.78)-58.69)+13.8475)}]),ng:a(2),Rb:m(2),og:k(2),Yg:a(3),ze:m(3),Zg:k(3),Ti:a(2),Vi:m(2),Ui:k(2),d:f}}();var D={V:function(a){return void 0===a},mf:function(a){return null===a},Sc:function(a){return"[object Number]"===Object.prototype.toString.call(a)},Tc:function(a){return"[object String]"===Object.prototype.toString.call(a)},Gd:function(a){return"function"===typeof a},jc:function(a){return a===Object(a)},Fd:function(a,m){return 1E-6>a-m&&-1E-6<a-m},jf:function(a){return D.V(a)||D.mf(a)||D.Tc(a)&&!/\S/.test(a)},N:function(a,m){return a&&a.hasOwnProperty(m)},nb:function(a,m){if(a)for(var k=m.length-
1;0<=k;k--)if(a.hasOwnProperty(m[k]))return!0;return!1},extend:function(a){D.dh(Array.prototype.slice.call(arguments,1),function(m){if(m)for(var k in m)m.hasOwnProperty(k)&&(a[k]=m[k])});return a},A:function(a,m){return a.map(function(a){return a[m]},[])},dh:function(a,m,k){null!=a&&(a.forEach?a.forEach(m,k):D.Ga(a,m,k))},Ga:function(a,m,k){for(var f in a)if(a.hasOwnProperty(f)&&!1===m.call(k,a[f],f,a))break},B:function(){for(var a=0;a<arguments.length;a++){var m=arguments[a];if(!(D.V(m)||D.Sc(m)&&
isNaN(m)||D.Tc(m)&&D.jf(m)))return m}},bg:function(a,m){var k=a.indexOf(m);0<=k&&a.splice(k,1)},$g:function(a,m,k){var f;return function(){var c=this,d=arguments,g=k&&!f;clearTimeout(f);f=setTimeout(function(){f=null;k||a.apply(c,d)},m);g&&a.apply(c,d)}},defer:function(a){setTimeout(a,1)},k:function(a){return a},ta:function(){}};var ua={ji:function(a,m,k){return v.hi()?function(){var f=m+":"+JSON.stringify(arguments),c=window.localStorage.getItem(f);c&&(c=JSON.parse(c));if(c&&Date.now()-c.t<k)return c.v;c=a.apply(this,arguments);window.localStorage.setItem(f,JSON.stringify({v:c,t:Date.now()}));return c}:a}};var va={m:function(a,m){function k(){var f=[];if(Array.isArray(a))for(var c=0;c<a.length;c++){var d=a[c];d&&f.push(d.apply(m,arguments))}else a&&f.push(a.apply(m,arguments));return f}k.empty=function(){return 0===a.length&&!D.Gd(a)};return k}};function wa(){var a={};this.j=function(m,k){var f=a[m];f||(f=[],a[m]=f);f.push(k)};this.p=function(m,k){var f=a[m];if(f)for(var c=Array.prototype.slice.call(arguments,1),d=0;d<f.length;d++)f[d].apply(this,c)}};var xa={jg:function(a){for(var m="",k=0;k<a.length;k++)m+=String.fromCharCode(a.charCodeAt(k)^1);return m}};function ya(a){function m(b,c,k){var m=this,p,r=0;this.id=g++;this.name=k?k:"{unnamed on "+b+"}";this.target=function(){return b};this.Gb=function(){return-1!=e.indexOf(m)};this.start=function(){if(!m.Gb()){if(-1==e.indexOf(m)){var b=l.now();!0===m.wf(b)&&(e=e.slice(),e.push(m))}0<e.length&&a.repeat(f)}return this};this.stop=function(){for(d(m);p<c.length;p++){var a=c[p];a.ib&&a.Xa.call()}return this};this.dg=function(){p=void 0};this.wf=function(a){r++;if(0!==c.length){var b;D.V(p)?(p=0,b=c[p],b.X&&
b.X.call(b,a,r,m)):b=c[p];for(;p<c.length;){if(b.Xa&&b.Xa.call(b,a,r,m))return!0;b.Da&&b.Da.call(b,a,r,m);D.V(p)&&(p=-1);++p<c.length&&(b=c[p],b.X&&b.X.call(b,a,r,m))}}return!1}}function k(a){return D.V(a)?e.slice():e.filter(function(c){return c.target()===a})}function f(){c();0==e.length&&a.cancel(f)}function c(){var a=l.now();e.forEach(function(c){!0!==c.wf(a)&&d(c)})}function d(a){e=e.filter(function(c){return c!==a})}var g=0,l=ba.create(),e=[];this.d=function(){for(var a=e.length-1;0<=a;a--)e[a].stop();
e=[]};this.D=function(){function a(){}function c(a){var b=a.target,d=a.duration,e=a.da,f,g;this.X=function(){f={};for(var c in a.G)b.hasOwnProperty(c)&&(f[c]={start:D.V(a.G[c].start)?b[c]:D.Gd(a.G[c].start)?a.G[c].start.call(void 0):a.G[c].start,end:D.V(a.G[c].end)?b[c]:D.Gd(a.G[c].end)?a.G[c].end.call(void 0):a.G[c].end,R:D.V(a.G[c].R)?X.Jb:a.G[c].R});g=l.now()};this.Xa=function(){var a=l.now()-g,a=0===d?1:Math.min(d,a)/d,c;for(c in f){var k=f[c];b[c]=k.start+(k.end-k.start)*k.R(a)}e&&e.call(b,a);
return 1>a}}function d(a,b,c){this.ib=c;this.Xa=function(){a.call(b);return!1}}function e(a){var b;this.X=function(c,d){b=d+a};this.Xa=function(a,c){return c<b}}function f(a){var b;this.X=function(c){b=c+a};this.Xa=function(a){return a<b}}function g(a){this.X=function(){a.forEach(function(a){a.start()})};this.Xa=function(){for(var b=0;b<a.length;b++)if(a[b].Gb())return!0;return!1}}a.m=function(a,b){return new function(){function k(b,c,e,f){return c?(D.V(e)&&(e=a),b.Bb(new d(c,e,f))):b}var l=[];this.Bb=
function(a){l.push(a);return this};this.eb=function(a){return this.Bb(new f(a))};this.oe=function(a){return this.Bb(new e(a||1))};this.call=function(a,b){return k(this,a,b,!1)};this.ib=function(a,b){return k(this,a,b,!0)};this.ia=function(b){D.V(b.target)&&(b.target=a);return this.Bb(new c(b))};this.Ya=function(a){return this.Bb(new g(a))};this.dg=function(){return this.Bb({Xa:function(a,b){b.dg();return!0}})};this.xa=function(){return new m(a,l,b)};this.start=function(){return this.xa().start()};
this.Eg=function(){var a=new V;this.oe().call(a.J).xa();return a.M()};this.ab=function(){var a=this.Eg();this.start();return a}}};a.tc=function(c){k(c).forEach(function(a){a.stop()});return a.m(c,void 0)};return a}()};var $=function(){var a={He:function(a,k){if(a.e)for(var f=a.e,c=0;c<f.length;c++)k(f[c],c)},Kc:function(m,k){if(m.e)for(var f=m.e,c=0;c<f.length;c++)if(!1===a.Kc(f[c],k)||!1===k(f[c],c))return!1}};a.F=a.Kc;a.Lc=function(m,k){if(m.e)for(var f=m.e,c=0;c<f.length;c++)if(!1===k(f[c],c)||!1===a.Lc(f[c],k))return!1};a.Fa=function(m,k){if(m.e)for(var f=m.e,c=0;c<f.length;c++)if(!1===a.Fa(f[c],k))return!1;return k(m)};a.Pj=a.Fa;a.xd=function(m,k){!1!==k(m)&&a.Lc(m,k)};a.Mc=function(m,k){var f=[];a.Lc(m,function(a){f.push(a)});
return k?f.filter(k):f};a.Ge=function(a,k){for(var f=a.parent;f&&!1!==k(f);)f=f.parent};a.li=function(a,k){for(var f=a.parent;f&&f!==k;)f=f.parent;return!!f};return a}();var M=new function(){function a(a,f){var c=a.x-f.x,d=a.y-f.y;return c*c+d*d}function m(a,f,c){for(var d=0;d<a.length;d++){var g=M.za(a[d],a[d+1]||a[0],f,c,!0);if(g)return g}}this.za=function(a,f,c,d,g){var l=a.x;a=a.y;var e=f.x-l;f=f.y-a;var b=c.x,h=c.y;c=d.x-b;var n=d.y-h;d=e*n-c*f;if(!(1E-12>=d&&-1E-12<=d)&&(b=b-l,h=h-a,c=(b*n-c*h)/d,d=(b*f-e*h)/d,0<=d&&(g||1>=d)&&0<=c&&1>=c))return{x:l+e*c,y:a+f*c}};this.Ig=function(a,f,c,d){var g=a.x;a=a.y;var l=f.x-g;f=f.y-a;var e=c.x;c=c.y;var b=d.x-e;d=d.y-
c;var h=l*d-b*f;if(!(1E-12>=h&&-1E-12<=h)&&(d=((e-g)*d-b*(c-a))/h,0<=d&&1>=d))return{x:g+l*d,y:a+f*d}};this.Bc=function(a,f,c){for(var d=M.k(f,{}),g=M.k(c,{}),l,e=g.x-d.x,b=g.y-d.y,h=[],g=0;g<c.length;g++)l=c[g],h.push({x:l.x-e,y:l.y-b});c=[];l=[];for(g=0;g<a.length;g++){var n=a[g],q=m(f,d,n);q?(c.push(q),l.push(m(h,d,n))):(c.push(null),l.push(null))}for(g=0;g<a.length;g++)if(q=c[g],n=l[g],q&&n){f=a[g];var h=d,p=q.x-d.x,q=q.y-d.y,q=Math.sqrt(p*p+q*q);if(1E-12<q){var p=f.x-d.x,r=f.y-d.y,q=Math.sqrt(p*
p+r*r)/q;f.x=h.x+q*(n.x-h.x);f.y=h.y+q*(n.y-h.y)}else f.x=h.x,f.y=h.y}for(g=0;g<a.length;g++)l=a[g],l.x+=e,l.y+=b};this.q=function(a,f){if(0!==a.length){var c,d,g,l;c=d=a[0].x;g=l=a[0].y;for(var e=a.length;0<--e;)c=Math.min(c,a[e].x),d=Math.max(d,a[e].x),g=Math.min(g,a[e].y),l=Math.max(l,a[e].y);f.x=c;f.y=g;f.f=d-c;f.i=l-g;return f}};this.A=function(a){return[{x:a.x,y:a.y},{x:a.x+a.f,y:a.y},{x:a.x+a.f,y:a.y+a.i},{x:a.x,y:a.y+a.i}]};this.k=function(a,f){for(var c=0,d=0,g=a.length,l=a[0],e=0,b=1;b<
g-1;b++)var h=a[b],n=a[b+1],m=l.y+h.y+n.y,p=(h.x-l.x)*(n.y-l.y)-(n.x-l.x)*(h.y-l.y),c=c+p*(l.x+h.x+n.x),d=d+p*m,e=e+p;f.x=c/(3*e);f.y=d/(3*e);f.ja=e/2;return f};this.re=function(a,f){this.k(a,f);f.Ob=Math.sqrt(f.ja/Math.PI)};this.Ua=function(a,f){for(var c=0;c<a.length;c++){var d=a[c],g=a[c+1]||a[0];if(0>(f.y-d.y)*(g.x-d.x)-(f.x-d.x)*(g.y-d.y))return!1}return!0};this.Lg=function(a,f,c){var d=a.x,g=f.x;a.x>f.x&&(d=f.x,g=a.x);g>c.x+c.f&&(g=c.x+c.f);d<c.x&&(d=c.x);if(d>g)return!1;var l=a.y,e=f.y,b=f.x-
a.x;1E-7<Math.abs(b)&&(e=(f.y-a.y)/b,a=a.y-e*a.x,l=e*d+a,e=e*g+a);l>e&&(d=e,e=l,l=d);e>c.y+c.i&&(e=c.y+c.i);l<c.y&&(l=c.y);return l<=e};this.se=function(k,f,c,d,g){var l,e;function b(b,c,d){if(f.x===n.x&&f.y===n.y)return d;var g=m(k,f,n),p=Math.sqrt(a(g,f)/(b*b+c*c));return p<h?(h=p,l=g.x,e=g.y,0!==c?Math.abs(e-f.y)/Math.abs(c):Math.abs(l-f.x)/Math.abs(b)):d}d=D.B(d,0.5);g=D.B(g,0.5);c=D.B(c,1);var h=Number.MAX_VALUE;e=l=0;var n={x:0,y:0},q,p=d*c;c=(1-d)*c;d=1-g;n.x=f.x-p;n.y=f.y-g;q=b(p,g,q);n.x=
f.x+c;n.y=f.y-g;q=b(c,g,q);n.x=f.x-p;n.y=f.y+d;q=b(p,d,q);n.x=f.x+c;n.y=f.y+d;return q=b(c,d,q)};this.Dg=function(a,f){function c(a,c,d){var e=c.x,f=d.x;c=c.y;d=d.y;var g=f-e,k=d-c;return Math.abs(k*a.x-g*a.y-e*d+f*c)/Math.sqrt(g*g+k*k)}for(var d=a.length,g=c(f,a[d-1],a[0]),l=0;l<d-1;l++){var e=c(f,a[l],a[l+1]);e<g&&(g=e)}return g};this.Wb=function(a,f,c){var d;c={x:f.x+Math.cos(c),y:f.y-Math.sin(c)};var g=[],l=[],e=a.length;for(d=0;d<e;d++){var b=M.Ig(a[d],a[(d+1)%e],f,c);if(b&&(g.push(b),2==l.push(d)))break}if(2==
g.length){var b=g[0],g=g[1],h=l[0],l=l[1],n=[g,b];for(d=h+1;d<=l;d++)n.push(a[d]);for(d=[b,g];l!=h;)l=(l+1)%e,d.push(a[l]);a=[n,d];e=c.x-f.x;d=g.x-b.x;0===e&&(e=c.y-f.y,d=g.y-b.y);(0>e?-1:0<e?1:0)!==(0>d?-1:0<d?1:0)&&a.reverse();return a}};this.Aa=function(a,f,c,d){d.x=a*(f.x-c.x)+c.x;d.y=a*(f.y-c.y)+c.y;return d};this.d=a;this.qe=function(a,f,c){if(D.Sc(f))f=2*Math.PI*f/360;else{var d=M.q(a,{});switch(f){case "random":f=Math.random()*Math.PI*2;break;case "top":f=Math.atan2(-d.i,0);break;case "bottom":f=
Math.atan2(d.i,0);break;case "topleft":f=Math.atan2(-d.i,-d.f);break;default:f=Math.atan2(d.i,d.f)}}d=M.k(a,{});return M.Aa(c,m(a,d,{x:d.x+Math.cos(f),y:d.y+Math.sin(f)}),d,{})};return this};var za=new function(){function a(a,c){this.face=a;this.kd=c;this.pc=this.dd=null}function m(a,c,f){this.ma=[a,c,f];this.C=Array(3);var e=c.y-a.y,b=f.z-a.z,h=c.x-a.x;c=c.z-a.z;var k=f.x-a.x;a=f.y-a.y;this.Oa={x:e*b-c*a,y:c*k-h*b,z:h*a-e*k};this.jb=[];this.td=this.visible=!1}this.S=function(d){function f(b,c,d){var g,h,k=b.ma[0],l=b.Oa,r=l.x,s=l.y,l=l.z,m=Array(n);c=c.jb;g=c.length;for(e=0;e<g;e++)h=c[e].kd,m[h.index]=!0,0>r*(h.x-k.x)+s*(h.y-k.y)+l*(h.z-k.z)&&a.d(b,h);c=d.jb;g=c.length;for(e=0;e<g;e++)h=
c[e].kd,!0!==m[h.index]&&0>r*(h.x-k.x)+s*(h.y-k.y)+l*(h.z-k.z)&&a.d(b,h)}var l,e,b,h,n=d.length;for(l=0;l<n;l++)d[l].index=l,d[l].$b=null;var q=[],p;if(0<(p=function(){function b(a,c,d,e){var f=(c.y-a.y)*(d.z-a.z)-(c.z-a.z)*(d.y-a.y),g=(c.z-a.z)*(d.x-a.x)-(c.x-a.x)*(d.z-a.z),h=(c.x-a.x)*(d.y-a.y)-(c.y-a.y)*(d.x-a.x);return f*e.x+g*e.y+h*e.z>f*a.x+g*a.y+h*a.z?new m(a,c,d):new m(d,c,a)}function c(a,b,d,e){function f(a,b,c){a=a.ma;b=a[0]==b?0:a[1]==b?1:2;return a[(b+1)%3]!=c?(b+2)%3:b}b.C[f(b,d,e)]=
a;a.C[f(a,e,d)]=b}if(4>n)return 0;var e=d[0],f=d[1],g=d[2],h=d[3],k=b(e,f,g,h),l=b(e,g,h,f),r=b(e,f,h,g),s=b(f,g,h,e);c(k,l,g,e);c(k,r,e,f);c(k,s,f,g);c(l,r,h,e);c(l,s,g,h);c(r,s,h,f);q.push(k,l,r,s);for(e=4;e<n;e++)for(f=d[e],g=0;4>g;g++)h=q[g],k=h.ma[0],l=h.Oa,0>l.x*(f.x-k.x)+l.y*(f.y-k.y)+l.z*(f.z-k.z)&&a.d(h,f);return 4}())){for(;p<n;){b=d[p];if(b.$b){for(l=b.$b;null!==l;)l.face.visible=!0,l=l.pc;var r,s;l=0;a:for(;l<q.length;l++)if(h=q[l],!1===h.visible){var w=h.C;for(e=0;3>e;e++)if(!0===w[e].visible){r=
h;s=e;break a}}h=[];var w=[],u=r,y=s;do if(h.push(u),w.push(y),y=(y+1)%3,!1===u.C[y].visible){do for(l=u.ma[y],u=u.C[y],e=0;3>e;e++)u.ma[e]==l&&(y=e);while(!1===u.C[y].visible&&(u!==r||y!==s))}while(u!==r||y!==s);var x=null,A=null;for(l=0;l<h.length;l++){var u=h[l],y=w[l],C=u.C[y],K=u.ma[(y+1)%3],B=u.ma[y],H=K.y-b.y,Q=B.z-b.z,O=K.x-b.x,P=K.z-b.z,F=B.x-b.x,T=B.y-b.y,N;0<c.length?(N=c.pop(),N.ma[0]=b,N.ma[1]=K,N.ma[2]=B,N.Oa.x=H*Q-P*T,N.Oa.y=P*F-O*Q,N.Oa.z=O*T-H*F,N.jb.length=0,N.visible=!1,N.td=!0):
N={ma:[b,K,B],C:Array(3),Oa:{x:H*Q-P*T,y:P*F-O*Q,z:O*T-H*F},jb:[],visible:!1};q.push(N);u.C[y]=N;N.C[1]=u;null!==A&&(A.C[0]=N,N.C[2]=A);A=N;null===x&&(x=N);f(N,u,C)}A.C[0]=x;x.C[2]=A;l=[];for(e=0;e<q.length;e++)if(h=q[e],!0===h.visible){w=h.jb;u=w.length;for(b=0;b<u;b++)y=w[b],x=y.dd,A=y.pc,null!==x&&(x.pc=A),null!==A&&(A.dd=x),null===x&&(y.kd.$b=A),k.push(y);h.td&&c.push(h)}else l.push(h);q=l}p++}for(l=0;l<q.length;l++)h=q[l],h.td&&c.push(h)}return{Je:q}};a.d=function(c,f){var l;0<k.length?(l=k.pop(),
l.face=c,l.kd=f,l.pc=null,l.dd=null):l=new a(c,f);c.jb.push(l);var e=f.$b;null!==e&&(e.dd=l);l.pc=e;f.$b=l};for(var k=Array(2E3),f=0;f<k.length;f++)k[f]=new a(null,null);for(var c=Array(1E3),f=0;f<c.length;f++)c[f]={ma:Array(3),C:Array(3),Oa:{x:0,y:0,z:0},jb:[],visible:!1}};var Aa=new function(){function a(a,f,c,d,g,l,e,b){var h=(a-c)*(l-b)-(f-d)*(g-e);return Math.abs(h)<m?void 0:{x:((a*d-f*c)*(g-e)-(a-c)*(g*b-l*e))/h,y:((a*d-f*c)*(l-b)-(f-d)*(g*b-l*e))/h}}var m=1E-12;this.bb=function(k,f){for(var c=k[0],d=c.x,g=c.y,l=c.x,e=c.y,b=k.length-1;0<b;b--)c=k[b],d=Math.min(d,c.x),g=Math.min(g,c.y),l=Math.max(l,c.x),e=Math.max(e,c.y);if(l-d<3*f||e-g<3*f)c=void 0;else{a:{c=!0;void 0==c&&(c=!1);d=[];g=k.length;for(l=0;l<=g;l++){var e=k[l%g],b=k[(l+1)%g],h=k[(l+2)%g],n,q,p;n=b.x-
e.x;q=b.y-e.y;p=Math.sqrt(n*n+q*q);var r=f*n/p,s=f*q/p;n=h.x-b.x;q=h.y-b.y;p=Math.sqrt(n*n+q*q);n=f*n/p;q=f*q/p;if(e=a(e.x-s,e.y+r,b.x-s,b.y+r,b.x-q,b.y+n,h.x-q,h.y+n))if(d.push(e),h=d.length,c&&3<=h&&(e=d[h-3],b=d[h-2],h=d[h-1],0>(b.x-e.x)*(h.y-e.y)-(h.x-e.x)*(b.y-e.y))){c=void 0;break a}}d.shift();c=3>d.length?void 0:d}if(!c)a:{d=k.slice(0);for(c=0;c<k.length;c++){l=k[c%k.length];b=k[(c+1)%k.length];h=b.x-l.x;g=b.y-l.y;e=Math.sqrt(h*h+g*g);h=f*h/e;e=f*g/e;g=l.x-e;l=l.y+h;e=b.x-e;b=b.y+h;if(0!=d.length){s=
g-e;q=l-b;h=[];n=p=!0;r=void 0;for(r=0;r<d.length;r++){var w=s*(l-d[r].y)-(g-d[r].x)*q;w<=m&&w>=-m&&(w=0);h.push(w);0<w&&(p=!1);0>w&&(n=!1)}if(p)d=[];else if(!n){s=[];for(r=0;r<d.length;r++)q=(r+1)%d.length,p=h[r],n=h[q],0<=p&&s.push(d[r]),(0<p&&0>n||0>p&&0<n)&&s.push(a(d[r].x,d[r].y,d[q].x,d[q].y,g,l,e,b));d=s}}if(3>d.length){c=void 0;break a}}c=d}}return c};return this};var Ba=new function(){function a(a){for(var f=a[0].x,c=a[0].y,d=f,g=c,l=1;l<a.length;l++)var e=a[l],f=Math.min(f,e.x),c=Math.min(c,e.y),d=Math.max(d,e.x),g=Math.max(g,e.y);a=d-f;g=g-c;return[{x:f+2*a,y:c+2*g,f:0},{x:f+2*a,y:c-2*g,f:0},{x:f-2*a,y:c+2*g,f:0}]}var m=1E-12;this.S=function(k,f){function c(){for(b=0;b<p.length;b++){var a=p[b],c=a.ma,d=c[0],e=c[1],f=c[2],c=d.x,g=d.y,d=d.z,h=e.x,k=e.y,e=e.z,l=f.x,r=f.y,f=f.z,s=c*(k-r)+h*(r-g)+l*(g-k);a.ha={x:-(g*(e-f)+k*(f-d)+r*(d-e))/s/2,y:-(d*(h-l)+e*(l-
c)+f*(c-h))/s/2}}}function d(a){for(b=0;b<p.length;b++){var c=p[b];c.tb=!M.Ua(a,c.ha)}}function g(a,b){var c=Array(b.length),d;for(d=0;d<c.length;d++)c[d]=[];for(d=0;d<a.length;d++){var e=a[d];if(!(0>e.Oa.z))for(var f=e.C,g=0;g<f.length;g++){var h=f[g];if(!(0>h.Oa.z)){var k=e.ma,l=k[(g+1)%3].index,k=k[g].index;2<l&&c[l-3].push([e,h,2<k?b[k-3]:null])}}}return c}function l(a){var b=[a[0]],c=a[0][0],d=a[0][1],e=a.length,f=1;a:for(;f<e;f++)for(var g=1;g<e;g++){var h=a[g];if(null!==h){if(h[1]===c)if(b.unshift(h),
c=h[0],a[g]=null,b.length===e)break a;else continue;if(h[0]===d&&(b.push(h),d=h[1],a[g]=null,b.length===e))break a}}b[0][0]!=b[e-1][1]&&b.push([b[e-1][1],b[0][0]]);return b}function e(a,b,c,d){var e=[],f=[],g=c.length,h,k=b.length,l=0,r=-1,s=-1,n=-1,p=null,q=d;for(d=0;d<g;d++){var w=(q+1)%g,u=c[q][0],E=c[w][0];if(M.d(u.ha,E.ha)>m)if(u.tb&&E.tb){var I=[],J=[];for(h=0;h<k;h++){r=(l+1)%k;if(p=M.za(b[l],b[r],u.ha,E.ha,!1))if(J.push(l),2===I.push(p))break;l=r}if(2===I.length){r=I[1];p=M.d(u.ha,I[0]);r=
M.d(u.ha,r);u=p<r?0:1;p=p<r?1:0;r=J[u];-1===s&&(s=r);if(-1!==n)for(;r!=n;)n=(n+1)%k,e.push(b[n]),f.push(null);e.push(I[u],I[p]);f.push(c[q][2],null);n=J[p]}}else if(u.tb&&!E.tb)for(h=0;h<k;h++){r=(l+1)%k;if(p=M.za(b[l],b[r],u.ha,E.ha,!1)){if(-1!==n)for(I=n;l!=I;)I=(I+1)%k,e.push(b[I]),f.push(null);e.push(p);f.push(c[q][2]);-1===s&&(s=l);break}l=r}else if(!u.tb&&E.tb)for(h=0;h<k;h++){r=(l+1)%k;if(p=M.za(b[l],b[r],u.ha,E.ha,!1)){e.push(u.ha,p);f.push(c[q][2],null);n=l;break}l=r}else e.push(u.ha),f.push(c[q][2]);
q=w}if(0==e.length)f=e=null;else if(-1!==n)for(;s!=n;)n=(n+1)%k,e.push(b[n]),f.push(null);a.o=e;a.C=f}if(1===k.length)k[0].o=f.slice(0),k[0].C=[];else{var b,h;h=a(f);var n=[],q;for(b=0;b<h.length;b++)q=h[b],n.push({x:q.x,y:q.y,z:q.x*q.x+q.y*q.y-q.f});for(b=0;b<k.length;b++)q=k[b],q.o=null,n.push({x:q.x,y:q.y,z:q.x*q.x+q.y*q.y-q.f});var p=za.S(n).Je;c();d(f);n=g(p,k);for(b=0;b<k.length;b++)if(q=n[b],0!==q.length){var r=k[b];q=l(q);var s=q.length,w=-1;for(h=0;h<s;h++)q[h][0].tb&&(w=h);if(0<=w)e(r,f,
q,w);else{var w=[],u=[];for(h=0;h<s;h++)M.d(q[h][0].ha,q[(h+1)%s][0].ha)>m&&(w.push(q[h][0].ha),u.push(q[h][2]));r.o=w;r.C=u}r.o&&3>r.o.length&&(r.o=null,r.C=null)}}};this.zc=function(k,f){var c,d,g=!1,l=k.length;for(d=0;d<l;d++)c=k[d],null===c.o&&(g=!0),c.pe=c.f;if(g){var g=a(f),e=[],b,h;d=k.length;for(c=0;c<g.length;c++)b=g[c],e.push({x:b.x,y:b.y,z:b.x*b.x+b.y*b.y});for(c=0;c<d;c++)b=k[c],e.push({x:b.x,y:b.y,z:b.x*b.x+b.y*b.y});b=za.S(e).Je;g=Array(d);for(c=0;c<d;c++)g[c]={};e=b.length;for(c=0;c<
e;c++)if(h=b[c],0<h.Oa.z){var n=h.ma,m=n.length;for(h=0;h<m-1;h++){var p=n[h].index-3,r=n[h+1].index-3;0<=p&&0<=r&&(g[p][r]=!0,g[r][p]=!0)}h=n[0].index-3;0<=r&&0<=h&&(g[r][h]=!0,g[h][r]=!0)}for(c=0;c<d;c++){h=g[c];b=k[c];var r=Number.MAX_VALUE,e=null,s;for(s in h)h=k[s],n=M.d(b,h),r>n&&(r=n,e=h);b.Wj=e;b.uf=Math.sqrt(r)}for(d=0;d<l;d++)c=k[d],s=Math.min(Math.sqrt(c.f),0.95*c.uf),c.f=s*s;this.S(k,f);for(d=0;d<l;d++)c=k[d],c.pe!==c.f&&0<c.uc&&(s=Math.min(c.uc,c.pe-c.f),c.f+=s,c.uc-=s)}}};var Ca=new function(){this.Cg=function(a){a=a.e;for(var m=0,k=a.length,f=0;f<k;f++){var c=a[f];if(c.o){var d=c.x,g=c.y;M.k(c.o,c);d=d-c.x;c=g-c.y;c=(0<d?d:-d)+(0<c?c:-c);m<c&&(m=c)}}return m};this.ya=function(a,m){var k=a.e,f,c,d,g;switch(m){case "random":return a.e[Math.floor(k.length*Math.random())];case "topleft":f=k[0];var l=f.x+f.y;for(g=1;g<k.length;g++)c=k[g],d=c.x+c.y,d<l&&(l=d,f=c);return f;case "bottomright":f=k[0];l=f.x+f.y;for(g=1;g<k.length;g++)c=k[g],d=c.x+c.y,d>l&&(l=d,f=c);return f;
default:f=k[0];d=c=M.d(a,f);for(g=k.length-1;1<=g;g--)l=k[g],c=M.d(a,l),c<d&&(d=c,f=l);return f}};this.Ja=function(a,m,k){var f=a.e;if(f[0].C){var c=f.length;for(a=0;a<c;a++)f[a].ld=!1,f[a].ic=0;var c=[],d,g;g=d=0;c[d++]=m||f[0];for(m=m.ic=0;g<d;)if(f=c[g++],!f.ld&&f.C){k(f,m++,f.ic);f.ld=!0;var l=f.C,e=l.length;for(a=0;a<e;a++){var b=l[a];b&&!0!==b.ld&&(0===b.ic&&(b.ic=f.ic+1),c[d++]=b)}}}else for(a=0;a<f.length;a++)k(f[a],a,1)}};var G=function(){function a(a,e,h,r,s,p,w,P){var F=D.extend({},l,a);1>a.lineHeight&&(a.lineHeight=1);a=F.fontFamily;var T=F.fontStyle+" "+F.fontVariant+" "+F.fontWeight,N=F.rb,U=F.Zc,t=T+" "+a;F.Ne=t;var z={la:!1,mc:0,fontSize:0};e.save();e.font=T+" "+x+"px "+a;e.textBaseline="middle";e.textAlign="center";m(e,F);h=h.trim();u.text=h;c(r,s,p,y);if(/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/.test(h))f(u),k(e,u,t),d(F,u,y,U,N,!0,z);else if(k(e,u,t),d(F,u,y,U,N,!1,
z),!z.la&&(w&&(f(u),k(e,u,t)),P||w))P&&(z.ec=!0),d(F,u,y,U,U,!0,z);if(z.la){var L="",E=0,I=Number.MAX_VALUE,J=Number.MIN_VALUE;g(F,u,z.mc,z.fontSize,y,z.ec,function(a,c){0<L.length&&c===b&&(L+=b);L+=a},function(a,b,c,d,f){d===q&&(L+=n);e.save();e.translate(p.x,b);a=z.fontSize/x;e.scale(a,a);e.fillText(L,0,0);e.restore();L=c;E<f&&(E=f);I>b&&(I=b);J<b&&(J=b)});z.ea={x:p.x-E/2,y:I-z.fontSize/2,f:E,i:J-I+z.fontSize};e.restore()}else e.clear&&e.clear();return z}function m(a,c){var d=c.Ne,f=e[d];void 0===
f&&(f={},e[d]=f);f[b]=a.measureText(b).width;f[h]=a.measureText(h).width}function k(a,b,c){var d,f=b.text.split(/(\n|[ \f\r\t\v\u2028\u2029]+|\u00ad+|\u200b+)/),g=[],h=[],k=f.length>>>1;for(d=0;d<k;d++)g.push(f[2*d]),h.push(f[2*d+1]);2*d<f.length&&(g.push(f[2*d]),h.push(void 0));c=e[c];for(d=0;d<g.length;d++)f=g[d],k=c[f],void 0===k&&(k=a.measureText(f).width,c[f]=k);b.md=g;b.kg=h}function f(a){for(var c=a.text.split(/\s+/),d=[],e={".":!0,",":!0,";":!0,"?":!0,"!":!0,":":!0,"\u3002":!0},f=0;f<c.length;f++){var g=
c[f];if(3<g.length){for(var h="",h=h+g.charAt(0),h=h+g.charAt(1),k=2;k<g.length-2;k++){var l=g.charAt(k);e[l]||(h+=p);h+=l}h+=p;h+=g.charAt(g.length-2);h+=g.charAt(g.length-1);d.push(h)}else d.push(g)}a.text=d.join(b)}function c(a,b,c,d){for(var e,f,g=0;g<a.length;g++)a[g].y===b.y&&(void 0===e?e=g:f=g);void 0===f&&(f=e);e!==f&&a[f].x<a[e].x&&(g=e,e=f,f=g);d.o=a;d.q=b;d.vd=c;d.sf=e;d.tf=f}function d(a,b,c,d,e,f,h){var k=a.lineHeight,l=Math.max(a.cb,0.001),r=a.sb,n=b.md,s=c.vd,p=c.q,m=void 0,q=void 0;
switch(a.verticalAlign){case "top":s=p.y+p.i-s.y;break;case "bottom":s=s.y-p.y;break;default:s=2*Math.min(s.y-p.y,p.y+p.i-s.y)}r=Math.min(s,r*c.q.i);if(0>=r)h.la=!1;else{s=d;e=Math.min(e,r);p=Math.min(1,r/Math.max(20,b.md.length));do{var w=(s+e)/2,u=Math.min(n.length,Math.floor((r+w*(k-1-2*l))/(w*k))),y=void 0;if(0<u){var x=1,Y=u;do{var Z=Math.floor((x+Y)/2);if(g(a,b,Z,w,c,f&&w===d&&Z===u,null,null)){if(Y=m=y=Z,x===Y)break}else if(x=Z+1,x>Y)break}while(1)}void 0!==y?s=q=w:e=w}while(e-s>p);void 0===
q?(h.la=!1,h.fontSize=0):(h.la=!0,h.fontSize=q,h.mc=m,h.ec=f&&w===s)}}function g(a,c,d,f,g,k,l,n){var p=a.ob,m=f*(a.lineHeight-1),q=a.verticalAlign,u=Math.max(a.cb,0.001);a=e[a.Ne];var y=c.md;c=c.kg;var z=g.o,L=g.vd,E=g.sf,I=g.tf,J;switch(q){case "top":g=L.y+f/2+f*u;J=1;break;case "bottom":g=L.y-(f*d+m*(d-1))+f/2-f*u;J=-1;break;default:g=L.y-(f*(d-1)/2+m*(d-1)/2),J=1}q=g;for(u=0;u<d;u++)r[2*u]=g-f/2,r[2*u+1]=g+f/2,g+=J*f,g+=J*m;for(;s.length<r.length;)s.push(Array(2));u=r;g=2*d;J=s;for(var R=z.length,
Y=E,E=(E-1+R)%R,Z=I,I=(I+1)%R,W=0;W<g;){for(var ea=u[W],aa=z[E];aa.y<ea;)Y=E,E=(E-1+R)%R,aa=z[E];for(var ca=z[I];ca.y<ea;)Z=I,I=(I+1)%R,ca=z[I];var ma=z[Y],na=z[Z],ca=na.x+(ca.x-na.x)*(ea-na.y)/(ca.y-na.y);J[W][0]=ma.x+(aa.x-ma.x)*(ea-ma.y)/(aa.y-ma.y);J[W][1]=ca;W++}for(u=0;u<d;u++)z=2*u,g=L.x,J=g-s[z][0],R=s[z][1]-g,J=J<R?J:R,R=g-s[z+1][0],z=s[z+1][1]-g,z=R<z?R:z,w[u]=2*(J<z?J:z)-p*f;Y=a[b]*f/x;J=a[h]*f/x;p=0;E=w[p];L=0;z=void 0;for(u=0;u<y.length;u++){g=y[u];Z=c[u];R=a[g]*f/x;if(L+R<E&&y.length-
u>=d-p&&"\n"!=z)L+=R," "===Z&&(L+=Y),l&&l(g,z);else{if(R>E&&(p!==d-1||!k))return!1;if(p+1>=d){if(k){d=E-L-J;if(d>J||R>J)d=Math.floor(g.length*d/R),0<d&&l&&l(g.substring(0,d),z);l&&l(h,void 0);n&&n(p,q,g,z,L);return!0}return!1}p++;n&&n(p,q,g,z,L);q+=f;q+=m;E=w[p];L=R;" "===Z&&(L+=Y);if(R>E&&(p!==d||!k))return!1}z=Z}n&&n(p,q,void 0,void 0,L);return!0}var l={rb:72,Zc:0,lineHeight:1.05,ob:1,cb:0.5,sb:0.9,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",verticalAlign:"center"},
e={},b=" ",h="\u2026",n="\u2010",q="\u00ad",p="\u200b",r=[],s=[],w=[],u={text:"",md:void 0,kg:void 0},y={o:void 0,q:void 0,vd:void 0,sf:0,tf:0},x=100;return{Le:a,xe:function(b,c,d,e,f,g,h,k,l,r,s,n){var p,m=0,q=0;d=d.toString().trim();!n&&l.result&&d===l.rg&&Math.abs(r-l.ue)/r<=s&&(p=l.result,p.la&&(m=g.x-l.yg,q=g.y-l.zg,s=l.jd,c.save(),c.translate(m,q),s.Ta(c),c.restore()));p||(s=l.jd,s.clear(),p=a(b,s,d,e,f,g,h,k),p.la&&s.Ta(c),l.ue=r,l.yg=g.x,l.zg=g.y,l.result=p,l.rg=d);return p.la?{la:!0,mc:p.mc,
fontSize:p.fontSize,ea:{x:p.ea.x+m,y:p.ea.y+q,f:p.ea.f,i:p.ea.i},ec:p.ec}:{la:!1}},Ai:function(){return{ue:0,yg:0,zg:0,result:void 0,jd:new ga,rg:void 0}},Ea:l}}();var Da=new function(){function a(a,c){return function(d,g,l,e){function b(a,d,e,f,g){if(0!=a.length){var r=a.shift(),s=k(r),n,p,m,q;if(c(f,g)){n=d;m=s/f;do{s=r.shift();p=s.vc;q=p/m;p=s;var P=e,F=m;p.x=n+q/2;p.y=P+F/2;l&&h(s,n,e,q,m);n+=q}while(0<r.length);return b(a,d,e+m,f,g-m)}n=e;q=s/g;do s=r.shift(),p=s.vc,m=p/q,p=s,P=n,F=m,p.x=d+q/2,p.y=P+F/2,l&&h(s,d,n,q,m),n+=m;while(0<r.length);return b(a,d+q,e,f-q,g)}}function h(a,b,c,d,e){a.o=[{x:b,y:c},{x:b+d,y:c},{x:b+d,y:c+e},{x:b,y:c+e}]}var n=g.x,m=
g.y,p=g.f;g=g.i;if(0!=d.length)if(1==d.length)d[0].x=n+p/2,d[0].y=m+g/2,d[0].Ed=0,l&&h(d[0],n,m,p,g);else{d=d.slice(0);for(var r=0,s=0;s<d.length;s++)r+=d[s].T;r=p*g/r;for(s=0;s<d.length;s++)d[s].vc=d[s].T*r;e=a(d,p,g,[[d.shift()]],e);b(e,n,m,p,g)}}}function m(a,c,d,g){function l(a){return Math.max(Math.pow(h*a/b,d),Math.pow(b/(h*a),g))}var e=k(a),b=e*e,h=c*c;c=l(a[0].vc);for(e=1;e<a.length;e++)c=Math.max(c,l(a[e].vc));return c}function k(a){for(var c=0,d=0;d<a.length;d++)c+=a[d].vc;return c}this.te=
a(function(a,c,d,g,l){l=Math.pow(2,l);for(var e=1/l,b=c<d;0<a.length;){var h=g[g.length-1],n=a.shift(),q=b?c:d,p=b?l:e,r=b?e:l,s=m(h,q,p,r);h.push(n);q=m(h,q,p,r);s<q&&(h.pop(),g.push([n]),b?d-=k(h)/c:c-=k(h)/d,b=c<d)}return g},function(a,c){return a<c});this.Xb=a(function(a,c,d,g,l){function e(a){if(1<g.length){for(var d=g[g.length-1],e=g[g.length-2].slice(0),f=0;f<d.length;f++)e.push(d[f]);m(e,c,b,h)<a&&g.splice(-2,2,e)}}for(var b=Math.pow(2,l),h=1/b;0<a.length;){d=g[g.length-1];l=m(d,c,b,h);if(0==
a.length)return;var k=a.shift();d.push(k);var q=m(d,c,b,h);l<q&&(d.pop(),e(l),g.push([k]))}e(m(g[g.length-1],c,b,h));return g},function(){return!0})};function Ea(a){var m={},k=a.Ud,f;a.c.j("model:loaded",function(a){f=a});this.H=function(){a.c.p("api:initialized",this)};this.Dc=function(a,d,f,l){this.pd(m,d);this.qd(m,d);this.od(m,d,!1);l&&l(m);a(k,m,f)};this.ud=function(a,d,g,k,e,b,h){if(a){for(a=d.length-1;0<=a;a--){var n=d[a],m=D.extend({group:n.group},e);m[g]=k(n);b(m)}0<d.length&&h(D.extend({groups:$.Mc(f,k).map(function(a){return a.group})},e))}};this.qd=function(a,d){a.selected=d.selected;a.hovered=d.Eb;a.open=d.open;a.openness=d.Lb;a.exposed=
d.U;a.exposure=d.ka;a.transitionProgress=d.ua;a.revealed=!d.ca.Na();a.browseable=d.Qa?d.O:void 0;a.visible=d.$;a.labelDrawn=d.ra&&d.ra.la;return a};this.pd=function(a,d){var f=d.parent;a.group=d.group;a.parent=f&&f.group;a.weightNormalized=d.wg;a.level=d.K-1;a.siblingCount=f&&f.e.length;a.hasChildren=!d.empty();a.index=d.index;a.indexByWeight=d.Ed;a.description=d.description;a.attribution=d.na;return a};this.od=function(a,d,f){a.polygonCenterX=d.L.x;a.polygonCenterY=d.L.y;a.polygonArea=d.L.ja;a.boxLeft=
d.q.x;a.boxTop=d.q.y;a.boxWidth=d.q.f;a.boxHeight=d.q.i;if(d.ra&&d.ra.la){var k=d.ra.ea;a.labelBoxLeft=k.x;a.labelBoxTop=k.y;a.labelBoxWidth=k.f;a.labelBoxHeight=k.i;a.labelFontSize=d.ra.fontSize}f&&d.ba&&(a.polygon=d.ba.map(function(a){return{x:a.x,y:a.y}}),a.neighbors=d.C&&d.C.map(function(a){return a&&a.group}));return a}};var la=new function(){var a=window.console;this.Pa=function(a){throw"FoamTree: "+a;};this.info=function(m){a.info("FoamTree: "+m)};this.warn=function(m){a.warn("FoamTree: "+m)}};function Fa(a){function m(b,d){b.e=[];b.La=!0;var e=c(d),f=0;if(("flattened"===a.xb||"always"===a.ah&&b.group&&b.group.description)&&0<d.length&&0<b.K){var g=d.reduce(function(a,b){return a+D.B(b.weight,1)},0),h=k(b.group,!1);h.description=!0;h.T=g*a.cc;h.index=f++;h.parent=b;h.K=b.K+1;h.id=h.id+"_d";b.e.push(h)}for(g=0;g<d.length;g++){var l=d[g],h=D.B(l.weight,1);if(0>=h)if(a.wj)h=0.9*e;else continue;l=k(l,!0);l.T=h;l.index=f;l.parent=b;l.K=b.K+1;b.e.push(l);f++}}function k(a,b){var c=new Ga;f(a);
c.id=a.__id;c.group=a;b&&(n[a.__id]=c);return c}function f(a){D.N(a,"__id")||(Object.defineProperty(a,"__id",{enumerable:!1,configurable:!1,writable:!1,value:h}),h++)}function c(a){for(var b=Number.MAX_VALUE,c=0;c<a.length;c++){var d=a[c].weight;0<d&&b>d&&(b=d)}b===Number.MAX_VALUE&&(b=1);return b}function d(a){if(!a.empty()){a=a.e;var b=0,c;for(c=a.length-1;0<=c;c--){var d=a[c].T;b<d&&(b=d)}for(c=a.length-1;0<=c;c--)d=a[c],d.wg=d.T/b}}function g(a){if(!a.empty()){a=a.e.slice(0).sort(function(a,b){return a.T<
b.T?1:a.T>b.T?-1:a.index-b.index});for(var b=0;b<a.length;b++)a[b].Ed=b}}function l(){for(var c=b.e.reduce(function(a,b){return a+b.T},0),d=0;d<b.e.length;d++){var e=b.e[d];e.na&&(e.T=Math.max(0.025,a.Tg)*c)}}var e=this,b=new Ga,h,n,q,p,r;this.H=function(){return b};this.S=function(b){var c=b.group.groups,e=a.si;return!b.e&&!b.description&&c&&0<c.length&&r+c.length<=e?(r+=c.length,m(b,c),d(b),g(b),!0):!1};this.Z=function(a){function c(a){var b=a.groups;if(b)for(var d=0;d<b.length;d++){var e=b[d];
f(e);var g=e.__id;n[g]=null;p[g]=a;g=e.id;D.V(g)||(q[g]=e);c(e)}}function e(a,b){if(!a)return b;var c=Math.max(b,a.__id||0),d=a.groups;if(d&&0<d.length)for(var f=d.length-1;0<=f;f--)c=e(d[f],c);return c}b.group=a;b.Ca=!1;b.O=!1;b.Qa=!1;b.open=!0;b.Lb=1;h=e(a,0)+1;n={};q={};p={};r=0;a&&(f(a),n[a.__id]=b,D.V(a.id)||(q[a.id]=a),c(a));m(b,a&&a.groups||[]);(function(a){if(!a.empty()){var b=k({attribution:!0});b.index=a.e.length;b.parent=a;b.K=a.K+1;b.na=!0;a.e.push(b)}})(b);d(b);l();g(b)};this.update=
function(a){a.forEach(function(a){$.Fa(a,function(a){if(!a.empty()){a=a.e;for(var b=c(a.map(function(a){return a.group})),d=0;d<a.length;d++){var e=a[d];e.T=0<e.group.weight?e.group.weight:0.9*b}}});d(a);a===b&&l();g(a)})};this.k=function(a){return function(){if(D.V(a)||D.mf(a))return[];if(Array.isArray(a))return a.map(e.d,e);if(D.jc(a)){if(D.N(a,"__id"))return[e.d(a)];if(D.N(a,"all")){var c=[];$.F(b,function(a){c.push(a)});return c}if(D.N(a,"groups"))return e.k(a.groups)}return[e.d(a)]}().filter(function(a){return void 0!==
a})};this.d=function(a){if(D.jc(a)&&D.N(a,"__id")){if(a=a.__id,D.N(n,a)){if(null===n[a]){for(var b=p[a],c=[];b;){b=b.__id;c.push(b);if(n[b])break;b=p[b]}for(b=c.length-1;0<=b;b--)this.S(n[c[b]])}return n[a]}}else if(D.N(q,a))return this.d(q[a])};this.A=function(a,b,c){return{e:e.k(a),Ia:D.B(a&&a[b],!0),Ha:D.B(a&&a.keepPrevious,c)}}}
function Ha(a,m,k){var f={};m.Ha&&$.F(a,function(a){k(a)&&(f[a.id]=a)});a=m.e;m=m.Ia;for(var c=a.length-1;0<=c;c--){var d=a[c];f[d.id]=m?d:void 0}var g=[];D.Ga(f,function(a){void 0!==a&&g.push(a)});return g};function Ia(a){function m(a,b){var c=a.ka;b.opacity=1;b.Ka=1;b.va=0>c?1-p.ei/100*c:1;b.wa=0>c?1-p.fi/100*c:1;b.fa=0>c?1+0.5*c:1}function k(a){a=a.ka;return Math.max(0.001,0===a?1:1+a*(p.Wa-1))}function f(a,b){for(var c=a.reduce(function(a,b){a[b.id]=b;return a},{}),d=a.length-1;0<=d;d--)$.F(a[d],function(a){c[a.id]=void 0});var e=[];D.Ga(c,function(a){a&&$.Ge(a,function(a){a.open||e.push(a)})});var f=[];D.Ga(c,function(a){a&&a.open&&f.push(a)});d=[];0!==e.length&&d.push(x.Kb({e:e,Ia:!0,Ha:!0},b,!0));
return pa(d)}function c(c,f,k,n){var m=l();if(0===c.length&&!m)return(new V).J().M();var q=c.reduce(function(a,b){a[b.id]=!0;return a},{}),s=[];c=[];if(A.reduce(function(a,b){return a||q[b.id]&&(!b.U||1!==b.ka)||!q[b.id]&&!b.parent.U&&(b.U||-1!==b.ka)},!1)){var x=[],B={};A.forEach(function(a){q[a.id]&&(a.U||s.push(a),a.U=!0,$.Fa(a,function(a){x.push(b(a,1));B[a.id]=!0}))});0<x.length?($.F(r,function(a){q[a.id]||(a.U&&s.push(a),a.U=!1);B[a.id]||x.push(b(a,-1))}),c.push(y.D.m({}).Ya(x).call(h).ab()),
d(q),c.push(g(m)),k&&(u.sc(C,p.Qc,p.Va,X.pa(p.gc)),u.Qb())):(c.push(e(k)),f&&$.F(r,function(a){a.U&&s.push(a)}))}return pa(c).P(function(){w.ud(f,s,"exposed",function(a){return a.U},{indirect:n},a.options.Df,a.options.Cf)})}function d(a){A.reduce(n(!0,void 0,function(b){return b.U||a[b.id]}),q(C));C.x-=C.f*(p.Wa-1)/2;C.y-=C.i*(p.Wa-1)/2;C.f*=p.Wa;C.i*=p.Wa}function g(b){if(b||!u.Rd())return y.D.m(s).ia({duration:0.7*p.Va,G:{x:{end:C.x+C.f/2,R:X.pa(p.gc)},y:{end:C.y+C.i/2,R:X.pa(p.gc)}},da:function(){a.c.p("foamtree:dirty",
!0)}}).ab();s.x=C.x+C.f/2;s.y=C.y+C.i/2;return(new V).J().M()}function l(){return!!A&&A.reduce(function(a,b){return a||0!==b.ka},!1)}function e(a){var c=[],d=[];$.F(r,function(a){0!==a.ka&&d.push(b(a,0,function(){this.U=!1}))});c.push(y.D.m({}).Ya(d).ab());u.content(0,0,K,B);a&&(c.push(u.reset(p.Va,X.pa(p.gc))),u.Qb());return pa(c)}function b(b,c,d){var e=y.D.m(b);0===b.ka&&0!==c&&e.call(function(){this.Cc(H);this.Ab(m)});e.ia({duration:p.Va,G:{ka:{end:c,R:X.pa(p.gc)}},da:function(){r.I=!0;r.Ma=!0;
a.c.p("foamtree:dirty",!0)}});0===c&&e.call(function(){this.Nd();this.nc();this.fd(H);this.ed(m)});return e.call(d).xa()}function h(){var a=r.e.reduce(n(!1,H.Ub,void 0),q({})).ea,b=p.Qc,c=Math.min(a.x,C.x-C.f*b),d=Math.max(a.x+a.f,C.x+C.f*(1+b)),e=Math.min(a.y,C.y-C.i*b),a=Math.max(a.y+a.i,C.y+C.i*(1+b));u.content(c,e,d-c,a-e)}function n(a,b,c){var d={};return function(e,f){if(!c||c(f)){for(var g=a?f.ba||f.o:f.o,h,k=g.length-1;0<=k;k--)h=void 0!==b?b(f,g[k],d):g[k],e.$c=Math.min(e.$c,h.x),e.Od=Math.max(e.Od,
h.x),e.ad=Math.min(e.ad,h.y),e.Pd=Math.max(e.Pd,h.y);e.ea.x=e.$c;e.ea.y=e.ad;e.ea.f=e.Od-e.$c;e.ea.i=e.Pd-e.ad}return e}}function q(a){return{$c:Number.MAX_VALUE,Od:Number.MIN_VALUE,ad:Number.MAX_VALUE,Pd:Number.MIN_VALUE,ea:a}}var p=a.options,r,s,w,u,y,x,A,C,K,B,H={qf:function(a,b){b.scale=k(a);return!1},Tb:function(a,b){var c=k(a),d=s.x,e=s.y;b.translate(d,e);b.scale(c,c);b.translate(-d,-e)},Vb:function(a,b,c){a=k(a);var d=s.x,e=s.y;c.x=(b.x-d)/a+d;c.y=(b.y-e)/a+e},Ub:function(a,b,c){a=k(a);var d=
s.x,e=s.y;c.x=(b.x-d)*a+d;c.y=(b.y-e)*a+e;return c}};a.c.j("stage:initialized",function(a,b,c,d){s={x:c/2,y:d/2};K=c;B=d;C={x:0,y:0,f:K,i:B}});a.c.j("stage:resized",function(a,b,c,d){s.x*=c/a;s.y*=d/b;K=c;B=d});a.c.j("api:initialized",function(a){w=a});a.c.j("zoom:initialized",function(a){u=a});a.c.j("model:loaded",function(a,b){r=a;A=b});a.c.j("model:childrenAttached",function(a){A=a});a.c.j("timeline:initialized",function(a){y=a});a.c.j("openclose:initialized",function(a){x=a});var Q=["groupExposureScale",
"groupUnexposureScale","groupExposureZoomMargin"];a.c.j("options:changed",function(a){D.nb(a,Q)&&l()&&(d({}),u.Dj(C,p.Qc),u.Qb())});this.H=function(){a.c.p("expose:initialized",this)};this.fc=function(a,b,d,e){var g=a.e.reduce(function(a,b){for(var c=b;c=c.parent;)a[c.id]=!0;return a},{}),h=Ha(r,a,function(a){return a.U&&!a.open&&!g[a.id]}),k=new V;f(h,b).P(function(){c(h.filter(function(a){return a.o&&a.ba}),b,d,e).P(k.J)});return k.M()}};function Ja(a){function m(c){function b(a,b){var c=Math.min(1,Math.max(0,a.ua));b.opacity=c;b.va=1;b.wa=c;b.Ka=c;b.fa=a.Hb}var h=a.options,k=h.rj,m=h.sj,p=h.oj,r=h.pj,s=h.qj,w=h.fe,u=k+m+p+r+s,y=0<u?w/u:0,x=[];l.gb(h.gg,h.fg,h.hg,h.ig,h.eg);if(0===y&&c.e&&c.O){w=c.e;for(u=0;u<w.length;u++){var A=w[u];A.ua=1;A.Hb=1;A.Ab(b);A.nc();A.ed(b)}c.I=!0;a.c.p("foamtree:dirty",0<y);return(new V).J().M()}if(c.e&&c.O){Ca.Ja(c,Ca.ya(c,a.options.he),function(c,d,e){c.Cc(l);c.Ab(b);e="groups"===a.options.ge?e:d;
d=f.D.m(c).eb(e*y*k).ia({duration:y*m,G:{ua:{end:1,R:X.pa(h.nj)}},da:function(){this.I=!0;a.c.p("foamtree:dirty",0<y)}}).xa();e=f.D.m(c).eb(g?y*(p+e*r):0).ia({duration:g?y*s:0,G:{Hb:{end:1,R:X.Jb}},da:function(){this.I=!0;a.c.p("foamtree:dirty",0<y)}}).xa();c=f.D.m(c).Ya([d,e]).oe().ib(function(){this.Nd();this.nc();this.fd(l);this.ed(b)}).xa();x.push(c)});d.d();var C=new V;f.D.m({}).Ya(x).call(function(){d.k();C.J()}).start();return C.M()}return(new V).J().M()}var k,f,c=[],d=new qa(D.ta);a.c.j("stage:initialized",
function(){});a.c.j("stage:resized",function(){});a.c.j("stage:newLayer",function(a,b){c.push(b)});a.c.j("model:loaded",function(a){k=a;d.clear()});a.c.j("zoom:initialized",function(){});a.c.j("timeline:initialized",function(a){f=a});var g=!1;a.c.j("render:renderers:resolved",function(a){g=a.labelPlainFill||!1});var l=new function(){var a=0,b=0,c=0,d=0,f=0,g=0;this.gb=function(k,l,m,u,y){a=1+l;b=1-a;c=m;d=u;f=y;g=k};this.qf=function(g,k){k.scale=a+b*g.ua;return 0!==f||0!==c||0!==d};this.Tb=function(k,
l){var m=a+b*k.ua,u=k.parent,y=g*k.x+(1-g)*u.x,x=g*k.y+(1-g)*u.y;l.translate(y,x);l.scale(m,m);m=1-k.ua;l.rotate(f*Math.PI*m);l.translate(-y,-x);l.translate(u.q.f*c*m,u.q.i*d*m)};this.Vb=function(f,k,l){var m=a+b*f.ua,q=g*f.x+(1-g)*f.parent.x,x=g*f.y+(1-g)*f.parent.y,A=1-f.ua;f=f.parent;l.x=(k.x-q)/m+q-f.q.f*c*A;l.y=(k.y-x)/m+x-f.q.i*d*A};this.Ub=function(f,k,l){var m=a+b*f.ua,q=g*f.x+(1-g)*f.parent.x,x=g*f.y+(1-g)*f.parent.y,A=1-f.ua;f=f.parent;l.x=(k.x-q)*m+q-f.q.f*c*A;l.y=(k.y-x)*m+x-f.q.i*d*A}};
this.H=function(){};this.k=function(){function c(a,b){var d=Math.min(1,Math.max(0,a.ua));b.opacity=d;b.va=1;b.wa=d;b.Ka=d;b.fa=a.Hb}function b(a,b){var c=Math.min(1,Math.max(0,a.Zd));b.opacity=c;b.Ka=c;b.va=1;b.wa=1;b.fa=a.Hb}var h=a.options,m=h.Yd,q=h.Ki,p=h.Li,r=h.Mi,s=h.Gi,w=h.Hi,u=h.Ii,y=h.Ci,x=h.Di,A=h.Ei,C=s+w+u+y+x+A+q+p+r,K=0<C?m/C:0,B=[];d.A()?l.gb(h.Qi,h.Oi,h.Ri,h.Si,h.Ni):l.gb(h.gg,h.fg,h.hg,h.ig,h.eg);Ca.Ja(k,Ca.ya(k,a.options.Pi),function(d,k,m){var n="groups"===a.options.Ji?m:k;B.push(f.D.m(d).call(function(){this.Ab(c)}).eb(g?
K*(s+n*w):0).ia({duration:g?K*u:0,G:{Hb:{end:0,R:X.Jb}},da:function(){this.I=!0;a.c.p("foamtree:dirty",!0)}}).xa());$.F(d,function(c){B.push(f.D.m(c).call(function(){this.Cc(l);this.Ab(b)}).eb(K*(y+x*n)).ia({duration:K*A,G:{Zd:{end:0,R:X.Jb}},da:function(){this.I=!0;a.c.p("foamtree:dirty",!0)}}).ib(function(){this.selected=!1;this.fd(l)}).xa())});B.push(f.D.m(d).call(function(){this.Cc(l)}).eb(K*(q+p*n)).ia({duration:K*r,G:{ua:{end:0,R:X.pa(h.Fi)}},da:function(){this.I=!0;a.c.p("foamtree:dirty",!0)}}).ib(function(){this.selected=
!1;this.fd(l)}).xa())});return f.D.m({}).Ya(B).ab()};this.d=function(a){return m(a)}};function Ka(a){function m(a,b){var c=[];$.F(g,function(b){if(b.e){var d=D.N(a,b.id);b.open!==d&&(d||b.U||$.F(b,function(a){if(a.U)return c.push(b),!1}))}});if(0===c.length)return(new V).J().M();var f;for(f=c.length-1;0<=f;f--)c[f].open=!1;var k=d.fc({e:c,Ia:!0,Ha:!0},b,!0,!0);for(f=c.length-1;0<=f;f--)c[f].open=!0;return k}function k(d,b,h){function k(b,d){b.Ab(m);var e=c.D.m(b).ia({duration:a.options.cd,G:{Lb:{end:d?1:0,R:X.ze}},da:function(){this.I=!0;a.c.p("foamtree:dirty",!0)}}).call(function(){this.open=
d;b.fb=!1}).ib(function(){this.nc();this.ed(m);delete f[this.id]}).xa();return f[b.id]=e}function m(a,b){b.opacity=1-a.Lb;b.va=1;b.wa=1;b.fa=1;b.Ka=1}var p=[],r=[];$.F(g,function(a){if(a.O&&a.Y){var b=D.N(d,a.id),c=f[a.id];if(c&&c.Gb())c.stop();else if(a.open===b)return;a.fb=b;b||(a.open=b,a.Td=!1);r.push(a);p.push(k(a,b))}});return 0<p.length?(a.c.p("openclose:changing"),c.D.m({}).Ya(p).ab().P(function(){l.ud(b,r,"open",function(a){return a.open},{indirect:h},a.options.Lf,a.options.Kf)})):(new V).J().M()}
var f,c,d,g,l;a.c.j("api:initialized",function(a){l=a});a.c.j("model:loaded",function(a){g=a;f={}});a.c.j("timeline:initialized",function(a){c=a});a.c.j("expose:initialized",function(a){d=a});this.H=function(){a.c.p("openclose:initialized",this)};this.Kb=function(c,b,d){if("flattened"==a.options.xb)return(new V).J().M();c=Ha(g,c,function(a){return a.open||a.fb});for(var f=new V,l=0;l<c.length;l++)c[l].fb=!0;0<c.length&&a.c.p("foamtree:attachChildren",c);var p=c.reduce(function(a,b){a[b.id]=!0;return a},
{});m(p,b).P(function(){k(p,b,d).P(f.J)});return f.M()}};function La(a){function m(c,d){var g=Ha(k,c,function(a){return a.selected});$.F(k,function(a){!0===a.selected&&(a.selected=!a.selected,a.I=!a.I,a.$a=!a.$a)});var l;for(l=g.length-1;0<=l;l--){var e=g[l];e.selected=!e.selected;e.I=!e.I;e.$a=!e.$a}var b=[];$.F(k,function(a){a.I&&b.push(a)});0<b.length&&a.c.p("foamtree:dirty",!1);f.ud(d,b,"selected",function(a){return a.selected},{},a.options.Nf,a.options.Mf)}var k,f;a.c.j("api:initialized",function(a){f=a});a.c.j("model:loaded",function(a){k=a});this.H=
function(){a.c.p("select:initialized",this)};this.select=function(a,d){return m(a,d)}};function Ma(a){function m(a){return function(b){a.call(this,{x:b.x,y:b.y,scale:b.scale,wd:b.delta,ctrlKey:b.ctrlKey,metaKey:b.metaKey,altKey:b.altKey,shiftKey:b.shiftKey,wb:b.secondary,touches:b.touches})}}function k(){function b(a){return function(b){b.x*=N/q.clientWidth;b.y*=U/q.clientHeight;return a(b)}}"external"!==n.gf&&("hammerjs"===n.gf&&D.N(window,"Hammer")&&(E.H(q),E.m("tap",b(h.d),!0),E.m("doubletap",b(h.k),!0),E.m("hold",b(h.ya),!0),E.m("touch",b(h.Aa),!1),E.m("release",b(h.Ba),!1),E.m("dragstart",
b(h.Z),!0),E.m("drag",b(h.A),!0),E.m("dragend",b(h.S),!0),E.m("transformstart",b(h.Ua),!0),E.m("transform",b(h.Ja),!0),E.m("transformend",b(h.bb),!0)),F=new ta(q),T=new sa,F.d(b(h.d)),F.k(b(h.k)),F.ya(b(h.ya)),F.Ba(b(h.Aa)),F.Pa(b(h.Ba)),F.Z(b(h.Z)),F.A(b(h.A)),F.S(b(h.S)),F.za(b(h.za)),F.Ja(b(h.za)),F.Aa(b(h.Pa)),T.addEventListener("keyup",function(b){var c=!1,d=void 0,e=n.Rf({keyCode:b.keyCode,preventDefault:function(){c=!0},preventOriginalEventDefault:function(){d="prevent"},allowOriginalEventDefault:function(){d=
"allow"}});"prevent"===d&&b.preventDefault();(c=c||0<=e.indexOf(!1))||27===b.keyCode&&a.c.p("interaction:reset")}))}function f(){p.Hc(2)?a.c.p("interaction:reset"):p.normalize(n.wc,X.pa(n.xc))}function c(a){return function(){x.empty()||a.apply(this,arguments)}}function d(a,b,c){var d={},f={};return function(g){var h;switch(a){case "click":h=n.xf;break;case "doubleclick":h=n.yf;break;case "hold":h=n.Ef;break;case "hover":h=n.Ff;break;case "mousemove":h=n.Hf;break;case "mousewheel":h=n.Jf;break;case "mousedown":h=
n.Gf;break;case "mouseup":h=n.If;break;case "dragstart":h=n.Bf;break;case "drag":h=n.zf;break;case "dragend":h=n.Af;break;case "transformstart":h=n.Qf;break;case "transform":h=n.Of;break;case "transformend":h=n.Pf}var k=!1,m=!h.empty(),r=p.absolute(g,d),q=(b||m)&&l(r),s=(b||m)&&e(r);m&&(m=q?q.group:null,r=q?q.Vb(r,f):r,g.Mb=void 0,h=h({type:a,group:m,topmostClosedGroup:m,bottommostOpenGroup:s?s.group:null,x:g.x,y:g.y,xAbsolute:r.x,yAbsolute:r.y,scale:D.B(g.scale,1),secondary:g.wb,touches:D.B(g.touches,
1),delta:D.B(g.wd,0),ctrlKey:g.ctrlKey,metaKey:g.metaKey,altKey:g.altKey,shiftKey:g.shiftKey,preventDefault:function(){k=!0},preventOriginalEventDefault:function(){g.Mb="prevent"},allowOriginalEventDefault:function(){g.Mb="allow"}}),k=k||0<=h.indexOf(!1),q&&q.na&&"click"===a&&(k=!1));k||c&&c({Ec:q,Vg:s},g)}}function g(a){function b(a,c){var d=c.e;if(d){for(var e=-Number.MAX_VALUE,f,g=0;g<d.length;g++){var h=d[g];!h.description&&h.$&&I(h,a)&&h.scale>e&&(f=h,e=h.scale)}var k;f&&(k=b(a,f));return k||
f||c}return c}for(var c=t.length,d=t[0].scale,e=t[0].scale,f=0;f<c;f++){var g=t[f],g=g.scale;g<d&&(d=g);g>e&&(e=g)}if(d!==e)for(f=0;f<c;f++)if(g=t[f],g.scale===e&&g.$&&I(g,a))return b(a,g);return b(a,x)}function l(a,b){var c;if("flattened"===n.xb)c=g(a);else{c=b||0;for(var d=t.length,e=void 0,f=0;f<d;f++){var h=t[f];h.scale>c&&!1===h.open&&h.$&&I(h,a)&&(e=h,c=h.scale)}c=e}c&&c.description&&(c=c.parent);return c}function e(a){var b=void 0,c=0;$.Kc(x,function(d){!0===d.open&&d.$&&d.scale>c&&I(d,a)&&
(b=d,c=d.scale)});return b}var b=v.mi(),h=this,n=a.options,q,p,r,s,w,u,y,x,A=!1,C,K,B,H,Q,O,P,F,T,N,U;a.c.j("stage:initialized",function(a,b,c,d){q=b;N=c;U=d;k()});a.c.j("stage:resized",function(a,b,c,d){N=c;U=d});a.c.j("stage:disposed",function(){F.kb();E.kb();T.d()});a.c.j("expose:initialized",function(a){s=a});a.c.j("zoom:initialized",function(a){p=a});a.c.j("openclose:initialized",function(a){w=a});a.c.j("select:initialized",function(a){u=a});a.c.j("titlebar:initialized",function(a){y=a});a.c.j("timeline:initialized",
function(a){r=a});var t;a.c.j("model:loaded",function(a,b){x=a;t=b});a.c.j("model:childrenAttached",function(a){t=a});this.H=function(){};this.Aa=c(d("mousedown",!1,function(){p.xi()}));this.Ba=c(d("mouseup",!1,void 0));this.d=c(d("click",!0,function(a,b){if(!b.wb&&!b.shiftKey){var c=a.Ec;c&&(c.na?document.location.href=xa.jg("iuuq;..b`ssnurd`sbi/bnl.gn`lusdd"):u.select({e:[c],Ia:!c.selected,Ha:b.metaKey||b.ctrlKey},!0))}}));this.k=c(d("doubleclick",!0,function(b,c){var d,e;if(c.wb||c.shiftKey){if(d=
b.Ec)d.parent.U&&(d=d.parent),e={e:d.parent!==x?[d.parent]:[],Ia:!0,Ha:!1},u.select(e,!0),s.fc(e,!0,!0,!1)}else if(d=b.Ec)e={e:[d],Ia:!0,Ha:!1},d.fb=!0,a.c.p("foamtree:attachChildren",[d]),s.fc(e,!0,!0,!1);d&&r.D.m({}).eb(n.Va/2).call(function(){w.Kb({e:$.Mc(x,function(a){return a.Td&&!$.li(d,a)}),Ia:!1,Ha:!0},!0,!0);d.Td=!0;w.Kb({e:[d],Ia:!(c.wb||c.shiftKey),Ha:!0},!0,!0)}).start()}));this.ya=c(d("hold",!0,function(a,b){var c=!(b.metaKey||b.ctrlKey||b.shiftKey)&&!b.wb,d;(d=c?a.Ec:a.Vg)&&d!==x&&w.Kb({e:[d],
Ia:c,Ha:!0},!0,!1)}));this.Z=c(d("dragstart",!1,function(a,b){C=b.x;K=b.y;B=Date.now();A=!0}));this.A=c(d("drag",!1,function(a,b){if(A){var c=Date.now();O=Math.min(1,c-B);B=c;var c=b.x-C,d=b.y-K;p.vi(c,d);H=c;Q=d;C=b.x;K=b.y}}));this.S=c(d("dragend",!1,function(){if(A){A=!1;var a=Math.sqrt(H*H+Q*Q)/O;4<=a?p.wi(a,H,Q):p.vf()}}));this.Ua=c(d("transformstart",!1,function(a,b){P=1;C=b.x;K=b.y}));var z=1,L=!1;this.Ja=c(d("transform",!1,function(a,b){var c=b.scale-0.01;p.Pg(b,c/P,b.x-C,b.y-K);P=c;C=b.x;
K=b.y;z=P;L=L||2<b.touches}));this.bb=c(d("transformend",!1,function(){L&&0.8>z?a.c.p("interaction:reset"):f();L=!1}));this.Pa=c(d("mousewheel",!1,function(){var a=D.$g(function(){f()},300);return function(c,d){var e=n.Jj;1!==e&&(e=Math.pow(e,d.wd),b?(p.Qg(d,e),a()):p.Yb(d,e,n.wc,X.pa(n.xc)).P(f))}}()));this.za=c(function(){var b=void 0,c={},e=!1,f,g=d("hover",!1,function(){b&&(b.Eb=!1,0<b.K&&(b.I=!0));f&&(f.Eb=!0,0<f.K&&(f.I=!0));y.update(f);a.c.p("foamtree:dirty",!1)}),h=d("mousemove",!1,void 0);
return function(a){if("out"===a.type)f=void 0,e=f!==b;else if(p.absolute(a,c),b&&!b.open&&I(b,c)){var d=l(c,b.scale);d&&d!==b?(e=!0,f=d):e=!1}else f=l(c),e=f!==b;e&&(g(a),b=f,e=!1);b&&h(a)}}());this.gb={click:m(this.d),doubleclick:m(this.k),hold:m(this.ya),mouseup:m(this.Ba),mousedown:m(this.Aa),dragstart:m(this.Z),drag:m(this.A),dragend:m(this.S),transformstart:m(this.Ua),transform:m(this.Ja),transformend:m(this.bb),hover:m(this.za),mousewheel:m(this.Pa)};var E=function(){function a(b,c){return function(a){a=
a.gesture;var d=a.center,d=ra.Ie(q,d.pageX,d.pageY,{});d.scale=a.scale;d.wb=1<a.touches.length;d.touches=a.touches.length;b.call(q,d);(void 0===d.Mb&&c||"prevent"===d.Mb)&&a.preventDefault()}}var b,c={};return{H:function(a){b=window.Hammer(a,{doubletap_interval:350,hold_timeout:400,doubletap_distance:10})},m:function(d,e,f){c[d]=e;b.on(d,a(e,f))},kb:function(){b&&D.Ga(c,function(a,c){b.off(c,a)})}}}(),I=function(){var a={};return function(b,c){b.Vb(c,a);return b.ba&&M.Ua(b.ba,a)}}()};function Na(a){function m(a,d,f,k){var e,b=0,h=[];for(e=0;e<d.length;e++){var m=Math.sqrt(M.d(d[e],d[(e+1)%d.length]));h.push(m);b+=m}for(e=0;e<h.length;e++)h[e]/=b;a[0].x=f.x;a[0].y=f.y;var q=m=b=0;for(e=1;e<a.length;e++){for(var p=a[e],r=0.95*Math.pow(e/a.length,k),b=b+0.3819;m<b;)m+=h[q],q=(q+1)%h.length;var s=(q-1+h.length)%h.length,w=1-(m-b)/h[s],u=d[s].x,s=d[s].y,y=d[q].x,x=d[q].y,u=(u-f.x)*r+f.x,s=(s-f.y)*r+f.y,y=(y-f.x)*r+f.x,x=(x-f.y)*r+f.y;p.x=u*(1-w)+y*w;p.y=s*(1-w)+x*w}}var k={random:{Fb:function(a,
d){for(var f=0;f<a.length;f++){var k=a[f];k.x=d.x+Math.random()*d.f;k.y=d.y+Math.random()*d.i}},Zb:"box"},ordered:{Fb:function(a,d){var g=a.slice(0);f.lc&&g.sort(Oa);Da.Xb(g,d,!1,f.ce)},Zb:"box"},squarified:{Fb:function(a,d){var g=a.slice(0);f.lc&&g.sort(Oa);Da.te(g,d,!1,f.ce)},Zb:"box"},fisheye:{Fb:function(a,d,g){a=a.slice(0);f.lc&&a.sort(Oa);m(a,d,g,0.25)},Zb:"polygon"},blackhole:{Fb:function(a,d,g){a=a.slice(0);f.lc&&a.sort(Oa).reverse();m(a,d,g,1)},Zb:"polygon"}};k.order=k.ordered;k.treemap=
k.squarified;var f=a.options;this.d=function(a,d,g){if(0<a.length){g=k[g.relaxationInitializer||g.initializer||f.ij||"random"];if("box"===g.Zb){var l=M.q(d,{});g.Fb(a,l);M.Bc(a,M.A(l),d)}else g.Fb(a,d,M.k(d,{}));for(l=a.length-1;0<=l;l--){g=a[l];if(g.description){a=M.qe(d,f.Ic,f.bh);g.x=a.x;g.y=a.y;break}if(g.na){a=M.qe(d,f.ve,f.Rg);g.x=a.x;g.y=a.y;break}}}}};function Pa(a){var m,k=a.options,f=new Qa(a,this),c=new Ra(a,this),d={relaxed:f,ordered:c,squarified:c},g=d[a.options.Wc]||f;this.Ag=5E-5;a.c.j("model:loaded",function(a){m=a});a.c.j("options:changed",function(a){a.layout&&D.N(d,k.Wc)&&(g=d[k.Wc])});this.step=function(a,c,b,d){return g.step(a,c,b,d)};this.complete=function(a){g.complete(a)};this.kf=function(a){return a===m?!0:2*Math.sqrt(a.L.ja/(Math.PI*a.e.length))>=Math.max(k.Ve,5E-5)};this.yd=function(a,c){for(var b=Math.pow(k.Ra,a.K),d=k.lb*b,
b=k.Ad*b,f=a.e,m=f.length-1;0<=m;m--){var p=f[m];g.we(p,b);var r=p;r.ba=0<d?Aa.bb(r.o,d):r.o;r.ba&&(M.q(r.ba,r.q),M.re(r.ba,r.L));p.e&&c.push(p)}};this.qc=function(a){g.qc(a)};this.Nb=function(a){g.Nb(a)}};function Qa(a,m){function k(a){if(a.e){a=a.e;for(var b=0;b<a.length;b++){var c=a[b];c.uc=c.rc*n.Rh}}}function f(a,d){m.kf(a)&&(a.u||(a.u=Aa.bb(a.o,n.Ad*Math.pow(n.Ra,a.K-1)),a.u&&a.e[0]&&a.e[0].description&&"stab"==n.dc&&l(a)),a.u&&(b.Nb(a),q.d(c(a),a.u,a.group),a.O=!0,d(a)),k(a))}function c(a){return"stab"===n.dc&&0<a.e.length&&a.e[0].description?a.e.slice(1):a.e}function d(a){var b=c(a);Ba.S(b,a.u);Ba.zc(b,a.u);return Ca.Cg(a)*Math.sqrt(h.L.ja/a.L.ja)}function g(a){return a<n.ag||1E-4>a}function l(a){var b=
n.cc/(1+n.cc),c=M.q(a.u,{}),d={x:c.x,y:0},e=c.y,f=c.i,g=n.Ce*Math.pow(n.Ra,a.K-1),h=f*n.Be,k=n.Ic;"bottom"==k||0<=k&&180>k?(k=Math.PI,e+=f,f=-1):(k=0,f=1);for(var l,m=a.u,p=k,q=0,P=1,F=M.k(m,{}),T=F.ja,b=T*b,N=0;q<P&&20>N++;){var U=(q+P)/2;d.y=c.y+c.i*U;l=M.Wb(m,d,p);M.k(l[0],F);var t=F.ja-b;if(0.01>=Math.abs(t)/T)break;else 0<(0==p?1:-1)*t?P=U:q=U}M.q(l[0],c);if(c.i<g||c.i>h)d.y=c.i<g?e+f*Math.min(g,h):e+f*h,l=M.Wb(a.u,d,k);a.e[0].o=l[0];a.u=l[1]}function e(a){a!==h&&2*Math.sqrt(a.L.ja/(Math.PI*
a.e.length))<Math.max(0.85*n.Ve,m.Ag)&&(a.O=!1,a.Ca=!1,a.Qa=!0,a.u=null)}var b=this,h,n=a.options,q=new Na(a),p=0;a.c.j("model:loaded",function(a){h=a;p=0});this.step=function(a,b,k,l){function q(b){b.O&&b.Ca?e(b):b.Qa&&b.o&&f(b,function(){var d=c(b);Ba.S(d,b.u);Ba.zc(d,b.u);a(b)});if(!b.u||!b.O)return 0;var h;b.parent&&b.parent.aa||b.La?(h=d(b),l&&l(b),b.La=!g(h)&&!k,b.aa=!0):h=0;m.yd(b,C);return h}function x(a,b,c){p<a&&(p=a);var d=n.ag;n.Sd(b?1:1-(a-d)/(p-d||1),b,c);b&&(p=0)}for(var A=0,C=[h];0<
C.length;)A=Math.max(A,q(C.shift()));var K=g(A);b&&x(A,K,k);return K};this.complete=function(a){for(var b=[h];0<b.length;){var c=b.shift();!c.O&&c.Qa&&c.o&&f(c,a);if(c.u){if(c.parent&&c.parent.aa||c.La){for(var e=1E-4>c.L.ja,k=0;!(g(d(c))||e&&32<k++););c.aa=!0;c.La=!1}m.yd(c,b)}}};this.qc=function(a){$.F(a,k)};this.we=function(a,b){if(a.O){var d=a.u;d&&(a.Xd=d);a.u=Aa.bb(a.o,b);a.u&&a.e[0]&&a.e[0].description&&"stab"==n.dc&&l(a);d&&!a.u&&(a.aa=!0);a.u&&a.Xd&&M.Bc(c(a),a.Xd,a.u)}};this.Nb=function(a){for(var b=
c(a),d=a.ja,e,f=e=0;f<b.length;f++)e+=b[f].T;a.ck=e;for(a=0;a<b.length;a++)f=b[a],f.pg=f.f,f.rc=d/Math.PI*(0<e?f.T/e:1/b.length)}};function Ra(a,m){function k(a,d){if(m.kf(a)){if(!a.u||a.parent&&a.parent.aa){var e=l.Ad*Math.pow(l.Ra,a.K-1);a.u=M.A(c(M.q(a.o,{}),e))}a.u&&(a.O=!0,d(a))}else a.O=!1,$.Fa(a,function(a){a.u=null})}function f(a){function c(a){function b(){e.o=M.A(f);e.x=f.x+f.f/2;e.y=f.y+f.i/2}var d=l.cc/(1+l.cc),e=a.e[0],f=M.q(a.u,{}),g=f.i,d=Math.min(Math.max(g*d,l.Ce*Math.pow(l.Ra,a.K-1)),g*l.Be),h=l.Ic;"bottom"==h||0<=h&&180>h?(f.i=g-d,a.u=M.A(f),f.y+=g-d,f.i=d,b()):(f.i=d,b(),f.y+=d,f.i=g-d,a.u=M.A(f))}var f;"stab"==
l.dc&&0<a.e.length&&a.e[0].description?(f=a.e.slice(1),c(a)):f=a.e;l.lc&&f.sort(Oa);"floating"==l.dc&&d(f,l.Ic,function(a){return a.description});d(f,l.ve,function(a){return a.na});var g=M.q(a.u,{});(e[l.Wc]||Da.Xb)(f,g,!0,l.ce);a.La=!1;a.aa=!0;a.I=!0;a.Ma=!0}function c(a,c){var d=2*c;a.x+=c;a.y+=c;a.f-=d;a.i-=d;return a}function d(a,c,d){for(var e=0;e<a.length;e++){var f=a[e];if(d(f)){a.splice(e,1);"topleft"==c||135<=c&&315>c?a.unshift(f):a.push(f);break}}}var g,l=a.options,e={squarified:Da.te,ordered:Da.Xb};
a.c.j("model:loaded",function(a){g=a});this.step=function(a,c,d){this.complete(a);c&&l.Sd(1,!0,d);return!0};this.complete=function(a){for(var c=[g];0<c.length;){var d=c.shift();(!d.O||d.parent&&d.parent.aa)&&d.Qa&&d.o&&k(d,a);d.u&&((d.parent&&d.parent.aa||d.La)&&f(d),m.yd(d,c))}};this.Nb=this.qc=this.we=D.ta};var Sa=new function(){this.Fg=function(a){a.beginPath();a.moveTo(3.2,497);a.bezierCurveTo(0.1,495.1,0,494.1,0,449.6);a.bezierCurveTo(0,403.5,-0.1,404.8,4.1,402.6);a.bezierCurveTo(5.2,402,7.4,401.4,9,401.2);a.bezierCurveTo(10.6,401,31.2,400.6,54.7,400.2);a.bezierCurveTo(99.5,399.4,101,399.5,104.6,402.3);a.bezierCurveTo(107.9,404.9,107.6,404,129.3,473.2);a.bezierCurveTo(131,478.6,132.9,484.4,133.4,486.1);a.bezierCurveTo(135.2,491.4,135.4,494.9,134,496.4);a.bezierCurveTo(132.8,497.7,131.7,497.7,68.6,
497.7);a.bezierCurveTo(24.2,497.7,4,497.5,3.2,497);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(162.1,497);a.bezierCurveTo(159.5,496.3,157.7,494.6,156.2,491.6);a.bezierCurveTo(155.5,490.3,148.7,469.4,141.1,445.2);a.bezierCurveTo(126.1,397.5,125.6,395.4,128.1,389.8);a.bezierCurveTo(129.5,386.7,164.1,339,168,334.9);a.bezierCurveTo(170.3,332.5,172.2,332.1,175.1,333.7);a.bezierCurveTo(176.1,334.2,189.3,347,204.3,362.1);a.bezierCurveTo(229.4,387.4,231.8,390,233.5,394);a.bezierCurveTo(235.2,
397.8,235.4,399.2,235.4,404.3);a.bezierCurveTo(235.3,415,230.5,489.9,229.8,492.5);a.bezierCurveTo(228.4,497.5,229.2,497.4,194.7,497.5);a.bezierCurveTo(177.8,497.6,163.1,497.4,162.1,497);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(258.7,497);a.bezierCurveTo(255.8,496.1,252.6,492.3,252,489.1);a.bezierCurveTo(251.4,484.8,256.8,405.2,258.1,401.1);a.bezierCurveTo(260.4,393.4,262.7,391.1,300.4,359.2);a.bezierCurveTo(319.9,342.6,337.7,327.9,339.9,326.5);a.bezierCurveTo(347.4,
321.6,350.4,321,372,320.5);a.bezierCurveTo(393.4,320,400.5,320.4,407.5,322.5);a.bezierCurveTo(413.9,324.4,487.4,359.5,490.6,362.1);a.bezierCurveTo(492,363.3,493.9,365.8,495,367.7);a.lineTo(496.8,371.2);a.lineTo(497,419.3);a.bezierCurveTo(497.1,445.7,497,468,496.8,468.8);a.bezierCurveTo(496.2,471.6,489.6,480.8,485,485.3);a.bezierCurveTo(478.6,491.7,474.9,494.1,468.2,496);a.lineTo(462.3,497.7);a.lineTo(361.6,497.7);a.bezierCurveTo(303.1,497.6,259.9,497.3,258.7,497);a.closePath();a.fillStyle="rgba(200,200,200,1)";
a.fill();a.beginPath();a.moveTo(4.4,380.8);a.bezierCurveTo(2.9,380.2,1.7,379.8,1.6,379.8);a.bezierCurveTo(1.5,379.8,1.2,378.8,0.7,377.6);a.bezierCurveTo(0.2,376.1,0,361.6,0,331.2);a.bezierCurveTo(0,281.2,-0.2,283.1,4.9,280.9);a.bezierCurveTo(7.1,279.9,19.3,278.2,54.8,274.1);a.bezierCurveTo(80.6,271.1,102.9,268.6,104.4,268.6);a.bezierCurveTo(105.8,268.6,109.1,269.4,111.7,270.4);a.bezierCurveTo(116,272.1,117.2,273.2,133.4,289.3);a.bezierCurveTo(150.9,306.8,153.4,310,153.4,314.5);a.bezierCurveTo(153.4,
317.6,151.1,321.3,136.4,341.2);a.bezierCurveTo(109.4,377.8,111.6,375.3,105.4,378.1);a.lineTo(101.3,380);a.lineTo(75.7,380.5);a.bezierCurveTo(6.8,381.8,7.3,381.8,4.4,380.8);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(243.5,372.4);a.bezierCurveTo(240.2,370.8,136.6,266.7,134.2,262.6);a.bezierCurveTo(132.1,259,131.7,254.9,133.2,251.3);a.bezierCurveTo(134.5,248.2,166.3,206,169.3,203.4);a.bezierCurveTo(172.6,200.5,178.5,198.4,183.2,198.4);a.bezierCurveTo(187.1,198.4,275.2,
204.1,281.6,204.8);a.bezierCurveTo(289.7,205.7,294.6,208.7,297.6,214.6);a.bezierCurveTo(300.5,220.3,327.4,297.4,327.8,301.1);a.bezierCurveTo(328.3,305.7,326.7,310.4,323.4,314);a.bezierCurveTo(322,315.6,307.8,327.9,291.9,341.3);a.bezierCurveTo(256.2,371.4,256.6,371.2,253.9,372.5);a.bezierCurveTo(251.1,373.9,246.5,373.9,243.5,372.4);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(489.3,339.1);a.bezierCurveTo(488.6,338.9,473.7,331.9,456.3,323.6);a.bezierCurveTo(435.9,313.9,
423.8,307.8,422.4,306.4);a.bezierCurveTo(419.5,303.7,418,300.2,418,296.1);a.bezierCurveTo(418,292.5,438,185,439.3,181.6);a.bezierCurveTo(441.2,176.6,445.5,173.1,450.8,172.1);a.bezierCurveTo(456,171.2,487.1,169.2,489.6,169.7);a.bezierCurveTo(493.1,170.3,495.5,171.9,497,174.7);a.bezierCurveTo(498.1,176.7,498.2,181.7,498.4,253.2);a.bezierCurveTo(498.5,295.3,498.4,330.9,498.2,332.5);a.bezierCurveTo(497.5,337.4,493.7,340.2,489.3,339.1);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();
a.moveTo(353.2,300.7);a.bezierCurveTo(350.4,299.8,347.9,297.9,346.5,295.6);a.bezierCurveTo(345.8,294.5,338.2,273.7,329.6,249.5);a.bezierCurveTo(314.6,207.1,314.1,205.3,314.1,200.4);a.bezierCurveTo(314.1,196.7,314.4,194.6,315.3,193);a.bezierCurveTo(316,191.7,322.5,181.6,329.8,170.6);a.bezierCurveTo(346.8,144.8,345.4,145.8,365.8,144.4);a.bezierCurveTo(380.9,143.4,385.7,143.7,390.6,146.3);a.bezierCurveTo(397.3,149.8,417.4,164.4,419.2,167);a.bezierCurveTo(422.4,171.8,422.4,171.8,410.6,234.4);a.bezierCurveTo(402.3,
278.6,399.3,293.2,398.1,295.3);a.bezierCurveTo(395.4,300.1,393.7,300.5,373,300.9);a.bezierCurveTo(363.1,301.1,354.2,301,353.2,300.7);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(6.2,259.9);a.bezierCurveTo(4.9,259.2,3.2,257.8,2.4,256.8);a.bezierCurveTo(1,254.9,1,254.8,0.8,148.7);a.bezierCurveTo(0.7,74,0.9,40.8,1.4,36.7);a.bezierCurveTo(2.3,29.6,4.7,24.4,9.8,18.3);a.bezierCurveTo(14.1,13.1,20.9,7.3,25,5.3);a.bezierCurveTo(26.5,4.6,31,3.3,34.9,2.6);a.bezierCurveTo(41.3,
1.3,44.2,1.2,68.5,1.4);a.lineTo(95.1,1.6);a.lineTo(99,3.5);a.bezierCurveTo(101.2,4.6,103.9,6.6,105.2,8.1);a.bezierCurveTo(107.7,11,153.1,88.2,155.8,94);a.bezierCurveTo(159.1,101.4,159.6,104.7,159.5,121.6);a.bezierCurveTo(159.5,147.8,158.4,177.2,157.3,181);a.bezierCurveTo(156.8,182.8,155.6,186.1,154.6,188.1);a.bezierCurveTo(152.6,192.2,119.5,237.2,115.1,241.8);a.bezierCurveTo(112.1,244.9,106.3,248.3,102,249.4);a.bezierCurveTo(99.2,250.1,13,261.1,10.1,261.1);a.bezierCurveTo(9.2,261.1,7.5,260.6,6.2,
259.9);a.closePath();a.fillStyle="rgba(200,200,200,1)";a.fill();a.beginPath();a.moveTo(234.1,183.4);a.bezierCurveTo(180.2,179.7,182.3,180,179.5,174.5);a.lineTo(178,171.4);a.lineTo(178.7,142.4);a.bezierCurveTo(179.4,114.8,179.5,113.3,180.9,110.4);a.bezierCurveTo(183.5,105,182.7,105.2,237.9,95.3);a.bezierCurveTo(285.1,86.7,287.9,86.3,291,87.1);a.bezierCurveTo(292.8,87.6,295.3,88.8,296.7,89.9);a.bezierCurveTo(299.1,91.8,321.9,124.4,325,130.3);a.bezierCurveTo(326.9,134,327.2,139.1,325.7,142.6);a.bezierCurveTo(324.5,
145.5,302.5,179.1,300.2,181.5);a.bezierCurveTo(297,184.9,293.5,186.3,287.4,186.5);a.bezierCurveTo(284.4,186.6,260.4,185.2,234.1,183.4);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(435.8,153.4);a.bezierCurveTo(434.8,153.1,433,152.3,431.7,151.6);a.bezierCurveTo(428.4,150,410.1,137.1,407,134.4);a.bezierCurveTo(404.1,131.8,402.7,128.3,403.2,125.1);a.bezierCurveTo(403.6,122.9,420.3,81.3,423,75.9);a.bezierCurveTo(424.7,72.6,426.6,70.4,429.3,68.9);a.bezierCurveTo(431.1,67.9,
435,67.7,462.2,67.6);a.lineTo(493.1,67.3);a.lineTo(495.4,69.6);a.bezierCurveTo(497,71.3,497.8,72.8,498.1,75);a.bezierCurveTo(498.4,76.6,498.5,92.9,498.4,111.1);a.bezierCurveTo(498.2,141.2,498.1,144.3,497,146.3);a.bezierCurveTo(494.8,150.3,493.3,150.6,470.3,152.4);a.bezierCurveTo(448.6,154,438.8,154.3,435.8,153.4);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(346.6,125.3);a.bezierCurveTo(345,124.5,342.6,122.6,341.4,121);a.bezierCurveTo(337.1,115.7,313,79.8,311.6,76.7);
a.bezierCurveTo(309.4,71.7,309.3,68,311.2,58.2);a.bezierCurveTo(319.2,16.9,321.3,7.1,322.4,5.2);a.bezierCurveTo(323.1,4,324.7,2.4,326,1.6);a.bezierCurveTo(328.3,0.3,329.4,0.3,353.9,0.3);a.bezierCurveTo(379.2,0.3,379.5,0.3,382.4,1.8);a.bezierCurveTo(384,2.7,386,4.5,386.9,5.9);a.bezierCurveTo(388.6,8.6,405.1,46.3,407.2,52.2);a.bezierCurveTo(408.7,56.3,408.8,60.7,407.7,64.1);a.bezierCurveTo(407.3,65.4,402.2,78.2,396.3,92.7);a.bezierCurveTo(382.6,126.3,384.1,124.6,366.6,126);a.bezierCurveTo(353.4,127.1,
350,127,346.6,125.3);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(179.2,85.6);a.bezierCurveTo(175.7,84.6,171.9,82,170,79.2);a.bezierCurveTo(167.2,75.2,130.6,12.4,129.3,9.3);a.bezierCurveTo(128.2,6.7,128.1,5.9,128.8,4.2);a.bezierCurveTo(130.5,0,125.2,0.3,211.7,0);a.bezierCurveTo(255.3,-0.1,292.2,0,293.9,0.3);a.bezierCurveTo(297.7,0.8,301.1,4,301.8,7.6);a.bezierCurveTo(302.3,10.5,293.9,55.2,291.9,59.6);a.bezierCurveTo(290.4,63,286.1,66.9,282.3,68.3);a.bezierCurveTo(279.6,
69.3,193.5,85.1,185.5,86.1);a.bezierCurveTo(183.8,86.3,181,86.1,179.2,85.6);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(431.9,47.7);a.bezierCurveTo(428.7,46.9,426.4,45.2,424.6,42.3);a.bezierCurveTo(421.8,37.8,409.2,7.7,409.2,5.5);a.bezierCurveTo(409.2,1.2,408,1.3,451.6,1.3);a.bezierCurveTo(495,1.3,494,1.2,496.1,5.4);a.bezierCurveTo(497,7.2,497.2,10.2,497,25.5);a.lineTo(496.8,43.5);a.lineTo(494.9,45.4);a.lineTo(493,47.3);a.lineTo(474.8,47.7);a.bezierCurveTo(450.1,48.3,
434.5,48.3,431.9,47.7);a.closePath();a.fillStyle="rgba(200,200,200,1)";a.fill();a.beginPath();a.moveTo(1.3,511.9);a.lineTo(1.3,514.3);a.lineTo(3.7,514.3);a.bezierCurveTo(7.2,514.4,9.5,515.5,10.6,517.6);a.bezierCurveTo(11.7,519.8,12.1,522.7,12,526.3);a.lineTo(12,591);a.lineTo(22.8,591);a.lineTo(22.8,553.2);a.lineTo(49.9,553.2);a.lineTo(49.9,548.5);a.lineTo(22.8,548.5);a.lineTo(22.8,516.7);a.lineTo(41.9,516.7);a.bezierCurveTo(46.7,516.7,50.4,517.8,52.9,520);a.bezierCurveTo(55.5,522.2,56.8,525.7,56.8,
530.5);a.lineTo(59.2,530.5);a.lineTo(59.2,521.5);a.bezierCurveTo(59.3,519,58.7,516.8,57.3,514.9);a.bezierCurveTo(55.9,513,53.1,512,49,511.9);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(107.6,562.8);a.bezierCurveTo(107.6,569.9,106.2,575.7,103.5,580.3);a.bezierCurveTo(100.8,584.8,97.2,587.2,92.7,587.4);a.bezierCurveTo(88.1,587.2,84.5,584.8,81.8,580.3);a.bezierCurveTo(79.1,575.7,77.8,569.9,77.7,562.8);a.bezierCurveTo(77.8,555.8,79.1,550,81.8,545.4);a.bezierCurveTo(84.5,
540.8,88.1,538.4,92.7,538.3);a.bezierCurveTo(97.2,538.4,100.8,540.8,103.5,545.4);a.bezierCurveTo(106.2,550,107.6,555.8,107.6,562.8);a.moveTo(66.3,562.8);a.bezierCurveTo(66.4,571.1,68.7,578,73.2,583.5);a.bezierCurveTo(77.8,589.1,84.2,591.9,92.7,592.1);a.bezierCurveTo(101.1,591.9,107.6,589.1,112.1,583.5);a.bezierCurveTo(116.7,578,118.9,571.1,119,562.8);a.bezierCurveTo(118.9,554.5,116.7,547.6,112.1,542.1);a.bezierCurveTo(107.6,536.6,101.1,533.7,92.7,533.5);a.bezierCurveTo(84.2,533.7,77.8,536.6,73.2,
542.1);a.bezierCurveTo(68.7,547.6,66.4,554.5,66.3,562.8);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(161.5,579.6);a.bezierCurveTo(160.3,581.4,158.9,583.1,157.2,584.5);a.bezierCurveTo(155.4,585.9,153.1,586.7,150.1,586.8);a.bezierCurveTo(147,586.8,144.4,585.9,142.2,584);a.bezierCurveTo(140,582.1,138.9,579.3,138.8,575.4);a.bezierCurveTo(138.8,571.7,140.5,568.9,143.8,566.7);a.bezierCurveTo(147.2,564.6,151.9,563.5,157.9,563.4);a.lineTo(161.5,563.4);a.moveTo(172.3,591);
a.lineTo(172.3,558.6);a.bezierCurveTo(172.1,548.2,169.9,541.3,165.8,538);a.bezierCurveTo(161.7,534.7,156.9,533.2,151.3,533.5);a.bezierCurveTo(147.6,533.5,144.1,533.8,140.8,534.5);a.bezierCurveTo(137.4,535.1,135,536.2,133.4,537.7);a.bezierCurveTo(131.9,539.2,131.1,540.8,130.7,542.6);a.bezierCurveTo(130.4,544.4,130.3,546.4,130.4,548.5);a.lineTo(135.8,548.5);a.bezierCurveTo(136.7,544.6,138.3,542,140.5,540.5);a.bezierCurveTo(142.8,538.9,145.6,538.2,148.9,538.3);a.bezierCurveTo(152.6,538.1,155.6,539.4,
157.9,542.2);a.bezierCurveTo(160.2,545,161.4,550.5,161.5,558.6);a.lineTo(157.9,558.6);a.bezierCurveTo(149.6,558.5,142.5,559.7,136.6,562.1);a.bezierCurveTo(130.7,564.5,127.6,568.9,127.4,575.4);a.bezierCurveTo(127.7,581.8,129.8,586.3,133.6,588.7);a.bezierCurveTo(137.4,591.1,141.1,592.3,144.7,592.1);a.bezierCurveTo(149.2,592.1,152.8,591.3,155.6,590);a.bezierCurveTo(158.3,588.6,160.3,587.1,161.5,585.6);a.lineTo(162.1,585.6);a.lineTo(166.3,591);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();
a.moveTo(200.3,539.5);a.bezierCurveTo(199.8,538,198.7,536.8,197,536);a.bezierCurveTo(195.4,535.1,193.1,534.7,190.2,534.7);a.lineTo(179.4,534.7);a.lineTo(179.4,537.1);a.lineTo(181.8,537.1);a.bezierCurveTo(185.3,537.1,187.6,538.2,188.7,540.4);a.bezierCurveTo(189.8,542.5,190.3,545.4,190.2,549.1);a.lineTo(190.2,591);a.lineTo(200.9,591);a.lineTo(200.9,545.2);a.bezierCurveTo(202.4,543.5,204.2,542,206.2,540.8);a.bezierCurveTo(208.3,539.6,210.5,538.9,212.9,538.9);a.bezierCurveTo(215.9,538.8,218.3,540,219.9,
542.5);a.bezierCurveTo(221.6,544.9,222.4,549.1,222.5,555);a.lineTo(222.5,591);a.lineTo(233.2,591);a.lineTo(233.2,555);a.bezierCurveTo(233.3,553.8,233.2,552.3,233.2,550.6);a.bezierCurveTo(233.1,549,232.9,547.6,232.6,546.7);a.bezierCurveTo(233.9,544.8,235.7,543,238,541.4);a.bezierCurveTo(240.4,539.8,242.7,539,245.2,538.9);a.bezierCurveTo(248.2,538.8,250.6,540,252.3,542.5);a.bezierCurveTo(253.9,544.9,254.8,549.1,254.8,555);a.lineTo(254.8,591);a.lineTo(265.6,591);a.lineTo(265.6,555);a.bezierCurveTo(265.4,
546.5,263.8,540.8,260.6,537.8);a.bezierCurveTo(257.4,534.7,253.4,533.3,248.8,533.5);a.bezierCurveTo(245.4,533.5,242.2,534.2,238.9,535.7);a.bezierCurveTo(235.7,537.1,233,539.2,230.9,541.9);a.bezierCurveTo(229.3,538.6,227.3,536.4,224.8,535.2);a.bezierCurveTo(222.3,534,219.5,533.4,216.5,533.5);a.bezierCurveTo(212.9,533.6,209.8,534.2,207.1,535.4);a.bezierCurveTo(204.5,536.5,202.4,537.9,200.9,539.5);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(284,511.9);a.bezierCurveTo(279.9,
512,277.2,513,275.8,514.9);a.bezierCurveTo(274.4,516.8,273.7,519,273.8,521.5);a.lineTo(273.8,530.5);a.lineTo(276.2,530.5);a.bezierCurveTo(276.3,525.7,277.6,522.2,280.1,520);a.bezierCurveTo(282.7,517.8,286.4,516.7,291.2,516.7);a.lineTo(302,516.7);a.lineTo(302,590.9);a.lineTo(312.7,590.9);a.lineTo(312.7,516.7);a.lineTo(339.7,516.7);a.lineTo(339.7,511.9);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(349.4,590.9);a.lineTo(360.2,590.9);a.lineTo(360.2,546.7);a.bezierCurveTo(361.4,
544.8,363,543.4,364.9,542.3);a.bezierCurveTo(366.9,541.2,369.1,540.7,371.5,540.7);a.bezierCurveTo(373.7,540.7,375.5,541,377.2,541.6);a.bezierCurveTo(378.9,542.2,380.2,543.1,381.1,544.3);a.lineTo(385.9,540.7);a.bezierCurveTo(385.3,539.5,384.7,538.4,384,537.5);a.bezierCurveTo(383.4,536.6,382.6,535.9,381.7,535.3);a.bezierCurveTo(380.8,534.7,379.7,534.2,378.3,533.9);a.bezierCurveTo(377,533.6,375.8,533.5,374.5,533.5);a.bezierCurveTo(370.9,533.6,367.9,534.3,365.5,535.7);a.bezierCurveTo(363.2,537,361.4,
538.5,360.2,540.1);a.lineTo(359.6,540.1);a.bezierCurveTo(359,538.3,357.9,536.9,356.3,536);a.bezierCurveTo(354.6,535.1,352.4,534.7,349.4,534.7);a.lineTo(339.8,534.7);a.lineTo(339.8,537.1);a.lineTo(341,537.1);a.bezierCurveTo(344.5,537.1,346.8,538.2,347.9,540.4);a.bezierCurveTo(349,542.5,349.5,545.4,349.4,549.1);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(440.3,559.8);a.bezierCurveTo(440.3,551.4,438.3,544.9,434.4,540.4);a.bezierCurveTo(430.4,535.8,424.4,533.5,416.3,533.5);
a.bezierCurveTo(408.8,533.7,403,536.6,399,542.1);a.bezierCurveTo(395,547.6,393,554.5,393,562.8);a.bezierCurveTo(393,571.1,395.1,578,399.3,583.5);a.bezierCurveTo(403.5,589.1,409.7,591.9,418.1,592.1);a.bezierCurveTo(422.6,592.2,426.7,591.2,430.2,589.2);a.bezierCurveTo(433.8,587.2,437,584,439.7,579.6);a.lineTo(437.3,577.8);a.bezierCurveTo(435.2,580.8,432.9,583.1,430.2,584.8);a.bezierCurveTo(427.6,586.5,424.4,587.3,420.5,587.4);a.bezierCurveTo(415.4,587.2,411.4,585.1,408.6,580.9);a.bezierCurveTo(405.8,
576.8,404.4,571.3,404.4,564.6);a.lineTo(440,564.6);a.moveTo(404.4,559.8);a.bezierCurveTo(404.4,553.7,405.6,548.7,407.9,544.9);a.bezierCurveTo(410.3,541,413.3,539,416.9,538.9);a.bezierCurveTo(421.1,538.9,424.3,540.8,426.4,544.4);a.bezierCurveTo(428.4,548.1,429.5,553.2,429.5,559.8);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill();a.beginPath();a.moveTo(497.1,559.8);a.bezierCurveTo(497.1,551.4,495.1,544.9,491.2,540.4);a.bezierCurveTo(487.2,535.8,481.2,533.5,473.1,533.5);a.bezierCurveTo(465.6,533.7,
459.9,536.6,455.9,542.1);a.bezierCurveTo(451.9,547.6,449.8,554.5,449.8,562.8);a.bezierCurveTo(449.8,571.1,451.9,578,456.1,583.5);a.bezierCurveTo(460.3,589.1,466.6,591.9,474.9,592.1);a.bezierCurveTo(479.4,592.2,483.5,591.2,487.1,589.2);a.bezierCurveTo(490.6,587.2,493.8,584,496.5,579.6);a.lineTo(494.1,577.8);a.bezierCurveTo(492,580.8,489.7,583.1,487.1,584.8);a.bezierCurveTo(484.4,586.5,481.2,587.3,477.3,587.4);a.bezierCurveTo(472.2,587.2,468.2,585.1,465.4,580.9);a.bezierCurveTo(462.6,576.8,461.2,571.3,
461.2,564.6);a.lineTo(496.8,564.6);a.moveTo(461.2,559.8);a.bezierCurveTo(461.2,553.7,462.4,548.7,464.8,544.9);a.bezierCurveTo(467.1,541,470.1,539,473.7,538.9);a.bezierCurveTo(477.9,538.9,481.1,540.8,483.2,544.4);a.bezierCurveTo(485.3,548.1,486.3,553.2,486.3,559.8);a.closePath();a.fillStyle="rgba(220,20,3,1)";a.fill()}};Sa.yc={width:498,height:592};function Ta(a,m){function k(a,b){var c=a.L.Ob,d=c/15,e=0.5*c/15,c=c/5,f=a.L.x,g=a.L.y;b.fillRect(f-e,g-e,d,d);b.fillRect(f-e-c,g-e,d,d);b.fillRect(f-e+c,g-e,d,d)}function f(a,b,c,d){null===a&&c.clearRect(0,0,H,Q);var e,f=Array(W.length);for(e=W.length-1;0<=e;e--)f[e]=W[e].qa(c,d);for(e=W.length-1;0<=e;e--)f[e]&&W[e].X(c,d);T.Jc([c,B],function(d){var e;if(null!==a){c.save();c.globalCompositeOperation="destination-out";c.fillStyle=c.strokeStyle="rgba(255, 255, 255, 1)";for(e=a.length-1;0<=e;e--){var g=
a[e],h=g.o;h&&(c.save(),c.beginPath(),g.Tb(c),ia.le(c,h),c.fill(),g=t.lb*Math.pow(t.Ra,g.K-1),0<g&&(c.lineWidth=g/2,c.stroke()),c.restore())}c.restore()}d=d.scale;if(0!==b.length){e={};for(h=W.length-1;0<=h;h--)W[h].Ng(e);for(g=Z.length-1;0<=g;g--)if(h=Z[g],e[h.id])for(var k=h.be,h=0;h<b.length;h++){var l=b[h];!l.parent||l.parent.Ca&&l.parent.O?k(l,d):l.ca.clear()}}for(e=W.length-1;0<=e;e--)g=W[e],f[e]&&g.ee(b,c,d)});for(e=W.length-1;0<=e;e--)f[e]&&W[e].Da(c);t.rd&&(c.canvas.style.opacity=0.99,setTimeout(function(){c.canvas.style.opacity=
1},1))}function c(a){s===u?a<0.9*q&&(s=w,x=A,e()):a>=q&&(s=u,x=C,e())}function d(){function a(b,c,d){b.Cb=Math.floor(1E3*b.scale)-d*c;0<b.opacity&&!b.open&&c++;var e=b.e;if(e)for(var f=e.length-1;0<=f;f--)b.W&&a(e[f],c,d)}var b=null,d=null,e=null;T.Jc([],function(f){c(f.scale);var h=!1;$.F(z,function(a){a.W&&(h=a.Nd()||h,a.nc(),a.Sa=P.d(a)||a.Sa)});h&&(z.I=!0);var k="onSurfaceDirty"===t.oh;$.xd(z,function(a){a.parent&&a.parent.aa&&(a.ca.clear(),a.Sa=!0,k||(a.Fc=!0,a.ac.clear()));k&&(a.Fc=!0,a.ac.clear())});
var l=f.scale*f.scale;$.xd(z,function(a){if(a.O){for(var b=a.e,c=0;c<b.length;c++)if(5<b[c].L.ja*l){a.Y=!0;return}a.Y=!1}});n(f);e=[];$.Lc(z,function(a){if(a.parent.Y&&a.$&&a.W){e.push(a);for(var b=a.parent;b!==z&&(b.open||0===b.opacity);)b=b.parent;b!==z&&0.02>Math.abs(b.scale-a.scale)&&(a.scale=Math.min(a.scale,b.scale))}});a(z,0,"flattened"===t.xb?-1:1);e.sort(function(a,b){return a.Cb-b.Cb});if(g())b=e,d=null;else{var m={},p={},q="none"!=t.Cd&&t.lb<t.mb/2,r=t.lb<t.Rc/2+t.Bd*t.Xe.a;$.F(z,function(a){if(a.W&&
!a.description&&(a.aa||a.I||a.Yc&&a.parent.Y&&a.Sa)){var b,c,d=[a],e=a.C||a.parent.e;if(q)for(b=0;b<e.length;b++)(c=e[b])&&d.push(c);else if(r)if(!a.selected&&a.$a){c=!0;for(b=0;b<e.length;b++)e[b]?d.push(e[b]):c=!1;!c&&1<a.K&&d.push(a.parent)}else for(b=0;b<e.length;b++)(c=e[b])&&c.selected&&d.push(c);var f;for(b=a.parent;b!=z;)b.selected&&(f=b),b=b.parent;f&&d.push(f);for(b=0;b<d.length;b++){f=d[b];for(a=f.parent;a&&a!==z;)0<a.opacity&&(f=a),a=a.parent;p[f.id]=!0;$.Fa(f,function(a){m[a.id]=!0})}}});
b=e.filter(function(a){return m[a.id]});d=b.filter(function(a){return p[a.id]})}});(function(){var a=!1;t.$f&&$.F(z,function(b){if(b.W&&0!==b.sa.a&&1!==b.sa.a)return a=!0,!1});a?($.Kc(z,function(a){if(a.W&&(a.opacity!==a.bd||a.Ma)){var b=a.e;if(b){for(var c=0,d=b.length-1;0<=d;d--)c=Math.max(c,b[d].Xc);a.Xc=c+a.opacity*a.sa.a}else a.Xc=a.opacity*a.sa.a}}),$.F(z,function(a){if(a.W&&(a.opacity!==a.bd||a.Ma)){for(var b=a.Xc,c=a;(c=c.parent)&&c!==z;)b+=c.opacity*c.sa.a*t.Yf;a.sd=0<b?1-Math.pow(1-a.sa.a,
1/b):0;a.bd=a.opacity}})):$.F(z,function(a){a.W&&(a.sd=1,a.bd=-1)})})();return{vg:b,ug:d,$:e}}function g(){var a=z.aa||z.I||"none"==t.ef;if(!a&&!z.empty()){var b=z.e[0].scale;$.F(z,function(c){if(c.W&&c.$&&c.scale!==b)return a=!0,!1})}!a&&0<t.Re&&1!=t.Wa&&$.F(z,function(b){if(b.W&&0<b.ka)return a=!0,!1});"accurate"==t.ef&&(a=(a=a||0===t.lb)||"none"!=t.Cd&&t.lb<t.mb/2,!a&&t.lb<t.Rc/2+t.Bd*t.Xe.a&&$.F(z,function(b){if(b.W&&(b.selected&&!b.$a||!b.selected&&b.$a))return a=!0,!1}));return a}function l(){if(t.n!==
t.yb)return!0;var a="polygonPlainFill polygonPlainStroke polygonGradientFill polygonGradientStroke labelPlainFill contentDecoration".split(" ");$.F(z,function(b){if(b.W&&b.U)return a.push("polygonExposureShadow"),!1});for(var b=a.length-1;0<=b;b--){var c=a[b];if(!!E[c]!==!!J[c])return!0}return!1}function e(){function a(c,d,e,f,g){function h(a,b,c,d,e){a[d]&&(b-=c*p[d],a[d]=!1,e&&(b+=c*p[e],a[e]=!0));return b}c=D.extend({},c);switch(e){case "never":c.labelPlainFill=!1;break;case "always":case "auto":c.labelPlainFill=
!0}if(t.Pc)switch(f){case "never":c.contentDecoration=!1;break;case "always":case "auto":c.contentDecoration=!0}else c.contentDecoration=!1;var k=0;D.Ga(c,function(a,b){a&&(k+=d*p["contentDecoration"===b?"labelPlainFill":b])});c.polygonExposureShadow=b;k+=2*p.polygonExposureShadow;if(k<=g||(k=h(c,k,2,"polygonExposureShadow"))<=g||(k=h(c,k,d,"polygonGradientFill","polygonPlainFill"))<=g||(k=h(c,k,d,"polygonGradientStroke"))<=g||(k=h(c,k,d,"polygonPlainStroke"))<=g||"auto"===f&&(k=h(c,k,d,"contentDecoration"))<=
g)return c;"auto"===e&&(k=h(c,k,d,"labelPlainFill"));return c}var b=s===w,c=0,d=0;$.He(z,function(a){var b=1;$.F(a,function(){b++});c+=b;d=Math.max(d,b)});var e={};switch(t.xh){case "plain":e.polygonPlainFill=!0;break;case "gradient":e.polygonPlainFill=!b,e.polygonGradientFill=b}switch(t.Cd){case "plain":e.polygonPlainStroke=!0;break;case "gradient":e.polygonPlainStroke=!b,e.polygonGradientStroke=b}E=a(e,c,t.Hj,t.Fj,t.Gj);J=a(e,2*d,"always","always",t.hh);I=a(e,c,"always","always",t.gh)}function b(a){return function(b,
c){return b===s?!0===E[a]:!0===(c?J:I)[a]}}function h(a,b){return function(c,d){return a(c,d)&&b(c,d)}}function n(a){z.$=!0;$.xd(z,function(b){if(b.W&&b.Y&&b.Ca&&b.O&&(z.I||b.aa||b.me)){b.me=!1;var c=b.e,d={x:0,y:0,f:0,i:0},e=!!b.u;if(1<H/a.f){var f;for(f=c.length-1;0<=f;f--)c[f].$=!1;if(b.$&&e)for(f=c.length-1;0<=f;f--)if(b=c[f],1!==b.scale&&(b.Vb(a,d),d.f=a.f/b.scale,d.i=a.i/b.scale),!1===b.$&&b.o){var e=b.o,g=e.length;if(M.Ua(b.o,1===b.scale?a:d))b.$=!0;else for(var h=0;h<g;h++)if(M.Lg(e[h],e[(h+
1)%g],1===b.scale?a:d)){b.$=!0;b.C&&(b=b.C[h])&&(c[b.index].$=!0);break}}}else for(f=0;f<c.length;f++)c[f].$=e}})}var q=v.nf()?50:1E4,p,r,s,w,u,y,x,A,C,K,B,H,Q,O,P=new Va(a),F=new Wa(a),T,N,U,t=a.options,z,L,E,I,J;a.c.j("stage:initialized",function(a,b,c,d){O=a;H=c;Q=d;r=O.oc("wireframe",t.yb,!1);w=r.getContext("2d");u=new ha(w);y=O.oc("hifi",t.n,!1);A=y.getContext("2d");C=new ha(A);s=w;x=A;w.n=t.yb;u.n=t.yb;A.n=t.n;C.n=t.n;K=O.oc("tmp",Math.max(t.n,t.yb),!0);B=K.getContext("2d");B.n=1;[w,A,B].forEach(function(a){a.scale(a.n,
a.n)})});a.c.j("stage:resized",function(a,b,c,d){H=c;Q=d;[w,A,B].forEach(function(a){a.scale(a.n,a.n)})});a.c.j("model:loaded",function(b){function c(a){var b=0;if(!a.empty()){for(var d=a.e,e=d.length-1;0<=e;e--)b=Math.max(b,c(d[e]));b+=1}return a.mg=b}z=b;L=!0;c(z);e();a.c.p("render:renderers:resolved",E,J,I)});var R="groupFillType groupStrokeType wireframeDrawMaxDuration wireframeLabelDrawing wireframeContentDecorationDrawing finalCompleteDrawMaxDuration finalIncrementalDrawMaxDuration groupContentDecorator".split(" "),
Y=["groupLabelLightColor","groupLabelDarkColor","groupLabelColorThreshold","groupUnexposureLabelColorThreshold"];a.c.j("options:changed",function(a){function b(a,c,d,e){O.hj(a,d);c.n=d;e&&c.scale(d,d)}a.dataObject||(D.nb(a,R)&&e(),D.nb(a,Y)&&$.F(z,function(a){a.zd=-1}));var c=D.N(a,"pixelRatio");a=D.N(a,"wireframePixelRatio");if(c||a)c&&b(y,x,t.n,!0),a&&b(r,s,t.yb,!0),b(K,B,Math.max(t.n,t.yb),!1)});a.c.j("zoom:initialized",function(a){T=a});a.c.j("timeline:initialized",function(a){N=a});a.c.j("api:initialized",
function(a){U=a});var Z=[{id:"offsetPolygon",be:function(a){if((a.selected||0<a.opacity&&!1===a.open||!a.Y)&&a.ca.Na()){var b=a.ca;b.clear();if(a.ba){var c=a.ba,d=t.jh;0<d?(d=Math.min(1,d*Math.pow(1-t.kh*d,a.mg)),ia.tj(b,c,a.parent.L.Ob/32,d)):ia.le(b,c)}a.Vd=!0}}},{id:"label",be:function(a){a.Sa&&a.Yc&&P.k(a)}},{id:"custom",be:function(b,c){if(b.ba&&(0<b.opacity&&(!1===b.open||!0===b.selected)||!b.Y)&&b.Fc&&a.options.Pc&&!b.na){var d={};U.pd(d,b);U.qd(d,b);U.od(d,b,!0);d.context=b.ac;d.polygonContext=
b.ca;d.labelContext=b.Uc;d.shapeDirty=b.Vd;d.viewportScale=c;var e={groupLabelDrawn:!0,groupPolygonDrawn:!0};a.options.nh(a.Ud,d,e);b.of=e.groupLabelDrawn;b.Wd=e.groupPolygonDrawn;b.Vd=!1;b.Fc=!1}}}].reverse(),W=[new function(a){var b=Array(a.length);this.ee=function(c,d,e){if(0!==c.length){var f,g,h=[],k=c[0].Cb;for(f=0;f<c.length;f++)g=c[f],g.Cb!==k&&(h.push(f),k=g.Cb);h.push(f);for(var l=k=0;l<h.length;l++){for(var m=h[l],p=a.length-1;0<=p;p--)if(b[p]){var n=a[p];d.save();for(f=k;f<m;f++)g=c[f],
d.save(),g.Tb(d),n.vb.call(n,g,d,e),d.restore();n.hb.call(n,d,e);d.restore()}k=m}}};this.qa=function(c,d){for(var e=!1,f=a.length-1;0<=f;f--)b[f]=a[f].qa(c,d),e|=b[f];return e};this.X=function(c,d){for(var e=a.length-1;0<=e;e--)if(b[e]){var f=a[e];f.X.call(f,c,d)}};this.Da=function(c){for(var d=a.length-1;0<=d;d--)if(b[d]){var e=a[d];e.Da.call(e,c)}};this.Ng=function(c){for(var d=a.length-1;0<=d;d--){var e=a[d];if(b[d])for(var f=e.Za.length-1;0<=f;f--)c[e.Za[f]]=!0}}}([{Za:["offsetPolygon"],qa:b("polygonExposureShadow"),
X:function(a){B.save();B.scale(a.n,a.n)},Da:function(){B.restore()},d:function(){},hb:function(a){this.lg&&(this.lg=!1,a.save(),a.setTransform(1,0,0,1,0,0),a.drawImage(K,0,0,a.canvas.width,a.canvas.height,0,0,a.canvas.width,a.canvas.height),a.restore(),B.save(),B.setTransform(1,0,0,1,0,0),B.clearRect(0,0,K.width,K.height),B.restore())},vb:function(a,b,c){if(!(a.open&&a.Y||a.ca.Na())){var d=t.Re*a.opacity*a.ka*("flattened"===t.xb?1-a.parent.ka:(1-a.Lb)*a.parent.Lb)*(1.1<=t.Wa?1:(t.Wa-1)/0.1);0<d&&
(B.save(),B.beginPath(),a.Tb(B),a.ca.Ta(B),B.shadowBlur=c*b.n*d,B.shadowColor=t.ph,B.fillStyle="rgba(0, 0, 0, 1)",B.globalCompositeOperation="source-over",B.globalAlpha=a.opacity,B.fill(),B.shadowBlur=0,B.shadowColor="transparent",B.globalCompositeOperation="destination-out",B.fill(),B.restore(),this.lg=!0)}}},{Za:["offsetPolygon"],qa:function(){return!0},X:function(){function a(b){var d=b.sa,e=b.Eb,f=b.selected,g=c(d.l*b.va+(e?t.Ch:0)+(f?t.Th:0)),h=c(d.s*b.wa+(e?t.Dh:0)+(f?t.Uh:0));b=b.Qe;b.h=(d.h+
(e?t.Bh:0)+(f?t.Sh:0))%360;b.s=h;b.l=g;return b}function c(a){return 100<a?100:0>a?0:a}var d=[{type:"fill",qa:b("polygonPlainFill"),hd:function(b,c){c.fillStyle=S.Ac(a(b))}},{type:"fill",qa:b("polygonGradientFill"),hd:function(b,d){var e=b.L.Ob,f=a(b),e=d.createRadialGradient(b.x,b.y,0,b.x,b.y,e*t.th);e.addColorStop(0,S.Z((f.h+t.qh)%360,c(f.s+t.sh),c(f.l+t.rh)));e.addColorStop(1,S.Z((f.h+t.uh)%360,c(f.s+t.wh),c(f.l+t.vh)));b.ca.Ta(d);d.fillStyle=e}},{type:"stroke",qa:h(b("polygonPlainStroke"),function(){return 0<
t.mb}),hd:function(a,b){var d=a.sa,e=a.Eb,f=a.selected;b.strokeStyle=S.Z((d.h+t.af+(e?t.Se:0)+(f?t.Ye:0))%360,c(d.s*a.wa+t.cf+(e?t.Ue:0)+(f?t.$e:0)),c(d.l*a.va+t.bf+(e?t.Te:0)+(f?t.Ze:0)));b.lineWidth=t.mb*Math.pow(t.Ra,a.K-1)}},{type:"stroke",qa:h(b("polygonGradientStroke"),function(){return 0<t.mb}),hd:function(a,b){var d=a.L.Ob*t.$h,e=a.sa,f=Math.PI*t.Wh/180,d=b.createLinearGradient(a.x+d*Math.cos(f),a.y+d*Math.sin(f),a.x+d*Math.cos(f+Math.PI),a.y+d*Math.sin(f+Math.PI)),g=a.Eb,h=a.selected,f=(e.h+
t.af+(g?t.Se:0)+(h?t.Ye:0))%360,k=c(e.s*a.wa+t.cf+(g?t.Ue:0)+(h?t.$e:0)),e=c(e.l*a.va+t.bf+(g?t.Te:0)+(h?t.Ze:0));d.addColorStop(0,S.Z((f+t.Xh)%360,c(k+t.Zh),c(e+t.Yh)));d.addColorStop(1,S.Z((f+t.ai)%360,c(k+t.ci),c(e+t.bi)));b.strokeStyle=d;b.lineWidth=t.mb*Math.pow(t.Ra,a.K-1)}}],e=Array(d.length);return function(a,b){for(var c=d.length-1;0<=c;c--)e[c]=d[c].qa(a,b);this.xj=d;this.Wg=e}}(),Da:function(){},d:function(){},hb:function(){},vb:function(a,b){if(a.Wd&&!((0===a.opacity||a.open)&&a.Y||a.ca.Na()||
!t.De&&a.description)){var c=this.xj,d=this.Wg;b.beginPath();a.ca.Ta(b);for(var e=!1,f=!1,g=c.length-1;0<=g;g--){var h=c[g];if(d[g])switch(h.hd(a,b),h.type){case "fill":e=!0;break;case "stroke":f=!0}}c=(a.Y?a.opacity:1)*a.sa.a;d=!a.empty();g=t.$f?a.sd:1;e&&(e=d&&a.Y&&a.O&&a.e[0].W?1-a.e.reduce(function(a,b){return a+b.ua*b.Zd},0)/a.e.length*(1-t.Yf):1,b.globalAlpha=c*e*g,Xa(b));f&&(b.globalAlpha=c*(d?t.yi:1)*g,b.closePath(),Ya(b),b.stroke())}}},{Za:["offsetPolygon"],qa:function(){return 0<t.Rc},X:function(){},
Da:function(){},d:function(){},hb:function(){},vb:function(a,b,c){if(a.Wd&&a.selected&&!a.ca.Na()){b.globalAlpha=a.Ka;b.beginPath();var d=Math.pow(t.Ra,a.K-1);b.lineWidth=t.Rc*d;b.strokeStyle=t.Vh;var e=t.Bd;0<e&&(b.shadowBlur=e*d*c*b.n,b.shadowColor=t.We);a.ca.Ta(b);b.closePath();b.stroke()}}},{Za:[],qa:function(){return!0},X:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(a,b){function c(d){var e=Sa.yc.width,f=Sa.yc.height,g=M.se(a.ba,a.L,e/f),g=Math.min(Math.min(0.9*g,0.5*
a.q.i)/f,0.5*a.q.f/e);b.save();b.translate(a.x,a.y);b.globalAlpha=a.opacity*a.fa;b.scale(g,g);b.translate(-e/2,-f/2);d(b);b.restore()}a.na&&!a.ca.Na()&&c(function(a){Sa.Fg(a)})}},{Za:[],qa:function(a,b){return function(c,d){return a(c,d)||b(c,d)}}(b("labelPlainFill"),h(b("contentDecoration"),function(){return t.Pc})),X:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(a,b,c){(0<a.opacity&&0<a.fa&&!a.open||!a.Y)&&!a.ca.Na()&&(a.Vc=a.ra&&a.ra.la&&t.n*a.ra.fontSize*a.scale*c>=t.Ph,
"auto"===a.Hd?!t.De&&a.description?a.qb=a.parent.qb:(b=a.Qe,c=b.h+(b.s<<9)+(b.l<<16),a.zd!==c&&(a.qb=S.Bg(b)>(0>a.ka?t.di:t.Eh)?t.Fh:t.Oh,a.zd=c)):a.qb=a.Hd)}},{Za:["custom"],qa:h(b("contentDecoration"),function(){return t.Pc}),X:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(a,b){!(0<a.opacity&&0<a.fa&&!a.open||!a.Y)||a.ac.Na()||a.ca.Na()||(a.Vc||void 0===a.ra?(b.globalAlpha=a.fa*(a.Y?a.opacity:1)*(a.empty()?1:t.Zf),b.fillStyle=a.qb,b.strokeStyle=a.qb,a.ac.Ta(b)):k(a,b))}},
{Za:["label"],qa:b("labelPlainFill"),X:function(){},Da:function(){},d:function(){},hb:function(){},vb:function(a,b,c){a.of&&a.Yc&&(0<a.opacity&&0<a.fa&&!a.open||!a.Y)&&!a.ca.Na()&&a.ra&&(b.fillStyle=a.qb,b.globalAlpha=a.fa*(a.Y?a.opacity:1)*(a.empty()?1:t.Zf),a.Vc?Za(a,b,c):k(a,b))}}].reverse())];this.H=function(){p=ua.ji(function(){return ja.eh()},"CarrotSearchFoamTree",12096E5)($a());F.H()};this.clear=function(){s.clearRect(0,0,H,Q);x.clearRect(0,0,H,Q)};var ea=!1,aa=void 0;this.k=function(a){ea?
aa=a:a()};this.ee=function(){function a(){window.clearTimeout(b);ea=!0;b=setTimeout(function(){ea=!1;if(l()){var a=!g();f(null,c.$,x,a);D.defer(function(){ca.uj();aa&&(aa(),aa=void 0)})}else aa&&(aa(),aa=void 0)},Math.max(t.Ij,3*m.qg.Kd,3*m.qg.Jd))}var b,c;return function(b){ab(F);c=d();var e=null!==c.ug,g=0<O.kc("hifi"),h=g&&(e||!b);b=e||L||!b;L=!1;g&&!h&&ca.vj();f(c.ug,c.vg,h?x:s,b);$.Fa(z,function(a){a.aa=!1;a.I=!1;a.$a=!1});h||a();t.Uf(e)}}();this.d=function(a){a=a||{};ab(F);z.I=!0;var b=d(),
c=t.n;try{var e=D.B(a.pixelRatio,t.n);t.n=e;var g=O.oc("export",e,!0),h=g.getContext("2d");s===u&&(h=new ha(h));h.scale(e,e);var k=D.N(a,"backgroundColor");k&&(h.save(),h.fillStyle=a.backgroundColor,h.fillRect(0,0,H,Q),h.restore());f(k?[]:null,b.vg,h,!0)}finally{t.n=c}return g.toDataURL(D.B(a.format,"image/png"),D.B(a.quality,0.8))};var ca=function(){function a(b,d,e,f){function g(a,b,c,d){return N.D.m({opacity:O.kc(a)}).ia({duration:c,G:{opacity:{end:b,R:d}},da:function(){O.kc(a,this.opacity)}}).xa()}
var h=D.Fd(O.kc(b),1),k=D.Fd(O.kc(e),0);if(!h||!k){for(var l=c.length-1;0<=l;l--)c[l].stop();c=[];h||c.push(g(b,1,d,X.Rb));k||c.push(g(e,0,f,X.ng));return N.D.m({}).Ya(c).start()}}var b,c=[];return{vj:function(){t.rd?1!==r.style.opacity&&(r.style.visibility="visible",y.style.visibility="hidden",r.style.opacity=1,y.style.opacity=0):b&&b.Gb()||(b=a("wireframe",t.Me,"hifi",t.Me))},uj:function(){t.rd?(y.style.visibility="visible",r.style.visibility="hidden",r.style.opacity=0,y.style.opacity=1):a("hifi",
t.xg,"wireframe",t.xg)}}}();ab=function(a){a.apply()};Xa=function(a){a.fill()};Ya=function(a){a.stroke()};return this}var Xa,Ya,ab;function Va(a){function m(a){return f.Nh?(e.fontFamily=c.fontFamily,e.fontStyle=c.fontStyle,e.fontVariant=c.fontVariant,e.fontWeight=c.fontWeight,e.lineHeight=c.lineHeight,e.horizontalPadding=c.ob,e.verticalPadding=c.cb,e.maxTotalTextHeight=c.sb,e.maxFontSize=c.rb,g.Dc(f.Mh,a,e),d.fontFamily=e.fontFamily,d.fontStyle=e.fontStyle,d.fontVariant=e.fontVariant,d.fontWeight=e.fontWeight,d.lineHeight=e.lineHeight,d.ob=e.horizontalPadding,d.cb=e.verticalPadding,d.sb=e.maxTotalTextHeight,d.rb=e.maxFontSize,
d):c}function k(a){"undefined"!==typeof a.groupLabelFontFamily&&(c.fontFamily=a.groupLabelFontFamily);"undefined"!==typeof a.groupLabelFontStyle&&(c.fontStyle=a.groupLabelFontStyle);"undefined"!==typeof a.groupLabelFontVariant&&(c.fontVariant=a.groupLabelFontVariant);"undefined"!==typeof a.groupLabelFontWeight&&(c.fontWeight=a.groupLabelFontWeight);"undefined"!==typeof a.groupLabelLineHeight&&(c.lineHeight=a.groupLabelLineHeight);"undefined"!==typeof a.groupLabelHorizontalPadding&&(c.ob=a.groupLabelHorizontalPadding);
"undefined"!==typeof a.groupLabelVerticalPadding&&(c.cb=a.groupLabelVerticalPadding);"undefined"!==typeof a.groupLabelMaxTotalHeight&&(c.sb=a.groupLabelMaxTotalHeight);"undefined"!==typeof a.groupLabelMaxFontSize&&(c.rb=a.groupLabelMaxFontSize)}var f=a.options,c={},d={},g,l={groupLabel:""},e={};a.c.j("api:initialized",function(a){g=a});a.c.j("options:changed",k);k(a.Ud);this.d=function(a){if(!a.ba)return!1;var c=a.group.label;f.Hh&&!a.na&&(l.labelText=c,g.Dc(f.Gh,a,l),c=l.labelText);a.pf=c;return a.Id!==
c};this.k=function(a){var c=a.pf;a.Id=c;a.Uc.clear();a.ra=void 0;!a.ba||D.jf(c)||"flattened"===f.xb&&!a.empty()&&a.O&&a.e[0].W||(a.ra=G.xe(m(a),a.Uc,c,a.ba,a.q,a.L,!1,!1,a.ni,a.L.ja,f.Qh,a.Sa));a.Sa=!1};Za=this.A=function(a,c){a.Uc.Ta(c)}}var Za;function Wa(a){function m(a,c){var d=a.e,e=d.length,f,g,k=l.L.Ob;for(f=0;f<e;f++)g=d[f],g.Db=(180*(Math.atan2(g.x-a.x,g.y-a.y)+c)/Math.PI+180)/360,g.Oc=Math.min(1,Math.sqrt(M.d(g,a))/k)}function k(a,c){var d=a.e,e=d.length;if(1===e||2===e&&d[0].description)d[0].Db=0.5;else{var f,g,k=0,l=Number.MAX_VALUE,m=Math.sin(c),y=Math.cos(c);for(f=0;f<e;f++){g=d[f];var x=g.x*m+g.y*y;k<x&&(k=x);l>x&&(l=x);g.Db=x;g.Oc=1}for(f=0;f<e;f++)g=d[f],g.Db=(g.Db-l)/(k-l)}}function f(a,c,d,e){c=c[e];return c+(d[e]-c)*a}
var c={radial:m,linear:k},d=a.options,g,l,e={groupColor:null,labelColor:null};a.c.j("model:loaded",function(a){l=a});a.c.j("api:initialized",function(a){g=a});this.H=function(){};this.apply=function(){function a(c){if(c.O&&c.Ca){var k=c.e,l,m;if(c.aa||c.Ma||w){0===c.K?n(c,d.Xi*Math.PI/180):q(c,d.aj*Math.PI/180);for(l=k.length-1;0<=l;l--){m=k[l];m.Ma=!0;var B=m.Db,H,Q,O,P,F=m.Pe;0===c.K?(H=f(B,p,r,"h"),Q=(y+(1-y)*m.Oc)*f(B,p,r,"s"),O=(1+(0>m.ka?u*(m.ka+1):u)*(1-m.Oc))*f(B,p,r,"l"),P=f(B,p,r,"a")):
(O=c.sa,H=O.h,Q=O.s,O=h(O.l,B,d.bj,d.cj),P=c.Pe.a);F.h=H;F.s=Q;F.l=O;F.a=P;H=m.sa;m.na?(H.h=0,H.s=0,H.l="light"==d.Sg?90:10,H.a=1):(H.h=F.h,H.s=F.s,H.l=F.l,H.a=F.a);w&&!m.na&&(e.groupColor=H,e.labelColor="auto",g.Dc(s,m,e,function(a){a.ratio=B}),m.sa=S.Ba(e.groupColor),m.sa.a=D.N(e.groupColor,"a")?e.groupColor.a:1,"auto"!==e.labelColor&&(m.Hd=S.Mg(e.labelColor)))}c.Ma=!1}for(l=k.length-1;0<=l;l--)a(k[l])}}function h(a,b,c,d){var e=0>a+c*d?0:100<a+c*d?100:a+c*d;return e+b*((0>a-c*(1-d)?0:100<a-c*(1-
d)?100:a-c*(1-d))-e)}var n=c[d.Wi]||m,q=k,p=d.fj,r=d.Zi,s=d.lh,w=d.mh,u=d.$i,y=d.dj;a(l)};return this};function Ga(){this.uc=this.pe=this.rc=this.pg=this.f=this.wg=this.T=this.y=this.x=this.id=0;this.o=this.parent=this.e=null;this.q={x:0,y:0,f:0,i:0};this.C=null;this.Id=this.pf=void 0;this.ld=!1;this.Oc=this.Db=0;this.Pe={h:0,s:0,l:0,a:0,model:"hsla"};this.sa={h:0,s:0,l:0,a:0,model:"hsla"};this.Qe={h:0,s:0,l:0,model:"hsl"};this.zd=-1;this.Hd="auto";this.qb="#000";this.mg=this.K=this.Ed=this.index=0;this.na=!1;this.ja=this.uf=0;this.$=!1;this.ba=null;this.L={x:0,y:0,ja:0,Ob:0};this.Xd=this.u=null;this.Yc=
this.W=this.$a=this.Fc=this.me=this.Vd=this.Sa=this.Ma=this.I=this.aa=this.La=this.Ca=this.O=this.Qa=!1;this.wa=this.va=this.Ka=this.fa=this.opacity=this.scale=1;this.ua=0;this.Zd=1;this.Lb=this.ka=this.Hb=0;this.description=this.selected=this.Eb=this.Td=this.open=this.U=!1;this.Cb=0;this.of=this.Wd=this.Y=!0;this.ra=void 0;this.Vc=!1;this.Uc=new ga;this.ca=new ga;this.ac=new ga;this.ni=G.Ai();this.Xc=0;this.sd=1;this.bd=-1;this.empty=function(){return!this.e||0===this.e.length};var a=[];this.Cc=
function(c){a.push(c)};this.fd=function(c){D.bg(a,c)};var m={scale:1};this.Nd=function(){var c=!1;this.scale=1;for(var d=0;d<a.length;d++)c=a[d].qf(this,m)||c,this.scale*=m.scale;return c};this.Tb=function(c){for(var d=0;d<a.length;d++)a[d].Tb(this,c)};this.Ub=function(c,d){d.x=c.x;d.y=c.y;for(var f=0;f<a.length;f++)a[f].Ub(this,d,d);return d};this.Vb=function(c,d){d.x=c.x;d.y=c.y;for(var f=0;f<a.length;f++)a[f].Vb(this,d,d);return d};var k=[];this.Ab=function(a){k.push(a)};this.ed=function(a){D.bg(k,
a)};var f={opacity:1,wa:1,va:1,fa:1,Ka:1};this.nc=function(){if(0!==k.length){this.Ka=this.fa=this.va=this.wa=this.opacity=1;for(var a=k.length-1;0<=a;a--)(0,k[a])(this,f),this.opacity*=f.opacity,this.va*=f.va,this.wa*=f.wa,this.fa*=f.fa,this.Ka*=f.Ka}}}function Oa(a,m){return m.T>a.T?1:m.T<a.T?-1:a.index-m.index};function bb(a){var m=this,k,f,c,d,g=a.options,l,e;a.c.j("stage:initialized",function(b,e,l,q){c=l;d=q;k=b.oc("titlebar",g.n,!1);f=k.getContext("2d");f.n=g.n;f.scale(f.n,f.n);a.c.p("titlebar:initialized",m)});a.c.j("stage:resized",function(a,e,g,k){c=g;d=k;f.scale(f.n,f.n)});a.c.j("zoom:initialized",function(a){e=a});a.c.j("api:initialized",function(a){l=a});a.c.j("model:loaded",function(){f.clearRect(0,0,c,d)});this.update=function(a){f.clearRect(0,0,c,d);if(a){!a.empty()&&a.e[0].description&&(a=
a.e[0]);var h=g.Cj,k=g.Bj,m=Math.min(d/2,g.ne+2*h),p=m-2*h,r=c-2*k;if(!(0>=p||0>=r)){var s=a.Vc?a.ra.fontSize*a.scale*e.scale():0,w,u={titleBarText:a.Id,titleBarTextColor:g.tg,titleBarBackgroundColor:g.sg,titleBarMaxFontSize:g.ne,titleBarShown:s<g.ti};a.na?w=xa.jg("B`ssnu!Rd`sbi!Gn`lUsdd!whrt`mh{`uhno/!Bmhbj!uid!mnfn!un!fn!un!iuuq;..b`ssnurd`sbi/bnl.gn`lusdd!gns!lnsd!edu`hmr/"):(l.Dc(g.yj,a,u,function(a){a.titleBarWidth=r;a.titleBarHeight=p;a.labelFontSize=s;a.viewportScale=e.scale()}),w=u.titleBarText);
w&&0!==w.length&&u.titleBarShown&&(a=e.nd(a.Ub(a,{}),{}).y>d/2,h={x:k,y:a?h:d-m+h,f:r,i:p},k=M.A(h),f.fillStyle=g.sg,f.fillRect(0,a?0:d-m,c,m),f.fillStyle=g.tg,G.Le({fontFamily:g.zj||g.Ih,fontStyle:g.$j||g.Jh,fontWeight:g.bk||g.Lh,fontVariant:g.ak||g.Kh,rb:g.ne,Zc:g.Aj,ob:0,cb:0,sb:1},f,w,k,h,{x:h.x+h.f/2,y:h.y+h.i/2},!0,!0).la||f.clearRect(0,0,c,d))}}}};function cb(a){function m(a,b,c){u=!0;h&&h.stop();q&&q.stop();return g(e.reset(a),b,c).P(function(){u=!1})}function k(b){e.update(b);s.I=!0;a.c.p("foamtree:dirty",!0)}function f(a,b){return e.d((0!==e.k()?0.35:1)*a,(0!==e.A()?0.35:1)*b)}function c(){if(1===b.Pb){var a=Math.round(1E4*e.k())/1E4;0!==a&&(n.$d=a,h=w.D.tc(n).ia({duration:500,G:{x:{start:a,end:0,R:X.Rb}},da:function(){e.d(n.x-n.$d,0);k(1);n.$d=n.x}}).start())}}function d(){if(1===b.Pb){var a=Math.round(1E4*e.A())/1E4;0!==a&&(p.ae=a,q=w.D.tc(p).ia({duration:500,
G:{y:{start:a,end:0,R:X.Rb}},da:function(){e.d(0,p.y-p.ae);k(1);p.ae=p.y}}).start())}}function g(a,c,d){return a?w.D.tc(b).ia({duration:void 0===c?700:c,G:{Pb:{start:0,end:1,R:d||X.og}},da:function(){k(b.Pb)}}).ab():(new V).J().M()}function l(a){return function(){return u?(new V).J().M():a.apply(this,arguments)}}var e=new oa(a),b={Pb:1},h,n={Ee:0,x:0,$d:0},q,p={Fe:0,y:0,ae:0},r=this,s,w,u=!1;a.c.j("model:loaded",function(a){s=a;e.reset(!1);e.update(1)});a.c.j("timeline:initialized",function(a){w=
a});this.H=function(){a.c.p("zoom:initialized",this)};this.reset=function(a,b){e.Qb(1);return m(!0,a,b)};this.normalize=l(function(a,b){e.Hc(1)?m(!1,a,b):r.vf()});this.vf=function(){c();d()};this.k=l(function(a,b,c,d){return r.sc(a.q,b,c,d)});this.Yb=l(function(a,b,c,d){return g(e.Yb(a,b),c,d)});this.sc=l(function(a,b,c,d){return g(e.sc(a,b),c,d)});this.Dj=l(function(a,b){e.sc(a,b)&&k(1)});this.vi=l(function(a,c){1===b.Pb&&f(a,c)&&k(1)});this.Qg=l(function(a,b){e.Yb(a,b)&&k(1)});this.Pg=l(function(a,
b,c,d){a=0|e.Yb(a,b);(a|=f(c,d))&&k(1)});this.wi=l(function(a,g,l){1===b.Pb&&(h=w.D.tc(n).ia({duration:a/0.03,G:{Ee:{start:g,end:0,R:X.Rb}},da:function(){e.d(n.Ee,0)&&k(1);c()}}).start(),q=w.D.tc(p).ia({duration:a/0.03,G:{Fe:{start:l,end:0,R:X.Rb}},da:function(){f(0,p.Fe)&&k(1);d()}}).start())});this.xi=function(){h&&0===e.k()&&h.stop();q&&0===e.A()&&q.stop()};this.Jc=function(a,b){e.Jc(a,b)};this.Qb=function(a){return e.Qb(a)};this.Hc=function(a){return e.Hc(a)};this.Rd=function(){return e.Rd()};
this.absolute=function(a,b){return e.absolute(a,b)};this.nd=function(a,b){return e.nd(a,b)};this.scale=function(){return e.scale()};this.d=function(a){return e.S(a)};this.content=function(a,b,c,d){e.content(a,b,c,d)}};function db(a,m,k){function f(a){var b=[];$.F(q,function(c){a(c)&&b.push(c.group)});return{groups:b}}function c(a,b){var c=n.options,d=c.mj,e=c.lj,c=c.fe,f=0<d+e?c:0,g=[];Ca.Ja(a,Ca.ya(a,n.options.he),function(a,c,h){c="groups"===n.options.ge?h:c;a.e&&(a=r.D.m(a).eb(f*(e+d*c)).call(b).xa(),g.push(a))});return r.D.m({}).Ya(g).ab()}function d(a){Y||(Y=!0,p.d(function(){Y=!1;n.c.p("repaint:before");C.ee(this.Og)},{Og:a}))}function g(a){function c(a,b){var f=a.W;a.W=b<=d;a.Yc=b<=e;a.W!==f&&$.Ge(a,function(a){a.me=
!0});a.open||a.fb||b++;if(f=a.e)for(var g=0;g<f.length;g++)c(f[g],b)}var d=n.options.rf,e=Math.min(n.options.rf,n.options.qi);if(a)for(var f=0;f<a.length;f++){var g=a[f];c(g,b(g))}else c(q,0)}function l(a,b){var c=[],d=e(a,b);d.ui&&n.c.p("model:childrenAttached",$.Mc(q));d.gj&&A.complete(function(a){J.pb(a);c.push(a)});for(var f=d=0;f<c.length;f++){var g=c[f];g.e&&(d+=g.e.length);g.Ca=!0;H.d(g)}return d}function e(a,b){function c(a,b){var k=!a.na&&b-(a.fb?1:0)<d;f=f||k;a.Qa=a.Qa||k;a.open||a.fb||
b++;var l=a.e;!l&&k&&(e=x.S(a)||e,l=a.e,h&&(a.Sa=!0));if(l)for(k=0;k<l.length;k++)g.push(l[k],b)}var d=b||n.options.ri,e=!1,f=!1,g,h="flattened"===m.xb;for(g=a?a.reduce(function(a,b){a.push(b,1);return a},[]):[q,1];0<g.length;)c(g.shift(),g.shift());return{ui:e,gj:f}}function b(a){for(var b=0;a.parent;)a.open||a.fb||b++,a=a.parent;return b}var h=this,n={c:new wa,options:m,Ud:k},q,p=new da,r=new ya(p),s=ba.create(),w=new ka(n),u=new cb(n),y=new Ea(n),x=new Fa(n.options),A=new Pa(n),C=new Ta(n,p),K=
new Ma(n);new bb(n);var B=new Ia(n),H=new Ja(n),Q=new Ka(n),O=new La(n);n.c.j("stage:initialized",function(a,b,c,d){t.ff(c,d)});n.c.j("stage:resized",function(a,b,c,d){t.kj(a,b,c,d)});n.c.j("foamtree:attachChildren",l);n.c.j("openclose:changing",g);n.c.j("interaction:reset",function(){R(!0)});n.c.j("foamtree:dirty",d);this.H=function(){n.c.p("timeline:initialized",r);q=x.H();w.H(a);y.H();C.H();K.H();B.H();H.H();u.H();Q.H();O.H()};this.kb=function(){r.d();I.stop();p.k();w.kb()};var P="groupLabelFontFamily groupLabelFontStyle groupLabelFontVariant groupLabelFontWeight groupLabelLineHeight groupLabelHorizontalPadding groupLabelVerticalPadding groupLabelDottingThreshold groupLabelMaxTotalHeight groupLabelMinFontSize groupLabelMaxFontSize groupLabelDecorator".split(" "),
F="rainbowColorDistribution rainbowLightnessDistribution rainbowColorDistributionAngle rainbowLightnessDistributionAngle rainbowColorModelStartPoint rainbowLightnessCorrection rainbowSaturationCorrection rainbowStartColor rainbowEndColor rainbowHueShift rainbowHueShiftCenter rainbowSaturationShift rainbowSaturationShiftCenter rainbowLightnessShift rainbowLightnessShiftCenter attributionTheme".split(" "),T=!1,N=["groupBorderRadius","groupBorderRadiusCorrection","groupBorderWidth","groupInsetWidth",
"groupBorderWidthScaling"],U=["maxGroupLevelsDrawn","maxGroupLabelLevelsDrawn"];this.Xb=function(a){n.c.p("options:changed",a);D.nb(a,P)&&$.F(q,function(a){a.Sa=!0});D.nb(a,F)&&(q.Ma=!0);D.nb(a,N)&&(T=!0);D.nb(a,U)&&(g(),l())};this.reload=function(){z.reload()};this.yc=function(a,b){D.defer(function(){if(T)t.oi(a),T=!1;else{if(b)for(var c=x.k(b),e=c.length-1;0<=e;e--)c[e].I=!0;else q.I=!0;d(a)}})};this.Z=function(){w.k()};this.update=function(a){a=a?x.k(a):[q];var b=a.reduce(function(a,b){a[b.id]=
b;return a},{});a=a.filter(function(a){for(a=a.parent;a;){if(D.N(b,a.id))return!1;a=a.parent}return!0});x.update(a);t.Ej(a)};this.reset=function(){return R(!1)};this.S=C.d;this.Ja=function(){var a={};return function(b,c){var d=x.d(b);return d?y.od(a,d,c):null}}();this.Ba=function(){var a={x:0,y:0},b={x:0,y:0};return function(c,d){var e=x.d(c);return e?(a.x=d.x,a.y=d.y,e.Ub(a,a),u.nd(a,a),b.x=a.x,b.y=a.y,b):null}}();this.ya=function(){var a={};return function(b){return(b=x.d(b))?y.qd(a,b):null}}();
this.Wb=function(){var a={};return function(b){return(b=x.d(b))?y.pd(a,b):null}}();this.za=function(){var a={};return function(){return u.d(a)}}();this.zc=function(){this.A({groups:f(function(a){return a.group.selected}),newState:!0,keepPrevious:!1});this.k({groups:f(function(a){return a.group.open}),newState:!0,keepPrevious:!1});this.d({groups:f(function(a){return a.group.exposed}),newState:!0,keepPrevious:!1})};this.Pa=function(){return f(function(a){return a.U})};this.d=function(a){return z.submit(function(){return B.fc(x.A(a,
"exposed",!1),!1,!0,!1)})};this.bb=function(){return f(function(a){return a.open})};this.k=function(a){return z.submit(function(){return Q.Kb(x.A(a,"open",!0),!1,!1)})};this.Ua=function(){return f(function(a){return a.selected})};this.A=function(a){return z.submit(function(){O.select(x.A(a,"selected",!0),!1);return(new V).J().M()})};this.Bc=function(a){return(a=x.d(a))?a===q?u.reset(m.wc,X.pa(m.xc)):u.k(a,m.Qc,m.wc,X.pa(m.xc)):(new V).J().M()};this.Aa=function(a,b){var c=x.k(a);if(c){var d=l(c,b);
g(c);return d}return 0};this.gb=function(a){return K.gb[a]};this.Ac=function(){var a=fa;return{frames:a.frames,totalTime:a.totalTime,lastFrameTime:a.Jd,lastInterFrameTime:a.Kd,fps:a.Oe}};var t=function(){function a(d,f){var g=d||c,h=f||e;c=g;e=h;var k=m.bc&&m.bc.boundary;k&&2<k.length?q.o=k.map(function(a){return{x:g*a.x,y:h*a.y}}):q.o=[{x:0,y:0},{x:g,y:0},{x:g,y:h},{x:0,y:h}];b()}function b(){q.aa=!0;q.u=q.o;q.q=M.q(q.o,q.q);q.L=q;M.re(q.o,q.L)}var c,e;return{ff:a,kj:function(b,c,e,f){J.stop();var g=
e/b,h=f/c;$.He(q,function(a){a.x=a.x*g+(Math.random()-0.5)*e/1E3;a.y=a.y*h+(Math.random()-0.5)*f/1E3});a(e,f);q.La=!0;A.step(J.pb,!0,!1,function(a){var b=a.e;if(b){A.Nb(a);for(var c=b.length-1;0<=c;c--){var d=b[c];d.f=d.rc}a.La=!0}})?d(!1):(A.qc(q),n.options.de?(d(!1),I.cg(),I.gd()):(A.complete(J.pb),q.Ma=!0,d(!1)))},oi:function(a){var c=!1;q.empty()||(b(),I.Gb()||(c=A.step(J.pb,!1,!1),d(a)));return c},Ej:function(a){a.forEach(function(a){$.Fa(a,function(a){a.empty()||A.Nb(a)});A.qc(a);n.options.de?
(I.cg(),$.Fa(a,function(a){a.empty()||J.df(a)})):($.Fa(a,function(a){a.empty()||J.pb(a)}),A.complete(J.pb),a.Ma=!0,d(!1))})}}}(),z=function(){function a(){0===m.Yd&&u.reset(0);n.options.Tf(m.bc);t.ff();x.Z(m.bc);e();g();n.c.p("model:loaded",q,$.Mc(q));var c;q.empty()||(q.open=!0,q.Qa=!0,m.de?c=I.gd():(I.zi(),c=f()),b(),0<m.fe?(C.clear(),w.d(1)):c=pa([c,d(1)]));n.options.Sf(m.bc);c&&(n.options.Wf(),c.P(function(){C.k(function(){p.d(n.options.Vf)})}))}function b(){var a=m.Va,c=m.cd;m.Va=0;m.cd=0;h.zc();
m.Va=a;m.cd=c}function d(a,b){return 0===m.Ke||b?(w.d(a),(new V).J().M()):r.D.m({opacity:w.d()}).oe(2).ia({duration:m.Ke,G:{opacity:{end:a,R:X.pa(m.fh)}},da:function(){w.d(this.opacity)}}).ab()}function f(){$.Fa(q,function(a){a.Ca=!1});var a=new V,b=new qa(a.J);b.d();q.Ca=!0;H.d(q).P(b.k);c(q,function Ua(){this.O&&this.o&&(this.aa=this.Ca=!0,b.d(),H.d(this).P(b.k),b.d(),c(this,Ua).P(b.k))});return a.M()}function k(){for(var a=0;a<s.length;a++){var b=s[a],c=b.action();D.N(c,"then")?c.P(b.Ae.J):b.Ae.J()}s=
[]}var l=!1,s=[];return{reload:function(){l||(q.empty()?a():(J.stop(),r.d(),I.stop(),l=!0,pa(0<m.Yd?[H.k(),R(!1)]:[d(0)]).P(function(){d(0,!0);l=!1;a();D.defer(k)})))},submit:function(a){if(l){var b=new V;s.push({action:a,Ae:b});return b.M()}return a()}}}(),L,E=new qa(function(){L.J()}),I=function(){function a(){f||(E.A()&&(L=new V),E.d(),b(),f=!0,p.repeat(e));return L.M()}function b(){g=s.now()}function e(){var b=s.now()-g>m.jj,b=A.step(function(b){b.Ca=!0;J.df(b);E.d();H.d(b).P(E.k);E.d();c(b,function(){this.Qa=
!0;a()}).P(E.k)},!0,b)||b;d(!0);b&&(f=!1,E.k());return b}var f=!1,g;return{zi:function(){A.complete(J.pb)},gd:a,cg:b,Gb:function(){return!E.A()},stop:function(){p.cancel(e);f=!1;E.clear()}}}(),J=function(){function a(b){var c=!b.empty();b.Ca=!0;if(c){for(var d=b.e,e=d.length-1;0<=e;e--){var f=d[e];f.f=f.rc}b.La=!0}return c}var b=[];return{df:function(c){var d=n.options,e=d.zh;0<e?Ca.Ja(c,Ca.ya(c,n.options.he),function(a,c,f){c="groups"===n.options.ge?f:c;E.d();b.push(r.D.m(a).eb(c*d.yh*e).ia({duration:e,
G:{f:{start:a.pg,end:a.rc,R:X.pa(d.Ah)}},da:function(){this.f=Math.max(0,this.f);this.parent.La=!0;I.gd()}}).ib(E.k).start())}):a(c)&&I.gd()},pb:a,stop:function(){for(var a=b.length-1;0<=a;a--)b[a].stop();b=[]}}}(),R=function(){var a=!1;return function(b){if(a)return(new V).J().M();a=!0;var c=[];c.push(u.reset(m.wc,X.pa(m.xc)));var d=new V;B.fc({e:[],Ia:!1,Ha:!1},b,!1,!0).P(function(){Q.Kb({e:[],Ia:!1,Ha:!1},b,!1).P(d.J)});c.push(d.M());return pa(c).P(function(){a=!1;b&&m.Xf()})}}(),Y=!1}
function $a(){return{version:"3.4.9",build:"bugfix/3.4.x/b84d33dd",brandingAllowed:!1}};v.Dd(function(){window.CarrotSearchFoamTree=function(a){function m(a,b){if(!l||l.exists(a))switch(a){case "selection":return h.Ua();case "open":return h.bb();case "exposure":return h.Pa();case "state":return h.ya.apply(this,b);case "geometry":return h.Ja.apply(this,b);case "hierarchy":return h.Wb.apply(this,b);case "containerCoordinates":return h.Ba.apply(this,b);case "imageData":return h.S.apply(this,b);case "viewport":return h.za();case "times":return h.Ac();case "onModelChanged":case "onRedraw":case "onRolloutStart":case "onRolloutComplete":case "onRelaxationStep":case "onGroupHover":case "onGroupOpenOrCloseChanging":case "onGroupExposureChanging":case "onGroupSelectionChanging":case "onGroupSelectionChanged":case "onGroupClick":case "onGroupDoubleClick":case "onGroupHold":var c=
e[a];return Array.isArray(c)?c:[c];default:return e[a]}}function k(a){function d(a,b){return D.N(f,a)?(b(f[a]),delete f[a],1):0}var f;if(0===arguments.length)return 0;1===arguments.length?f=D.extend({},arguments[0]):2===arguments.length&&(f={},f[arguments[0]]=arguments[1]);l&&l.validate(f,b.pi);var g=0;h&&(g+=d("selection",h.A),g+=d("open",h.k),g+=d("exposure",h.d));var k={};D.Ga(f,function(a,b){if(e[b]!==a||D.jc(a))k[b]=a,g++;e[b]=a});0<g&&c(k);return g}function f(a,b){var d="on"+a.charAt(0).toUpperCase()+
a.slice(1),f=e[d];e[d]=b(Array.isArray(f)?f:[f]);f={};f[d]=e[d];c(f)}function c(a){(function(){function c(b,d){return D.N(a,b)||void 0===d?va.m(e[b],g):d}b.pi=e.logging;b.bc=e.dataObject;b.n=e.pixelRatio;b.yb=e.wireframePixelRatio;b.xb=e.stacking;b.ah=e.descriptionGroup;b.dc=e.descriptionGroupType;b.Ic=e.descriptionGroupPosition;b.bh=e.descriptionGroupDistanceFromCenter;b.cc=e.descriptionGroupSize;b.Ce=e.descriptionGroupMinHeight;b.Be=e.descriptionGroupMaxHeight;b.De=e.descriptionGroupPolygonDrawn;
b.Wc=e.layout;b.lc=e.layoutByWeightOrder;b.wj=e.showZeroWeightGroups;b.Ve=e.groupMinDiameter;b.ce=e.rectangleAspectRatioPreference;b.ij=e.initializer||e.relaxationInitializer;b.jj=e.relaxationMaxDuration;b.de=e.relaxationVisible;b.ag=e.relaxationQualityThreshold;b.Rh=e.groupResizingBudget;b.zh=e.groupGrowingDuration;b.yh=e.groupGrowingDrag;b.Ah=e.groupGrowingEasing;b.jh=e.groupBorderRadius;b.lb=e.groupBorderWidth;b.Ra=e.groupBorderWidthScaling;b.Ad=e.groupInsetWidth;b.kh=e.groupBorderRadiusCorrection;
b.mb=e.groupStrokeWidth;b.Rc=e.groupSelectionOutlineWidth;b.Vh=e.groupSelectionOutlineColor;b.Bd=e.groupSelectionOutlineShadowSize;b.We=e.groupSelectionOutlineShadowColor;b.Sh=e.groupSelectionFillHueShift;b.Uh=e.groupSelectionFillSaturationShift;b.Th=e.groupSelectionFillLightnessShift;b.Ye=e.groupSelectionStrokeHueShift;b.$e=e.groupSelectionStrokeSaturationShift;b.Ze=e.groupSelectionStrokeLightnessShift;b.xh=e.groupFillType;b.th=e.groupFillGradientRadius;b.qh=e.groupFillGradientCenterHueShift;b.sh=
e.groupFillGradientCenterSaturationShift;b.rh=e.groupFillGradientCenterLightnessShift;b.uh=e.groupFillGradientRimHueShift;b.wh=e.groupFillGradientRimSaturationShift;b.vh=e.groupFillGradientRimLightnessShift;b.Cd=e.groupStrokeType;b.mb=e.groupStrokeWidth;b.af=e.groupStrokePlainHueShift;b.cf=e.groupStrokePlainSaturationShift;b.bf=e.groupStrokePlainLightnessShift;b.$h=e.groupStrokeGradientRadius;b.Wh=e.groupStrokeGradientAngle;b.ai=e.groupStrokeGradientUpperHueShift;b.ci=e.groupStrokeGradientUpperSaturationShift;
b.bi=e.groupStrokeGradientUpperLightnessShift;b.Xh=e.groupStrokeGradientLowerHueShift;b.Zh=e.groupStrokeGradientLowerSaturationShift;b.Yh=e.groupStrokeGradientLowerLightnessShift;b.Bh=e.groupHoverFillHueShift;b.Dh=e.groupHoverFillSaturationShift;b.Ch=e.groupHoverFillLightnessShift;b.Se=e.groupHoverStrokeHueShift;b.Ue=e.groupHoverStrokeSaturationShift;b.Te=e.groupHoverStrokeLightnessShift;b.Wa=e.groupExposureScale;b.ph=e.groupExposureShadowColor;b.Re=e.groupExposureShadowSize;b.Qc=e.groupExposureZoomMargin;
b.ei=e.groupUnexposureLightnessShift;b.fi=e.groupUnexposureSaturationShift;b.di=e.groupUnexposureLabelColorThreshold;b.Va=e.exposeDuration;b.gc=e.exposeEasing;b.cd=e.openCloseDuration;b.lh=va.m(e.groupColorDecorator,g);b.mh=e.groupColorDecorator!==D.ta;b.Gh=va.m(e.groupLabelDecorator,g);b.Hh=e.groupLabelDecorator!==D.ta;b.Mh=va.m(e.groupLabelLayoutDecorator,g);b.Nh=e.groupLabelLayoutDecorator!==D.ta;b.nh=va.m(e.groupContentDecorator,g);b.Pc=e.groupContentDecorator!==D.ta;b.oh=e.groupContentDecoratorTriggering;
b.ej=e.rainbowStartColor;b.Yi=e.rainbowEndColor;b.Wi=e.rainbowColorDistribution;b.Xi=e.rainbowColorDistributionAngle;b.aj=e.rainbowLightnessDistributionAngle;b.bj=e.rainbowLightnessShift;b.cj=e.rainbowLightnessShiftCenter;b.dj=e.rainbowSaturationCorrection;b.$i=e.rainbowLightnessCorrection;b.Yf=e.parentFillOpacity;b.yi=e.parentStrokeOpacity;b.Zf=e.parentLabelOpacity;b.$f=e.parentOpacityBalancing;b.Qh=e.groupLabelUpdateThreshold;b.Ih=e.groupLabelFontFamily;b.Jh=e.groupLabelFontStyle;b.Kh=e.groupLabelFontVariant;
b.Lh=e.groupLabelFontWeight;b.Ph=e.groupLabelMinFontSize;b.Sj=e.groupLabelMaxFontSize;b.Rj=e.groupLabelLineHeight;b.Qj=e.groupLabelHorizontalPadding;b.Uj=e.groupLabelVerticalPadding;b.Tj=e.groupLabelMaxTotalHeight;b.Fh=e.groupLabelDarkColor;b.Oh=e.groupLabelLightColor;b.Eh=e.groupLabelColorThreshold;b.Gj=e.wireframeDrawMaxDuration;b.Hj=e.wireframeLabelDrawing;b.Fj=e.wireframeContentDecorationDrawing;b.xg=e.wireframeToFinalFadeDuration;b.Ij=e.wireframeToFinalFadeDelay;b.gh=e.finalCompleteDrawMaxDuration;
b.hh=e.finalIncrementalDrawMaxDuration;b.Me=e.finalToWireframeFadeDuration;b.rd=e.androidStockBrowserWorkaround;b.ef=e.incrementalDraw;b.si=e.maxGroups;b.ri=e.maxGroupLevelsAttached;b.rf=e.maxGroupLevelsDrawn;b.qi=e.maxGroupLabelLevelsDrawn;b.he=e.rolloutStartPoint;b.ge=e.rolloutMethod;b.nj=e.rolloutEasing;b.fe=e.rolloutDuration;b.fg=e.rolloutScalingStrength;b.hg=e.rolloutTranslationXStrength;b.ig=e.rolloutTranslationYStrength;b.eg=e.rolloutRotationStrength;b.gg=e.rolloutTransformationCenter;b.rj=
e.rolloutPolygonDrag;b.sj=e.rolloutPolygonDuration;b.oj=e.rolloutLabelDelay;b.pj=e.rolloutLabelDrag;b.qj=e.rolloutLabelDuration;b.mj=e.rolloutChildGroupsDrag;b.lj=e.rolloutChildGroupsDelay;b.Pi=e.pullbackStartPoint;b.Ji=e.pullbackMethod;b.Fi=e.pullbackEasing;b.Xj=e.pullbackType;b.Yd=e.pullbackDuration;b.Oi=e.pullbackScalingStrength;b.Ri=e.pullbackTranslationXStrength;b.Si=e.pullbackTranslationYStrength;b.Ni=e.pullbackRotationStrength;b.Qi=e.pullbackTransformationCenter;b.Ki=e.pullbackPolygonDelay;
b.Li=e.pullbackPolygonDrag;b.Mi=e.pullbackPolygonDuration;b.Gi=e.pullbackLabelDelay;b.Hi=e.pullbackLabelDrag;b.Ii=e.pullbackLabelDuration;b.Ci=e.pullbackChildGroupsDelay;b.Di=e.pullbackChildGroupsDrag;b.Ei=e.pullbackChildGroupsDuration;b.Ke=e.fadeDuration;b.fh=e.fadeEasing;b.Jj=e.zoomMouseWheelFactor;b.wc=e.zoomMouseWheelDuration;b.xc=e.zoomMouseWheelEasing;b.ti=e.maxLabelSizeForTitleBar;b.zj=e.titleBarFontFamily;b.sg=e.titleBarBackgroundColor;b.tg=e.titleBarTextColor;b.Aj=e.titleBarMinFontSize;b.ne=
e.titleBarMaxFontSize;b.Bj=e.titleBarTextPaddingLeftRight;b.Cj=e.titleBarTextPaddingTopBottom;b.yj=e.titleBarDecorator;b.Nj=e.attributionText;b.Kj=e.attributionLogo;b.Mj=e.attributionLogoScale;b.Oj=e.attributionUrl;b.ve=e.attributionPosition;b.Rg=e.attributionDistanceFromCenter;b.Tg=e.attributionWeight;b.Sg=e.attributionTheme;b.gf=e.interactionHandler;b.Tf=c("onModelChanging",b.Tf);b.Sf=c("onModelChanged",b.Sf);b.Uf=c("onRedraw",b.Uf);b.Wf=c("onRolloutStart",b.Wf);b.Vf=c("onRolloutComplete",b.Vf);
b.Sd=c("onRelaxationStep",b.Sd);b.Xf=c("onViewReset",b.Xf);b.Lf=c("onGroupOpenOrCloseChanging",b.Lf);b.Kf=c("onGroupOpenOrCloseChanged",b.Kf);b.Df=c("onGroupExposureChanging",b.Df);b.Cf=c("onGroupExposureChanged",b.Cf);b.Nf=c("onGroupSelectionChanging",b.Nf);b.Mf=c("onGroupSelectionChanged",b.Mf);b.Ff=c("onGroupHover",b.Ff);b.Hf=c("onGroupMouseMove",b.Hf);b.xf=c("onGroupClick",b.xf);b.yf=c("onGroupDoubleClick",b.yf);b.Ef=c("onGroupHold",b.Ef);b.Jf=c("onGroupMouseWheel",b.Jf);b.If=c("onGroupMouseUp",
b.If);b.Gf=c("onGroupMouseDown",b.Gf);b.Bf=c("onGroupDragStart",b.Bf);b.zf=c("onGroupDrag",b.zf);b.Af=c("onGroupDragEnd",b.Af);b.Qf=c("onGroupTransformStart",b.Qf);b.Of=c("onGroupTransform",b.Of);b.Pf=c("onGroupTransformEnd",b.Pf);b.Rf=c("onKeyUp",b.Rf)})();b.fj=S.Ba(b.ej);b.Zi=S.Ba(b.Yi);b.Xe=S.Ba(b.We);b.Lj=null;h&&(h.Xb(a),D.N(a,"dataObject")&&h.reload())}function d(a){return function(){return a.apply(this,arguments).ih(g)}}var g=this,l=window.CarrotSearchFoamTree.asserts,e=D.extend({},window.CarrotSearchFoamTree.defaults),
b={};k(a);(a=e.element||document.getElementById(e.id))||la.Pa("Element to embed FoamTree in not found.");e.element=a;var h=new db(a,b,e);h.H();var n={get:function(a){return 0===arguments.length?D.extend({},e):m(arguments[0],Array.prototype.slice.call(arguments,1))},set:k,on:function(a,b){f(a,function(a){a.push(b);return a})},off:function(a,b){f(a,function(a){return a.filter(function(a){return a!==b})})},resize:h.Z,redraw:h.yc,update:h.update,attach:h.Aa,select:d(h.A),expose:d(h.d),open:d(h.k),reset:d(h.reset),
zoom:d(h.Bc),trigger:function(a,b){var c=h.gb(a);c&&c(b)},dispose:function(){function a(){throw"FoamTree instance disposed";}h.kb();D.Ga(n,function(b,c){"dispose"!==c&&(g[c]=a)})}};D.Ga(n,function(a,b){g[b]=a});h.reload()};window["CarrotSearchFoamTree.asserts"]&&(window.CarrotSearchFoamTree.asserts=window["CarrotSearchFoamTree.asserts"],delete window["CarrotSearchFoamTree.asserts"]);window.CarrotSearchFoamTree.supported=!0;window.CarrotSearchFoamTree.version=$a;window.CarrotSearchFoamTree.defaults=
Object.freeze({id:void 0,element:void 0,logging:!1,dataObject:void 0,pixelRatio:1,wireframePixelRatio:1,layout:"relaxed",layoutByWeightOrder:!0,showZeroWeightGroups:!0,groupMinDiameter:10,rectangleAspectRatioPreference:-1,relaxationInitializer:"fisheye",relaxationMaxDuration:3E3,relaxationVisible:!1,relaxationQualityThreshold:1,stacking:"hierarchical",descriptionGroup:"auto",descriptionGroupType:"stab",descriptionGroupPosition:225,descriptionGroupDistanceFromCenter:1,descriptionGroupSize:0.125,descriptionGroupMinHeight:35,
descriptionGroupMaxHeight:0.5,descriptionGroupPolygonDrawn:!1,maxGroups:5E4,maxGroupLevelsAttached:4,maxGroupLevelsDrawn:4,maxGroupLabelLevelsDrawn:3,groupGrowingDuration:0,groupGrowingEasing:"bounce",groupGrowingDrag:0,groupResizingBudget:2,groupBorderRadius:0.15,groupBorderWidth:4,groupBorderWidthScaling:0.6,groupInsetWidth:6,groupBorderRadiusCorrection:1,groupSelectionOutlineWidth:5,groupSelectionOutlineColor:"#222",groupSelectionOutlineShadowSize:0,groupSelectionOutlineShadowColor:"#fff",groupSelectionFillHueShift:0,
groupSelectionFillSaturationShift:0,groupSelectionFillLightnessShift:0,groupSelectionStrokeHueShift:0,groupSelectionStrokeSaturationShift:0,groupSelectionStrokeLightnessShift:-10,groupFillType:"gradient",groupFillGradientRadius:1,groupFillGradientCenterHueShift:0,groupFillGradientCenterSaturationShift:0,groupFillGradientCenterLightnessShift:20,groupFillGradientRimHueShift:0,groupFillGradientRimSaturationShift:0,groupFillGradientRimLightnessShift:-5,groupStrokeType:"plain",groupStrokeWidth:1.5,groupStrokePlainHueShift:0,
groupStrokePlainSaturationShift:0,groupStrokePlainLightnessShift:-10,groupStrokeGradientRadius:1,groupStrokeGradientAngle:45,groupStrokeGradientUpperHueShift:0,groupStrokeGradientUpperSaturationShift:0,groupStrokeGradientUpperLightnessShift:20,groupStrokeGradientLowerHueShift:0,groupStrokeGradientLowerSaturationShift:0,groupStrokeGradientLowerLightnessShift:-20,groupHoverFillHueShift:0,groupHoverFillSaturationShift:0,groupHoverFillLightnessShift:20,groupHoverStrokeHueShift:0,groupHoverStrokeSaturationShift:0,
groupHoverStrokeLightnessShift:-10,groupExposureScale:1.15,groupExposureShadowColor:"rgba(0, 0, 0, 0.5)",groupExposureShadowSize:50,groupExposureZoomMargin:0.1,groupUnexposureLightnessShift:65,groupUnexposureSaturationShift:-65,groupUnexposureLabelColorThreshold:0.35,exposeDuration:700,exposeEasing:"squareInOut",groupColorDecorator:D.ta,groupLabelDecorator:D.ta,groupLabelLayoutDecorator:D.ta,groupContentDecorator:D.ta,groupContentDecoratorTriggering:"onLayoutDirty",openCloseDuration:500,rainbowColorDistribution:"radial",
rainbowColorDistributionAngle:-45,rainbowLightnessDistributionAngle:45,rainbowSaturationCorrection:0.1,rainbowLightnessCorrection:0.4,rainbowStartColor:"hsla(0, 100%, 55%, 1)",rainbowEndColor:"hsla(359, 100%, 55%, 1)",rainbowLightnessShift:30,rainbowLightnessShiftCenter:0.4,parentFillOpacity:0.7,parentStrokeOpacity:1,parentLabelOpacity:1,parentOpacityBalancing:!0,wireframeDrawMaxDuration:15,wireframeLabelDrawing:"auto",wireframeContentDecorationDrawing:"auto",wireframeToFinalFadeDuration:500,wireframeToFinalFadeDelay:300,
finalCompleteDrawMaxDuration:80,finalIncrementalDrawMaxDuration:100,finalToWireframeFadeDuration:200,androidStockBrowserWorkaround:v.hf(),incrementalDraw:"fast",groupLabelFontFamily:"sans-serif",groupLabelFontStyle:"normal",groupLabelFontWeight:"normal",groupLabelFontVariant:"normal",groupLabelLineHeight:1.05,groupLabelHorizontalPadding:1,groupLabelVerticalPadding:1,groupLabelMinFontSize:6,groupLabelMaxFontSize:160,groupLabelMaxTotalHeight:0.9,groupLabelUpdateThreshold:0.05,groupLabelDarkColor:"#000",
groupLabelLightColor:"#fff",groupLabelColorThreshold:0.35,rolloutStartPoint:"center",rolloutEasing:"squareOut",rolloutMethod:"groups",rolloutDuration:2E3,rolloutScalingStrength:-0.7,rolloutTranslationXStrength:0,rolloutTranslationYStrength:0,rolloutRotationStrength:-0.7,rolloutTransformationCenter:0.7,rolloutPolygonDrag:0.1,rolloutPolygonDuration:0.5,rolloutLabelDelay:0.8,rolloutLabelDrag:0.1,rolloutLabelDuration:0.5,rolloutChildGroupsDrag:0.1,rolloutChildGroupsDelay:0.2,pullbackStartPoint:"center",
pullbackEasing:"squareIn",pullbackMethod:"groups",pullbackDuration:1500,pullbackScalingStrength:-0.7,pullbackTranslationXStrength:0,pullbackTranslationYStrength:0,pullbackRotationStrength:-0.7,pullbackTransformationCenter:0.7,pullbackPolygonDelay:0.3,pullbackPolygonDrag:0.1,pullbackPolygonDuration:0.8,pullbackLabelDelay:0,pullbackLabelDrag:0.1,pullbackLabelDuration:0.3,pullbackChildGroupsDelay:0.1,pullbackChildGroupsDrag:0.1,pullbackChildGroupsDuration:0.3,fadeDuration:700,fadeEasing:"cubicInOut",
zoomMouseWheelFactor:1.5,zoomMouseWheelDuration:500,zoomMouseWheelEasing:"squareOut",maxLabelSizeForTitleBar:8,titleBarFontFamily:null,titleBarFontStyle:"normal",titleBarFontWeight:"normal",titleBarFontVariant:"normal",titleBarBackgroundColor:"rgba(0, 0, 0, 0.5)",titleBarTextColor:"rgba(255, 255, 255, 1)",titleBarMinFontSize:10,titleBarMaxFontSize:40,titleBarTextPaddingLeftRight:20,titleBarTextPaddingTopBottom:15,titleBarDecorator:D.ta,attributionText:null,attributionLogo:null,attributionLogoScale:0.5,
attributionUrl:"http://carrotsearch.com/foamtree",attributionPosition:"bottom-right",attributionDistanceFromCenter:1,attributionWeight:0.025,attributionTheme:"light",interactionHandler:v.ii()?"hammerjs":"builtin",onModelChanging:[],onModelChanged:[],onRedraw:[],onRolloutStart:[],onRolloutComplete:[],onRelaxationStep:[],onViewReset:[],onGroupOpenOrCloseChanging:[],onGroupOpenOrCloseChanged:[],onGroupExposureChanging:[],onGroupExposureChanged:[],onGroupSelectionChanging:[],onGroupSelectionChanged:[],
onGroupHover:[],onGroupMouseMove:[],onGroupClick:[],onGroupDoubleClick:[],onGroupHold:[],onGroupMouseWheel:[],onGroupMouseUp:[],onGroupMouseDown:[],onGroupDragStart:[],onGroupDrag:[],onGroupDragEnd:[],onGroupTransformStart:[],onGroupTransform:[],onGroupTransformEnd:[],onKeyUp:[],selection:null,open:null,exposure:null,imageData:null,hierarchy:null,geometry:null,containerCoordinates:null,state:null,viewport:null,times:null});window.CarrotSearchFoamTree.geometry=Object.freeze(function(){return{rectangleInPolygon:function(a,
m,k,f,c,d,g){c=D.B(c,1);d=D.B(d,0.5);g=D.B(g,0.5);a=M.se(a,{x:m,y:k},f,d,g)*c;return{x:m-a*f*d,y:k-a*g,w:a*f,h:a}},circleInPolygon:function(a,m,k){return M.Dg(a,{x:m,y:k})},stabPolygon:function(a,m,k,f){return M.Wb(a,{x:m,y:k},f)},polygonCentroid:function(a){a=M.k(a,{});return{x:a.x,y:a.y,area:a.ja}},boundingBox:function(a){for(var m=a[0].x,k=a[0].y,f=a[0].x,c=a[0].y,d=1;d<a.length;d++){var g=a[d];g.x<m&&(m=g.x);g.y<k&&(k=g.y);g.x>f&&(f=g.x);g.y>c&&(c=g.y)}return{x:m,y:k,w:f-m,h:c-k}}}}())},function(){window.CarrotSearchFoamTree=
function(){window.console.error("FoamTree is not supported on this browser.")};window.CarrotSearchFoamTree.Zj=!1});})();


              </script>
              <script id="bundle-data" type="application/json">
                {"groups":[{"label":"E:\\laragon\\www\\wp-content\\themes\\argon\\assets\\dist\\argon-theme.js","weight":628269,"groups":[{"label":"node_modules","weight":532106,"groups":[{"label":"@fancyapps\\ui\\dist\\fancybox.esm.js","weight":106153},{"label":"jquery\\dist\\jquery.js","weight":89999},{"label":"nouislider\\dist\\nouislider.js","weight":26711},{"label":"highlight.js\\lib","weight":144995,"groups":[{"label":"core.js","weight":22812},{"label":"languages","weight":120754,"groups":[{"label":"scss.js","weight":9953},{"label":"less.js","weight":9859},{"label":"css.js","weight":8915},{"label":"swift.js","weight":8722},{"label":"typescript.js","weight":7350},{"label":"sql.js","weight":6684},{"label":"javascript.js","weight":6259},{"label":"cpp.js","weight":5599},{"label":"php.js","weight":5483},{"label":"perl.js","weight":4462},{"label":"csharp.js","weight":3923},{"label":"c.js","weight":3803},{"label":"python.js","weight":3528},{"label":"ruby.js","weight":3405},{"label":"kotlin.js","weight":3387},{"label":"bash.js","weight":3076},{"label":"r.js","weight":2931},{"label":"objectivec.js","weight":2792},{"label":"rust.js","weight":2791},{"label":"java.js","weight":2731},{"label":"vbnet.js","weight":2589},{"label":"markdown.js","weight":1930},{"label":"lua.js","weight":1881},{"label":"xml.js","weight":1857},{"label":"yaml.js","weight":1734},{"label":"go.js","weight":1118},{"label":"makefile.js","weight":1081},{"label":"ini.js","weight":951},{"label":"diff.js","weight":528},{"label":"php-template.js","weight":435},{"label":"json.js","weight":370},{"label":"shell.js","weight":262},{"label":"python-repl.js","weight":235},{"label":"plaintext.js","weight":130}]},{"label":"common.js","weight":1429}]},{"label":"izitoast\\dist\\js\\iziToast.js","weight":18502},{"label":"pangu\\dist\\browser\\pangu.js","weight":12521},{"label":"@simonwep\\pickr\\dist\\webpack:","weight":27945,"groups":[{"label":"src\\js","weight":25751,"groups":[{"label":"pickr.js","weight":12162},{"label":"template.js","weight":3197},{"label":"utils","weight":6459,"groups":[{"label":"color.js","weight":2817},{"label":"utils.js","weight":2234},{"label":"hsvacolor.js","weight":1408}]},{"label":"libs","weight":3933,"groups":[{"label":"moveable.js","weight":1910},{"label":"nanopop.js","weight":1653},{"label":"selectable.js","weight":370}]}]},{"label":"webpack","weight":2194,"groups":[{"label":"universalModuleDefinition","weight":1290},{"label":"bootstrap","weight":904}]}]},{"label":"clipboard\\dist\\clipboard.js","weight":9202},{"label":"tippy.js","weight":15461,"groups":[{"label":"src","weight":15419,"groups":[{"label":"createTippy.ts","weight":9073},{"label":"props.ts","weight":1674},{"label":"template.ts","weight":1496},{"label":"dom-utils.ts","weight":1244},{"label":"utils.ts","weight":709},{"label":"bindGlobalEventListeners.ts","weight":429},{"label":"addons\\createSingleton.ts","weight":290},{"label":"index.ts","weight":256},{"label":"constants.ts","weight":161},{"label":"browser.ts","weight":87}]},{"label":"build\\base.js","weight":42}]},{"label":"bootstrap\\js\\src","weight":10171,"groups":[{"label":"modal.js","weight":8725},{"label":"util.js","weight":1446}]},{"label":"regenerator-runtime\\runtime.js","weight":6476},{"label":"headroom.js\\dist\\headroom.js","weight":4304},{"label":"nprogress\\nprogress.js","weight":4033},{"label":"highlightjs-line-numbers.js\\src\\highlightjs-line-numbers.js","weight":3219},{"label":"qrcode\\lib","weight":20236,"groups":[{"label":"core","weight":16346,"groups":[{"label":"qrcode.js","weight":2800},{"label":"segments.js","weight":2237},{"label":"mask-pattern.js","weight":1689},{"label":"error-correction-code.js","weight":1411},{"label":"version.js","weight":928},{"label":"mode.js","weight":858},{"label":"regex.js","weight":684},{"label":"alphanumeric-data.js","weight":659},{"label":"utils.js","weight":626},{"label":"kanji-data.js","weight":518},{"label":"bit-matrix.js","weight":487},{"label":"numeric-data.js","weight":486},{"label":"error-correction-level.js","weight":480},{"label":"reed-solomon-encoder.js","weight":444},{"label":"polynomial.js","weight":431},{"label":"bit-buffer.js","weight":401},{"label":"alignment-pattern.js","weight":350},{"label":"byte-data.js","weight":350},{"label":"galois-field.js","weight":253},{"label":"format-info.js","weight":139},{"label":"finder-pattern.js","weight":67},{"label":"version-check.js","weight":48}]},{"label":"renderer","weight":2849,"groups":[{"label":"utils.js","weight":1322},{"label":"svg-tag.js","weight":870},{"label":"canvas.js","weight":657}]},{"label":"browser.js","weight":950},{"label":"can-promise.js","weight":91}]},{"label":"clamp-js\\clamp.js","weight":2368},{"label":"jquery.easing\\jquery.easing.js","weight":2327},{"label":"@popperjs\\core\\lib","weight":19257,"groups":[{"label":"modifiers","weight":9006,"groups":[{"label":"computeStyles.js","weight":2045},{"label":"preventOverflow.js","weight":1657},{"label":"flip.js","weight":1337},{"label":"arrow.js","weight":952},{"label":"applyStyles.js","weight":944},{"label":"hide.js","weight":736},{"label":"offset.js","weight":570},{"label":"eventListeners.js","weight":553},{"label":"popperOffsets.js","weight":212}]},{"label":"createPopper.js","weight":1909},{"label":"utils","weight":3303,"groups":[{"label":"detectOverflow.js","weight":808},{"label":"computeOffsets.js","weight":473},{"label":"computeAutoPlacement.js","weight":459},{"label":"orderModifiers.js","weight":395},{"label":"mergeByName.js","weight":239},{"label":"getOppositePlacement.js","weight":143},{"label":"getOppositeVariationPlacement.js","weight":119},{"label":"debounce.js","weight":117},{"label":"rectToClientRect.js","weight":96},{"label":"within.js","weight":78},{"label":"expandToHashMap.js","weight":70},{"label":"getFreshSideObject.js","weight":64},{"label":"getMainAxisFromPlacement.js","weight":60},{"label":"math.js","weight":50},{"label":"mergePaddingObject.js","weight":42},{"label":"getBasePlacement.js","weight":38},{"label":"getVariation.js","weight":38},{"label":"getAltAxis.js","weight":14}]},{"label":"dom-utils","weight":4589,"groups":[{"label":"getClippingRect.js","weight":797},{"label":"getOffsetParent.js","weight":690},{"label":"getCompositeRect.js","weight":456},{"label":"getDocumentRect.js","weight":352},{"label":"getBoundingClientRect.js","weight":302},{"label":"instanceOf.js","weight":251},{"label":"getViewportRect.js","weight":240},{"label":"listScrollParents.js","weight":205},{"label":"getLayoutRect.js","weight":189},{"label":"contains.js","weight":176},{"label":"getWindow.js","weight":137},{"label":"isScrollParent.js","weight":115},{"label":"getScrollParent.js","weight":113},{"label":"getParentNode.js","weight":95},{"label":"getDocumentElement.js","weight":91},{"label":"getWindowScroll.js","weight":84},{"label":"getNodeName.js","weight":60},{"label":"isTableElement.js","weight":56},{"label":"getWindowScrollBarX.js","weight":54},{"label":"getHTMLElementScroll.js","weight":51},{"label":"getComputedStyle.js","weight":48},{"label":"getNodeScroll.js","weight":27}]},{"label":"enums.js","weight":422},{"label":"popper.js","weight":28}]},{"label":"dijkstrajs\\dijkstra.js","weight":1064},{"label":"@swc\\helpers\\src","weight":6681,"groups":[{"label":"_construct.js","weight":583},{"label":"_wrap_native_super.js","weight":535},{"label":"_object_spread.js","weight":450},{"label":"_async_to_generator.js","weight":401},{"label":"_get.js","weight":351},{"label":"_is_native_reflect_construct.js","weight":325},{"label":"_create_class.js","weight":308},{"label":"_get_prototype_of.js","weight":303},{"label":"_inherits.js","weight":272},{"label":"_set_prototype_of.js","weight":270},{"label":"_non_iterable_spread.js","weight":265},{"label":"_define_property.js","weight":264},{"label":"_non_iterable_rest.js","weight":259},{"label":"_is_native_function.js","weight":248},{"label":"_create_super.js","weight":243},{"label":"_type_of.js","weight":237},{"label":"_iterable_to_array.js","weight":210},{"label":"_assert_this_initialized.js","weight":204},{"label":"_array_without_holes.js","weight":189},{"label":"_class_call_check.js","weight":175},{"label":"_super_prop_base.js","weight":136},{"label":"_array_with_holes.js","weight":126},{"label":"_possible_constructor_return.js","weight":125},{"label":"_sliced_to_array.js","weight":103},{"label":"_to_consumable_array.js","weight":99}]},{"label":"encode-utf8\\index.js","weight":481}]},{"label":"src","weight":93904,"groups":[{"label":"js","weight":69362,"groups":[{"label":"comments","weight":24957,"groups":[{"label":"send-comment.js","weight":10724},{"label":"comment-pin.js","weight":2515},{"label":"emotion-panel.js","weight":1993},{"label":"reply-comment.js","weight":1502},{"label":"comment-page.js","weight":1487},{"label":"edit-comment.js","weight":1457},{"label":"comment-edit-history.js","weight":1265},{"label":"text-avatar.js","weight":1127},{"label":"comment-form.js","weight":1060},{"label":"comment-vote.js","weight":998},{"label":"comment-fold.js","weight":528},{"label":"comment-parent-info.js","weight":301}]},{"label":"float-action-btns.js","weight":3527},{"label":"share.js","weight":3297},{"label":"code-highlight.js","weight":3245},{"label":"i18n","weight":7581,"groups":[{"label":"ru_RU.js","weight":3175},{"label":"en_US.js","weight":2507},{"label":"zh_TW.js","weight":1899}]},{"label":"pjax.js","weight":2902},{"label":"blog-settings","weight":2930,"groups":[{"label":"theme-color.js","weight":2073},{"label":"card-radius.js","weight":857}]},{"label":"toolbar.js","weight":1664},{"label":"waterflow.js","weight":1629},{"label":"search.js","weight":1500},{"label":"shuoshuo-vote.js","weight":1347},{"label":"utils","weight":3112,"groups":[{"label":"time-calculation.js","weight":1302},{"label":"color-calculation.js","weight":816},{"label":"go-to-hash.js","weight":378},{"label":"random.js","weight":340},{"label":"cookies.js","weight":142},{"label":"clamp.js","weight":134}]},{"label":"sidebar.js","weight":1212},{"label":"shortcodes","weight":1526,"groups":[{"label":"github-card.js","weight":1204},{"label":"collapse-block.js","weight":322}]},{"label":"lazyload.js","weight":967},{"label":"banner-cover.js","weight":907},{"label":"banner-typing-effect.js","weight":826},{"label":"console-info.js","weight":817},{"label":"responsible.js","weight":647},{"label":"shuoshuo-fold.js","weight":590},{"label":"search-filter.js","weight":514},{"label":"post-outdated-toast.js","weight":473},{"label":"fancybox.js","weight":427},{"label":"headroom.js","weight":413},{"label":"tippy.js","weight":383},{"label":"article-reference.js","weight":352},{"label":"pangu.js","weight":306},{"label":"zoomify.js","weight":291},{"label":"horizontal-scroll.js","weight":232},{"label":"hitokoto.js","weight":216},{"label":"article-password.js","weight":212},{"label":"i18n.js","weight":200},{"label":"main.js","weight":160}]},{"label":"libs","weight":21749,"groups":[{"label":"jquery-pjax-plus\\jquery.pjax.plus.js","weight":9102},{"label":"headindex\\headindex.js","weight":5339},{"label":"jquery-lazyload\\lazyload.js","weight":3979},{"label":"zoomify\\zoomify.js","weight":3329}]},{"label":"argon-design-system\\js\\argon.js","weight":2636},{"label":"argon-theme.js","weight":157}]},{"label":"Code from unknown source files","weight":2259}]},{"label":"E:\\laragon\\www\\wp-content\\themes\\argon\\assets\\dist\\argon-theme.css","weight":458773,"groups":[{"label":"src","weight":366269,"groups":[{"label":"argon-design-system\\css\\argon.css","weight":236699},{"label":"style.css","weight":115372},{"label":"js\\css","weight":14198,"groups":[{"label":"pickr\\monolith.min.css","weight":9939},{"label":"comment-emotion.scss","weight":2566},{"label":"share.scss","weight":1652},{"label":"horizontal-scroll.scss","weight":41}]}]},{"label":"node_modules","weight":91416,"groups":[{"label":"izitoast\\dist\\css","weight":39008,"groups":[{"label":"iziToast.min.css","weight":37524},{"label":"iziToast.css","weight":1484}]},{"label":"fontawesome-4.7\\css\\font-awesome.css","weight":30864},{"label":"@fancyapps\\ui\\dist\\fancybox.css","weight":15168},{"label":"tippy.js","weight":6376,"groups":[{"label":"themes","weight":4967,"groups":[{"label":"light-border.css","weight":2812},{"label":"translucent.css","weight":845},{"label":"light.css","weight":691},{"label":"material.css","weight":619}]},{"label":"dist\\tippy.css","weight":1409}]}]},{"label":"Code from unknown source files","weight":948},{"label":"%3Cinput%20css%20NzT6NA%3E","weight":38}]},{"label":"E:\\laragon\\www\\wp-content\\themes\\argon\\assets\\dist\\fontawesome-webfont.3981e506.eot","weight":165742,"groups":[{"label":"node_modules\\fontawesome-4.7\\fonts\\fontawesome-webfont.eot","weight":165742}]},{"label":"E:\\laragon\\www\\wp-content\\themes\\argon\\assets\\dist\\fontawesome-webfont.3981e506.eot","weight":165742,"groups":[{"label":"node_modules\\fontawesome-4.7\\fonts\\fontawesome-webfont.eot","weight":165742}]},{"label":"E:\\laragon\\www\\wp-content\\themes\\argon\\assets\\dist\\fontawesome-webfont.58488e7e.woff2","weight":77160,"groups":[{"label":"node_modules\\fontawesome-4.7\\fonts\\fontawesome-webfont.woff2","weight":77160}]},{"label":"E:\\laragon\\www\\wp-content\\themes\\argon\\assets\\dist\\fontawesome-webfont.ed962b83.woff","weight":98024,"groups":[{"label":"node_modules\\fontawesome-4.7\\fonts\\fontawesome-webfont.woff","weight":98024}]},{"label":"E:\\laragon\\www\\wp-content\\themes\\argon\\assets\\dist\\fontawesome-webfont.0caf0c90.ttf","weight":165548,"groups":[{"label":"node_modules\\fontawesome-4.7\\fonts\\fontawesome-webfont.ttf","weight":165548}]},{"label":"E:\\laragon\\www\\wp-content\\themes\\argon\\assets\\dist\\fontawesome-webfont.a9323ae9.svg","weight":387787,"groups":[{"label":"node_modules\\fontawesome-4.7\\fonts\\fontawesome-webfont.svg","weight":444379}]}]}
              </script>
            </head>
            <body>
              <script>
                /* eslint-env browser */
/* global CarrotSearchFoamTree */

let bundleData = JSON.parse(document.getElementById('bundle-data').innerText);

let visualization = document.createElement('div');
visualization.style.height = '100vh';
visualization.style.width = '100vw';
document.body.appendChild(visualization);

let tooltip;

// Foam Tree docs:
// https://get.carrotsearch.com/foamtree/demo/api/index.html
// Some options from Parcel 1 Visualizer:
// https://github.com/gregtillbrook/parcel-plugin-bundle-visualiser/blob/ca5440fc61c85e40e7abc220ad99e274c7c104c6/src/buildReportAssets/init.js#L4
// and Webpack Bundle Analyzer:
// https://github.com/webpack-contrib/webpack-bundle-analyzer/blob/4a232f0cf7bbfed907a5c554879edd5d6f4b48ce/client/components/Treemap.jsx
let foamtree = new CarrotSearchFoamTree({
  element: visualization,
  dataObject: bundleData,
  layout: 'squarified',
  stacking: 'flattened',
  pixelRatio: window.devicePixelRatio || 1,
  maxGroups: Infinity,
  maxGroupLevelsDrawn: Infinity,
  maxGroupLabelLevelsDrawn: Infinity,
  maxGroupLevelsAttached: Infinity,
  rolloutDuration: 0,
  pullbackDuration: 0,
  maxLabelSizeForTitleBar: 0, // disable the title bar
  onGroupHover(e) {
    if (e.group.label == null || e.group.weight == null) {
      if (tooltip != null) {
        tooltip.remove();
        tooltip = null;
      }
      return;
    }

    if (tooltip == null) {
      tooltip = document.createElement('div');
      tooltip.classList.add('tooltip');
      document.body.appendChild(tooltip);
    }

    tooltip.style.transform = translate3d(e.xAbsolute, e.yAbsolute, 0);
    tooltip.innerHTML = `
      <div class="tooltip-content">
        <div>
          <span class="tooltip-title">${e.group.label}</span>
        </div>
        <dl>
          <div>
            <dt>Size</dt>
            <dd>${formatSize(e.group.weight)}</dd>
          </div>
        </dl>
      </div>
    `;
  },
  onGroupClick(e) {
    this.zoom(e.group);
  },
});

visualization.addEventListener('mousemove', e => {
  if (tooltip == null) {
    return;
  }

  tooltip.style.transform = translate3d(
    visualization.clientLeft + e.clientX,
    visualization.clientTop + e.clientY,
    0,
  );
});

window.addEventListener(
  'resize',
  debounce(() => {
    foamtree.resize();
  }, 100),
);

function debounce(fn, delay) {
  let timeout;

  return function(...args) {
    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      fn(...args);
    }, delay);
  };
}

function translate3d(x, y, z) {
  return `translate3d(${x}px, ${y}px, ${z}px)`;
}

function formatSize(x){
  const units = ['bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  let l = 0, n = parseInt(x, 10) || 0;
  while(n >= 1024 && ++l){
      n /= 1024;
  }
  return(`${n.toFixed(n < 10 && l > 0 ? 1 : 0)} ${units[l]}` + (l ? ` (${x} bytes)` : ''));
}



              </script>
            </body>
          </html>
        