{"Version": 3, "Meta": {"Duration": 3.8, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 110, "TotalSegmentCount": 718, "TotalPointCount": 2138, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.133, 0, 0.267, -30, 0.4, -30, 1, 0.978, -30, 1.556, -30, 2.133, -30, 1, 2.267, -30, 2.4, -12, 2.533, -12, 0, 3.8, -12]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -1, 1, 0.133, -1, 0.267, 14, 0.4, 14, 1, 0.978, 14, 1.556, 14, 2.133, 14, 1, 2.267, 14, 2.4, -14, 2.533, -14, 0, 3.8, -14]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.133, 0, 0.267, -10, 0.4, -10, 1, 0.978, -10, 1.556, -10, 2.133, -10, 1, 2.267, -10, 2.4, 6, 2.533, 6, 0, 3.8, 6]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.133, 0, 0.267, 6, 0.4, 6, 1, 0.978, 6, 1.556, 6, 2.133, 6, 0, 3.8, 6]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, 27.376, 0.2, 27.376, 1, 0.289, 27.376, 0.378, -29.959, 0.467, -29.959, 1, 0.544, -29.959, 0.622, 10.982, 0.7, 10.982, 1, 0.778, 10.982, 0.856, -4.318, 0.933, -4.318, 1, 1.011, -4.318, 1.089, 1.708, 1.167, 1.708, 1, 1.244, 1.708, 1.322, -0.674, 1.4, -0.674, 1, 1.478, -0.674, 1.556, 0.265, 1.633, 0.265, 1, 1.7, 0.265, 1.767, -0.105, 1.833, -0.105, 1, 1.911, -0.105, 1.989, 0.042, 2.067, 0.042, 1, 2.156, 0.042, 2.244, -27.389, 2.333, -27.389, 1, 2.422, -27.389, 2.511, 29.962, 2.6, 29.962, 1, 2.678, 29.962, 2.756, -10.983, 2.833, -10.983, 1, 2.911, -10.983, 2.989, 4.318, 3.067, 4.318, 1, 3.089, 4.318, 3.111, 2.763, 3.133, 2.763, 1, 3.144, 2.763, 3.156, 30, 3.167, 30, 1, 3.189, 30, 3.211, 30, 3.233, 30, 1, 3.244, 30, 3.256, -30, 3.267, -30, 1, 3.289, -30, 3.311, -30, 3.333, -30, 1, 3.356, -30, 3.378, 30, 3.4, 30, 1, 3.433, 30, 3.467, 30, 3.5, 30, 1, 3.567, 30, 3.633, -14.703, 3.7, -14.703, 1, 3.733, -14.703, 3.767, -9.588, 3.8, -4.473]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.133, 1, 0.267, 0, 0.4, 0, 1, 0.978, 0, 1.556, 0, 2.133, 0, 1, 2.267, 0, 2.4, 1, 2.533, 1, 1, 2.722, 1, 2.911, 1, 3.1, 1, 1, 3.133, 1, 3.167, 0, 3.2, 0, 1, 3.233, 0, 3.267, 1, 3.3, 1, 0, 3.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.133, 1, 0.267, 0, 0.4, 0, 1, 0.978, 0, 1.556, 0, 2.133, 0, 1, 2.267, 0, 2.4, 1, 2.533, 1, 1, 2.722, 1, 2.911, 1, 3.1, 1, 1, 3.133, 1, 3.167, 0, 3.2, 0, 1, 3.233, 0, 3.267, 1, 3.3, 1, 0, 3.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.133, 0, 0.267, -1, 0.4, -1, 1, 0.989, -1, 1.578, -0.3, 2.167, -0.3, 0, 3.8, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.133, 0, 0.267, -1, 0.4, -1, 1, 0.989, -1, 1.578, -0.3, 2.167, -0.3, 0, 3.8, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.2, 1, 0.133, -0.2, 0.267, -1, 0.4, -1, 1, 0.978, -1, 1.556, -1, 2.133, -1, 1, 2.267, -1, 2.4, -0.5, 2.533, -0.5, 0, 3.8, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.2, 1, 0.133, -0.2, 0.267, -1, 0.4, -1, 1, 0.978, -1, 1.556, -1, 2.133, -1, 1, 2.267, -1, 2.4, -0.5, 2.533, -0.5, 0, 3.8, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.8, 1, 0.256, -0.8, 0.511, -0.8, 0.767, -0.8, 1, 0.844, -0.8, 0.922, -0.2, 1, -0.2, 1, 1.078, -0.2, 1.156, -0.225, 1.233, -0.3, 1, 1.244, -0.311, 1.256, -0.4, 1.267, -0.4, 1, 1.322, -0.4, 1.378, -0.3, 1.433, -0.3, 1, 1.489, -0.3, 1.544, -0.4, 1.6, -0.4, 1, 1.622, -0.4, 1.644, -0.4, 1.667, -0.4, 1, 1.722, -0.4, 1.778, -0.4, 1.833, -0.4, 1, 1.844, -0.4, 1.856, -0.4, 1.867, -0.4, 1, 1.933, -0.4, 2, -0.4, 2.067, -0.4, 1, 2.089, -0.4, 2.111, -0.5, 2.133, -0.5, 1, 2.211, -0.5, 2.289, -0.2, 2.367, -0.2, 1, 2.433, -0.2, 2.5, -0.5, 2.567, -0.5, 1, 2.622, -0.5, 2.678, -0.4, 2.733, -0.4, 1, 2.789, -0.4, 2.844, -0.7, 2.9, -0.7, 0, 3.8, -0.7]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 0.844, 0, 0.922, 0.6, 1, 0.6, 1, 1.078, 0.6, 1.156, 0, 1.233, 0, 1, 1.244, 0, 1.256, 0.284, 1.267, 0.3, 1, 1.322, 0.379, 1.378, 0.4, 1.433, 0.4, 1, 1.489, 0.4, 1.544, 0.4, 1.6, 0.4, 1, 1.622, 0.4, 1.644, 0.7, 1.667, 0.7, 1, 1.722, 0.7, 1.778, 0.3, 1.833, 0.3, 1, 1.844, 0.3, 1.856, 0.7, 1.867, 0.7, 1, 1.933, 0.7, 2, 0.3, 2.067, 0.3, 1, 2.089, 0.3, 2.111, 0.7, 2.133, 0.7, 1, 2.211, 0.7, 2.289, 0.65, 2.367, 0.6, 1, 2.433, 0.557, 2.5, 0.557, 2.567, 0.5, 1, 2.622, 0.452, 2.678, 0, 2.733, 0, 1, 2.789, 0, 2.844, 0, 2.9, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0.094, 1, 0.089, 0.575, 0.178, 2.74, 0.267, 2.74, 1, 0.367, 2.74, 0.467, -1.584, 0.567, -1.584, 1, 0.667, -1.584, 0.767, 0.413, 0.867, 0.413, 1, 0.978, 0.413, 1.089, -0.11, 1.2, -0.11, 1, 1.311, -0.11, 1.422, 0.029, 1.533, 0.029, 1, 1.633, 0.029, 1.733, -0.008, 1.833, -0.008, 1, 1.944, -0.008, 2.056, 0.002, 2.167, 0.002, 1, 2.244, 0.002, 2.322, -1.263, 2.4, -1.263, 1, 2.5, -1.263, 2.6, 0.732, 2.7, 0.732, 1, 2.8, 0.732, 2.9, -0.191, 3, -0.191, 1, 3.111, -0.191, 3.222, 0.051, 3.333, 0.051, 1, 3.444, 0.051, 3.556, -0.013, 3.667, -0.013, 1, 3.711, -0.013, 3.756, -0.01, 3.8, -0.006]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.094, 1, 0.089, 0.575, 0.178, 2.74, 0.267, 2.74, 1, 0.367, 2.74, 0.467, -1.584, 0.567, -1.584, 1, 0.667, -1.584, 0.767, 0.413, 0.867, 0.413, 1, 0.978, 0.413, 1.089, -0.11, 1.2, -0.11, 1, 1.311, -0.11, 1.422, 0.029, 1.533, 0.029, 1, 1.633, 0.029, 1.733, -0.008, 1.833, -0.008, 1, 1.944, -0.008, 2.056, 0.002, 2.167, 0.002, 1, 2.244, 0.002, 2.322, -1.263, 2.4, -1.263, 1, 2.5, -1.263, 2.6, 0.732, 2.7, 0.732, 1, 2.8, 0.732, 2.9, -0.191, 3, -0.191, 1, 3.111, -0.191, 3.222, 0.051, 3.333, 0.051, 1, 3.444, 0.051, 3.556, -0.013, 3.667, -0.013, 1, 3.711, -0.013, 3.756, -0.01, 3.8, -0.006]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.1, 0, 0.167, 10.717, 0.233, 10.717, 1, 0.322, 10.717, 0.411, -12.156, 0.5, -12.156, 1, 0.6, -12.156, 0.7, 5.242, 0.8, 5.242, 1, 0.889, 5.242, 0.978, -2.271, 1.067, -2.271, 1, 1.167, -2.271, 1.267, 1.021, 1.367, 1.021, 1, 1.467, 1.021, 1.567, -0.445, 1.667, -0.445, 1, 1.756, -0.445, 1.844, 0.2, 1.933, 0.2, 1, 2.078, 0.2, 2.222, -16.63, 2.367, -16.63, 1, 2.456, -16.63, 2.544, 18.155, 2.633, 18.155, 1, 2.733, 18.155, 2.833, -7.523, 2.933, -7.523, 1, 3.033, -7.523, 3.133, 3.249, 3.233, 3.249, 1, 3.322, 3.249, 3.411, -1.452, 3.5, -1.452, 1, 3.6, -1.452, 3.7, 0.638, 3.8, 0.638]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -1.877, 0.167, -1.877, 1, 0.244, -1.877, 0.322, 4.593, 0.4, 4.593, 1, 0.489, 4.593, 0.578, -6.12, 0.667, -6.12, 1, 0.756, -6.12, 0.844, 4.644, 0.933, 4.644, 1, 1.022, 4.644, 1.111, -2.944, 1.2, -2.944, 1, 1.3, -2.944, 1.4, 1.727, 1.5, 1.727, 1, 1.589, 1.727, 1.678, -0.967, 1.767, -0.967, 1, 1.867, -0.967, 1.967, 0.512, 2.067, 0.512, 1, 2.1, 0.512, 2.133, 0.2, 2.167, 0.2, 1, 2.211, 0.2, 2.256, 2.593, 2.3, 2.593, 1, 2.378, 2.593, 2.456, -6.95, 2.533, -6.95, 1, 2.622, -6.95, 2.711, 8.819, 2.8, 8.819, 1, 2.889, 8.819, 2.978, -6.283, 3.067, -6.283, 1, 3.156, -6.283, 3.244, 3.894, 3.333, 3.894, 1, 3.433, 3.894, 3.533, -2.329, 3.633, -2.329, 1, 3.689, -2.329, 3.744, -0.911, 3.8, 0.152]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -1.877, 0.167, -1.877, 1, 0.244, -1.877, 0.322, 4.593, 0.4, 4.593, 1, 0.489, 4.593, 0.578, -6.12, 0.667, -6.12, 1, 0.756, -6.12, 0.844, 4.644, 0.933, 4.644, 1, 1.022, 4.644, 1.111, -2.944, 1.2, -2.944, 1, 1.3, -2.944, 1.4, 1.727, 1.5, 1.727, 1, 1.589, 1.727, 1.678, -0.967, 1.767, -0.967, 1, 1.867, -0.967, 1.967, 0.512, 2.067, 0.512, 1, 2.1, 0.512, 2.133, 0.2, 2.167, 0.2, 1, 2.211, 0.2, 2.256, 2.593, 2.3, 2.593, 1, 2.378, 2.593, 2.456, -6.95, 2.533, -6.95, 1, 2.622, -6.95, 2.711, 8.819, 2.8, 8.819, 1, 2.889, 8.819, 2.978, -6.283, 3.067, -6.283, 1, 3.156, -6.283, 3.244, 3.894, 3.333, 3.894, 1, 3.433, 3.894, 3.533, -2.329, 3.633, -2.329, 1, 3.689, -2.329, 3.744, -0.911, 3.8, 0.152]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 5.651, 0.267, 5.651, 1, 0.356, 5.651, 0.444, -6.389, 0.533, -6.389, 1, 0.644, -6.389, 0.756, 2.823, 0.867, 2.823, 1, 0.967, 2.823, 1.067, -1.304, 1.167, -1.304, 1, 1.267, -1.304, 1.367, 0.593, 1.467, 0.593, 1, 1.578, 0.593, 1.689, -0.273, 1.8, -0.273, 1, 1.9, -0.273, 2, 0.126, 2.1, 0.126, 1, 2.2, 0.126, 2.3, -5.706, 2.4, -5.706, 1, 2.489, -5.706, 2.578, 6.404, 2.667, 6.404, 1, 2.778, 6.404, 2.889, -2.831, 3, -2.831, 1, 3.1, -2.831, 3.2, 1.282, 3.3, 1.282, 1, 3.4, 1.282, 3.5, -0.583, 3.6, -0.583, 1, 3.667, -0.583, 3.733, -0.276, 3.8, -0.031]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -2.529, 0.167, -2.529, 1, 0.256, -2.529, 0.344, 8.436, 0.433, 8.436, 1, 0.533, 8.436, 0.633, -10.645, 0.733, -10.645, 1, 0.833, -10.645, 0.933, 8.165, 1.033, 8.165, 1, 1.144, 8.165, 1.256, -5.236, 1.367, -5.236, 1, 1.467, -5.236, 1.567, 3.113, 1.667, 3.113, 1, 1.778, 3.113, 1.889, -1.759, 2, -1.759, 1, 2.1, -1.759, 2.2, 3.43, 2.3, 3.43, 1, 2.389, 3.43, 2.478, -8.71, 2.567, -8.71, 1, 2.667, -8.71, 2.767, 10.682, 2.867, 10.682, 1, 2.967, 10.682, 3.067, -8.12, 3.167, -8.12, 1, 3.278, -8.12, 3.389, 5.197, 3.5, 5.197, 1, 3.6, 5.197, 3.7, -3.076, 3.8, -3.076]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 7.849, 0.267, 7.849, 1, 0.356, 7.849, 0.444, -8.874, 0.533, -8.874, 1, 0.644, -8.874, 0.756, 3.921, 0.867, 3.921, 1, 0.967, 3.921, 1.067, -1.811, 1.167, -1.811, 1, 1.267, -1.811, 1.367, 0.824, 1.467, 0.824, 1, 1.578, 0.824, 1.689, -0.379, 1.8, -0.379, 1, 1.9, -0.379, 2, 0.174, 2.1, 0.174, 1, 2.2, 0.174, 2.3, -7.925, 2.4, -7.925, 1, 2.489, -7.925, 2.578, 8.895, 2.667, 8.895, 1, 2.778, 8.895, 2.889, -3.932, 3, -3.932, 1, 3.1, -3.932, 3.2, 1.781, 3.3, 1.781, 1, 3.4, 1.781, 3.5, -0.809, 3.6, -0.809, 1, 3.667, -0.809, 3.733, -0.384, 3.8, -0.043]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 7.849, 0.267, 7.849, 1, 0.356, 7.849, 0.444, -8.874, 0.533, -8.874, 1, 0.644, -8.874, 0.756, 3.921, 0.867, 3.921, 1, 0.967, 3.921, 1.067, -1.811, 1.167, -1.811, 1, 1.267, -1.811, 1.367, 0.824, 1.467, 0.824, 1, 1.578, 0.824, 1.689, -0.379, 1.8, -0.379, 1, 1.9, -0.379, 2, 0.174, 2.1, 0.174, 1, 2.2, 0.174, 2.3, -7.925, 2.4, -7.925, 1, 2.489, -7.925, 2.578, 8.895, 2.667, 8.895, 1, 2.778, 8.895, 2.889, -3.932, 3, -3.932, 1, 3.1, -3.932, 3.2, 1.781, 3.3, 1.781, 1, 3.4, 1.781, 3.5, -0.809, 3.6, -0.809, 1, 3.667, -0.809, 3.733, -0.384, 3.8, -0.043]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 7.849, 0.267, 7.849, 1, 0.356, 7.849, 0.444, -8.874, 0.533, -8.874, 1, 0.644, -8.874, 0.756, 3.921, 0.867, 3.921, 1, 0.967, 3.921, 1.067, -1.811, 1.167, -1.811, 1, 1.267, -1.811, 1.367, 0.824, 1.467, 0.824, 1, 1.578, 0.824, 1.689, -0.379, 1.8, -0.379, 1, 1.9, -0.379, 2, 0.174, 2.1, 0.174, 1, 2.2, 0.174, 2.3, -7.925, 2.4, -7.925, 1, 2.489, -7.925, 2.578, 8.895, 2.667, 8.895, 1, 2.778, 8.895, 2.889, -3.932, 3, -3.932, 1, 3.1, -3.932, 3.2, 1.781, 3.3, 1.781, 1, 3.4, 1.781, 3.5, -0.809, 3.6, -0.809, 1, 3.667, -0.809, 3.733, -0.384, 3.8, -0.043]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 5.651, 0.267, 5.651, 1, 0.356, 5.651, 0.444, -6.389, 0.533, -6.389, 1, 0.644, -6.389, 0.756, 2.823, 0.867, 2.823, 1, 0.967, 2.823, 1.067, -1.304, 1.167, -1.304, 1, 1.267, -1.304, 1.367, 0.593, 1.467, 0.593, 1, 1.578, 0.593, 1.689, -0.273, 1.8, -0.273, 1, 1.9, -0.273, 2, 0.126, 2.1, 0.126, 1, 2.2, 0.126, 2.3, -5.706, 2.4, -5.706, 1, 2.489, -5.706, 2.578, 6.404, 2.667, 6.404, 1, 2.778, 6.404, 2.889, -2.831, 3, -2.831, 1, 3.1, -2.831, 3.2, 1.282, 3.3, 1.282, 1, 3.4, 1.282, 3.5, -0.583, 3.6, -0.583, 1, 3.667, -0.583, 3.733, -0.276, 3.8, -0.031]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.078, 0, 0.122, -2.529, 0.167, -2.529, 1, 0.256, -2.529, 0.344, 8.436, 0.433, 8.436, 1, 0.533, 8.436, 0.633, -10.645, 0.733, -10.645, 1, 0.833, -10.645, 0.933, 8.165, 1.033, 8.165, 1, 1.144, 8.165, 1.256, -5.236, 1.367, -5.236, 1, 1.467, -5.236, 1.567, 3.113, 1.667, 3.113, 1, 1.778, 3.113, 1.889, -1.759, 2, -1.759, 1, 2.1, -1.759, 2.2, 3.43, 2.3, 3.43, 1, 2.389, 3.43, 2.478, -8.71, 2.567, -8.71, 1, 2.667, -8.71, 2.767, 10.682, 2.867, 10.682, 1, 2.967, 10.682, 3.067, -8.12, 3.167, -8.12, 1, 3.278, -8.12, 3.389, 5.197, 3.5, 5.197, 1, 3.6, 5.197, 3.7, -3.076, 3.8, -3.076]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.111, 0, 0.189, 7.849, 0.267, 7.849, 1, 0.356, 7.849, 0.444, -8.874, 0.533, -8.874, 1, 0.644, -8.874, 0.756, 3.921, 0.867, 3.921, 1, 0.967, 3.921, 1.067, -1.811, 1.167, -1.811, 1, 1.267, -1.811, 1.367, 0.824, 1.467, 0.824, 1, 1.578, 0.824, 1.689, -0.379, 1.8, -0.379, 1, 1.9, -0.379, 2, 0.174, 2.1, 0.174, 1, 2.2, 0.174, 2.3, -7.925, 2.4, -7.925, 1, 2.489, -7.925, 2.578, 8.895, 2.667, 8.895, 1, 2.778, 8.895, 2.889, -3.932, 3, -3.932, 1, 3.1, -3.932, 3.2, 1.781, 3.3, 1.781, 1, 3.4, 1.781, 3.5, -0.809, 3.6, -0.809, 1, 3.667, -0.809, 3.733, -0.384, 3.8, -0.043]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 13.548, 0.333, 13.548, 1, 0.489, 13.548, 0.644, -5.664, 0.8, -5.664, 1, 1, -5.664, 1.2, 1.652, 1.4, 1.652, 1, 1.589, 1.652, 1.778, -0.483, 1.967, -0.483, 1, 2.033, -0.483, 2.1, -0.278, 2.167, -0.278, 1, 2.267, -0.278, 2.367, -13.386, 2.467, -13.386, 1, 2.622, -13.386, 2.778, 5.664, 2.933, 5.664, 1, 3.133, 5.664, 3.333, -1.65, 3.533, -1.65, 1, 3.622, -1.65, 3.711, -1.177, 3.8, -0.677]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -9.287, 0.3, -9.287, 1, 0.422, -9.287, 0.544, 1.358, 0.667, 1.358, 1, 0.722, 1.358, 0.778, 0.788, 0.833, 0.788, 1, 0.922, 0.788, 1.011, 1.694, 1.1, 1.694, 1, 1.244, 1.694, 1.389, -1.123, 1.533, -1.123, 1, 1.678, -1.123, 1.822, 0.344, 1.967, 0.344, 1, 2.033, 0.344, 2.1, 0.13, 2.167, 0.13, 1, 2.256, 0.13, 2.344, 9.269, 2.433, 9.269, 1, 2.556, 9.269, 2.678, -1.432, 2.8, -1.432, 1, 2.856, -1.432, 2.911, -0.819, 2.967, -0.819, 1, 3.056, -0.819, 3.144, -1.659, 3.233, -1.659, 1, 3.378, -1.659, 3.522, 1.116, 3.667, 1.116, 1, 3.711, 1.116, 3.756, 0.977, 3.8, 0.786]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -1.923, 0.3, -1.923, 1, 0.4, -1.923, 0.5, 4.38, 0.6, 4.38, 1, 0.722, 4.38, 0.844, -5.382, 0.967, -5.382, 1, 1.089, -5.382, 1.211, 3.94, 1.333, 3.94, 1, 1.456, 3.94, 1.578, -2.152, 1.7, -2.152, 1, 1.811, -2.152, 1.922, 1.058, 2.033, 1.058, 1, 2.111, 1.058, 2.189, -0.019, 2.267, -0.019, 1, 2.333, -0.019, 2.4, 1.497, 2.467, 1.497, 1, 2.567, 1.497, 2.667, -4.082, 2.767, -4.082, 1, 2.878, -4.082, 2.989, 5.199, 3.1, 5.199, 1, 3.222, 5.199, 3.344, -3.862, 3.467, -3.862, 1, 3.578, -3.862, 3.689, 1.085, 3.8, 1.984]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.211, 0, 0.322, -2.057, 0.433, -2.057, 1, 0.533, -2.057, 0.633, 6.203, 0.733, 6.203, 1, 0.856, 6.203, 0.978, -9.173, 1.1, -9.173, 1, 1.222, -9.173, 1.344, 8.618, 1.467, 8.618, 1, 1.578, 8.618, 1.689, -6.311, 1.8, -6.311, 1, 1.922, -6.311, 2.044, 4.114, 2.167, 4.114, 1, 2.267, 4.114, 2.367, -0.948, 2.467, -0.948, 1, 2.511, -0.948, 2.556, -0.216, 2.6, -0.216, 1, 2.689, -0.216, 2.778, -4.578, 2.867, -4.578, 1, 2.989, -4.578, 3.111, 8.292, 3.233, 8.292, 1, 3.356, 8.292, 3.478, -8.203, 3.6, -8.203, 1, 3.667, -8.203, 3.733, -3.058, 3.8, 1.058]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 13.548, 0.333, 13.548, 1, 0.489, 13.548, 0.644, -5.664, 0.8, -5.664, 1, 1, -5.664, 1.2, 1.652, 1.4, 1.652, 1, 1.589, 1.652, 1.778, -0.483, 1.967, -0.483, 1, 2.033, -0.483, 2.1, -0.278, 2.167, -0.278, 1, 2.267, -0.278, 2.367, -13.386, 2.467, -13.386, 1, 2.622, -13.386, 2.778, 5.664, 2.933, 5.664, 1, 3.133, 5.664, 3.333, -1.65, 3.533, -1.65, 1, 3.622, -1.65, 3.711, -1.177, 3.8, -0.677]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -9.287, 0.3, -9.287, 1, 0.422, -9.287, 0.544, 1.358, 0.667, 1.358, 1, 0.722, 1.358, 0.778, 0.788, 0.833, 0.788, 1, 0.922, 0.788, 1.011, 1.694, 1.1, 1.694, 1, 1.244, 1.694, 1.389, -1.123, 1.533, -1.123, 1, 1.678, -1.123, 1.822, 0.344, 1.967, 0.344, 1, 2.033, 0.344, 2.1, 0.13, 2.167, 0.13, 1, 2.256, 0.13, 2.344, 9.269, 2.433, 9.269, 1, 2.556, 9.269, 2.678, -1.432, 2.8, -1.432, 1, 2.856, -1.432, 2.911, -0.819, 2.967, -0.819, 1, 3.056, -0.819, 3.144, -1.659, 3.233, -1.659, 1, 3.378, -1.659, 3.522, 1.116, 3.667, 1.116, 1, 3.711, 1.116, 3.756, 0.977, 3.8, 0.786]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -1.923, 0.3, -1.923, 1, 0.4, -1.923, 0.5, 4.38, 0.6, 4.38, 1, 0.722, 4.38, 0.844, -5.382, 0.967, -5.382, 1, 1.089, -5.382, 1.211, 3.94, 1.333, 3.94, 1, 1.456, 3.94, 1.578, -2.152, 1.7, -2.152, 1, 1.811, -2.152, 1.922, 1.058, 2.033, 1.058, 1, 2.111, 1.058, 2.189, -0.019, 2.267, -0.019, 1, 2.333, -0.019, 2.4, 1.497, 2.467, 1.497, 1, 2.567, 1.497, 2.667, -4.082, 2.767, -4.082, 1, 2.878, -4.082, 2.989, 5.199, 3.1, 5.199, 1, 3.222, 5.199, 3.344, -3.862, 3.467, -3.862, 1, 3.578, -3.862, 3.689, 1.085, 3.8, 1.984]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 13.548, 0.333, 13.548, 1, 0.489, 13.548, 0.644, -5.664, 0.8, -5.664, 1, 1, -5.664, 1.2, 1.652, 1.4, 1.652, 1, 1.589, 1.652, 1.778, -0.483, 1.967, -0.483, 1, 2.033, -0.483, 2.1, -0.278, 2.167, -0.278, 1, 2.267, -0.278, 2.367, -13.386, 2.467, -13.386, 1, 2.622, -13.386, 2.778, 5.664, 2.933, 5.664, 1, 3.133, 5.664, 3.333, -1.65, 3.533, -1.65, 1, 3.622, -1.65, 3.711, -1.177, 3.8, -0.677]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -9.287, 0.3, -9.287, 1, 0.422, -9.287, 0.544, 1.358, 0.667, 1.358, 1, 0.722, 1.358, 0.778, 0.788, 0.833, 0.788, 1, 0.922, 0.788, 1.011, 1.694, 1.1, 1.694, 1, 1.244, 1.694, 1.389, -1.123, 1.533, -1.123, 1, 1.678, -1.123, 1.822, 0.344, 1.967, 0.344, 1, 2.033, 0.344, 2.1, 0.13, 2.167, 0.13, 1, 2.256, 0.13, 2.344, 9.269, 2.433, 9.269, 1, 2.556, 9.269, 2.678, -1.432, 2.8, -1.432, 1, 2.856, -1.432, 2.911, -0.819, 2.967, -0.819, 1, 3.056, -0.819, 3.144, -1.659, 3.233, -1.659, 1, 3.378, -1.659, 3.522, 1.116, 3.667, 1.116, 1, 3.711, 1.116, 3.756, 0.977, 3.8, 0.786]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -1.923, 0.3, -1.923, 1, 0.4, -1.923, 0.5, 4.38, 0.6, 4.38, 1, 0.722, 4.38, 0.844, -5.382, 0.967, -5.382, 1, 1.089, -5.382, 1.211, 3.94, 1.333, 3.94, 1, 1.456, 3.94, 1.578, -2.152, 1.7, -2.152, 1, 1.811, -2.152, 1.922, 1.058, 2.033, 1.058, 1, 2.111, 1.058, 2.189, -0.019, 2.267, -0.019, 1, 2.333, -0.019, 2.4, 1.497, 2.467, 1.497, 1, 2.567, 1.497, 2.667, -4.082, 2.767, -4.082, 1, 2.878, -4.082, 2.989, 5.199, 3.1, 5.199, 1, 3.222, 5.199, 3.344, -3.862, 3.467, -3.862, 1, 3.578, -3.862, 3.689, 1.085, 3.8, 1.984]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.211, 0, 0.322, -2.057, 0.433, -2.057, 1, 0.533, -2.057, 0.633, 6.203, 0.733, 6.203, 1, 0.856, 6.203, 0.978, -9.173, 1.1, -9.173, 1, 1.222, -9.173, 1.344, 8.618, 1.467, 8.618, 1, 1.578, 8.618, 1.689, -6.311, 1.8, -6.311, 1, 1.922, -6.311, 2.044, 4.114, 2.167, 4.114, 1, 2.267, 4.114, 2.367, -0.948, 2.467, -0.948, 1, 2.511, -0.948, 2.556, -0.216, 2.6, -0.216, 1, 2.689, -0.216, 2.778, -4.578, 2.867, -4.578, 1, 2.989, -4.578, 3.111, 8.292, 3.233, 8.292, 1, 3.356, 8.292, 3.478, -8.203, 3.6, -8.203, 1, 3.667, -8.203, 3.733, -3.058, 3.8, 1.058]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 13.548, 0.333, 13.548, 1, 0.489, 13.548, 0.644, -5.664, 0.8, -5.664, 1, 1, -5.664, 1.2, 1.652, 1.4, 1.652, 1, 1.589, 1.652, 1.778, -0.483, 1.967, -0.483, 1, 2.033, -0.483, 2.1, -0.278, 2.167, -0.278, 1, 2.267, -0.278, 2.367, -13.386, 2.467, -13.386, 1, 2.622, -13.386, 2.778, 5.664, 2.933, 5.664, 1, 3.133, 5.664, 3.333, -1.65, 3.533, -1.65, 1, 3.622, -1.65, 3.711, -1.177, 3.8, -0.677]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -9.287, 0.3, -9.287, 1, 0.422, -9.287, 0.544, 1.358, 0.667, 1.358, 1, 0.722, 1.358, 0.778, 0.788, 0.833, 0.788, 1, 0.922, 0.788, 1.011, 1.694, 1.1, 1.694, 1, 1.244, 1.694, 1.389, -1.123, 1.533, -1.123, 1, 1.678, -1.123, 1.822, 0.344, 1.967, 0.344, 1, 2.033, 0.344, 2.1, 0.13, 2.167, 0.13, 1, 2.256, 0.13, 2.344, 9.269, 2.433, 9.269, 1, 2.556, 9.269, 2.678, -1.432, 2.8, -1.432, 1, 2.856, -1.432, 2.911, -0.819, 2.967, -0.819, 1, 3.056, -0.819, 3.144, -1.659, 3.233, -1.659, 1, 3.378, -1.659, 3.522, 1.116, 3.667, 1.116, 1, 3.711, 1.116, 3.756, 0.977, 3.8, 0.786]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -1.923, 0.3, -1.923, 1, 0.4, -1.923, 0.5, 4.38, 0.6, 4.38, 1, 0.722, 4.38, 0.844, -5.382, 0.967, -5.382, 1, 1.089, -5.382, 1.211, 3.94, 1.333, 3.94, 1, 1.456, 3.94, 1.578, -2.152, 1.7, -2.152, 1, 1.811, -2.152, 1.922, 1.058, 2.033, 1.058, 1, 2.111, 1.058, 2.189, -0.019, 2.267, -0.019, 1, 2.333, -0.019, 2.4, 1.497, 2.467, 1.497, 1, 2.567, 1.497, 2.667, -4.082, 2.767, -4.082, 1, 2.878, -4.082, 2.989, 5.199, 3.1, 5.199, 1, 3.222, 5.199, 3.344, -3.862, 3.467, -3.862, 1, 3.578, -3.862, 3.689, 1.085, 3.8, 1.984]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 13.548, 0.333, 13.548, 1, 0.489, 13.548, 0.644, -5.664, 0.8, -5.664, 1, 1, -5.664, 1.2, 1.652, 1.4, 1.652, 1, 1.589, 1.652, 1.778, -0.483, 1.967, -0.483, 1, 2.033, -0.483, 2.1, -0.278, 2.167, -0.278, 1, 2.267, -0.278, 2.367, -13.386, 2.467, -13.386, 1, 2.622, -13.386, 2.778, 5.664, 2.933, 5.664, 1, 3.133, 5.664, 3.333, -1.65, 3.533, -1.65, 1, 3.622, -1.65, 3.711, -1.177, 3.8, -0.677]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -9.287, 0.3, -9.287, 1, 0.422, -9.287, 0.544, 1.358, 0.667, 1.358, 1, 0.722, 1.358, 0.778, 0.788, 0.833, 0.788, 1, 0.922, 0.788, 1.011, 1.694, 1.1, 1.694, 1, 1.244, 1.694, 1.389, -1.123, 1.533, -1.123, 1, 1.678, -1.123, 1.822, 0.344, 1.967, 0.344, 1, 2.033, 0.344, 2.1, 0.13, 2.167, 0.13, 1, 2.256, 0.13, 2.344, 9.269, 2.433, 9.269, 1, 2.556, 9.269, 2.678, -1.432, 2.8, -1.432, 1, 2.856, -1.432, 2.911, -0.819, 2.967, -0.819, 1, 3.056, -0.819, 3.144, -1.659, 3.233, -1.659, 1, 3.378, -1.659, 3.522, 1.116, 3.667, 1.116, 1, 3.711, 1.116, 3.756, 0.977, 3.8, 0.786]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -1.923, 0.3, -1.923, 1, 0.4, -1.923, 0.5, 4.38, 0.6, 4.38, 1, 0.722, 4.38, 0.844, -5.382, 0.967, -5.382, 1, 1.089, -5.382, 1.211, 3.94, 1.333, 3.94, 1, 1.456, 3.94, 1.578, -2.152, 1.7, -2.152, 1, 1.811, -2.152, 1.922, 1.058, 2.033, 1.058, 1, 2.111, 1.058, 2.189, -0.019, 2.267, -0.019, 1, 2.333, -0.019, 2.4, 1.497, 2.467, 1.497, 1, 2.567, 1.497, 2.667, -4.082, 2.767, -4.082, 1, 2.878, -4.082, 2.989, 5.199, 3.1, 5.199, 1, 3.222, 5.199, 3.344, -3.862, 3.467, -3.862, 1, 3.578, -3.862, 3.689, 1.085, 3.8, 1.984]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, 13.548, 0.333, 13.548, 1, 0.489, 13.548, 0.644, -5.664, 0.8, -5.664, 1, 1, -5.664, 1.2, 1.652, 1.4, 1.652, 1, 1.589, 1.652, 1.778, -0.483, 1.967, -0.483, 1, 2.033, -0.483, 2.1, -0.278, 2.167, -0.278, 1, 2.267, -0.278, 2.367, -13.386, 2.467, -13.386, 1, 2.622, -13.386, 2.778, 5.664, 2.933, 5.664, 1, 3.133, 5.664, 3.333, -1.65, 3.533, -1.65, 1, 3.622, -1.65, 3.711, -1.177, 3.8, -0.677]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, -9.287, 0.3, -9.287, 1, 0.422, -9.287, 0.544, 1.358, 0.667, 1.358, 1, 0.722, 1.358, 0.778, 0.788, 0.833, 0.788, 1, 0.922, 0.788, 1.011, 1.694, 1.1, 1.694, 1, 1.244, 1.694, 1.389, -1.123, 1.533, -1.123, 1, 1.678, -1.123, 1.822, 0.344, 1.967, 0.344, 1, 2.033, 0.344, 2.1, 0.13, 2.167, 0.13, 1, 2.256, 0.13, 2.344, 9.269, 2.433, 9.269, 1, 2.556, 9.269, 2.678, -1.432, 2.8, -1.432, 1, 2.856, -1.432, 2.911, -0.819, 2.967, -0.819, 1, 3.056, -0.819, 3.144, -1.659, 3.233, -1.659, 1, 3.378, -1.659, 3.522, 1.116, 3.667, 1.116, 1, 3.711, 1.116, 3.756, 0.977, 3.8, 0.786]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -1.923, 0.3, -1.923, 1, 0.4, -1.923, 0.5, 4.38, 0.6, 4.38, 1, 0.722, 4.38, 0.844, -5.382, 0.967, -5.382, 1, 1.089, -5.382, 1.211, 3.94, 1.333, 3.94, 1, 1.456, 3.94, 1.578, -2.152, 1.7, -2.152, 1, 1.811, -2.152, 1.922, 1.058, 2.033, 1.058, 1, 2.111, 1.058, 2.189, -0.019, 2.267, -0.019, 1, 2.333, -0.019, 2.4, 1.497, 2.467, 1.497, 1, 2.567, 1.497, 2.667, -4.082, 2.767, -4.082, 1, 2.878, -4.082, 2.989, 5.199, 3.1, 5.199, 1, 3.222, 5.199, 3.344, -3.862, 3.467, -3.862, 1, 3.578, -3.862, 3.689, 1.085, 3.8, 1.984]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0.099, 1, 0.089, 0.609, 0.178, 2.901, 0.267, 2.901, 1, 0.367, 2.901, 0.467, -1.677, 0.567, -1.677, 1, 0.667, -1.677, 0.767, 0.438, 0.867, 0.438, 1, 0.978, 0.438, 1.089, -0.116, 1.2, -0.116, 1, 1.311, -0.116, 1.422, 0.03, 1.533, 0.03, 1, 1.633, 0.03, 1.733, -0.008, 1.833, -0.008, 1, 1.944, -0.008, 2.056, 0.002, 2.167, 0.002, 1, 2.244, 0.002, 2.322, -1.337, 2.4, -1.337, 1, 2.5, -1.337, 2.6, 0.775, 2.7, 0.775, 1, 2.8, 0.775, 2.9, -0.203, 3, -0.203, 1, 3.111, -0.203, 3.222, 0.054, 3.333, 0.054, 1, 3.444, 0.054, 3.556, -0.014, 3.667, -0.014, 1, 3.711, -0.014, 3.756, -0.011, 3.8, -0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.488, 0.2, -1.488, 1, 0.289, -1.488, 0.378, 3.187, 0.467, 3.187, 1, 0.556, 3.187, 0.644, -2.065, 0.733, -2.065, 1, 0.833, -2.065, 0.933, 0.874, 1.033, 0.874, 1, 1.144, 0.874, 1.256, -0.313, 1.367, -0.313, 1, 1.467, -0.313, 1.567, 0.101, 1.667, 0.101, 1, 1.778, 0.101, 1.889, -0.031, 2, -0.031, 1, 2.111, -0.031, 2.222, 0.698, 2.333, 0.698, 1, 2.422, 0.698, 2.511, -1.475, 2.6, -1.475, 1, 2.689, -1.475, 2.778, 0.956, 2.867, 0.956, 1, 2.967, 0.956, 3.067, -0.405, 3.167, -0.405, 1, 3.278, -0.405, 3.389, 0.145, 3.5, 0.145, 1, 3.6, 0.145, 3.7, -0.047, 3.8, -0.047]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0.052, 1, 0.1, -0.18, 0.2, -1.34, 0.3, -1.34, 1, 0.389, -1.34, 0.478, 4.399, 0.567, 4.399, 1, 0.678, 4.399, 0.789, -3.389, 0.9, -3.389, 1, 1.011, -3.389, 1.122, 1.633, 1.233, 1.633, 1, 1.333, 1.633, 1.433, -0.644, 1.533, -0.644, 1, 1.644, -0.644, 1.756, 0.224, 1.867, 0.224, 1, 1.967, 0.224, 2.067, -0.067, 2.167, -0.067, 1, 2.256, -0.067, 2.344, 0.645, 2.433, 0.645, 1, 2.522, 0.645, 2.611, -2.036, 2.7, -2.036, 1, 2.811, -2.036, 2.922, 1.573, 3.033, 1.573, 1, 3.144, 1.573, 3.256, -0.758, 3.367, -0.758, 1, 3.467, -0.758, 3.567, 0.299, 3.667, 0.299, 1, 3.711, 0.299, 3.756, 0.234, 3.8, 0.157]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, -0.104, 1, 0.011, -0.104, 0.022, -0.104, 0.033, -0.104, 1, 0.156, -0.104, 0.278, -2.08, 0.4, -2.08, 1, 0.511, -2.08, 0.622, 4.574, 0.733, 4.574, 1, 0.833, 4.574, 0.933, -3.939, 1.033, -3.939, 1, 1.144, -3.939, 1.256, 2.2, 1.367, 2.2, 1, 1.478, 2.2, 1.589, -0.969, 1.7, -0.969, 1, 1.811, -0.969, 1.922, 0.367, 2.033, 0.367, 1, 2.111, 0.367, 2.189, -0.007, 2.267, -0.007, 1, 2.356, -0.007, 2.444, 0.977, 2.533, 0.977, 1, 2.644, 0.977, 2.756, -2.129, 2.867, -2.129, 1, 2.967, -2.129, 3.067, 1.826, 3.167, 1.826, 1, 3.278, 1.826, 3.389, -1.022, 3.5, -1.022, 1, 3.6, -1.022, 3.7, 0.17, 3.8, 0.408]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.256, 0, 0.378, -2.124, 0.5, -2.124, 1, 0.611, -2.124, 0.722, 5.085, 0.833, 5.085, 1, 0.944, 5.085, 1.056, -4.836, 1.167, -4.836, 1, 1.278, -4.836, 1.389, 2.988, 1.5, 2.988, 1, 1.622, 2.988, 1.744, -1.456, 1.867, -1.456, 1, 1.978, -1.456, 2.089, 0.606, 2.2, 0.606, 1, 2.267, 0.606, 2.333, 0.15, 2.4, 0.15, 1, 2.478, 0.15, 2.556, 0.882, 2.633, 0.882, 1, 2.744, 0.882, 2.856, -2.329, 2.967, -2.329, 1, 3.078, -2.329, 3.189, 2.238, 3.3, 2.238, 1, 3.411, 2.238, 3.522, -1.387, 3.633, -1.387, 1, 3.689, -1.387, 3.744, -0.961, 3.8, -0.496]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0.099, 1, 0.089, 0.609, 0.178, 2.901, 0.267, 2.901, 1, 0.367, 2.901, 0.467, -1.677, 0.567, -1.677, 1, 0.667, -1.677, 0.767, 0.438, 0.867, 0.438, 1, 0.978, 0.438, 1.089, -0.116, 1.2, -0.116, 1, 1.311, -0.116, 1.422, 0.03, 1.533, 0.03, 1, 1.633, 0.03, 1.733, -0.008, 1.833, -0.008, 1, 1.944, -0.008, 2.056, 0.002, 2.167, 0.002, 1, 2.244, 0.002, 2.322, -1.337, 2.4, -1.337, 1, 2.5, -1.337, 2.6, 0.775, 2.7, 0.775, 1, 2.8, 0.775, 2.9, -0.203, 3, -0.203, 1, 3.111, -0.203, 3.222, 0.054, 3.333, 0.054, 1, 3.444, 0.054, 3.556, -0.014, 3.667, -0.014, 1, 3.711, -0.014, 3.756, -0.011, 3.8, -0.007]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.488, 0.2, -1.488, 1, 0.289, -1.488, 0.378, 3.187, 0.467, 3.187, 1, 0.556, 3.187, 0.644, -2.065, 0.733, -2.065, 1, 0.833, -2.065, 0.933, 0.874, 1.033, 0.874, 1, 1.144, 0.874, 1.256, -0.313, 1.367, -0.313, 1, 1.467, -0.313, 1.567, 0.101, 1.667, 0.101, 1, 1.778, 0.101, 1.889, -0.031, 2, -0.031, 1, 2.111, -0.031, 2.222, 0.698, 2.333, 0.698, 1, 2.422, 0.698, 2.511, -1.475, 2.6, -1.475, 1, 2.689, -1.475, 2.778, 0.956, 2.867, 0.956, 1, 2.967, 0.956, 3.067, -0.405, 3.167, -0.405, 1, 3.278, -0.405, 3.389, 0.145, 3.5, 0.145, 1, 3.6, 0.145, 3.7, -0.047, 3.8, -0.047]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0.052, 1, 0.1, -0.18, 0.2, -1.34, 0.3, -1.34, 1, 0.389, -1.34, 0.478, 4.399, 0.567, 4.399, 1, 0.678, 4.399, 0.789, -3.389, 0.9, -3.389, 1, 1.011, -3.389, 1.122, 1.633, 1.233, 1.633, 1, 1.333, 1.633, 1.433, -0.644, 1.533, -0.644, 1, 1.644, -0.644, 1.756, 0.224, 1.867, 0.224, 1, 1.967, 0.224, 2.067, -0.067, 2.167, -0.067, 1, 2.256, -0.067, 2.344, 0.645, 2.433, 0.645, 1, 2.522, 0.645, 2.611, -2.036, 2.7, -2.036, 1, 2.811, -2.036, 2.922, 1.573, 3.033, 1.573, 1, 3.144, 1.573, 3.256, -0.758, 3.367, -0.758, 1, 3.467, -0.758, 3.567, 0.299, 3.667, 0.299, 1, 3.711, 0.299, 3.756, 0.234, 3.8, 0.157]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, -0.104, 1, 0.011, -0.104, 0.022, -0.104, 0.033, -0.104, 1, 0.156, -0.104, 0.278, -2.08, 0.4, -2.08, 1, 0.511, -2.08, 0.622, 4.574, 0.733, 4.574, 1, 0.833, 4.574, 0.933, -3.939, 1.033, -3.939, 1, 1.144, -3.939, 1.256, 2.2, 1.367, 2.2, 1, 1.478, 2.2, 1.589, -0.969, 1.7, -0.969, 1, 1.811, -0.969, 1.922, 0.367, 2.033, 0.367, 1, 2.111, 0.367, 2.189, -0.007, 2.267, -0.007, 1, 2.356, -0.007, 2.444, 0.977, 2.533, 0.977, 1, 2.644, 0.977, 2.756, -2.129, 2.867, -2.129, 1, 2.967, -2.129, 3.067, 1.826, 3.167, 1.826, 1, 3.278, 1.826, 3.389, -1.022, 3.5, -1.022, 1, 3.6, -1.022, 3.7, 0.17, 3.8, 0.408]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0, 0.133, 0, 1, 0.256, 0, 0.378, -2.124, 0.5, -2.124, 1, 0.611, -2.124, 0.722, 5.085, 0.833, 5.085, 1, 0.944, 5.085, 1.056, -4.836, 1.167, -4.836, 1, 1.278, -4.836, 1.389, 2.988, 1.5, 2.988, 1, 1.622, 2.988, 1.744, -1.456, 1.867, -1.456, 1, 1.978, -1.456, 2.089, 0.606, 2.2, 0.606, 1, 2.267, 0.606, 2.333, 0.15, 2.4, 0.15, 1, 2.478, 0.15, 2.556, 0.882, 2.633, 0.882, 1, 2.744, 0.882, 2.856, -2.329, 2.967, -2.329, 1, 3.078, -2.329, 3.189, 2.238, 3.3, 2.238, 1, 3.411, 2.238, 3.522, -1.387, 3.633, -1.387, 1, 3.689, -1.387, 3.744, -0.961, 3.8, -0.496]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 1.267, 0, 2.533, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, 0, 1, 1.267, 0, 2.533, 0, 3.8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, 0, 1, 1.267, 0, 2.533, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 0, 0, 3.8, 0]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 3.8, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 3.8, 1]}]}