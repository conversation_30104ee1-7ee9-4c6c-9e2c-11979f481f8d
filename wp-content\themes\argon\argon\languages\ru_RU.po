msgid ""
msgstr ""
"Project-Id-Version: argon\n"
"POT-Creation-Date: 2020-08-04 19:26+0300\n"
"PO-Revision-Date: 2020-08-04 23:53+0300\n"
"Last-Translator: \n"
"Language-Team: ostiwe\n"
"Language: ru_RU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.1.1\n"
"X-Poedit-Basepath: ..\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: style.css\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: archive.php:13
msgid "篇文章"
msgstr "Статей"

#: comments.php:12 functions.php:3055 header.php:489
#: template-parts/shuoshuo-operations.php:5
msgid "评论"
msgstr "Комментарии"

#: comments.php:43
msgid "暂无评论"
msgstr "Комментариев нет"

#: comments.php:51
msgid "本文评论已关闭"
msgstr "Комментарии отключены"

#: comments.php:68 functions.php:3068
msgid "发送评论"
msgstr "Отправить комментарий"

#: comments.php:69
msgid "编辑评论"
msgstr "Редактировать комментарий"

#: comments.php:72
msgid "正在回复"
msgstr "Ответить"

#: comments.php:72
msgid " 的评论"
msgstr "комментарий"

#: comments.php:74
msgid "取消回复"
msgstr "Отменить"

#: comments.php:79
msgid "评论内容"
msgstr "Ваш комментарий"

#: comments.php:118
msgid "昵称"
msgstr "Имя"

#: comments.php:128
msgid "邮箱"
msgstr "Почта"

#: comments.php:139
msgid "验证码"
msgstr "Капча"

#: comments.php:156
msgid "网站"
msgstr "Ссылка на сайт"

#: comments.php:163
msgid "展开附加字段"
msgstr "Показать дополнительные поля"

#: comments.php:163
msgid "折叠附加字段"
msgstr "Скрыть дополнительные поля"

#: comments.php:176
msgid "评论仅发送者和博主可见"
msgstr "Комментарий виден только отправителю и владельцу блога"

#: comments.php:178 functions.php:664
msgid "悄悄话"
msgstr "Личный комментарий"

#: comments.php:182
msgid "有回复时邮件通知我"
msgstr "Отправить уведомление на почту, когда ответят на комментарий"

#: comments.php:184
msgid "邮件提醒"
msgstr "Уведомление по почте"

#: comments.php:190
msgid "发送"
msgstr "Отправить"

#: comments.php:191 functions.php:691
msgid "编辑"
msgstr "Редактировать"

#: comments.php:195
msgid "取消"
msgstr "Отменить"

#: functions.php:3
msgid "Argon 主题不支持 Wordpress 4.4 以下版本，请更新 Wordpress"
msgstr ""
"Argon не поддерживает версию Wordpress ниже 4.4, пожалуйста, обновите "
"Wordpress"

#: functions.php:126
msgid "左侧栏小工具"
msgstr "Сайдбар слева"

#: functions.php:128
msgid "左侧栏小工具 (如果设置会在侧栏增加一个 Tab)"
msgstr "Виджет левого сайдбара"

#: functions.php:137
msgid "右侧栏小工具"
msgstr "Сайдбар справа"

#: functions.php:139
msgid "右侧栏小工具 (在 \"Argon 主题选项\" 中选择 \"三栏布局\" 才会显示)"
msgstr ""
"Сайдбар справа (отображается только при выборе \"трехколоночного макета\" в "
"разделе \" Параметры темы Argon\")"

#: functions.php:265
msgid "这是一个加密页面，需要密码来查看"
msgstr "Для просмотра данной страницы необходим пароль"

#: functions.php:369
msgid "几秒读完"
msgstr "Пару секунд"

#: functions.php:372
msgid "1 分钟内"
msgstr "Меньше минуты"

#: functions.php:375
msgid "分钟"
msgstr "минут"

#: functions.php:377
msgid "小时"
msgstr "час"

#: functions.php:399 template-parts/content-shuoshuo-details.php:16
#: template-parts/content-shuoshuo-preview.php:24
#: template-parts/content-shuoshuo.php:16
msgid "置顶"
msgstr "Закреплено"

#: functions.php:405
msgid "需要密码"
msgstr "Необходим пароль"

#: functions.php:411 functions.php:419
msgid "发布于"
msgstr "Опубликовано "

#: functions.php:411 functions.php:419
msgid "编辑于"
msgstr "Изменено "

#: functions.php:633
msgid "最初版本"
msgstr "Первое редактирование "

#: functions.php:661 functions.php:708
msgid "博主"
msgstr "Автор"

#: functions.php:667 functions.php:711
msgid "待审核"
msgstr "Ожидает модерации"

#: functions.php:680
msgid "已编辑"
msgstr "Изменено"

#: functions.php:684
msgid "前"
msgstr " назад"

#: functions.php:693
msgid "回复"
msgstr "Ответ"

#: functions.php:791
msgid "验证码错误"
msgstr "Неверно решенная капча"

#: functions.php:851
msgid "不能回复其他人的悄悄话评论"
msgstr "Нельзя отвечать на личные комментарии других пользователей"

#: functions.php:945
msgid "查看图片"
msgstr "Показать изображение"

#: functions.php:988 functions.php:989
msgid "您在"
msgstr "Ваш комментарий"

#: functions.php:988 functions.php:989
msgid "的评论有了新的回复"
msgstr "Есть новый ответ"

#: functions.php:997
msgid "回复了你"
msgstr "Ответов"

#: functions.php:1001
msgid "前往查看"
msgstr "Просмотров"

#: functions.php:1004
msgid "退订该评论的邮件提醒"
msgstr "Отписаться от уведомлений по электронной почте с напоминанием"

#: functions.php:1077
msgid "博主关闭了编辑评论功能"
msgstr "Редактирование комментариев отключено"

#: functions.php:1086
msgid "您不是这条评论的作者或 Token 已过期"
msgstr ""
"Вы не являетесь автором этого комментария или срок действия токена истек"

#: functions.php:1092
msgid "新的评论为空"
msgstr "Новый комментарий пуст"

#: functions.php:1118
msgid "编辑评论成功"
msgstr "Комментарий изменен"

#: functions.php:1126
msgid "编辑评论失败，可能原因: 与原评论相同"
msgstr ""
"Не удалось отредактировать комментарий, возможная причина: нет изменений"

#: functions.php:1343
msgid "该说说已被赞过"
msgstr "This essay has been voted"

#: functions.php:1352
msgid "点赞成功"
msgstr "Голос учтен"

#: functions.php:1361
msgid ""
"警告：你可能修改了 Argon 主题页脚的版权声明，Argon 主题要求你至少保留主题的 "
"Github 链接或主题的发布文章链接。"
msgstr ""
"Внимание: подвал сайта, возможно, был изменен. Пожалуйста, убедитесь что в "
"подвале сайта есть ссылка на GitHub репозиторий данной темы."

#: functions.php:1500 functions.php:2705
msgid "显示字数和预计阅读时间"
msgstr "Отображение количества слов и расчетного примерного времени для чтения"

#: functions.php:1503
msgid "跟随全局设置"
msgstr "Использовать глобальные настройки"

#: functions.php:1504 functions.php:2580 functions.php:2590 functions.php:2601
#: functions.php:2666 functions.php:2710 functions.php:2741 functions.php:2752
#: functions.php:3181 functions.php:3269
msgid "不显示"
msgstr "Скрыть"

#: functions.php:1506
msgid "是否显示字数和预计阅读时间 Meta 信息"
msgstr "Показать количества слов и расчетное время для чтения в мета-тегах"

#: functions.php:1507
msgid "Meta 中隐藏发布时间和分类"
msgstr "Скрыть время публикации и категории в мета-тегах"

#: functions.php:1510 functions.php:3074
msgid "不隐藏"
msgstr "Показать"

#: functions.php:1511 functions.php:3075
msgid "隐藏"
msgstr "Спрятать"

#: functions.php:1513
msgid ""
"适合特定的页面，例如友链页面。开启后文章 Meta 的第一行只显示阅读数和评论数。"
msgstr ""
"Если эта опция включена, то в первой строке мета будет отображаться только "
"количество прочтений и комментариев."

#: functions.php:1514
msgid "自定义 CSS"
msgstr "Свои стили CSS"

#: functions.php:1517
msgid "给该文章添加单独的 CSS"
msgstr "Свои стили CSS для этой страницы"

#: functions.php:1521
msgid "文章设置"
msgstr "Настройки заметок"

#: functions.php:2172 functions.php:2203
msgid "Argon 主题设置"
msgstr "Настройки темы Argon"

#: functions.php:2172
msgid "Argon 主题选项"
msgstr "Настойки Argon"

#: functions.php:2204
msgid "按下"
msgstr "Нажмите"

#: functions.php:2204
msgid "或在右侧目录中来查找设置"
msgstr "или найдите необходимую категорию из списка справа"

#: functions.php:2210
msgid "全局"
msgstr "Основные"

#: functions.php:2211 header.php:536
msgid "主题色"
msgstr "Цвет темы"

#: functions.php:2213
msgid "主题颜色"
msgstr "Цвет темы"

#: functions.php:2217
msgid "选择预置颜色 或"
msgstr "Выберите предустановленные цвета или"

#: functions.php:2217
msgid "自定义色值"
msgstr "свой цвет"

#: functions.php:2218
msgid "预置颜色："
msgstr "Пресеты"

#: functions.php:2220
msgid "默认"
msgstr "По умолчанию"

#: functions.php:2221
msgid "粉"
msgstr "Pink"

#: functions.php:2222
msgid "水鸭青"
msgstr "Teal"

#: functions.php:2223
msgid "蓝灰"
msgstr "Bluegrey"

#: functions.php:2224
msgid "天蓝"
msgstr "Blue"

#: functions.php:2225
msgid "靛蓝"
msgstr "Indigo"

#: functions.php:2226
msgid "橙"
msgstr "Orange"

#: functions.php:2227
msgid "绿"
msgstr "Green"

#: functions.php:2228
msgid "红"
msgstr "Red"

#: functions.php:2229
msgid "紫"
msgstr "Purple"

#: functions.php:2230
msgid "黑"
msgstr "Black"

#: functions.php:2231
msgid "棕"
msgstr "Brown"

#: functions.php:2233
msgid "主题色与 \"Banner 渐变背景样式\" 选项搭配使用效果更佳"
msgstr ""
"Используйте эту опцию с \"градиентным фоновым стилем баннера\" для улучшения "
"эффектов"

#: functions.php:2262
msgid "允许用户自定义主题色（位于博客浮动操作栏设置菜单中）"
msgstr "Разрешить пользовательский цвет темы (в плавающем меню настроек блога)"

#: functions.php:2268 header.php:492 header.php:501
msgid "夜间模式"
msgstr "Темная тема"

#: functions.php:2270
msgid "夜间模式切换方案"
msgstr "Схема переключения темной темы"

#: functions.php:2274
msgid "默认使用日间模式"
msgstr "Светлая тема по умолчанию"

#: functions.php:2275
msgid "默认使用夜间模式"
msgstr "Темная тема по умолчанию"

#: functions.php:2276
msgid "跟随系统夜间模式"
msgstr "Настройки системы пользователя"

#: functions.php:2277
msgid "根据时间切换夜间模式 (22:00 ~ 7:00)"
msgstr "Переключение по времени (с 10 вечера до 7 утра)"

#: functions.php:2279
msgid "Argon 主题会根据这里的选项来决定是否默认使用夜间模式。"
msgstr "Автоматический включать темную тему исходя из этих настроек"

#: functions.php:2279
msgid "用户也可以手动切换夜间模式，用户的设置将保留到标签页关闭为止。"
msgstr ""
"Пользователи так же могут самостоятельно включать\\выключать темную тему. Их "
"собственные настройки темы будут сохранены до тех пор, пока они не закроют "
"вкладку."

#: functions.php:2283
msgid "夜间模式颜色方案"
msgstr "Цветая схема темной темы"

#: functions.php:2287
msgid "灰黑"
msgstr "Dark Grey"

#: functions.php:2288
msgid "暗黑 (AMOLED Black)"
msgstr "Black (For AMOLED)"

#: functions.php:2290
msgid "夜间模式默认的配色方案。"
msgstr "Цветовая схема для темной темы по умолчанию"

#: functions.php:2293
msgid "卡片"
msgstr "Карточки"

#: functions.php:2295
msgid "卡片圆角大小"
msgstr "Радиус закругления"

#: functions.php:2298
msgid "卡片的圆角大小，默认为"
msgstr "По умолчанию радиус закругления для карточки "

#: functions.php:2298
msgid "。建议设置为"
msgstr ". Рекомендуется в пределах "

#: functions.php:2302
msgid "卡片阴影"
msgstr "Тень карточек"

#: functions.php:2308 header.php:516
msgid "浅阴影"
msgstr "Меленькая"

#: functions.php:2312 header.php:516
msgid "深阴影"
msgstr "Большая"

#: functions.php:2315
msgid "卡片默认阴影大小。"
msgstr "Размер теней для карточек по умолчанию"

#: functions.php:2318
msgid "布局"
msgstr "Макет"

#: functions.php:2320
msgid "页面布局"
msgstr "Макет страницы"

#: functions.php:2327
msgid "双栏"
msgstr "2 Колонки"

#: functions.php:2333
msgid "单栏"
msgstr "1 Колонка"

#: functions.php:2339
msgid "三栏"
msgstr "3 Колонки"

#: functions.php:2341
msgid "使用单栏时，关于左侧栏的设置将失效。"
msgstr ""
"Если использовать макет с одной колонкой, то настройки для сайдбара будут "
"игнорироваться"

#: functions.php:2341
msgid "使用三栏时，请前往 \"外观-小工具\" 设置页面配置右侧栏内容。"
msgstr ""
"Если используется макет с тремя колонками - перейдите на страницу настроек "
"\"внешний вид - виджеты\" для настройки содержимого."

#: functions.php:2344 header.php:508
msgid "字体"
msgstr "Шрифт"

#: functions.php:2346
msgid "默认字体"
msgstr "Шрифт по умолчанию"

#: functions.php:2359
msgid "默认使用无衬线字体/衬线字体。"
msgstr "Стиль шрифта по умолчанию"

#: functions.php:2368 functions.php:2942 functions.php:3280
msgid "不使用"
msgstr "Не использовать"

#: functions.php:2372
msgid ""
"选择主题资源文件的引用地址。使用 CDN 可以加速资源文件的访问并减少服务器压力。"
msgstr ""
"Адрес файла ресурсов темы. Использование CDN может ускорить доступ к файлам "
"ресурсов и снизить нагрузку на сервер"

#: functions.php:2375
msgid "子目录"
msgstr "Каталоги"

#: functions.php:2377
msgid "Wordpress 安装目录"
msgstr "Директория, где установлен WordPress"

#: functions.php:2380
msgid "如果 Wordpress 安装在子目录中，请在此填写子目录地址（例如"
msgstr ""
"Если Wordpress установлен не в корневой директории то укажите путь до нее, к "
"примеру ("

#: functions.php:2380
msgid "），注意前后各有一个斜杠。默认为"
msgstr "), до и после каждого из них есть косая черта (/). По умолчанию: "

#: functions.php:2380
msgid "。"
msgstr "."

#: functions.php:2380
msgid "如果不清楚该选项的用处，请保持默认。"
msgstr "Если вы не знаете об этой опции, оставьте значение по умолчанию"

#: functions.php:2383 functions.php:2385
msgid "日期格式"
msgstr "Формат даты"

#: functions.php:2396
msgid "顶栏"
msgstr "Шапка сайта"

#: functions.php:2397
msgid "标题"
msgstr "Название"

#: functions.php:2399
msgid "顶栏标题"
msgstr "Название сайта"

#: functions.php:2402 functions.php:2425 functions.php:2540
msgid "留空则显示博客名称"
msgstr "Если этот параметр пуст, тема покажет название блога"

#: functions.php:2405
msgid "顶栏图标"
msgstr "Логотип"

#: functions.php:2407
msgid "图标地址"
msgstr "Ссылка на логотип"

#: functions.php:2410
msgid "图片地址，留空则不显示"
msgstr "URL адрес логотипа, если этот параметр пуст, он не будет отображаться"

#: functions.php:2414
msgid "图标链接"
msgstr "Ссылка логотипа"

#: functions.php:2417
msgid "点击图标后会跳转到的链接，留空则不跳转"
msgstr ""
"Ссылка для логотипа. Если не указано то при нажатии на логотип никаких "
"действий не будет"

#: functions.php:2420
msgid "顶部 Banner (封面)"
msgstr "Баннер"

#: functions.php:2422
msgid "Banner 标题"
msgstr "Название баннера"

#: functions.php:2429
msgid "Banner 副标题"
msgstr "Описание баннера"

#: functions.php:2432
msgid "显示在 Banner 标题下，留空则不显示"
msgstr ""
"Показывать под заголовком баннера, если этот параметр пуст, то он не будет "
"отображаться"

#: functions.php:2436
msgid "Banner 背景图 (地址)"
msgstr "Ссылка на фоновое изображение баннера"

#: functions.php:2439
msgid "需带上 http(s) ，留空则显示默认背景"
msgstr ""
"Ссылка должна быть http(s). Оставьте его пустым, чтобы показать фон по "
"умолчанию"

#: functions.php:2439 functions.php:2547
msgid "输入"
msgstr "Ввод"

#: functions.php:2439
msgid "调用必应每日一图"
msgstr "чтобы использовать фон Bing."

#: functions.php:2443
msgid "Banner 渐变背景样式"
msgstr "Стиль градиента для баннера"

#: functions.php:2447 functions.php:2448 functions.php:2449 functions.php:2450
#: functions.php:2451 functions.php:2452 functions.php:2453 functions.php:2462
#: functions.php:2463 functions.php:2464 functions.php:2465 functions.php:2466
#: functions.php:2467 functions.php:2468
msgid "样式"
msgstr "Стиль"

#: functions.php:2457
msgid "隐藏背景半透明圆"
msgstr "Скрыть фон полупрозрачным кругом"

#: functions.php:2459
msgid "如果设置了背景图则不生效"
msgstr "Если фоновое изображение установлено, оно не вступит в силу"

#: functions.php:2460
msgid "样式预览 (推荐选择前三个样式)"
msgstr ""
"Предварительный просмотр стиля (рекомендуется выбрать первые три стиля)"

#: functions.php:2477 functions.php:3029
msgid "动画"
msgstr "Анимации"

#: functions.php:2479
msgid "Banner 标题打字动画"
msgstr "Анимация названия баннера в стиле \"печатная машинка\""

#: functions.php:2483 functions.php:2804 functions.php:2847 functions.php:3049
#: functions.php:3209 functions.php:3258
msgid "不启用"
msgstr "Выключено"

#: functions.php:2484 functions.php:2805 functions.php:2901 functions.php:2953
#: functions.php:3050 functions.php:3208 functions.php:3259
msgid "启用"
msgstr "Включено"

#: functions.php:2486
msgid "启用后 Banner 标题会以打字的形式出现。"
msgstr ""
"Если этот параметр включен, заголовок баннера будет отображаться с эффектом "
"набора текста."

#: functions.php:2490
msgid "Banner 标题打字动画时长"
msgstr "Скорость анимации названия баннера"

#: functions.php:2492
msgid "ms/字"
msgstr "мс/буква"

#: functions.php:2496 functions.php:2498
msgid "页面背景"
msgstr "Фон сайта"

#: functions.php:2501
msgid ""
"页面背景的地址，需带上 http(s)。留空则不设置页面背景。如果设置了背景，推荐修"
"改以下选项来增强页面整体观感。"
msgstr ""
"URL-адрес фона страницы http(s). Оставьте его пустым, чтобы не устанавливать "
"фон страницы. Если фон установлен, рекомендуется изменить следующие "
"параметры, чтобы улучшить общий внешний вид страницы."

#: functions.php:2505
msgid "页面背景（夜间模式时）"
msgstr "Фон сайта (Темная тема)"

#: functions.php:2508
msgid ""
"夜间模式时页面背景的地址，需带上 http(s)。设置后日间模式和夜间模式会使用不同"
"的背景。留空则跟随日间模式背景。该选项仅在设置了日间模式背景时生效。"
msgstr ""
"URL-адрес фона страницы для темного режима http(s). Если он установлен, "
"дневной и ночной режимы будут использовать разные фоны. Оставьте пустым, "
"чтобы использовать фон дневного режима. Эта опция вступает в силу только "
"тогда, когда установлен фон дневного режима."

#: functions.php:2512
msgid "背景不透明度"
msgstr "Прозрачность фона"

#: functions.php:2515
msgid "0 ~ 1 的小数，越小透明度越高，默认为 1 不透明"
msgstr ""
"Десятичная дробь от 0 до 1, Чем меньше, тем выше прозрачность, по умолчанию "
"используется 1."

#: functions.php:2519
msgid "Banner 透明化"
msgstr "Сделать баннер прозрачным"

#: functions.php:2523 functions.php:3291 header.php:522
msgid "关闭"
msgstr "Выключить"

#: functions.php:2524 functions.php:3292
msgid "开启"
msgstr "Включить"

#: functions.php:2529
msgid "在顶栏添加浅色遮罩，Banner 标题添加阴影（当背景过亮影响文字阅读时勾选）"
msgstr ""
"Немного затемнить шапку и добавить тень к заголовку баннера (проверьте, "
"когда фон слишком яркий и влияет на чтение текста)"

#: functions.php:2532
msgid ""
"Banner 透明化可以使博客背景沉浸。建议在设置背景时开启此选项。该选项仅会在设置"
"页面背景时生效。"
msgstr ""
"Рекомендуется включить эту опцию при настройке фона. Этот параметр вступает "
"в силу только в том случае, если задан фон страницы."

#: functions.php:2535
msgid "左侧栏"
msgstr "Сайдбар слева"

#: functions.php:2537
msgid "左侧栏标题"
msgstr "Заголовок сайдбара"

#: functions.php:2544
msgid "左侧栏子标题（格言）"
msgstr "Описание сайдара"

#: functions.php:2547
msgid "留空则不显示"
msgstr "Оставьте пустым, чтобы не показывать."

#: functions.php:2547
msgid "调用一言 API"
msgstr "для использования Hitokoto API."

#: functions.php:2551
msgid "左侧栏作者名称"
msgstr "Имя владельца блога"

#: functions.php:2554
msgid "留空则显示博客名"
msgstr "Если этот параметр пуст, тема покажет название блога."

#: functions.php:2558
msgid "左侧栏作者头像地址"
msgstr "Ссылка на аватар владельца блога"

#: functions.php:2561
msgid "需带上 http(s) 开头"
msgstr "Ссылка должна быть http(s)"

#: functions.php:2564
msgid "博客公告"
msgstr "Объявление"

#: functions.php:2566
msgid "公告内容"
msgstr "Текст объявления"

#: functions.php:2569
msgid "显示在左侧栏顶部，留空则不显示，支持 HTML 标签"
msgstr ""
"Объявление в блоге (отображается в верхней части левого сайдбара). "
"Поддерживаются HTML-теги."

#: functions.php:2572
msgid "浮动操作按钮"
msgstr "Плавующие кнопки"

#: functions.php:2573
msgid "浮动操作按钮位于页面右下角（或左下角）"
msgstr ""
"Плавающие кнопки действий размещаются в правом нижнем углу (или нижнем левом "
"углу) страницы."

#: functions.php:2575
msgid "显示设置按钮"
msgstr "Показать кнопку настроек"

#: functions.php:2579 functions.php:2591 functions.php:2602 functions.php:2662
#: functions.php:2709 functions.php:2740 functions.php:2753 functions.php:3270
msgid "显示"
msgstr "Показать"

#: functions.php:2582
msgid ""
"是否在浮动操作按钮栏中显示设置按钮。点击设置按钮可以唤出设置菜单修改夜间模式/"
"字体/滤镜等外观选项。"
msgstr ""
"Следует ли отображать кнопку Настройки на панели плавающих кнопок действий. "
"Нажмите кнопку Настройки, чтобы вызвать меню настроек для изменения "
"параметров внешнего вида, таких как darkmode/font/filter."

#: functions.php:2586
msgid "显示夜间模式切换按钮"
msgstr "Отключить кнопку переключения темной темы"

#: functions.php:2593
msgid ""
"如果开启了设置按钮显示，建议关闭此选项。（夜间模式选项在设置菜单中已经存在）"
msgstr ""
"Отдельная кнопка переключения темы, рекомендуется отключить эту опцию. "
"(Опция переключения темы есть в меню настроек)"

#: functions.php:2597
msgid "显示跳转到评论按钮"
msgstr "Показать кнопку для перехода к комментариям"

#: functions.php:2604
msgid "仅在允许评论的文章中显示"
msgstr "Будет отображаться только на страницах, где включены комментарии"

#: functions.php:2609
msgid "网站描述 (Description Meta 标签)"
msgstr "Описание сайта"

#: functions.php:2612
msgid "设置针对搜索引擎的 Description Meta 标签内容。"
msgstr "Установите описание мета-контента для поисковиков (Yandex, Google)"

#: functions.php:2612
msgid ""
"在文章中，Argon 会自动根据文章内容生成描述。在其他页面中，Argon 将使用这里设"
"置的内容。如不填，Argon 将不会在其他页面输出 Description Meta 标签。"
msgstr ""
"В статьях Argon автоматический сгенерирует мета-теги на основе содержания "
"статьи. На остальных страницах Argon будет использовать мета-теги описанные "
"здесь. Если оставить данное поле пустым, Argon не будет выводить мета-теги "
"на других страницах."

#: functions.php:2616
msgid "搜索引擎关键词（Keywords Meta 标签）"
msgstr "Ключевые слова"

#: functions.php:2619
msgid ""
"设置针对搜索引擎使用的关键词（Keywords Meta 标签内容）。用英文逗号隔开。不设"
"置则不输出该 Meta 标签。"
msgstr ""
"Ключевые слова для поисковых систем (Yandex, Google). Разделите их при "
"помощи запятых."

#: functions.php:2622 sidebar.php:130
msgid "文章"
msgstr "Заметки"

#: functions.php:2623
msgid "文章 Meta 信息"
msgstr "Мета информация"

#: functions.php:2625
msgid "第一行"
msgstr "Первая строка"

#: functions.php:2660
msgid "拖动来自定义文章 Meta 信息的显示和顺序"
msgstr ""
"Перетащите, чтобы настроить отображение и порядок мета-тегов для статьи."

#: functions.php:2668 functions.php:2774
msgid "发布时间"
msgstr "Время публикации"

#: functions.php:2669
msgid "修改时间"
msgstr "Время изменения"

#: functions.php:2670
msgid "浏览量"
msgstr "Просмотры"

#: functions.php:2671
msgid "评论数"
msgstr "Комментарии"

#: functions.php:2672
msgid "所属分类"
msgstr "Категории"

#: functions.php:2673
msgid "作者"
msgstr "Автор"

#: functions.php:2703
msgid "第二行"
msgstr "Вторая строка"

#: functions.php:2715
msgid "每分钟阅读字数"
msgstr "Количество слов в минуту"

#: functions.php:2718
msgid "字/分钟"
msgstr "слово/минута"

#: functions.php:2719
msgid "预计阅读时间由每分钟阅读字数计算"
msgstr ""
"Расчетное время чтения рассчитывается по количеству прочитанных слов в минуту"

#: functions.php:2722
msgid "文章头图 (特色图片)"
msgstr "Изображение статьи"

#: functions.php:2724
msgid "文章头图的位置"
msgstr "Расположение изображения на странице статьи"

#: functions.php:2728
msgid "文章卡片顶端"
msgstr "Вверху статьи"

#: functions.php:2729
msgid "Banner (顶部背景)"
msgstr "Фоновое изображение баннера"

#: functions.php:2731
msgid "阅读界面中文章头图的位置"
msgstr "Положение, где будет отображаться изображение статьи (Обложка)"

#: functions.php:2734 template-parts/share.php:50
msgid "分享"
msgstr "Поделиться"

#: functions.php:2736
msgid "显示文章分享按钮"
msgstr "Показать кнопку \"поделиться\""

#: functions.php:2746
msgid "左侧栏文章目录"
msgstr "Оглавление в левом сайдбаре"

#: functions.php:2748
msgid "在目录中显示序号"
msgstr "Показать номер главы"

#: functions.php:2755
msgid "例：3.2.5"
msgstr "Например: 3.2.5"

#: functions.php:2758 template-parts/content-single.php:100
msgid "赞赏"
msgstr "Пожертвования"

#: functions.php:2760
msgid "赞赏二维码图片链接"
msgstr "Ссылка на изображение QR кода для пожертвований"

#: functions.php:2763
msgid ""
"赞赏二维码图片链接，填写后会在文章最后显示赞赏按钮，留空则不显示赞赏按钮"
msgstr ""
"После заполнения, кнопка Пожертвовать будет отображаться в конце статьи, "
"оставьте ее пустой, чтобы не отображать кнопку \"Пожертвовать\""

#: functions.php:2766
msgid "其他"
msgstr "Другое"

#: functions.php:2768
msgid "文章过时信息显示"
msgstr "Устаревшая информация об статье"

#: functions.php:2770
msgid "当一篇文章的"
msgstr "Когда c "

#: functions.php:2773
msgid "最后修改时间"
msgstr "Последнего редактирования"

#: functions.php:2776
msgid "距离现在超过"
msgstr "статьи прошло больше "

#: functions.php:2778
msgid "天时，用"
msgstr "дней, показать уведомление "

#: functions.php:2781
msgid "在文章顶部显示信息条"
msgstr "Отображение информационной панели в верхней части статьи"

#: functions.php:2782
msgid "在页面右上角弹出提示条"
msgstr "Всплывающая панель предупреждений в правой верхней части страницы"

#: functions.php:2784
msgid "的方式提示"
msgstr "."

#: functions.php:2786
msgid ""
"本文最后更新于 %date_delta% 天前，其中的信息可能已经有所发展或是发生改变。"
msgstr ""
"В последний раз эта статья обновлялась %date_delta% несколько дней назад. "
"Возможно, информация в нем была изменена."

#: functions.php:2787
msgid "天数为 -1 表示永不提示。"
msgstr "Установите -1 чтобы никогда не показывать это."

#: functions.php:2787
msgid "表示文章发布/修改时间与当前时间的差距，"
msgstr ""
"означает разрыв между временем публикации / изменения статьи и текущим "
"временем,"

#: functions.php:2787
msgid "表示文章发布时间与当前时间的差距，"
msgstr "означает разрыв между временем публикации статьи и текущим временем,"

#: functions.php:2787
msgid "表示文章修改时间与当前时间的差距（单位: 天）。"
msgstr "означает разрыв между временем изменения статьи и текущим временем,"

#: functions.php:2790
msgid "页脚"
msgstr "Подвал"

#: functions.php:2792
msgid "页脚内容"
msgstr "Содержание подавла"

#: functions.php:2795 functions.php:3019 functions.php:3026
msgid "HTML , 支持 script 等标签"
msgstr "поддерживаются HTML, script и другие теги."

#: functions.php:2798
msgid "代码高亮"
msgstr "Выделение кода"

#: functions.php:2800
msgid "启用 Highlight.js 代码高亮"
msgstr "Включить выделение кода Highlight.js"

#: functions.php:2807
msgid "所有 pre 下的 code 标签会被自动解析"
msgstr "Все теги кода в теге pre будут автоматически проанализированы"

#: functions.php:2811
msgid "高亮配色方案（主题）"
msgstr "Цветовая схема выделения"

#: functions.php:2829
msgid "查看所有主题预览"
msgstr "Посмотреть все примеры"

#: functions.php:2836
msgid "数学公式"
msgstr "Рендер формул"

#: functions.php:2838
msgid "数学公式渲染方案"
msgstr "Библиотека для рендера формул"

#: functions.php:2857 functions.php:2870 functions.php:2883
msgid "地址"
msgstr "URL"

#: functions.php:2859 functions.php:2872
msgid "，默认为"
msgstr ", По умолчанию"

#: functions.php:2885
msgid "Argon 会同时引用"
msgstr "Argon будет подключать то и другое"

#: functions.php:2885
msgid "和"
msgstr "и"

#: functions.php:2885
msgid ""
"两个文件，所以在此填写的是上层的路径，而不是具体的文件。注意路径后要带一个斜"
"杠。"
msgstr ""
"two files, so input the parent directory path in here, not the specific "
"file. There is a slash after the path."

#: functions.php:2885
msgid "默认为"
msgstr "По умолчанию "

#: functions.php:2897
msgid "是否启用 Lazyload"
msgstr "Включить Lazyload"

#: functions.php:2902 functions.php:2954 functions.php:2988
msgid "禁用"
msgstr "Выключить"

#: functions.php:2904
msgid "是否启用 Lazyload 加载文章内图片"
msgstr "Lazyload будет загружать изображения по мере прокрутки страницы."

#: functions.php:2908
msgid "提前加载阈值"
msgstr "Порог ленивой загрузки"

#: functions.php:2911
msgid "图片距离页面底部还有多少距离就开始提前加载"
msgstr ""
"Расстояние от нижней части экрана, когда изображение начнет загружаться."

#: functions.php:2915
msgid "LazyLoad 图片加载完成过渡"
msgstr "Анимация после загрузки изображения"

#: functions.php:2921
msgid "不使用过渡"
msgstr "Никаких переходов"

#: functions.php:2927
msgid "LazyLoad 图片加载动效"
msgstr "Анимация ленивой загрузки"

#: functions.php:2931 functions.php:2932 functions.php:2933 functions.php:2934
#: functions.php:2935 functions.php:2936 functions.php:2937 functions.php:2938
#: functions.php:2939 functions.php:2940 functions.php:2941
msgid "加载动画"
msgstr "Анимация загрузки"

#: functions.php:2944
msgid "在图片被加载之前显示的加载效果"
msgstr "Анимация загрузки перед отображением изображения."

#: functions.php:2944
msgid "预览所有效果"
msgstr "Посмотреть все варианты"

#: functions.php:2947
msgid "图片放大浏览"
msgstr "Увеличение изображения"

#: functions.php:2949
msgid "是否启用图片放大浏览 (Zoomify)"
msgstr "Включить увеличение изображения"

#: functions.php:2956
msgid "开启后，文章中图片被单击时会放大预览"
msgstr ""
"Если этот параметр включен, изображение в статье будет увеличено для "
"просмотра при нажатии кнопки"

#: functions.php:2960
msgid "缩放动画长度"
msgstr "Время анимации"

#: functions.php:2963
msgid "图片被单击后缩放到全屏动画的时间长度"
msgstr "Время анимации увеличения изображения"

#: functions.php:2967
msgid "缩放动画曲线"
msgstr "Функция перехода анимации"

#: functions.php:2971
msgid "例："
msgstr "Например:"

#: functions.php:2971
msgid "如果你不知道这是什么，参考"
msgstr "Если не знаете что это, вы можете ознакомиться с этим "

#: functions.php:2971
msgid "这里"
msgstr "тут"

#: functions.php:2976
msgid "图片最大缩放比例"
msgstr "Максимальный коэффициент масштабирования изображения"

#: functions.php:2979
msgid "图片相对于页面的最大缩放比例 (0 ~ 1 的小数)"
msgstr ""
"Максимальное отношение масштабирования изображения относительно страницы "
"(десятичное от 0 до 1)"

#: functions.php:2984
msgid "启用 Pangu.js (自动在中英文之间添加空格)"
msgstr ""
"Включите Pangu.js (автоматическое добавление пробелов между китайским и "
"английским языками)"

#: functions.php:2989
msgid "格式化文章内容"
msgstr "Формат содержание статьи"

#: functions.php:2990
msgid "格式化说说"
msgstr "Format essays"

#: functions.php:2991
msgid "格式化评论区"
msgstr "Format comments"

#: functions.php:2992
msgid "格式化文章内容和评论区"
msgstr "Format articles and comments"

#: functions.php:2993
msgid "格式化文章内容和说说"
msgstr "Format articles and essays"

#: functions.php:2994
msgid "格式化说说和评论区"
msgstr "Format essays and comments"

#: functions.php:2995
msgid "格式化文章内容、说说和评论区"
msgstr "Format article, essays and comments"

#: functions.php:2997
msgid "开启后，会自动在中文和英文之间添加空格"
msgstr ""
"After opening, it will automatically add space between Chinese and English"

#: functions.php:3000
msgid "脚本"
msgstr "Скрипты"

#: functions.php:3002
msgid "注意"
msgstr "Предупреждение"

#: functions.php:3004
msgid ""
"Argon 使用 pjax 方式加载页面 (无刷新加载) , 所以除非页面手动刷新，否则您的脚"
"本只会被执行一次。"
msgstr ""
"Argon использует Pjax для загрузки страницы (загрузка без обновления), "
"поэтому, если страница не будет обновлена вручную, ваш скрипт будет выполнен "
"только один раз."

#: functions.php:3005
msgid "如果您想让每次页面跳转(加载新页面)时都执行脚本，请将脚本写入"
msgstr ""
"Если вы хотите выполнять скрипт каждый раз при загрузке новой страницы, "
"Пожалуйста, запишите его в"

#: functions.php:3005
msgid "中"
msgstr "функцию"

#: functions.php:3005
msgid "示例写法"
msgstr "Для примера "

#: functions.php:3008
msgid "页面每次跳转都会执行这里的代码"
msgstr "Код здесь будет выполняться каждый раз при загрузке страницы"

#: functions.php:3012
msgid "当页面第一次载入时，"
msgstr "Когда страница загружается в первый раз, скрипт в"

#: functions.php:3012
msgid "中的脚本不会执行，所以您可以手动执行"
msgstr "не будет выполняться, поэтому вы можете выполнить его вручную"

#: functions.php:3012
msgid "来让页面初次加载时也执行脚本"
msgstr "чтобы выполнить скрипт при первой загрузке страницы."

#: functions.php:3016
msgid "页头脚本"
msgstr "Скрипты в Header"

#: functions.php:3019
msgid "插入到 body 之前"
msgstr "Будет вставлен перед Body"

#: functions.php:3023
msgid "页尾脚本"
msgstr "Скрипты подвала"

#: functions.php:3026
msgid "插入到 body 之后"
msgstr "Будет вставлен после Body"

#: functions.php:3031
msgid "是否启用平滑滚动"
msgstr "Включить плавную прокрутку"

#: functions.php:3035
msgid "使用平滑滚动方案 1 (平滑) (推荐)"
msgstr "Плавная прокрутка, вариант 1 (Рекомендуется)"

#: functions.php:3036
msgid "使用平滑滚动方案 1 (脉冲式滚动) (仿 Edge) (推荐)"
msgstr "Плавная прокрутка, вариант 1 (Импульс) (Рекомендуется)"

#: functions.php:3037
msgid "使用平滑滚动方案 2 (较稳)"
msgstr "Плавная прокрутка, вариант 2"

#: functions.php:3038
msgid "使用平滑滚动方案 3"
msgstr "Плавная прокрутка, вариант 3"

#: functions.php:3039
msgid "不使用平滑滚动"
msgstr "Выключено"

#: functions.php:3041
msgid ""
"能增强浏览体验，但可能出现一些小问题，如果有问题请切换方案或关闭平滑滚动"
msgstr ""
"Может улучшить опыт прокрутки, но могут возникнуть некоторые незначительные "
"проблемы. Если есть проблема, пожалуйста, смените схему или выключите "
"плавную прокрутку."

#: functions.php:3045
msgid "是否启用进入文章动画"
msgstr "Включена ли анимация перехода к статье"

#: functions.php:3052
msgid "从首页或分类目录进入文章时，使用平滑过渡（可能影响加载文章时的性能）"
msgstr ""
"Используйте переход при вводе статей с главной страницы или каталога "
"категорий (может повлиять на производительность при загрузке статей)"

#: functions.php:3056
msgid "评论分页"
msgstr "Разбивка комментариев"

#: functions.php:3058
msgid "评论分页方式"
msgstr "Способ разбивки на страницы комментариев"

#: functions.php:3062
msgid "无限加载"
msgstr "Добавлять"

#: functions.php:3063
msgid "页码"
msgstr "Обычная пагинация"

#: functions.php:3065
msgid "无限加载：点击 \"加载更多\" 按钮来加载更多评论。"
msgstr ""
"Добавить: Нажмите кнопку\" Загрузить больше\", чтобы загрузить больше "
"комментариев."

#: functions.php:3065
msgid "页码：显示页码来分页。"
msgstr "Номер страницы: отображение кнопок номера страницы."

#: functions.php:3065
msgid ""
"推荐选择\"无限加载\"时将 Wordpress 设置中的讨论设置项设为 \"默认显示最后一"
"页，在每个页面顶部显示新的评论\"。"
msgstr ""
"При выборе \"добавить \"рекомендуется установить пункт Настройки обсуждения "
"в настройках WordPress в \"отображение последней страницы по умолчанию и "
"отображение новых комментариев в верхней части каждой страницы\"."

#: functions.php:3070
msgid "是否隐藏 \"昵称\"、\"邮箱\"、\"网站\" 输入框"
msgstr "Спрятать поля \"имя\", \"почта\", и \"ссылка на сайт\" "

#: functions.php:3077
msgid ""
"选项仅在 \"设置-评论-评论作者必须填入姓名和电子邮件地址\" 选项未勾选的前提下"
"生效。如勾选了 \"评论作者必须填入姓名和电子邮件地址\"，则只有 \"网站\" 输入框"
"会被隐藏。"
msgstr ""
"Опция вступает в силу только в том случае, если в настройках комментирования "
"выставлена опция \"автор комментария должен заполнить имя и адрес "
"электронной почты\".\n"
"Если установлена опция \"автор комментария должен заполнить имя и адрес "
"электронной почты\" то будет скрыто только поле ввода \"веб-сайт\"."

#: functions.php:3081
msgid "评论是否需要验证码"
msgstr "Капча для отправки комментария"

#: functions.php:3085
msgid "需要"
msgstr "Включить"

#: functions.php:3086
msgid "不需要"
msgstr "Отключить"

#: functions.php:3092
msgid "是否允许在评论中使用 Markdown 语法"
msgstr "Разрешить Markdown для написания комменатрия"

#: functions.php:3096 functions.php:3107 functions.php:3119 functions.php:3130
#: functions.php:3147
msgid "允许"
msgstr "Разрешить"

#: functions.php:3097 functions.php:3108 functions.php:3118 functions.php:3129
#: functions.php:3146
msgid "不允许"
msgstr "Запретить"

#: functions.php:3103
msgid "是否允许评论者再次编辑评论"
msgstr "Разрешить редактировать комментарии"

#: functions.php:3110
msgid "同一个评论者可以再次编辑评论。"
msgstr "Автор может редактировать свой комментарий."

#: functions.php:3114
msgid "是否允许评论者使用悄悄话模式"
msgstr "Разрешить отправлять приватные комментарии"

#: functions.php:3121
msgid "评论者使用悄悄话模式发送的评论和其下的所有回复只有发送者和博主能看到。"
msgstr ""
"Только отправитель и владелец блога могут видеть комментарии, отправленные в "
"режиме приватного комментария, и все ответы под ним."

#: functions.php:3125
msgid "是否允许评论者接收评论回复邮件提醒"
msgstr ""
"Разрешено ли комментатору получать уведомления о ответах на комментарии"

#: functions.php:3135
msgid "评论时默认勾选 \"启用邮件通知\" 复选框"
msgstr ""
"установите флажок \"Включить уведомление по электронной почте\" по умолчанию"

#: functions.php:3138
msgid "评论者开启邮件提醒后，其评论有回复时会有邮件通知。"
msgstr ""
"Если комментатор включит уведомление по электронной почте, то ему "
"отправиться уведомление по электронной почте, когда на его комментарий "
"ответят."

#: functions.php:3142
msgid "允许评论者使用 QQ 头像"
msgstr "Разрешить использование аватаров QQ"

#: functions.php:3149
msgid ""
"开启后，评论者可以使用 QQ 号代替邮箱输入，头像会根据评论者的 QQ 号获取。"
msgstr ""
"Если эта опция включена, комментаторы могут использовать QQ-номер вместо "
"электронной почты, и аватар будет автоматически получать QQ-номер "
"комментатора."

#: functions.php:3152
msgid "评论区"
msgstr "Зона комментариев"

#: functions.php:3154
msgid "评论头像垂直位置"
msgstr "Вертикальное положение Аватара"

#: functions.php:3158
msgid "居上"
msgstr "Вверху"

#: functions.php:3159
msgid "居中"
msgstr "По середине"

#: functions.php:3165
msgid "谁可以查看评论编辑记录"
msgstr "Кто может видеть историю редактирования комментариев"

#: functions.php:3169
msgid "只有博主"
msgstr "Только владелец блога"

#: functions.php:3170
msgid "评论发送者和博主"
msgstr "Автор комментария и владелец блога"

#: functions.php:3171
msgid "任何人"
msgstr "Любой желающий"

#: functions.php:3173
msgid "点击评论右侧的 \"已编辑\" 标记来查看编辑记录"
msgstr ""
"Нажмите на отметку \"отредактировано\" в правой части комментария, чтобы "
"просмотреть историю редактирования"

#: functions.php:3177
msgid "评论者 UA 显示"
msgstr "Показывать пользовательский \"User Agent\""

#: functions.php:3182
msgid "浏览器"
msgstr "Браузер"

#: functions.php:3183
msgid "浏览器+版本号"
msgstr "Браузер + его версия"

#: functions.php:3184
msgid "平台+浏览器+版本号"
msgstr "Платформа + Браузер + версия браузера"

#: functions.php:3185
msgid "平台+浏览器"
msgstr "Платформа + Браузер"

#: functions.php:3186
msgid "平台"
msgstr "Платформа"

#: functions.php:3188
msgid "设置是否在评论区显示评论者 UA 及显示哪些部分"
msgstr ""
"Следует ли отображать UA комментатора в области комментариев и какие части "
"отображаются"

#: functions.php:3192
msgid "折叠过长评论"
msgstr "Скрывать часть контента длинных комментариев"

#: functions.php:3196
msgid "不折叠"
msgstr "Не скрывать"

#: functions.php:3197
msgid "折叠"
msgstr "Скрывать"

#: functions.php:3199
msgid "开启后，过长的评论会被折叠，需要手动展开"
msgstr ""
"При включении, слишком длинные комментарии будут свернуты и должны быть "
"развернуты вручную"

#: functions.php:3202
msgid "杂项"
msgstr "Другое"

#: functions.php:3204
msgid "是否启用 Pjax"
msgstr "Включить Pjax"

#: functions.php:3211
msgid "Pjax 可以增强页面的跳转体验"
msgstr "Pjax может улучшить опыт переход по страницам"

#: functions.php:3215
msgid "首页隐藏特定 分类/Tag 下的文章"
msgstr "Скрыть определенные категории / теги статей на домашней странице"

#: functions.php:3218
msgid "输入要隐藏的 分类/Tag 的 ID，用英文逗号分隔，留空则不隐藏"
msgstr ""
"Введите идентификаторы категорий / тегов, которые будут скрыты, разделенные "
"запятыми, оставьте пустыми, чтобы не скрывать"

#: functions.php:3218
msgid "点此查看"
msgstr "кликните сюда "

#: functions.php:3218
msgid "所有分类和 Tag 的 ID"
msgstr "для просмотра идентификаторов всех категорий и тегов"

#: functions.php:3254
msgid "美化登录界面"
msgstr "Украсьте страницу входа в систему"

#: functions.php:3261
msgid "使用 Argon Design 风格的登录界面"
msgstr "Использовать стили Argon для интерфейса входа"

#: functions.php:3265
msgid "博客首页是否显示说说"
msgstr "Показывать Записи на главной странице"

#: functions.php:3272
msgid "开启后，博客首页文章和说说穿插显示"
msgstr ""
"Если включено, то Статьи и Заметки будут отображаться на главной странице "
"вместе"

#: functions.php:3276
msgid "是否使用 v2ex CDN 代理的 gravatar"
msgstr "Использовать V2EX CDN Gravatar"

#: functions.php:3281
msgid "使用"
msgstr "Использовать"

#: functions.php:3283
msgid "可以大幅增加国内 gravatar 头像加载的速度"
msgstr "Может увеличить скорость загрузки Gravatar в Китае."

#: functions.php:3287
msgid "是否修正时区错误"
msgstr "Исправить проблемы часовых поясов"

#: functions.php:3294
msgid "如遇到时区错误（例如一条刚发的评论显示 8 小时前），这个选项"
msgstr ""
"Если вы столкнулись с проблемой часового пояса (например, комментарий только "
"что опубликован, но отображается 8 часов назад), эта опция"

#: functions.php:3294
msgid "可能"
msgstr "возможно "

#: functions.php:3294
msgid "可以修复这个问题"
msgstr "помочь исправить эту проблему"

#: functions.php:3298
msgid "是否在文章列表内容预览中隐藏短代码"
msgstr "Скрыть короткие коды в предварительном просмотре"

#: functions.php:3302 functions.php:3313
msgid "否"
msgstr "Нет"

#: functions.php:3303 functions.php:3314
msgid "是"
msgstr "Да"

#: functions.php:3309
msgid "是否允许移动端缩放页面"
msgstr "Разрешить мобильному браузеру масштабировать страницу"

#: functions.php:3320
msgid "检测更新源"
msgstr "Источник обновления темы"

#: functions.php:3328
msgid "暂停更新 (不推荐)"
msgstr "Приостановка обновления (не рекомендуется)"

#: functions.php:3330
msgid "如更新主题速度较慢，可考虑更换更新源。"
msgstr ""
"Если обновление темы происходит медленно, вы можете изменить источник "
"обновления."

#: functions.php:3334
msgid "页脚附加内容"
msgstr "Дополнительный контент в подвале"

#: functions.php:3347
msgid "保存更改"
msgstr "Сохранить изменения"

#: functions.php:3348
msgid "导入设置"
msgstr "Импортировать настройки"

#: functions.php:3349
msgid "导出设置"
msgstr "Экспортировать настройки"

#: functions.php:3354
msgid "收起"
msgstr "-"

#: functions.php:3357
msgid "请复制并保存导出后的 JSON"
msgstr "Скопируйте и сохраните настройки темы в JSON"

#: functions.php:3357
msgid "确定"
msgstr "ОК"

#: functions.php:3433
msgid "展开"
msgstr "+"

#: functions.php:3578
msgid "字段导入失败"
msgstr "Ошибка импорта полей"

#: functions.php:3589
msgid "请输入要导入的备份 JSON"
msgstr "Вставьте код JSON"

#: functions.php:3592
msgid "已导入，请保存更改"
msgstr "Импортировано, изменения сохранены."

#: functions.php:3724
msgid "顶部导航"
msgstr "Toolbar Menu"

#: functions.php:3725
msgid "左侧栏菜单"
msgstr "Left Sidebar Menu"

#: functions.php:3726
msgid "左侧栏作者个人链接"
msgstr "Left Sidebar Author Links"

#: functions.php:3727
msgid "左侧栏友情链接"
msgstr "Left Sidebar Links"

#: functions.php:3738 functions.php:3739 functions.php:3749 shuoshuo.php:12
msgid "说说"
msgstr "Записи"

#: functions.php:3740 functions.php:3741
msgid "发表说说"
msgstr "Написать статью"

#: functions.php:3742
msgid "编辑说说"
msgstr "Редактировать статью"

#: functions.php:3743
msgid "新说说"
msgstr "Добавить статью"

#: functions.php:3744
msgid "查看说说"
msgstr "Посмотреть статью"

#: functions.php:3745
msgid "搜索说说"
msgstr "Найти статью"

#: functions.php:3746
msgid "暂无说说"
msgstr "Нет статей"

#: functions.php:3747
msgid "没有已遗弃的说说"
msgstr "Удаленных статей нет"

#: header.php:282 header.php:363 searchform.php:11 sidebar.php:63
msgid "搜索"
msgstr "Поиск"

#: header.php:343 searchform.php:7 sidebar.php:64
msgid "搜索什么..."
msgstr "Поиск..."

#: header.php:482
msgid "移至左侧"
msgstr "Переместить влево"

#: header.php:482
msgid "移至右侧"
msgstr "Переместить вправо"

#: header.php:486
msgid "回到顶部"
msgstr "Наверх"

#: header.php:492 header.php:501
msgid "暗黑模式"
msgstr "Темная тема"

#: header.php:492
msgid "日间模式"
msgstr "Светлая тема"

#: header.php:495
msgid "设置"
msgstr "Настройки"

#: header.php:501
msgid "切换到夜间模式"
msgstr "Переключиться на светлую тему"

#: header.php:501
msgid "切换到暗黑模式"
msgstr "Переключиться на темную тему"

#: header.php:514
msgid "阴影"
msgstr "Тени"

#: header.php:520
msgid "滤镜"
msgstr "Фильтр"

#: header.php:523
msgid "日落"
msgstr "Рассвет"

#: header.php:524
msgid "暗化"
msgstr "Безоблачно"

#: header.php:525
msgid "灰度"
msgstr "Градации серого"

#: header.php:529
msgid "恢复默认"
msgstr "Восстановить по умолчанию"

#: header.php:529
msgid "圆角"
msgstr "Закругленные углы"

#: header.php:541
msgid "菜单"
msgstr "Меню"

#: header.php:544
msgid "阅读进度"
msgstr "Процесс чтения"

#: search.php:7
msgid "的搜索结果"
msgstr "Результаты поиска"

#: search.php:11
msgid "个结果"
msgstr "Результаты"

#: shuoshuo.php:20
msgid "条说说"
msgstr "Заметки"

#: sidebar.php:6
msgid "公告"
msgstr "Объявление"

#: sidebar.php:80
msgid "文章目录"
msgstr "Каталог статей"

#: sidebar.php:84
msgid "站点概览"
msgstr "Обзор сайта"

#: sidebar.php:88
msgid "功能"
msgstr "Функции"

#: sidebar.php:136 sidebar.php:224
msgid "分类"
msgstr "Категории"

#: sidebar.php:142 sidebar.php:252
msgid "标签"
msgstr "Теги"

#: single.php:25
msgid "上一篇"
msgstr "Предыдущая статья"

#: single.php:30
msgid "下一篇"
msgstr "Следующая статья"

#: template-parts/content-none-search.php:4
msgid "没有搜索结果"
msgstr "Нет результатов поиска"

#: template-parts/content-none-search.php:5
msgid "换个关键词试试 ?"
msgstr "Попробуйте по другому ключевому слову"

#: template-parts/content-none-search.php:9
#: template-parts/content-none-tag.php:9
msgid "返回上一页"
msgstr "Вернуться на предыдущую страницу"

#: template-parts/content-none-tag.php:4
msgid "此分类没有文章"
msgstr "В этой категории нет постов"

#: template-parts/content-none-tag.php:5
msgid "这里什么都没有"
msgstr "Тут пусто."

#: template-parts/content-page.php:73 template-parts/content-single.php:70
#: template-parts/content-timeline.php:57
msgid "这是一篇受密码保护的文章，您需要提供访问密码"
msgstr "Это статья, защищенная паролем, для просмотра необходим пароль"

#: template-parts/content-page.php:80 template-parts/content-single.php:77
msgid "密码"
msgstr "Пароль"

#: template-parts/content-page.php:90 template-parts/content-single.php:87
msgid "确认"
msgstr "Отправить"

#: template-parts/content-shuoshuo-details.php:5
#: template-parts/content-shuoshuo-preview.php:13
#: template-parts/content-shuoshuo.php:5
msgid "月"
msgstr "/"

#: template-parts/content-shuoshuo-details.php:6
#: template-parts/content-shuoshuo-preview.php:14
#: template-parts/content-shuoshuo.php:6
msgid "日"
msgstr "день "

#: template-parts/content-single.php:47 template-parts/content.php:32
msgid "字"
msgstr "Букв"

#: template-parts/content.php:59
msgid "这篇文章受密码保护，输入密码才能阅读"
msgstr "Для просмотра данной статьи необходим пароль."

#: template-parts/content.php:62
msgid "这篇文章没有摘要"
msgstr "Никакой абстракции."

#: template-parts/share.php:3 template-parts/share.php:58
msgid "分享到微信"
msgstr "Поделиться в Wechat"

#: template-parts/share.php:8
msgid "分享到豆瓣"
msgstr "Поделиться в Douban"

#: template-parts/share.php:13
msgid "分享到 QQ"
msgstr "Поделиться в QQ"

#: template-parts/share.php:18
msgid "分享到 QQ 空间"
msgstr "Поделиться в Qzone"

#: template-parts/share.php:23
msgid "分享到微博"
msgstr "Поделиться в Weibo"

#: template-parts/share.php:28
msgid "分享到 Facebook"
msgstr "Поделиться в Facebook"

#: template-parts/share.php:33
msgid "分享到 Twitter"
msgstr "Поделиться в Twitter"

#: template-parts/share.php:39
msgid "分享到 Telegram"
msgstr "Поделиться в Telegram"

#: template-parts/share.php:44
msgid "复制链接"
msgstr "Скопировать ссылку"

#: template-parts/share.php:59
msgid "微信扫描二维码"
msgstr "Сканировать QR код"

#: template-parts/share.php:74
msgid "链接已复制"
msgstr "Скопировано"

#: template-parts/share.php:75
msgid "链接已复制到剪贴板"
msgstr "Ссылка скопирована в буфер обмена."

#: template-parts/share.php:88
msgid "复制失败"
msgstr "Ошибка при копировании"

#: template-parts/share.php:89
msgid "请手动复制链接"
msgstr ""
"Автоматическое копирование не удалось, пожалуйста скопируйте самостоятельно."

#: template-parts/shuoshuo-operations.php:5
msgid "已关闭"
msgstr "Отлючено"

#: unsubscribe-comment-mailnotice.php:7
msgid "评论不存在"
msgstr "Комментарий не найден"

#: unsubscribe-comment-mailnotice.php:8
msgid "错误"
msgstr "Ошибка"

#: unsubscribe-comment-mailnotice.php:9 unsubscribe-comment-mailnotice.php:14
msgid "评论 #"
msgstr "Комментарий #"

#: unsubscribe-comment-mailnotice.php:9
msgid " 不存在"
msgstr " не найден"

#: unsubscribe-comment-mailnotice.php:12 unsubscribe-comment-mailnotice.php:13
msgid "无需退订"
msgstr "Не от чего отписываться"

#: unsubscribe-comment-mailnotice.php:14
msgid " 的邮件通知已被退订或没有开启邮件通知"
msgstr " уже отписался"

#: unsubscribe-comment-mailnotice.php:17 unsubscribe-comment-mailnotice.php:18
msgid "退订失败"
msgstr "Отписка не удалась"

#: unsubscribe-comment-mailnotice.php:19
msgid "Token 不正确"
msgstr "Неверный токен"

#: unsubscribe-comment-mailnotice.php:23 unsubscribe-comment-mailnotice.php:24
msgid "退订成功"
msgstr "Подписка успешно отменена"

#: unsubscribe-comment-mailnotice.php:25
msgid "您已成功退订评论 #"
msgstr "Вы успешно отписались от комментариев"

#: unsubscribe-comment-mailnotice.php:25
msgid " 的邮件通知<br>该评论下有新回复时您将不会再收到通知"
msgstr "Вы отписались от уведомлениях об новых комменатриев."

#, fuzzy
#~| msgid "评论内容"
#~ msgid "评论内容不能为空"
#~ msgstr "Your comment..."

#, fuzzy
#~| msgid "发送"
#~ msgid "发送中"
#~ msgstr "Send"

#, fuzzy
#~| msgid "编辑"
#~ msgid "编辑中"
#~ msgstr "Edit"

#, fuzzy
#~| msgid "复制链接"
#~ msgid "复制"
#~ msgstr "Copy Link"

#, fuzzy
#~| msgid "全局"
#~ msgid "全屏"
#~ msgstr "General"

#, fuzzy
#~| msgid "链接已复制到剪贴板"
#~ msgid "代码已复制到剪贴板"
#~ msgstr "The link has been copied to the clipboard."

#, fuzzy
#~| msgid "请手动复制链接"
#~ msgid "请手动复制代码"
#~ msgstr "Please copy the link manually."

#, fuzzy
#~| msgid "分钟"
#~ msgid "分钟前"
#~ msgstr "minutes"

#, fuzzy
#~| msgid "小时"
#~ msgid "小时前"
#~ msgstr "hour"

#, fuzzy
#~| msgid "前"
#~ msgid "前天"
#~ msgstr " ago"

#, fuzzy
#~| msgid "前"
#~ msgid "天前"
#~ msgstr " ago"

#~ msgid "的评论"
#~ msgstr "'s comment"

#~ msgid "您在xxx[的评论有了新的回复]"
#~ msgstr "Has a New Reply"
