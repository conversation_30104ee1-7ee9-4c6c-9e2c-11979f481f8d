/*

Intellij-light style (c) Pegasis <<EMAIL>>

*/

.hljs {
  color: #000;
  background: #fff;
}

.hljs-subst,
.hljs-title {
  font-weight: normal;
  color: #000;
}

.hljs-title.function_ {
  color: #7A7A43;
}

.hljs-code,
.hljs-comment,
.hljs-quote {
  color: #8C8C8C;
  font-style: italic;
}

.hljs-meta {
  color: #9E880D;
}

.hljs-section {
  color: #871094;
}

.hljs-variable.language_,
.hljs-symbol,
.hljs-selector-class,
.hljs-selector-id,
.hljs-selector-tag,
.hljs-template-tag,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-keyword,
.hljs-meta .hljs-keyword,
.hljs-literal,
.hljs-name,
.hljs-built_in,
.hljs-type {
  color: #0033B3;
}

.hljs-property,
.hljs-attr {
  color: #871094;
}

.hljs-attribute {
  color: #174AD4;
}

.hljs-number {
  color: #1750EB;
}

.hljs-regexp {
  color: #264EFF;
}

.hljs-link {
  text-decoration: underline;
  color: #006DCC;
}

.hljs-meta .hljs-string,
.hljs-string {
  color: #067D17;
}

.hljs-char.escape_ {
  color: #0037A6;
}

.hljs-doctag {
  text-decoration: underline;
}

.hljs-template-variable {
  color: #248F8F;
}

.hljs-addition {
  background: #BEE6BE;
}

.hljs-deletion {
  background: #D6D6D6;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-variable,
.hljs-operator,
.hljs-punctuation,
.hljs-title.class_.inherited__,
.hljs-title.class_,
.hljs-params,
.hljs-bullet,
.hljs-formula,
.hljs-tag {
  /* purposely ignored */
}
