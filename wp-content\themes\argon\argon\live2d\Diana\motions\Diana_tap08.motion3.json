{"Version": 3, "Meta": {"Duration": 6.5, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 47, "TotalSegmentCount": 623, "TotalPointCount": 1872, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param35", "Segments": [0, -30, 1, 0.267, -30, 0.533, -30, 0.8, -30, 1, 0.911, -30, 1.022, 30, 1.133, 30, 1, 2.922, 30, 4.711, 30, 6.5, 30]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, -11, 1, 0.222, -11, 0.444, 10, 0.667, 10, 1, 0.822, 10, 0.978, -14, 1.133, -14, 1, 1.2, -14, 1.267, 30, 1.333, 30, 1, 1.367, 30, 1.4, -30, 1.433, -30, 1, 1.478, -30, 1.522, 23, 1.567, 23, 1, 1.611, 23, 1.656, -16, 1.7, -16, 1, 1.756, -16, 1.811, 15, 1.867, 15, 1, 1.911, 15, 1.956, -30, 2, -30, 1, 2.178, -30, 2.356, 30, 2.533, 30, 1, 2.767, 30, 3, -30, 3.233, -30, 1, 3.456, -30, 3.678, 30, 3.9, 30, 1, 4.133, 30, 4.367, -30, 4.6, -30, 1, 4.8, -30, 5, 30, 5.2, 30, 1, 5.233, 30, 5.267, -14, 5.3, -14, 1, 5.344, -14, 5.389, 30, 5.433, 30, 1, 5.467, 30, 5.5, -30, 5.533, -30, 1, 5.578, -30, 5.622, 23, 5.667, 23, 1, 5.711, 23, 5.756, -16, 5.8, -16, 1, 5.856, -16, 5.911, 15, 5.967, 15, 1, 6.144, 15, 6.322, 14.279, 6.5, 13.154]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 0.433, 0, 1, 0.567, 0, 0.7, -5, 0.833, -5, 1, 1, -5, 1.167, 0, 1.333, 0, 1, 1.711, 0, 2.089, -6, 2.467, -6, 1, 2.689, -6, 2.911, 1, 3.133, 1, 1, 3.411, 1, 3.689, -10, 3.967, -10, 1, 4.444, -10, 4.922, 0, 5.4, 0, 0, 6.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.722, 0, 1.444, -10, 2.167, -10, 1, 2.867, -10, 3.567, 8, 4.267, 8, 1, 4.878, 8, 5.489, 0, 6.1, 0, 0, 6.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 1, 1, 0.144, 1, 0.289, 1, 0.433, 1, 1, 0.733, 1, 1.033, 0, 1.333, 0, 1, 1.444, 0, 1.556, 0, 1.667, 0, 1, 1.933, 0, 2.2, -4.593, 2.467, -8, 1, 2.567, -9.277, 2.667, -9, 2.767, -9, 1, 2.867, -9, 2.967, -6.661, 3.067, -1, 1, 3.178, 5.29, 3.289, 9, 3.4, 9, 1, 3.511, 9, 3.622, 1, 3.733, 1, 1, 3.856, 1, 3.978, 2, 4.1, 2, 1, 4.356, 2, 4.611, 1.758, 4.867, 1, 1, 5.278, -0.22, 5.689, -1, 6.1, -1, 0, 6.5, -1]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.144, 0, 0.289, -0.365, 0.433, 1, 1, 0.733, 3.835, 1.033, 10, 1.333, 10, 1, 1.444, 10, 1.556, 10, 1.667, 10, 1, 1.933, 10, 2.2, -12, 2.467, -12, 1, 2.567, -12, 2.667, -6.517, 2.767, 1, 1, 2.867, 8.517, 2.967, 11, 3.067, 11, 1, 3.178, 11, 3.289, 9.25, 3.4, 2, 1, 3.511, -5.25, 3.622, -12, 3.733, -12, 1, 3.856, -12, 3.978, 1, 4.1, 1, 1, 4.356, 1, 4.611, -15, 4.867, -15, 1, 5.278, -15, 5.689, 1, 6.1, 1, 0, 6.5, 1]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.822, 0, 1.644, -10, 2.467, -10, 1, 3.011, -10, 3.556, 7, 4.1, 7, 1, 4.533, 7, 4.967, -1, 5.4, -1, 0, 6.5, -1]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 6.5, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.4, 0, 0.8, 0, 1.2, 0, 1, 1.233, 0, 1.267, 30, 1.3, 30, 1, 1.333, 30, 1.367, 30, 1.4, 30, 1, 1.433, 30, 1.467, -30, 1.5, -30, 1, 1.544, -30, 1.589, -30, 1.633, -30, 1, 1.667, -30, 1.7, 30, 1.733, 30, 1, 1.767, 30, 1.8, 30, 1.833, 30, 1, 1.9, 30, 1.967, -14.58, 2.033, -14.58, 1, 2.122, -14.58, 2.211, 5.912, 2.3, 5.912, 1, 2.378, 5.912, 2.456, -2.481, 2.533, -2.481, 1, 2.622, -2.481, 2.711, 1.023, 2.8, 1.023, 1, 2.878, 1.023, 2.956, -0.432, 3.033, -0.432, 1, 3.122, -0.432, 3.211, 0.178, 3.3, 0.178, 1, 3.378, 0.178, 3.456, -0.075, 3.533, -0.075, 1, 3.622, -0.075, 3.711, 0.031, 3.8, 0.031, 1, 3.878, 0.031, 3.956, -0.013, 4.033, -0.013, 1, 4.122, -0.013, 4.211, 0.005, 4.3, 0.005, 1, 4.344, 0.005, 4.389, 0, 4.433, 0, 1, 4.467, 0, 4.5, 30, 4.533, 30, 1, 4.567, 30, 4.6, 30, 4.633, 30, 1, 4.667, 30, 4.7, -30, 4.733, -30, 1, 4.767, -30, 4.8, -30, 4.833, -30, 1, 4.867, -30, 4.9, 30, 4.933, 30, 1, 4.978, 30, 5.022, 30, 5.067, 30, 1, 5.089, 30, 5.111, -30, 5.133, -30, 1, 5.167, -30, 5.2, -30, 5.233, -30, 1, 5.289, -30, 5.344, 30, 5.4, 30, 1, 5.478, 30, 5.556, -11.789, 5.633, -11.789, 1, 5.722, -11.789, 5.811, 4.882, 5.9, 4.882, 1, 5.978, 4.882, 6.056, -2.023, 6.133, -2.023, 1, 6.222, -2.023, 6.311, 0.847, 6.4, 0.847, 1, 6.433, 0.847, 6.467, 0.626, 6.5, 0.375]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 1, 0.4, 0, 0.8, 0, 1.2, 0, 1, 1.233, 0, 1.267, 30, 1.3, 30, 1, 1.333, 30, 1.367, 30, 1.4, 30, 1, 1.433, 30, 1.467, -30, 1.5, -30, 1, 1.544, -30, 1.589, -30, 1.633, -30, 1, 1.667, -30, 1.7, 30, 1.733, 30, 1, 1.767, 30, 1.8, 30, 1.833, 30, 1, 1.9, 30, 1.967, -14.58, 2.033, -14.58, 1, 2.122, -14.58, 2.211, 5.912, 2.3, 5.912, 1, 2.378, 5.912, 2.456, -2.481, 2.533, -2.481, 1, 2.622, -2.481, 2.711, 1.023, 2.8, 1.023, 1, 2.878, 1.023, 2.956, -0.432, 3.033, -0.432, 1, 3.122, -0.432, 3.211, 0.178, 3.3, 0.178, 1, 3.378, 0.178, 3.456, -0.075, 3.533, -0.075, 1, 3.622, -0.075, 3.711, 0.031, 3.8, 0.031, 1, 3.878, 0.031, 3.956, -0.013, 4.033, -0.013, 1, 4.122, -0.013, 4.211, 0.005, 4.3, 0.005, 1, 4.344, 0.005, 4.389, 0, 4.433, 0, 1, 4.467, 0, 4.5, 30, 4.533, 30, 1, 4.567, 30, 4.6, 30, 4.633, 30, 1, 4.667, 30, 4.7, -30, 4.733, -30, 1, 4.767, -30, 4.8, -30, 4.833, -30, 1, 4.867, -30, 4.9, 30, 4.933, 30, 1, 4.978, 30, 5.022, 30, 5.067, 30, 1, 5.089, 30, 5.111, -30, 5.133, -30, 1, 5.167, -30, 5.2, -30, 5.233, -30, 1, 5.289, -30, 5.344, 30, 5.4, 30, 1, 5.478, 30, 5.556, -11.789, 5.633, -11.789, 1, 5.722, -11.789, 5.811, 4.882, 5.9, 4.882, 1, 5.978, 4.882, 6.056, -2.023, 6.133, -2.023, 1, 6.222, -2.023, 6.311, 0.847, 6.4, 0.847, 1, 6.433, 0.847, 6.467, 0.626, 6.5, 0.375]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.389, 1, 0.778, 1, 1.167, 1, 1, 1.244, 1, 1.322, 0, 1.4, 0, 1, 1.478, 0, 1.556, 1, 1.633, 1, 1, 2.556, 1, 3.478, 1, 4.4, 1, 1, 4.478, 1, 4.556, 0, 4.633, 0, 1, 4.7, 0, 4.767, 1, 4.833, 1, 1, 4.9, 1, 4.967, 0, 5.033, 0, 1, 5.089, 0, 5.144, 1, 5.2, 1, 0, 6.5, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 1.633, 0, 1, 2.7, 0, 3.767, 0, 4.833, 0, 0, 6.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.389, 1, 0.778, 1, 1.167, 1, 1, 1.244, 1, 1.322, 0, 1.4, 0, 1, 1.478, 0, 1.556, 1, 1.633, 1, 1, 2.556, 1, 3.478, 1, 4.4, 1, 1, 4.478, 1, 4.556, 0, 4.633, 0, 1, 4.7, 0, 4.767, 1, 4.833, 1, 1, 4.9, 1, 4.967, 0, 5.033, 0, 1, 5.089, 0, 5.144, 1, 5.2, 1, 0, 6.5, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0.2, 1, 0.222, 0.2, 0.444, 0.2, 0.667, 0.2, 1, 0.756, 0.2, 0.844, 1, 0.933, 1, 1, 1.178, 1, 1.422, 1, 1.667, 1, 1, 1.767, 1, 1.867, 1, 1.967, 1, 1, 2.456, 1, 2.944, 0.2, 3.433, 0.2, 1, 3.911, 0.2, 4.389, 1, 4.867, 1, 1, 4.967, 1, 5.067, 1, 5.167, 1, 0, 6.5, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.756, 0, 0.844, 0, 0.933, 0, 1, 1.178, 0, 1.422, 0, 1.667, 0, 1, 1.767, 0, 1.867, 1, 1.967, 1, 1, 2.456, 1, 2.944, 1, 3.433, 1, 1, 3.911, 1, 4.389, 1, 4.867, 1, 1, 4.967, 1, 5.067, 0, 5.167, 0, 0, 6.5, 0]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, -1, 0, 5.633, -1, 1, 5.8, -1, 5.967, -30, 6.133, -30, 0, 6.5, -30]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, -30, 0, 0.4, -30, 1, 0.533, -30, 0.667, 30, 0.8, 30, 1, 2.511, 30, 4.222, 30, 5.933, 30, 1, 6.089, 30, 6.244, -30, 6.4, -30, 0, 6.5, -30]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, -30, 0, 0.8, -30, 1, 1.844, -30, 2.889, -22, 3.933, -22, 1, 4.511, -22, 5.089, -28, 5.667, -28, 0, 6.5, -28]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -30, 0, 0.4, -30, 1, 0.533, -30, 0.667, 30, 0.8, 30, 1, 2.511, 30, 4.222, 30, 5.933, 30, 1, 6.089, 30, 6.244, -30, 6.4, -30, 0, 6.5, -30]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 1, 0, 6.5, 1]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.4, 0, 0.8, 0, 1.2, 0, 1, 1.256, 0, 1.311, 17.112, 1.367, 17.112, 1, 1.444, 17.112, 1.522, -18.128, 1.6, -18.128, 1, 1.689, -18.128, 1.778, 4.281, 1.867, 4.281, 1, 1.978, 4.281, 2.089, -0.809, 2.2, -0.809, 1, 2.3, -0.809, 2.4, 0.159, 2.5, 0.159, 1, 2.6, 0.159, 2.7, -0.03, 2.8, -0.03, 1, 2.911, -0.03, 3.022, 0.006, 3.133, 0.006, 1, 3.233, 0.006, 3.333, -0.001, 3.433, -0.001, 1, 3.533, -0.001, 3.633, 0, 3.733, 0, 1, 3.744, 0, 3.756, 0, 3.767, 0, 1, 3.867, 0, 3.967, 0, 4.067, 0, 1, 4.178, 0, 4.289, 0, 4.4, 0, 1, 4.411, 0, 4.422, 0, 4.433, 0, 1, 4.489, 0, 4.544, 17.112, 4.6, 17.112, 1, 4.678, 17.112, 4.756, -18.752, 4.833, -18.752, 1, 4.9, -18.752, 4.967, 17.408, 5.033, 17.408, 1, 5.089, 17.408, 5.144, -17.691, 5.2, -17.691, 1, 5.289, -17.691, 5.378, 3.704, 5.467, 3.704, 1, 5.578, 3.704, 5.689, -0.701, 5.8, -0.701, 1, 5.9, -0.701, 6, 0.138, 6.1, 0.138, 1, 6.2, 0.138, 6.3, -0.026, 6.4, -0.026, 1, 6.433, -0.026, 6.467, -0.024, 6.5, -0.02]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.944, 0, 1.556, -8.917, 2.167, -8.917, 1, 2.867, -8.917, 3.567, 7.355, 4.267, 7.355, 1, 4.878, 7.355, 5.489, -0.292, 6.1, -0.292, 1, 6.178, -0.292, 6.256, 0, 6.333, 0, 1, 6.389, 0, 6.444, 0, 6.5, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.944, 0, 1.556, -8.917, 2.167, -8.917, 1, 2.867, -8.917, 3.567, 7.355, 4.267, 7.355, 1, 4.878, 7.355, 5.489, -0.292, 6.1, -0.292, 1, 6.178, -0.292, 6.256, 0, 6.333, 0, 1, 6.389, 0, 6.444, 0, 6.5, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.944, 0, 1.556, -8.917, 2.167, -8.917, 1, 2.867, -8.917, 3.567, 7.355, 4.267, 7.355, 1, 4.878, 7.355, 5.489, -0.292, 6.1, -0.292, 1, 6.178, -0.292, 6.256, 0, 6.333, 0, 1, 6.389, 0, 6.444, 0, 6.5, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 1, 0.589, 0, 1.178, -4.525, 1.767, -4.525, 1, 1.844, -4.525, 1.922, -4.355, 2, -4.355, 1, 2.122, -4.355, 2.244, -4.711, 2.367, -4.711, 1, 2.422, -4.711, 2.478, -4.538, 2.533, -4.538, 1, 2.578, -4.538, 2.622, -4.748, 2.667, -4.748, 1, 2.733, -4.748, 2.8, -3.985, 2.867, -3.985, 1, 2.956, -3.985, 3.044, -5.305, 3.133, -5.305, 1, 3.289, -5.305, 3.444, 6.452, 3.6, 6.452, 1, 3.7, 6.452, 3.8, 0.317, 3.9, 0.317, 1, 4.022, 0.317, 4.144, 5.449, 4.267, 5.449, 1, 4.678, 5.449, 5.089, 0.485, 5.5, 0.485, 1, 5.511, 0.485, 5.522, 0.485, 5.533, 0.485, 1, 5.722, 0.485, 5.911, -0.294, 6.1, -0.294, 1, 6.222, -0.294, 6.344, -0.133, 6.467, -0.133, 1, 6.478, -0.133, 6.489, -0.134, 6.5, -0.135]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 1, 0.589, 0, 1.178, -4.525, 1.767, -4.525, 1, 1.844, -4.525, 1.922, -4.355, 2, -4.355, 1, 2.122, -4.355, 2.244, -4.711, 2.367, -4.711, 1, 2.422, -4.711, 2.478, -4.538, 2.533, -4.538, 1, 2.578, -4.538, 2.622, -4.748, 2.667, -4.748, 1, 2.733, -4.748, 2.8, -3.985, 2.867, -3.985, 1, 2.956, -3.985, 3.044, -5.305, 3.133, -5.305, 1, 3.289, -5.305, 3.444, 6.452, 3.6, 6.452, 1, 3.7, 6.452, 3.8, 0.317, 3.9, 0.317, 1, 4.022, 0.317, 4.144, 5.449, 4.267, 5.449, 1, 4.678, 5.449, 5.089, 0.485, 5.5, 0.485, 1, 5.511, 0.485, 5.522, 0.485, 5.533, 0.485, 1, 5.722, 0.485, 5.911, -0.294, 6.1, -0.294, 1, 6.222, -0.294, 6.344, -0.133, 6.467, -0.133, 1, 6.478, -0.133, 6.489, -0.134, 6.5, -0.135]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.211, 0, 0.389, 0.425, 0.567, 0.425, 1, 0.611, 0.425, 0.656, 0.387, 0.7, 0.387, 1, 0.822, 0.387, 0.944, 0.687, 1.067, 0.687, 1, 1.344, 0.687, 1.622, -0.026, 1.9, -0.026, 1, 2.022, -0.026, 2.144, 0.934, 2.267, 0.934, 1, 2.356, 0.934, 2.444, 0.396, 2.533, 0.396, 1, 2.567, 0.396, 2.6, 0.572, 2.633, 0.572, 1, 2.711, 0.572, 2.789, -0.898, 2.867, -0.898, 1, 2.922, -0.898, 2.978, -0.171, 3.033, -0.171, 1, 3.144, -0.171, 3.256, -7.818, 3.367, -7.818, 1, 3.511, -7.818, 3.656, 7.391, 3.8, 7.391, 1, 3.911, 7.391, 4.022, -5.273, 4.133, -5.273, 1, 4.256, -5.273, 4.378, 2.67, 4.5, 2.67, 1, 4.611, 2.67, 4.722, -0.283, 4.833, -0.283, 1, 4.944, -0.283, 5.056, 1.063, 5.167, 1.063, 1, 5.311, 1.063, 5.456, -0.068, 5.6, -0.068, 1, 5.678, -0.068, 5.756, 0.5, 5.833, 0.5, 1, 5.989, 0.5, 6.144, -0.171, 6.3, -0.171, 1, 6.367, -0.171, 6.433, -0.084, 6.5, -0.013]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 1, 0.589, 0, 1.178, -4.525, 1.767, -4.525, 1, 1.844, -4.525, 1.922, -4.355, 2, -4.355, 1, 2.122, -4.355, 2.244, -4.711, 2.367, -4.711, 1, 2.422, -4.711, 2.478, -4.538, 2.533, -4.538, 1, 2.578, -4.538, 2.622, -4.748, 2.667, -4.748, 1, 2.733, -4.748, 2.8, -3.985, 2.867, -3.985, 1, 2.956, -3.985, 3.044, -5.305, 3.133, -5.305, 1, 3.289, -5.305, 3.444, 6.452, 3.6, 6.452, 1, 3.7, 6.452, 3.8, 0.317, 3.9, 0.317, 1, 4.022, 0.317, 4.144, 5.449, 4.267, 5.449, 1, 4.678, 5.449, 5.089, 0.485, 5.5, 0.485, 1, 5.511, 0.485, 5.522, 0.485, 5.533, 0.485, 1, 5.722, 0.485, 5.911, -0.294, 6.1, -0.294, 1, 6.222, -0.294, 6.344, -0.133, 6.467, -0.133, 1, 6.478, -0.133, 6.489, -0.134, 6.5, -0.135]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.211, 0, 0.389, 0.304, 0.567, 0.304, 1, 0.611, 0.304, 0.656, 0.276, 0.7, 0.276, 1, 0.822, 0.276, 0.944, 0.49, 1.067, 0.49, 1, 1.344, 0.49, 1.622, -0.019, 1.9, -0.019, 1, 2.022, -0.019, 2.144, 0.667, 2.267, 0.667, 1, 2.356, 0.667, 2.444, 0.283, 2.533, 0.283, 1, 2.567, 0.283, 2.6, 0.409, 2.633, 0.409, 1, 2.711, 0.409, 2.789, -0.641, 2.867, -0.641, 1, 2.922, -0.641, 2.978, -0.122, 3.033, -0.122, 1, 3.144, -0.122, 3.256, -5.584, 3.367, -5.584, 1, 3.511, -5.584, 3.656, 5.279, 3.8, 5.279, 1, 3.911, 5.279, 4.022, -3.767, 4.133, -3.767, 1, 4.256, -3.767, 4.378, 1.907, 4.5, 1.907, 1, 4.611, 1.907, 4.722, -0.202, 4.833, -0.202, 1, 4.944, -0.202, 5.056, 0.76, 5.167, 0.76, 1, 5.311, 0.76, 5.456, -0.049, 5.6, -0.049, 1, 5.678, -0.049, 5.756, 0.357, 5.833, 0.357, 1, 5.989, 0.357, 6.144, -0.122, 6.3, -0.122, 1, 6.367, -0.122, 6.433, -0.06, 6.5, -0.01]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 1, 0.589, 0, 1.178, -4.525, 1.767, -4.525, 1, 1.844, -4.525, 1.922, -4.355, 2, -4.355, 1, 2.122, -4.355, 2.244, -4.711, 2.367, -4.711, 1, 2.422, -4.711, 2.478, -4.538, 2.533, -4.538, 1, 2.578, -4.538, 2.622, -4.748, 2.667, -4.748, 1, 2.733, -4.748, 2.8, -3.985, 2.867, -3.985, 1, 2.956, -3.985, 3.044, -5.305, 3.133, -5.305, 1, 3.289, -5.305, 3.444, 6.452, 3.6, 6.452, 1, 3.7, 6.452, 3.8, 0.317, 3.9, 0.317, 1, 4.022, 0.317, 4.144, 5.449, 4.267, 5.449, 1, 4.678, 5.449, 5.089, 0.485, 5.5, 0.485, 1, 5.511, 0.485, 5.522, 0.485, 5.533, 0.485, 1, 5.722, 0.485, 5.911, -0.294, 6.1, -0.294, 1, 6.222, -0.294, 6.344, -0.133, 6.467, -0.133, 1, 6.478, -0.133, 6.489, -0.134, 6.5, -0.135]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.211, 0, 0.389, 0.304, 0.567, 0.304, 1, 0.611, 0.304, 0.656, 0.276, 0.7, 0.276, 1, 0.822, 0.276, 0.944, 0.49, 1.067, 0.49, 1, 1.344, 0.49, 1.622, -0.019, 1.9, -0.019, 1, 2.022, -0.019, 2.144, 0.667, 2.267, 0.667, 1, 2.356, 0.667, 2.444, 0.283, 2.533, 0.283, 1, 2.567, 0.283, 2.6, 0.409, 2.633, 0.409, 1, 2.711, 0.409, 2.789, -0.641, 2.867, -0.641, 1, 2.922, -0.641, 2.978, -0.122, 3.033, -0.122, 1, 3.144, -0.122, 3.256, -5.584, 3.367, -5.584, 1, 3.511, -5.584, 3.656, 5.279, 3.8, 5.279, 1, 3.911, 5.279, 4.022, -3.767, 4.133, -3.767, 1, 4.256, -3.767, 4.378, 1.907, 4.5, 1.907, 1, 4.611, 1.907, 4.722, -0.202, 4.833, -0.202, 1, 4.944, -0.202, 5.056, 0.76, 5.167, 0.76, 1, 5.311, 0.76, 5.456, -0.049, 5.6, -0.049, 1, 5.678, -0.049, 5.756, 0.357, 5.833, 0.357, 1, 5.989, 0.357, 6.144, -0.122, 6.3, -0.122, 1, 6.367, -0.122, 6.433, -0.06, 6.5, -0.01]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 1, 0.589, 0, 1.178, -5.43, 1.767, -5.43, 1, 1.844, -5.43, 1.922, -5.226, 2, -5.226, 1, 2.122, -5.226, 2.244, -5.653, 2.367, -5.653, 1, 2.422, -5.653, 2.478, -5.445, 2.533, -5.445, 1, 2.578, -5.445, 2.622, -5.697, 2.667, -5.697, 1, 2.733, -5.697, 2.8, -4.783, 2.867, -4.783, 1, 2.956, -4.783, 3.044, -6.366, 3.133, -6.366, 1, 3.289, -6.366, 3.444, 7.743, 3.6, 7.743, 1, 3.7, 7.743, 3.8, 0.38, 3.9, 0.38, 1, 4.022, 0.38, 4.144, 6.539, 4.267, 6.539, 1, 4.678, 6.539, 5.089, 0.582, 5.5, 0.582, 1, 5.511, 0.582, 5.522, 0.583, 5.533, 0.583, 1, 5.722, 0.583, 5.911, -0.353, 6.1, -0.353, 1, 6.222, -0.353, 6.344, -0.16, 6.467, -0.16, 1, 6.478, -0.16, 6.489, -0.16, 6.5, -0.162]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.211, 0, 0.389, 0.364, 0.567, 0.364, 1, 0.611, 0.364, 0.656, 0.332, 0.7, 0.332, 1, 0.822, 0.332, 0.944, 0.588, 1.067, 0.588, 1, 1.344, 0.588, 1.622, -0.022, 1.9, -0.022, 1, 2.022, -0.022, 2.144, 0.801, 2.267, 0.801, 1, 2.356, 0.801, 2.444, 0.339, 2.533, 0.339, 1, 2.567, 0.339, 2.6, 0.491, 2.633, 0.491, 1, 2.711, 0.491, 2.789, -0.769, 2.867, -0.769, 1, 2.922, -0.769, 2.978, -0.147, 3.033, -0.147, 1, 3.144, -0.147, 3.256, -6.701, 3.367, -6.701, 1, 3.511, -6.701, 3.656, 6.335, 3.8, 6.335, 1, 3.911, 6.335, 4.022, -4.52, 4.133, -4.52, 1, 4.256, -4.52, 4.378, 2.288, 4.5, 2.288, 1, 4.611, 2.288, 4.722, -0.243, 4.833, -0.243, 1, 4.944, -0.243, 5.056, 0.912, 5.167, 0.912, 1, 5.311, 0.912, 5.456, -0.059, 5.6, -0.059, 1, 5.678, -0.059, 5.756, 0.429, 5.833, 0.429, 1, 5.989, 0.429, 6.144, -0.147, 6.3, -0.147, 1, 6.367, -0.147, 6.433, -0.072, 6.5, -0.012]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 0.589, 0, 1.178, -5.43, 1.767, -5.43, 1, 1.844, -5.43, 1.922, -5.226, 2, -5.226, 1, 2.122, -5.226, 2.244, -5.653, 2.367, -5.653, 1, 2.422, -5.653, 2.478, -5.445, 2.533, -5.445, 1, 2.578, -5.445, 2.622, -5.697, 2.667, -5.697, 1, 2.733, -5.697, 2.8, -4.783, 2.867, -4.783, 1, 2.956, -4.783, 3.044, -6.366, 3.133, -6.366, 1, 3.289, -6.366, 3.444, 7.743, 3.6, 7.743, 1, 3.7, 7.743, 3.8, 0.38, 3.9, 0.38, 1, 4.022, 0.38, 4.144, 6.539, 4.267, 6.539, 1, 4.678, 6.539, 5.089, 0.582, 5.5, 0.582, 1, 5.511, 0.582, 5.522, 0.583, 5.533, 0.583, 1, 5.722, 0.583, 5.911, -0.353, 6.1, -0.353, 1, 6.222, -0.353, 6.344, -0.16, 6.467, -0.16, 1, 6.478, -0.16, 6.489, -0.16, 6.5, -0.162]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.211, 0, 0.389, 0.364, 0.567, 0.364, 1, 0.611, 0.364, 0.656, 0.332, 0.7, 0.332, 1, 0.822, 0.332, 0.944, 0.588, 1.067, 0.588, 1, 1.344, 0.588, 1.622, -0.022, 1.9, -0.022, 1, 2.022, -0.022, 2.144, 0.801, 2.267, 0.801, 1, 2.356, 0.801, 2.444, 0.339, 2.533, 0.339, 1, 2.567, 0.339, 2.6, 0.491, 2.633, 0.491, 1, 2.711, 0.491, 2.789, -0.769, 2.867, -0.769, 1, 2.922, -0.769, 2.978, -0.147, 3.033, -0.147, 1, 3.144, -0.147, 3.256, -6.701, 3.367, -6.701, 1, 3.511, -6.701, 3.656, 6.335, 3.8, 6.335, 1, 3.911, 6.335, 4.022, -4.52, 4.133, -4.52, 1, 4.256, -4.52, 4.378, 2.288, 4.5, 2.288, 1, 4.611, 2.288, 4.722, -0.243, 4.833, -0.243, 1, 4.944, -0.243, 5.056, 0.912, 5.167, 0.912, 1, 5.311, 0.912, 5.456, -0.059, 5.6, -0.059, 1, 5.678, -0.059, 5.756, 0.429, 5.833, 0.429, 1, 5.989, 0.429, 6.144, -0.147, 6.3, -0.147, 1, 6.367, -0.147, 6.433, -0.072, 6.5, -0.012]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.4, 0, 0.8, 0, 1.2, 0, 1, 1.256, 0, 1.311, 5.704, 1.367, 5.704, 1, 1.444, 5.704, 1.522, -6.043, 1.6, -6.043, 1, 1.689, -6.043, 1.778, 1.427, 1.867, 1.427, 1, 1.978, 1.427, 2.089, -0.27, 2.2, -0.27, 1, 2.3, -0.27, 2.4, 0.053, 2.5, 0.053, 1, 2.6, 0.053, 2.7, -0.01, 2.8, -0.01, 1, 2.911, -0.01, 3.022, 0.002, 3.133, 0.002, 1, 3.233, 0.002, 3.333, 0, 3.433, 0, 1, 3.533, 0, 3.633, 0, 3.733, 0, 1, 3.744, 0, 3.756, 0, 3.767, 0, 1, 3.867, 0, 3.967, 0, 4.067, 0, 1, 4.178, 0, 4.289, 0, 4.4, 0, 1, 4.411, 0, 4.422, 0, 4.433, 0, 1, 4.489, 0, 4.544, 5.704, 4.6, 5.704, 1, 4.678, 5.704, 4.756, -6.251, 4.833, -6.251, 1, 4.9, -6.251, 4.967, 5.803, 5.033, 5.803, 1, 5.089, 5.803, 5.144, -5.897, 5.2, -5.897, 1, 5.289, -5.897, 5.378, 1.235, 5.467, 1.235, 1, 5.578, 1.235, 5.689, -0.234, 5.8, -0.234, 1, 5.9, -0.234, 6, 0.046, 6.1, 0.046, 1, 6.2, 0.046, 6.3, -0.009, 6.4, -0.009, 1, 6.433, -0.009, 6.467, -0.008, 6.5, -0.007]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 1, 0.589, 0, 1.178, -3.62, 1.767, -3.62, 1, 1.844, -3.62, 1.922, -3.484, 2, -3.484, 1, 2.122, -3.484, 2.244, -3.768, 2.367, -3.768, 1, 2.422, -3.768, 2.478, -3.63, 2.533, -3.63, 1, 2.578, -3.63, 2.622, -3.798, 2.667, -3.798, 1, 2.733, -3.798, 2.8, -3.188, 2.867, -3.188, 1, 2.956, -3.188, 3.044, -4.244, 3.133, -4.244, 1, 3.289, -4.244, 3.444, 5.162, 3.6, 5.162, 1, 3.7, 5.162, 3.8, 0.253, 3.9, 0.253, 1, 4.022, 0.253, 4.144, 4.359, 4.267, 4.359, 1, 4.678, 4.359, 5.089, 0.388, 5.5, 0.388, 1, 5.511, 0.388, 5.522, 0.388, 5.533, 0.388, 1, 5.722, 0.388, 5.911, -0.235, 6.1, -0.235, 1, 6.222, -0.235, 6.344, -0.107, 6.467, -0.107, 1, 6.478, -0.107, 6.489, -0.107, 6.5, -0.108]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.211, 0, 0.389, 0.243, 0.567, 0.243, 1, 0.611, 0.243, 0.656, 0.221, 0.7, 0.221, 1, 0.822, 0.221, 0.944, 0.392, 1.067, 0.392, 1, 1.344, 0.392, 1.622, -0.015, 1.9, -0.015, 1, 2.022, -0.015, 2.144, 0.534, 2.267, 0.534, 1, 2.356, 0.534, 2.444, 0.226, 2.533, 0.226, 1, 2.567, 0.226, 2.6, 0.327, 2.633, 0.327, 1, 2.711, 0.327, 2.789, -0.513, 2.867, -0.513, 1, 2.922, -0.513, 2.978, -0.098, 3.033, -0.098, 1, 3.144, -0.098, 3.256, -4.467, 3.367, -4.467, 1, 3.511, -4.467, 3.656, 4.223, 3.8, 4.223, 1, 3.911, 4.223, 4.022, -3.013, 4.133, -3.013, 1, 4.256, -3.013, 4.378, 1.526, 4.5, 1.526, 1, 4.611, 1.526, 4.722, -0.162, 4.833, -0.162, 1, 4.944, -0.162, 5.056, 0.608, 5.167, 0.608, 1, 5.311, 0.608, 5.456, -0.039, 5.6, -0.039, 1, 5.678, -0.039, 5.756, 0.286, 5.833, 0.286, 1, 5.989, 0.286, 6.144, -0.098, 6.3, -0.098, 1, 6.367, -0.098, 6.433, -0.048, 6.5, -0.008]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 1, 0.589, 0, 1.178, -7.24, 1.767, -7.24, 1, 1.844, -7.24, 1.922, -6.968, 2, -6.968, 1, 2.122, -6.968, 2.244, -7.537, 2.367, -7.537, 1, 2.422, -7.537, 2.478, -7.26, 2.533, -7.26, 1, 2.578, -7.26, 2.622, -7.596, 2.667, -7.596, 1, 2.733, -7.596, 2.8, -6.377, 2.867, -6.377, 1, 2.956, -6.377, 3.044, -8.488, 3.133, -8.488, 1, 3.289, -8.488, 3.444, 10.324, 3.6, 10.324, 1, 3.7, 10.324, 3.8, 0.507, 3.9, 0.507, 1, 4.022, 0.507, 4.144, 8.719, 4.267, 8.719, 1, 4.678, 8.719, 5.089, 0.776, 5.5, 0.776, 1, 5.511, 0.776, 5.522, 0.777, 5.533, 0.777, 1, 5.722, 0.777, 5.911, -0.471, 6.1, -0.471, 1, 6.222, -0.471, 6.344, -0.213, 6.467, -0.213, 1, 6.478, -0.213, 6.489, -0.214, 6.5, -0.216]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.611, 0, 1.189, -1.351, 1.767, -1.351, 1, 1.878, -1.351, 1.989, -1.243, 2.1, -1.243, 1, 2.189, -1.243, 2.278, -1.279, 2.367, -1.279, 1, 2.411, -1.279, 2.456, -1.255, 2.5, -1.255, 1, 2.567, -1.255, 2.633, -1.376, 2.7, -1.376, 1, 2.756, -1.376, 2.811, -1.252, 2.867, -1.252, 1, 2.956, -1.252, 3.044, -1.907, 3.133, -1.907, 1, 3.311, -1.907, 3.489, 2.147, 3.667, 2.147, 1, 3.767, 2.147, 3.867, 0.379, 3.967, 0.379, 1, 4.089, 0.379, 4.211, 1.432, 4.333, 1.432, 1, 4.944, 1.432, 5.556, -0.077, 6.167, -0.077, 1, 6.278, -0.077, 6.389, -0.056, 6.5, -0.049]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0, 1, 0.156, 0, 0.311, 0.046, 0.467, 0.046, 1, 0.544, 0.046, 0.622, -0.01, 0.7, -0.01, 1, 0.822, -0.01, 0.944, 0.038, 1.067, 0.038, 1, 1.256, 0.038, 1.444, -0.007, 1.633, -0.007, 1, 1.656, -0.007, 1.678, -0.005, 1.7, -0.005, 1, 1.789, -0.005, 1.878, -0.107, 1.967, -0.107, 1, 2.067, -0.107, 2.167, -0.021, 2.267, -0.021, 1, 2.356, -0.021, 2.444, -0.121, 2.533, -0.121, 1, 2.589, -0.121, 2.644, -0.003, 2.7, -0.003, 1, 2.744, -0.003, 2.789, -0.099, 2.833, -0.099, 1, 2.922, -0.099, 3.011, 0.504, 3.1, 0.504, 1, 3.2, 0.504, 3.3, -0.787, 3.4, -0.787, 1, 3.544, -0.787, 3.689, 1.359, 3.833, 1.359, 1, 3.944, 1.359, 4.056, -1.147, 4.167, -1.147, 1, 4.278, -1.147, 4.389, 0.699, 4.5, 0.699, 1, 4.633, 0.699, 4.767, -0.289, 4.9, -0.289, 1, 5.022, -0.289, 5.144, 0.123, 5.267, 0.123, 1, 5.389, 0.123, 5.511, -0.056, 5.633, -0.056, 1, 5.756, -0.056, 5.878, 0.012, 6, 0.012, 1, 6.111, 0.012, 6.222, -0.027, 6.333, -0.027, 1, 6.389, -0.027, 6.444, -0.018, 6.5, -0.008]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.003, 1, 0.011, -0.003, 0.022, -0.003, 0.033, -0.003, 1, 0.222, -0.003, 0.411, 0.155, 0.6, 0.155, 1, 0.7, 0.155, 0.8, 0.023, 0.9, 0.023, 1, 1.011, 0.023, 1.122, 0.123, 1.233, 0.123, 1, 1.5, 0.123, 1.767, -0.039, 2.033, -0.039, 1, 2.156, -0.039, 2.278, 0.158, 2.4, 0.158, 1, 2.589, 0.158, 2.778, -0.22, 2.967, -0.22, 1, 3.033, -0.22, 3.1, -0.024, 3.167, -0.024, 1, 3.278, -0.024, 3.389, -1.502, 3.5, -1.502, 1, 3.644, -1.502, 3.789, 2.507, 3.933, 2.507, 1, 4.056, 2.507, 4.178, -2.505, 4.3, -2.505, 1, 4.422, -2.505, 4.544, 1.893, 4.667, 1.893, 1, 4.8, 1.893, 4.933, -0.937, 5.067, -0.937, 1, 5.189, -0.937, 5.311, 0.556, 5.433, 0.556, 1, 5.556, 0.556, 5.678, -0.225, 5.8, -0.225, 1, 5.922, -0.225, 6.044, 0.097, 6.167, 0.097, 1, 6.278, 0.097, 6.389, -0.068, 6.5, -0.068]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, 0.01, 1, 0.078, 0.01, 0.156, -0.017, 0.233, -0.017, 1, 0.4, -0.017, 0.567, 0.151, 0.733, 0.151, 1, 0.844, 0.151, 0.956, -0.041, 1.067, -0.041, 1, 1.189, -0.041, 1.311, 0.13, 1.433, 0.13, 1, 1.678, 0.13, 1.922, -0.083, 2.167, -0.083, 1, 2.3, -0.083, 2.433, 0.156, 2.567, 0.156, 1, 2.711, 0.156, 2.856, -0.113, 3, -0.113, 1, 3.1, -0.113, 3.2, 0.267, 3.3, 0.267, 1, 3.422, 0.267, 3.544, -1.878, 3.667, -1.878, 1, 3.8, -1.878, 3.933, 3.34, 4.067, 3.34, 1, 4.2, 3.34, 4.333, -3.726, 4.467, -3.726, 1, 4.589, -3.726, 4.711, 3.204, 4.833, 3.204, 1, 4.956, 3.204, 5.078, -2.064, 5.2, -2.064, 1, 5.333, -2.064, 5.467, 1.282, 5.6, 1.282, 1, 5.722, 1.282, 5.844, -0.693, 5.967, -0.693, 1, 6.089, -0.693, 6.211, 0.34, 6.333, 0.34, 1, 6.389, 0.34, 6.444, 0.232, 6.5, 0.114]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.611, 0, 1.189, -1.351, 1.767, -1.351, 1, 1.878, -1.351, 1.989, -1.243, 2.1, -1.243, 1, 2.189, -1.243, 2.278, -1.279, 2.367, -1.279, 1, 2.411, -1.279, 2.456, -1.255, 2.5, -1.255, 1, 2.567, -1.255, 2.633, -1.376, 2.7, -1.376, 1, 2.756, -1.376, 2.811, -1.252, 2.867, -1.252, 1, 2.956, -1.252, 3.044, -1.907, 3.133, -1.907, 1, 3.311, -1.907, 3.489, 2.147, 3.667, 2.147, 1, 3.767, 2.147, 3.867, 0.379, 3.967, 0.379, 1, 4.089, 0.379, 4.211, 1.432, 4.333, 1.432, 1, 4.944, 1.432, 5.556, -0.077, 6.167, -0.077, 1, 6.278, -0.077, 6.389, -0.056, 6.5, -0.049]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0, 1, 0.156, 0, 0.311, 0.046, 0.467, 0.046, 1, 0.544, 0.046, 0.622, -0.01, 0.7, -0.01, 1, 0.822, -0.01, 0.944, 0.038, 1.067, 0.038, 1, 1.256, 0.038, 1.444, -0.007, 1.633, -0.007, 1, 1.656, -0.007, 1.678, -0.005, 1.7, -0.005, 1, 1.789, -0.005, 1.878, -0.107, 1.967, -0.107, 1, 2.067, -0.107, 2.167, -0.021, 2.267, -0.021, 1, 2.356, -0.021, 2.444, -0.121, 2.533, -0.121, 1, 2.589, -0.121, 2.644, -0.003, 2.7, -0.003, 1, 2.744, -0.003, 2.789, -0.099, 2.833, -0.099, 1, 2.922, -0.099, 3.011, 0.504, 3.1, 0.504, 1, 3.2, 0.504, 3.3, -0.787, 3.4, -0.787, 1, 3.544, -0.787, 3.689, 1.359, 3.833, 1.359, 1, 3.944, 1.359, 4.056, -1.147, 4.167, -1.147, 1, 4.278, -1.147, 4.389, 0.699, 4.5, 0.699, 1, 4.633, 0.699, 4.767, -0.289, 4.9, -0.289, 1, 5.022, -0.289, 5.144, 0.123, 5.267, 0.123, 1, 5.389, 0.123, 5.511, -0.056, 5.633, -0.056, 1, 5.756, -0.056, 5.878, 0.012, 6, 0.012, 1, 6.111, 0.012, 6.222, -0.027, 6.333, -0.027, 1, 6.389, -0.027, 6.444, -0.018, 6.5, -0.008]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.003, 1, 0.011, -0.003, 0.022, -0.003, 0.033, -0.003, 1, 0.222, -0.003, 0.411, 0.155, 0.6, 0.155, 1, 0.7, 0.155, 0.8, 0.023, 0.9, 0.023, 1, 1.011, 0.023, 1.122, 0.123, 1.233, 0.123, 1, 1.5, 0.123, 1.767, -0.039, 2.033, -0.039, 1, 2.156, -0.039, 2.278, 0.158, 2.4, 0.158, 1, 2.589, 0.158, 2.778, -0.22, 2.967, -0.22, 1, 3.033, -0.22, 3.1, -0.024, 3.167, -0.024, 1, 3.278, -0.024, 3.389, -1.502, 3.5, -1.502, 1, 3.644, -1.502, 3.789, 2.507, 3.933, 2.507, 1, 4.056, 2.507, 4.178, -2.505, 4.3, -2.505, 1, 4.422, -2.505, 4.544, 1.893, 4.667, 1.893, 1, 4.8, 1.893, 4.933, -0.937, 5.067, -0.937, 1, 5.189, -0.937, 5.311, 0.556, 5.433, 0.556, 1, 5.556, 0.556, 5.678, -0.225, 5.8, -0.225, 1, 5.922, -0.225, 6.044, 0.097, 6.167, 0.097, 1, 6.278, 0.097, 6.389, -0.068, 6.5, -0.068]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0.01, 1, 0.078, 0.01, 0.156, -0.017, 0.233, -0.017, 1, 0.4, -0.017, 0.567, 0.151, 0.733, 0.151, 1, 0.844, 0.151, 0.956, -0.041, 1.067, -0.041, 1, 1.189, -0.041, 1.311, 0.13, 1.433, 0.13, 1, 1.678, 0.13, 1.922, -0.083, 2.167, -0.083, 1, 2.3, -0.083, 2.433, 0.156, 2.567, 0.156, 1, 2.711, 0.156, 2.856, -0.113, 3, -0.113, 1, 3.1, -0.113, 3.2, 0.267, 3.3, 0.267, 1, 3.422, 0.267, 3.544, -1.878, 3.667, -1.878, 1, 3.8, -1.878, 3.933, 3.34, 4.067, 3.34, 1, 4.2, 3.34, 4.333, -3.726, 4.467, -3.726, 1, 4.589, -3.726, 4.711, 3.204, 4.833, 3.204, 1, 4.956, 3.204, 5.078, -2.064, 5.2, -2.064, 1, 5.333, -2.064, 5.467, 1.282, 5.6, 1.282, 1, 5.722, 1.282, 5.844, -0.693, 5.967, -0.693, 1, 6.089, -0.693, 6.211, 0.34, 6.333, 0.34, 1, 6.389, 0.34, 6.444, 0.232, 6.5, 0.114]}]}