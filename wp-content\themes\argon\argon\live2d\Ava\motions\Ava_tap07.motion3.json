{"Version": 3, "Meta": {"Duration": 7.2, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 112, "TotalSegmentCount": 1330, "TotalPointCount": 3952, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 1, 1, 0.133, 1, 0.267, -12, 0.4, -12, 1, 0.478, -12, 0.556, -12, 0.633, -12, 1, 0.778, -12, 0.922, -12, 1.067, -12, 1, 1.189, -12, 1.311, -11, 1.433, -11, 1, 1.578, -11, 1.722, -12, 1.867, -12, 1, 2.011, -12, 2.156, -12, 2.3, -12, 1, 2.6, -12, 2.9, -12, 3.2, -12, 1, 3.378, -12, 3.556, -12, 3.733, -12, 1, 3.911, -12, 4.089, -12, 4.267, -12, 1, 4.422, -12, 4.578, -12, 4.733, -12, 1, 4.922, -12, 5.111, -12, 5.3, -12, 1, 5.511, -12, 5.722, -14, 5.933, -14, 0, 7.2, -14]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 4, 1, 0.133, 4, 0.267, 29, 0.4, 29, 1, 0.478, 29, 0.556, 29, 0.633, 29, 1, 0.778, 29, 0.922, 24.109, 1.067, 14, 1, 1.189, 5.446, 1.311, 1, 1.433, 1, 1, 1.578, 1, 1.722, 12, 1.867, 12, 1, 2.011, 12, 2.156, 2, 2.3, 2, 1, 2.6, 2, 2.9, 2, 3.2, 2, 1, 3.378, 2, 3.556, 26, 3.733, 26, 1, 3.911, 26, 4.089, 8, 4.267, 8, 1, 4.422, 8, 4.578, 18, 4.733, 18, 1, 4.922, 18, 5.111, 1, 5.3, 1, 1, 5.511, 1, 5.722, 18, 5.933, 18, 0, 7.2, 18]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -1, 1, 0.133, -1, 0.267, 1, 0.4, 1, 1, 0.544, 1, 0.689, -5, 0.833, -5, 0, 7.2, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -1, 1, 0.133, -1, 0.267, -6, 0.4, -6, 1, 0.478, -6, 0.556, -6, 0.633, -6, 1, 1.322, -6, 2.011, -6, 2.7, -6, 1, 2.922, -6, 3.144, 3, 3.367, 3, 0, 7.2, 3]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 0.4, 0, 1, 0.478, 0, 0.556, 0, 0.633, 0, 1, 0.778, 0, 0.922, -10, 1.067, -10, 1, 1.256, -10, 1.444, -8, 1.633, -8, 1, 2, -8, 2.367, -8, 2.733, -8, 1, 2.956, -8, 3.178, 2, 3.4, 2, 1, 3.978, 2, 4.556, 1, 5.133, 1, 0, 7.2, 1]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.722, 0, 0.744, 30, 0.767, 30, 1, 0.8, 30, 0.833, 30, 0.867, 30, 1, 0.9, 30, 0.933, -30, 0.967, -30, 1, 1, -30, 1.033, -30, 1.067, -30, 1, 1.1, -30, 1.133, 30, 1.167, 30, 1, 1.178, 30, 1.189, 30, 1.2, 30, 1, 1.278, 30, 1.356, -12.253, 1.433, -12.253, 1, 1.511, -12.253, 1.589, 4.76, 1.667, 4.76, 1, 1.744, 4.76, 1.822, -1.863, 1.9, -1.863, 1, 1.967, -1.863, 2.033, 0.749, 2.1, 0.749, 1, 2.178, 0.749, 2.256, -0.3, 2.333, -0.3, 1, 2.411, -0.3, 2.489, 0.12, 2.567, 0.12, 1, 2.644, 0.12, 2.722, -0.048, 2.8, -0.048, 1, 2.878, -0.048, 2.956, 0.019, 3.033, 0.019, 1, 3.111, 0.019, 3.189, -0.007, 3.267, -0.007, 1, 3.389, -0.007, 3.511, 30, 3.633, 30, 1, 3.656, 30, 3.678, 30, 3.7, 30, 1, 3.756, 30, 3.811, -30, 3.867, -30, 1, 3.889, -30, 3.911, -30, 3.933, -30, 1, 4, -30, 4.067, 12.824, 4.133, 12.824, 1, 4.211, 12.824, 4.289, -4.975, 4.367, -4.975, 1, 4.444, -4.975, 4.522, 1.947, 4.6, 1.947, 1, 4.667, 1.947, 4.733, -0.783, 4.8, -0.783, 1, 4.878, -0.783, 4.956, 0.314, 5.033, 0.314, 1, 5.122, 0.314, 5.211, -27.472, 5.3, -27.472, 1, 5.389, -27.472, 5.478, 29.977, 5.567, 29.977, 1, 5.644, 29.977, 5.722, -10.988, 5.8, -10.988, 1, 5.878, -10.988, 5.956, 4.32, 6.033, 4.32, 1, 6.111, 4.32, 6.189, -1.709, 6.267, -1.709, 1, 6.344, -1.709, 6.422, 0.674, 6.5, 0.674, 1, 6.578, 0.674, 6.656, -0.265, 6.733, -0.265, 1, 6.8, -0.265, 6.867, 0.105, 6.933, 0.105, 1, 7.011, 0.105, 7.089, -0.042, 7.167, -0.042, 1, 7.178, -0.042, 7.189, -0.041, 7.2, -0.039]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.011, 1, 0.022, 1, 0.033, 1, 1, 0.244, 1, 0.456, 1, 0.667, 1, 1, 0.733, 1, 0.8, 0, 0.867, 0, 1, 0.922, 0, 0.978, 1, 1.033, 1, 1, 1.844, 1, 2.656, 1, 3.467, 1, 1, 3.578, 1, 3.689, 0, 3.8, 0, 1, 4.233, 0, 4.667, 0, 5.1, 0, 1, 5.233, 0, 5.367, 1, 5.5, 1, 0, 7.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 0.033, 0, 1, 1.178, 0, 2.322, 0, 3.467, 0, 1, 3.578, 0, 3.689, 0.5, 3.8, 0.5, 1, 4.233, 0.5, 4.667, 0.5, 5.1, 0.5, 0, 7.2, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.011, 1, 0.022, 1, 0.033, 1, 1, 0.244, 1, 0.456, 1, 0.667, 1, 1, 0.733, 1, 0.8, 0, 0.867, 0, 1, 0.922, 0, 0.978, 1, 1.033, 1, 1, 1.844, 1, 2.656, 1, 3.467, 1, 1, 3.578, 1, 3.689, 0, 3.8, 0, 1, 4.233, 0, 4.667, 0, 5.1, 0, 1, 5.233, 0, 5.367, 1, 5.5, 1, 0, 7.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 0.033, 0, 1, 1.178, 0, 2.322, 0, 3.467, 0, 1, 3.578, 0, 3.689, 0.5, 3.8, 0.5, 1, 4.233, 0.5, 4.667, 0.5, 5.1, 0.5, 0, 7.2, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 0.033, 0, 1, 0.133, 0, 0.233, 0.5, 0.333, 0.5, 1, 0.422, 0.5, 0.511, -0.2, 0.6, -0.2, 1, 0.733, -0.2, 0.867, 0.7, 1, 0.7, 1, 1.6, 0.7, 2.2, 0.679, 2.8, 0.6, 1, 2.911, 0.585, 3.022, -0.1, 3.133, -0.1, 1, 3.267, -0.1, 3.4, 0.8, 3.533, 0.8, 1, 3.811, 0.8, 4.089, 0.807, 4.367, 0.6, 1, 4.633, 0.401, 4.9, -0.1, 5.167, -0.1, 1, 5.233, -0.1, 5.3, 0.1, 5.367, 0.1, 1, 5.444, 0.1, 5.522, -0.1, 5.6, -0.1, 1, 5.667, -0.1, 5.733, 0.1, 5.8, 0.1, 1, 5.878, 0.1, 5.956, -0.1, 6.033, -0.1, 1, 6.1, -0.1, 6.167, 0.1, 6.233, 0.1, 1, 6.322, 0.1, 6.411, -0.1, 6.5, -0.1, 1, 6.567, -0.1, 6.633, 0.1, 6.7, 0.1, 0, 7.2, 0.1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 0.033, 0, 1, 0.133, 0, 0.233, -0.5, 0.333, -0.5, 1, 0.422, -0.5, 0.511, 0, 0.6, 0, 1, 0.733, 0, 0.867, -0.3, 1, -0.3, 1, 1.6, -0.3, 2.2, -0.3, 2.8, -0.3, 1, 2.911, -0.3, 3.022, 0, 3.133, 0, 1, 3.267, 0, 3.4, -0.4, 3.533, -0.4, 1, 3.811, -0.4, 4.089, -0.379, 4.367, -0.2, 1, 4.633, -0.028, 4.9, 0.2, 5.167, 0.2, 1, 5.233, 0.2, 5.3, 0.2, 5.367, 0.2, 1, 5.444, 0.2, 5.522, 0.2, 5.6, 0.2, 1, 5.667, 0.2, 5.733, 0.2, 5.8, 0.2, 1, 5.878, 0.2, 5.956, 0.2, 6.033, 0.2, 1, 6.1, 0.2, 6.167, 0.2, 6.233, 0.2, 1, 6.322, 0.2, 6.411, 0.2, 6.5, 0.2, 1, 6.567, 0.2, 6.633, 0.2, 6.7, 0.2, 0, 7.2, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0.4, 1, 0.689, 0.4, 1.378, 0.4, 2.067, 0.4, 1, 2.211, 0.4, 2.356, 0.303, 2.5, 0, 1, 2.7, -0.42, 2.9, -0.7, 3.1, -0.7, 0, 7.2, -0.7]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0.4, 1, 0.689, 0.4, 1.378, 0.4, 2.067, 0.4, 1, 2.211, 0.4, 2.356, 0.303, 2.5, 0, 1, 2.7, -0.42, 2.9, -0.7, 3.1, -0.7, 0, 7.2, -0.7]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.3, 0, 0.033, -0.3, 1, 1.178, -0.3, 2.322, -0.3, 3.467, -0.3, 1, 3.578, -0.3, 3.689, -1, 3.8, -1, 1, 4.156, -1, 4.511, -1, 4.867, -1, 1, 5.011, -1, 5.156, -0.1, 5.3, -0.1, 0, 7.2, -0.1]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.3, 0, 0.033, -0.3, 1, 1.178, -0.3, 2.322, -0.3, 3.467, -0.3, 1, 3.578, -0.3, 3.689, -1, 3.8, -1, 1, 4.156, -1, 4.511, -1, 4.867, -1, 1, 5.011, -1, 5.156, 0, 5.3, 0, 0, 7.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 0.033, 0, 1, 0.133, 0, 0.233, -0.8, 0.333, -0.8, 1, 1.178, -0.8, 2.022, -0.7, 2.867, -0.7, 0, 7.2, -0.7]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 0.033, 0, 1, 0.133, 0, 0.233, -0.9, 0.333, -0.9, 1, 1.178, -0.9, 2.022, -0.7, 2.867, -0.7, 0, 7.2, -0.7]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.8, 0, 0.4, -0.8, 1, 0.478, -0.8, 0.556, -0.8, 0.633, -0.8, 1, 0.778, -0.8, 0.922, -0.8, 1.067, -0.8, 1, 1.189, -0.8, 1.311, -0.4, 1.433, -0.4, 1, 1.556, -0.4, 1.678, -0.5, 1.8, -0.5, 1, 1.944, -0.5, 2.089, -0.5, 2.233, -0.5, 1, 2.278, -0.5, 2.322, -0.7, 2.367, -0.7, 1, 2.711, -0.7, 3.056, -0.6, 3.4, -0.6, 1, 3.467, -0.6, 3.533, -0.8, 3.6, -0.8, 1, 3.678, -0.8, 3.756, -0.4, 3.833, -0.4, 1, 3.922, -0.4, 4.011, -0.5, 4.1, -0.5, 1, 4.189, -0.5, 4.278, -0.5, 4.367, -0.5, 1, 4.467, -0.5, 4.567, -0.3, 4.667, -0.3, 1, 4.767, -0.3, 4.867, -1, 4.967, -1, 1, 5.078, -1, 5.189, -0.991, 5.3, -0.9, 1, 5.389, -0.827, 5.478, -0.7, 5.567, -0.7, 0, 7.2, -0.7]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.4, 0, 1, 0.478, 0, 0.556, 0, 0.633, 0, 1, 0.778, 0, 0.922, 0.461, 1.067, 0.6, 1, 1.189, 0.718, 1.311, 0.7, 1.433, 0.7, 1, 1.556, 0.7, 1.678, 0.3, 1.8, 0.3, 1, 1.944, 0.3, 2.089, 0.3, 2.233, 0.3, 1, 2.278, 0.3, 2.322, 0, 2.367, 0, 1, 2.711, 0, 3.056, 0, 3.4, 0, 1, 3.467, 0, 3.533, 0.506, 3.6, 0.6, 1, 3.678, 0.71, 3.756, 0.7, 3.833, 0.7, 1, 3.922, 0.7, 4.011, 0.3, 4.1, 0.3, 1, 4.189, 0.3, 4.278, 0.3, 4.367, 0.3, 1, 4.467, 0.3, 4.567, 0.2, 4.667, 0.2, 1, 4.767, 0.2, 4.867, 0.8, 4.967, 0.8, 1, 5.078, 0.8, 5.189, 0.693, 5.3, 0.4, 1, 5.389, 0.166, 5.478, 0, 5.567, 0, 0, 7.2, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 1, 1, 0.122, 1, 0.244, 11.489, 0.367, 12, 1, 0.6, 12.976, 0.833, 13, 1.067, 13, 1, 1.189, 13, 1.311, 8, 1.433, 8, 1, 1.578, 8, 1.722, 20, 1.867, 20, 1, 2.011, 20, 2.156, 14.399, 2.3, 14, 1, 2.633, 13.079, 2.967, 13.097, 3.3, 12, 1, 3.544, 11.196, 3.789, -1, 4.033, -1, 1, 4.4, -1, 4.767, 14, 5.133, 14, 0, 7.2, 14]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 1, 0.122, 0, 0.244, 13, 0.367, 13, 1, 0.6, 13, 0.833, 12.926, 1.067, 12, 1, 1.189, 11.515, 1.311, 9, 1.433, 9, 1, 1.578, 9, 1.722, 19, 1.867, 19, 1, 2.011, 19, 2.156, 12, 2.3, 12, 1, 2.633, 12, 2.967, 13, 3.3, 13, 1, 3.544, 13, 3.789, 1, 4.033, 1, 1, 4.4, 1, 4.767, 14, 5.133, 14, 0, 7.2, 14]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 3.743, 0.3, 3.743, 1, 0.4, 3.743, 0.5, -1.556, 0.6, -1.556, 1, 0.667, -1.556, 0.733, -0.54, 0.8, -0.54, 1, 0.922, -0.54, 1.044, -3.742, 1.167, -3.742, 1, 1.322, -3.742, 1.478, -2.362, 1.633, -2.362, 1, 1.756, -2.362, 1.878, -2.544, 2, -2.544, 1, 2.122, -2.544, 2.244, -2.507, 2.367, -2.507, 1, 2.578, -2.507, 2.789, -5.447, 3, -5.447, 1, 3.167, -5.447, 3.333, 2.408, 3.5, 2.408, 1, 3.622, 2.408, 3.744, 0.22, 3.867, 0.22, 1, 3.978, 0.22, 4.089, 0.571, 4.2, 0.571, 1, 4.533, 0.571, 4.867, 0.309, 5.2, 0.309, 1, 5.311, 0.309, 5.422, 0.315, 5.533, 0.315, 1, 5.656, 0.315, 5.778, 0.314, 5.9, 0.314, 1, 6.022, 0.314, 6.144, 0.314, 6.267, 0.314, 1, 6.378, 0.314, 6.489, 0.314, 6.6, 0.314, 1, 6.622, 0.314, 6.644, 0.314, 6.667, 0.314, 1, 6.678, 0.314, 6.689, 0.314, 6.7, 0.314, 1, 6.711, 0.314, 6.722, 0.314, 6.733, 0.314, 1, 6.756, 0.314, 6.778, 0.314, 6.8, 0.314, 1, 6.811, 0.314, 6.822, 0.314, 6.833, 0.314, 1, 6.844, 0.314, 6.856, 0.314, 6.867, 0.314, 1, 6.878, 0.314, 6.889, 0.314, 6.9, 0.314, 1, 6.911, 0.314, 6.922, 0.314, 6.933, 0.314, 1, 6.967, 0.314, 7, 0.314, 7.033, 0.314, 1, 7.044, 0.314, 7.056, 0.314, 7.067, 0.314, 1, 7.1, 0.314, 7.133, 0.314, 7.167, 0.314, 1, 7.178, 0.314, 7.189, 0.314, 7.2, 0.314]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0.002, 1, 0.011, 0.002, 0.022, -0.049, 0.033, -0.049, 1, 0.111, -0.049, 0.189, 7.066, 0.267, 7.066, 1, 0.356, 7.066, 0.444, -8.019, 0.533, -8.019, 1, 0.622, -8.019, 0.711, 1.823, 0.8, 1.823, 1, 0.911, 1.823, 1.022, -7.146, 1.133, -7.146, 1, 1.256, -7.146, 1.378, -2.35, 1.5, -2.35, 1, 1.622, -2.35, 1.744, -4.228, 1.867, -4.228, 1, 1.978, -4.228, 2.089, -3.408, 2.2, -3.408, 1, 2.3, -3.408, 2.4, -3.782, 2.5, -3.782, 1, 2.578, -3.782, 2.656, -3.635, 2.733, -3.635, 1, 2.811, -3.635, 2.889, -5.85, 2.967, -5.85, 1, 3.122, -5.85, 3.278, 2.933, 3.433, 2.933, 1, 3.533, 2.933, 3.633, -1.626, 3.733, -1.626, 1, 3.833, -1.626, 3.933, 0.366, 4.033, 0.366, 1, 4.144, 0.366, 4.256, -0.674, 4.367, -0.674, 1, 4.456, -0.674, 4.544, -0.328, 4.633, -0.328, 1, 4.756, -0.328, 4.878, -0.583, 5, -0.583, 1, 5.1, -0.583, 5.2, -0.497, 5.3, -0.497, 1, 5.311, -0.497, 5.322, -0.499, 5.333, -0.499, 1, 5.422, -0.499, 5.511, -0.197, 5.6, -0.197, 1, 5.733, -0.197, 5.867, -0.854, 6, -0.854, 1, 6.1, -0.854, 6.2, -0.373, 6.3, -0.373, 1, 6.411, -0.373, 6.522, -0.592, 6.633, -0.592, 1, 6.733, -0.592, 6.833, -0.492, 6.933, -0.492, 1, 7.022, -0.492, 7.111, -0.528, 7.2, -0.536]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, -16, 0, 4.433, -16, 1, 4.511, -16, 4.589, 15, 4.667, 15, 1, 4.744, 15, 4.822, -16, 4.9, -16, 1, 4.978, -16, 5.056, 15, 5.133, 15, 1, 5.211, 15, 5.289, -16, 5.367, -16, 1, 5.433, -16, 5.5, 15, 5.567, 15, 1, 5.656, 15, 5.744, -16, 5.833, -16, 1, 5.911, -16, 5.989, 15, 6.067, 15, 1, 6.144, 15, 6.222, -16, 6.3, -16, 1, 6.378, -16, 6.456, 15, 6.533, 15, 1, 6.611, 15, 6.689, -16, 6.767, -16, 1, 6.833, -16, 6.9, 15, 6.967, 15, 1, 7.044, 15, 7.122, 15, 7.2, 15]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, -30, 0, 3.9, -30, 1, 4.211, -30, 4.522, 30, 4.833, 30, 0, 7.2, 30]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 1, 0.089, 0, 0.178, 1.263, 0.267, 1.263, 1, 0.356, 1.263, 0.444, -0.359, 0.533, -0.359, 1, 0.6, -0.359, 0.667, 0.51, 0.733, 0.51, 1, 0.878, 0.51, 1.022, -5.02, 1.167, -5.02, 1, 1.322, -5.02, 1.478, -3.225, 1.633, -3.225, 1, 1.7, -3.225, 1.767, -3.419, 1.833, -3.419, 1, 1.944, -3.419, 2.056, -2.912, 2.167, -2.912, 1, 2.278, -2.912, 2.389, -3.605, 2.5, -3.605, 1, 2.8, -3.605, 3.1, 0.924, 3.4, 0.924, 1, 3.478, 0.924, 3.556, 0.407, 3.633, 0.407, 1, 3.756, 0.407, 3.878, 1.497, 4, 1.497, 1, 4.167, 1.497, 4.333, -0.046, 4.5, -0.046, 1, 4.667, -0.046, 4.833, 1.029, 5, 1.029, 1, 5.156, 1.029, 5.311, 0.041, 5.467, 0.041, 1, 5.656, 0.041, 5.844, 0.516, 6.033, 0.516, 1, 6.156, 0.516, 6.278, 0.391, 6.4, 0.391, 1, 6.522, 0.391, 6.644, 0.427, 6.767, 0.427, 1, 6.889, 0.427, 7.011, 0.417, 7.133, 0.417, 1, 7.156, 0.417, 7.178, 0.417, 7.2, 0.417]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.089, 0, 0.178, 1.263, 0.267, 1.263, 1, 0.356, 1.263, 0.444, -0.359, 0.533, -0.359, 1, 0.6, -0.359, 0.667, 0.51, 0.733, 0.51, 1, 0.878, 0.51, 1.022, -5.02, 1.167, -5.02, 1, 1.322, -5.02, 1.478, -3.225, 1.633, -3.225, 1, 1.7, -3.225, 1.767, -3.419, 1.833, -3.419, 1, 1.944, -3.419, 2.056, -2.912, 2.167, -2.912, 1, 2.278, -2.912, 2.389, -3.605, 2.5, -3.605, 1, 2.8, -3.605, 3.1, 0.924, 3.4, 0.924, 1, 3.478, 0.924, 3.556, 0.407, 3.633, 0.407, 1, 3.756, 0.407, 3.878, 1.497, 4, 1.497, 1, 4.167, 1.497, 4.333, -0.046, 4.5, -0.046, 1, 4.667, -0.046, 4.833, 1.029, 5, 1.029, 1, 5.156, 1.029, 5.311, 0.041, 5.467, 0.041, 1, 5.656, 0.041, 5.844, 0.516, 6.033, 0.516, 1, 6.156, 0.516, 6.278, 0.391, 6.4, 0.391, 1, 6.522, 0.391, 6.644, 0.427, 6.767, 0.427, 1, 6.889, 0.427, 7.011, 0.417, 7.133, 0.417, 1, 7.156, 0.417, 7.178, 0.417, 7.2, 0.417]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0.235, 1, 0.078, 1.276, 0.156, 5.443, 0.233, 5.443, 1, 0.322, 5.443, 0.411, -6.316, 0.5, -6.316, 1, 0.589, -6.316, 0.678, 2.241, 0.767, 2.241, 1, 0.878, 2.241, 0.989, -7.999, 1.1, -7.999, 1, 1.2, -7.999, 1.3, -3.427, 1.4, -3.427, 1, 1.489, -3.427, 1.578, -4.549, 1.667, -4.549, 1, 1.767, -4.549, 1.867, -4.028, 1.967, -4.028, 1, 2.056, -4.028, 2.144, -4.259, 2.233, -4.259, 1, 2.333, -4.259, 2.433, -4.157, 2.533, -4.157, 1, 2.667, -4.157, 2.8, -8.151, 2.933, -8.151, 1, 3.1, -8.151, 3.267, 5.46, 3.433, 5.46, 1, 3.522, 5.46, 3.611, -0.945, 3.7, -0.945, 1, 3.8, -0.945, 3.9, 1.768, 4, 1.768, 1, 4.1, 1.768, 4.2, 0.402, 4.3, 0.402, 1, 4.378, 0.402, 4.456, 0.83, 4.533, 0.83, 1, 4.644, 0.83, 4.756, 0.479, 4.867, 0.479, 1, 4.956, 0.479, 5.044, 0.548, 5.133, 0.548, 1, 5.222, 0.548, 5.311, 0.512, 5.4, 0.512, 1, 5.5, 0.512, 5.6, 0.528, 5.7, 0.528, 1, 5.789, 0.528, 5.878, 0.521, 5.967, 0.521, 1, 6.067, 0.521, 6.167, 0.525, 6.267, 0.525, 1, 6.356, 0.525, 6.444, 0.523, 6.533, 0.523, 1, 6.633, 0.523, 6.733, 0.524, 6.833, 0.524, 1, 6.922, 0.524, 7.011, 0.524, 7.1, 0.524, 1, 7.111, 0.524, 7.122, 0.524, 7.133, 0.524, 1, 7.156, 0.524, 7.178, 0.524, 7.2, 0.524]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, -0.001, 1, 0.011, 0, 0.022, 0.002, 0.033, 0.002, 1, 0.078, 0.002, 0.122, -0.973, 0.167, -0.973, 1, 0.256, -0.973, 0.344, 2.389, 0.433, 2.389, 1, 0.511, 2.389, 0.589, -3.253, 0.667, -3.253, 1, 0.756, -3.253, 0.844, 3.132, 0.933, 3.132, 1, 1.022, 3.132, 1.111, -2.519, 1.2, -2.519, 1, 1.3, -2.519, 1.4, 1.611, 1.5, 1.611, 1, 1.6, 1.611, 1.7, -0.868, 1.8, -0.868, 1, 1.889, -0.868, 1.978, 0.461, 2.067, 0.461, 1, 2.167, 0.461, 2.267, -0.235, 2.367, -0.235, 1, 2.456, -0.235, 2.544, 0.119, 2.633, 0.119, 1, 2.667, 0.119, 2.7, 0.066, 2.733, 0.066, 1, 2.778, 0.066, 2.822, 0.649, 2.867, 0.649, 1, 2.956, 0.649, 3.044, -1.625, 3.133, -1.625, 1, 3.256, -1.625, 3.378, 1.365, 3.5, 1.365, 1, 3.6, 1.365, 3.7, -1.468, 3.8, -1.468, 1, 3.9, -1.468, 4, 1.044, 4.1, 1.044, 1, 4.189, 1.044, 4.278, -0.624, 4.367, -0.624, 1, 4.467, -0.624, 4.567, 0.358, 4.667, 0.358, 1, 4.756, 0.358, 4.844, -0.19, 4.933, -0.19, 1, 5.033, -0.19, 5.133, 0.097, 5.233, 0.097, 1, 5.333, 0.097, 5.433, -0.047, 5.533, -0.047, 1, 5.622, -0.047, 5.711, 0.024, 5.8, 0.024, 1, 5.9, 0.024, 6, -0.011, 6.1, -0.011, 1, 6.189, -0.011, 6.278, 0.005, 6.367, 0.005, 1, 6.467, 0.005, 6.567, -0.003, 6.667, -0.003, 1, 6.756, -0.003, 6.844, 0.001, 6.933, 0.001, 1, 7.022, 0.001, 7.111, 0, 7.2, -0.001]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, -0.001, 1, 0.011, 0, 0.022, 0.002, 0.033, 0.002, 1, 0.078, 0.002, 0.122, -0.973, 0.167, -0.973, 1, 0.256, -0.973, 0.344, 2.389, 0.433, 2.389, 1, 0.511, 2.389, 0.589, -3.253, 0.667, -3.253, 1, 0.756, -3.253, 0.844, 3.132, 0.933, 3.132, 1, 1.022, 3.132, 1.111, -2.519, 1.2, -2.519, 1, 1.3, -2.519, 1.4, 1.611, 1.5, 1.611, 1, 1.6, 1.611, 1.7, -0.868, 1.8, -0.868, 1, 1.889, -0.868, 1.978, 0.461, 2.067, 0.461, 1, 2.167, 0.461, 2.267, -0.235, 2.367, -0.235, 1, 2.456, -0.235, 2.544, 0.119, 2.633, 0.119, 1, 2.667, 0.119, 2.7, 0.066, 2.733, 0.066, 1, 2.778, 0.066, 2.822, 0.649, 2.867, 0.649, 1, 2.956, 0.649, 3.044, -1.625, 3.133, -1.625, 1, 3.256, -1.625, 3.378, 1.365, 3.5, 1.365, 1, 3.6, 1.365, 3.7, -1.468, 3.8, -1.468, 1, 3.9, -1.468, 4, 1.044, 4.1, 1.044, 1, 4.189, 1.044, 4.278, -0.624, 4.367, -0.624, 1, 4.467, -0.624, 4.567, 0.358, 4.667, 0.358, 1, 4.756, 0.358, 4.844, -0.19, 4.933, -0.19, 1, 5.033, -0.19, 5.133, 0.097, 5.233, 0.097, 1, 5.333, 0.097, 5.433, -0.047, 5.533, -0.047, 1, 5.622, -0.047, 5.711, 0.024, 5.8, 0.024, 1, 5.9, 0.024, 6, -0.011, 6.1, -0.011, 1, 6.189, -0.011, 6.278, 0.005, 6.367, 0.005, 1, 6.467, 0.005, 6.567, -0.003, 6.667, -0.003, 1, 6.756, -0.003, 6.844, 0.001, 6.933, 0.001, 1, 7.022, 0.001, 7.111, 0, 7.2, -0.001]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.017, 0.033, -0.017, 1, 0.111, -0.017, 0.189, 2.606, 0.267, 2.606, 1, 0.356, 2.606, 0.444, -2.934, 0.533, -2.934, 1, 0.633, -2.934, 0.733, 0.633, 0.833, 0.633, 1, 0.944, 0.633, 1.056, -2.592, 1.167, -2.592, 1, 1.289, -2.592, 1.411, -0.81, 1.533, -0.81, 1, 1.656, -0.81, 1.778, -1.551, 1.9, -1.551, 1, 2, -1.551, 2.1, -1.214, 2.2, -1.214, 1, 2.311, -1.214, 2.422, -1.368, 2.533, -1.368, 1, 2.6, -1.368, 2.667, -1.311, 2.733, -1.311, 1, 2.811, -1.311, 2.889, -2.125, 2.967, -2.125, 1, 3.122, -2.125, 3.278, 1.106, 3.433, 1.106, 1, 3.533, 1.106, 3.633, -0.609, 3.733, -0.609, 1, 3.833, -0.609, 3.933, 0.14, 4.033, 0.14, 1, 4.144, 0.14, 4.256, -0.25, 4.367, -0.25, 1, 4.467, -0.25, 4.567, -0.117, 4.667, -0.117, 1, 4.778, -0.117, 4.889, -0.212, 5, -0.212, 1, 5.2, -0.212, 5.4, -0.068, 5.6, -0.068, 1, 5.733, -0.068, 5.867, -0.312, 6, -0.312, 1, 6.1, -0.312, 6.2, -0.133, 6.3, -0.133, 1, 6.411, -0.133, 6.522, -0.214, 6.633, -0.214, 1, 6.733, -0.214, 6.833, -0.177, 6.933, -0.177, 1, 7.022, -0.177, 7.111, -0.188, 7.2, -0.192]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, -0.014, 1, 0.011, -0.014, 0.022, 0.003, 0.033, 0.003, 1, 0.078, 0.003, 0.122, -1.173, 0.167, -1.173, 1, 0.256, -1.173, 0.344, 3.91, 0.433, 3.91, 1, 0.533, 3.91, 0.633, -4.946, 0.733, -4.946, 1, 0.833, -4.946, 0.933, 4.249, 1.033, 4.249, 1, 1.133, 4.249, 1.233, -3.275, 1.333, -3.275, 1, 1.444, -3.275, 1.556, 2.201, 1.667, 2.201, 1, 1.778, 2.201, 1.889, -1.321, 2, -1.321, 1, 2.122, -1.321, 2.244, 0.783, 2.367, 0.783, 1, 2.478, 0.783, 2.589, -0.439, 2.7, -0.439, 1, 2.767, -0.439, 2.833, 0.422, 2.9, 0.422, 1, 3, 0.422, 3.1, -1.32, 3.2, -1.32, 1, 3.322, -1.32, 3.444, 1.928, 3.567, 1.928, 1, 3.678, 1.928, 3.789, -1.699, 3.9, -1.699, 1, 4, -1.699, 4.1, 1.145, 4.2, 1.145, 1, 4.311, 1.145, 4.422, -0.693, 4.533, -0.693, 1, 4.644, -0.693, 4.756, 0.39, 4.867, 0.39, 1, 4.978, 0.39, 5.089, -0.214, 5.2, -0.214, 1, 5.367, -0.214, 5.533, 0.12, 5.7, 0.12, 1, 5.844, 0.12, 5.989, -0.178, 6.133, -0.178, 1, 6.244, -0.178, 6.356, 0.168, 6.467, 0.168, 1, 6.578, 0.168, 6.689, -0.116, 6.8, -0.116, 1, 6.911, -0.116, 7.022, 0.07, 7.133, 0.07, 1, 7.156, 0.07, 7.178, 0.065, 7.2, 0.057]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0.001, 1, 0.011, 0.001, 0.022, -0.024, 0.033, -0.024, 1, 0.111, -0.024, 0.189, 3.62, 0.267, 3.62, 1, 0.356, 3.62, 0.444, -4.075, 0.533, -4.075, 1, 0.633, -4.075, 0.733, 0.879, 0.833, 0.879, 1, 0.944, 0.879, 1.056, -3.6, 1.167, -3.6, 1, 1.289, -3.6, 1.411, -1.125, 1.533, -1.125, 1, 1.656, -1.125, 1.778, -2.154, 1.9, -2.154, 1, 2, -2.154, 2.1, -1.685, 2.2, -1.685, 1, 2.311, -1.685, 2.422, -1.9, 2.533, -1.9, 1, 2.6, -1.9, 2.667, -1.82, 2.733, -1.82, 1, 2.811, -1.82, 2.889, -2.952, 2.967, -2.952, 1, 3.122, -2.952, 3.278, 1.536, 3.433, 1.536, 1, 3.533, 1.536, 3.633, -0.846, 3.733, -0.846, 1, 3.833, -0.846, 3.933, 0.195, 4.033, 0.195, 1, 4.144, 0.195, 4.256, -0.348, 4.367, -0.348, 1, 4.467, -0.348, 4.567, -0.162, 4.667, -0.162, 1, 4.778, -0.162, 4.889, -0.295, 5, -0.295, 1, 5.2, -0.295, 5.4, -0.094, 5.6, -0.094, 1, 5.733, -0.094, 5.867, -0.433, 6, -0.433, 1, 6.1, -0.433, 6.2, -0.184, 6.3, -0.184, 1, 6.411, -0.184, 6.522, -0.298, 6.633, -0.298, 1, 6.733, -0.298, 6.833, -0.245, 6.933, -0.245, 1, 7.022, -0.245, 7.111, -0.261, 7.2, -0.267]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0.001, 1, 0.011, 0.001, 0.022, -0.024, 0.033, -0.024, 1, 0.111, -0.024, 0.189, 3.62, 0.267, 3.62, 1, 0.356, 3.62, 0.444, -4.075, 0.533, -4.075, 1, 0.633, -4.075, 0.733, 0.879, 0.833, 0.879, 1, 0.944, 0.879, 1.056, -3.6, 1.167, -3.6, 1, 1.289, -3.6, 1.411, -1.125, 1.533, -1.125, 1, 1.656, -1.125, 1.778, -2.154, 1.9, -2.154, 1, 2, -2.154, 2.1, -1.685, 2.2, -1.685, 1, 2.311, -1.685, 2.422, -1.9, 2.533, -1.9, 1, 2.6, -1.9, 2.667, -1.82, 2.733, -1.82, 1, 2.811, -1.82, 2.889, -2.952, 2.967, -2.952, 1, 3.122, -2.952, 3.278, 1.536, 3.433, 1.536, 1, 3.533, 1.536, 3.633, -0.846, 3.733, -0.846, 1, 3.833, -0.846, 3.933, 0.195, 4.033, 0.195, 1, 4.144, 0.195, 4.256, -0.348, 4.367, -0.348, 1, 4.467, -0.348, 4.567, -0.162, 4.667, -0.162, 1, 4.778, -0.162, 4.889, -0.295, 5, -0.295, 1, 5.2, -0.295, 5.4, -0.094, 5.6, -0.094, 1, 5.733, -0.094, 5.867, -0.433, 6, -0.433, 1, 6.1, -0.433, 6.2, -0.184, 6.3, -0.184, 1, 6.411, -0.184, 6.522, -0.298, 6.633, -0.298, 1, 6.733, -0.298, 6.833, -0.245, 6.933, -0.245, 1, 7.022, -0.245, 7.111, -0.261, 7.2, -0.267]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0.001, 1, 0.011, 0.001, 0.022, -0.024, 0.033, -0.024, 1, 0.111, -0.024, 0.189, 3.62, 0.267, 3.62, 1, 0.356, 3.62, 0.444, -4.075, 0.533, -4.075, 1, 0.633, -4.075, 0.733, 0.879, 0.833, 0.879, 1, 0.944, 0.879, 1.056, -3.6, 1.167, -3.6, 1, 1.289, -3.6, 1.411, -1.125, 1.533, -1.125, 1, 1.656, -1.125, 1.778, -2.154, 1.9, -2.154, 1, 2, -2.154, 2.1, -1.685, 2.2, -1.685, 1, 2.311, -1.685, 2.422, -1.9, 2.533, -1.9, 1, 2.6, -1.9, 2.667, -1.82, 2.733, -1.82, 1, 2.811, -1.82, 2.889, -2.952, 2.967, -2.952, 1, 3.122, -2.952, 3.278, 1.536, 3.433, 1.536, 1, 3.533, 1.536, 3.633, -0.846, 3.733, -0.846, 1, 3.833, -0.846, 3.933, 0.195, 4.033, 0.195, 1, 4.144, 0.195, 4.256, -0.348, 4.367, -0.348, 1, 4.467, -0.348, 4.567, -0.162, 4.667, -0.162, 1, 4.778, -0.162, 4.889, -0.295, 5, -0.295, 1, 5.2, -0.295, 5.4, -0.094, 5.6, -0.094, 1, 5.733, -0.094, 5.867, -0.433, 6, -0.433, 1, 6.1, -0.433, 6.2, -0.184, 6.3, -0.184, 1, 6.411, -0.184, 6.522, -0.298, 6.633, -0.298, 1, 6.733, -0.298, 6.833, -0.245, 6.933, -0.245, 1, 7.022, -0.245, 7.111, -0.261, 7.2, -0.267]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.017, 0.033, -0.017, 1, 0.111, -0.017, 0.189, 2.606, 0.267, 2.606, 1, 0.356, 2.606, 0.444, -2.934, 0.533, -2.934, 1, 0.633, -2.934, 0.733, 0.633, 0.833, 0.633, 1, 0.944, 0.633, 1.056, -2.592, 1.167, -2.592, 1, 1.289, -2.592, 1.411, -0.81, 1.533, -0.81, 1, 1.656, -0.81, 1.778, -1.551, 1.9, -1.551, 1, 2, -1.551, 2.1, -1.214, 2.2, -1.214, 1, 2.311, -1.214, 2.422, -1.368, 2.533, -1.368, 1, 2.6, -1.368, 2.667, -1.311, 2.733, -1.311, 1, 2.811, -1.311, 2.889, -2.125, 2.967, -2.125, 1, 3.122, -2.125, 3.278, 1.106, 3.433, 1.106, 1, 3.533, 1.106, 3.633, -0.609, 3.733, -0.609, 1, 3.833, -0.609, 3.933, 0.14, 4.033, 0.14, 1, 4.144, 0.14, 4.256, -0.25, 4.367, -0.25, 1, 4.467, -0.25, 4.567, -0.117, 4.667, -0.117, 1, 4.778, -0.117, 4.889, -0.212, 5, -0.212, 1, 5.2, -0.212, 5.4, -0.068, 5.6, -0.068, 1, 5.733, -0.068, 5.867, -0.312, 6, -0.312, 1, 6.1, -0.312, 6.2, -0.133, 6.3, -0.133, 1, 6.411, -0.133, 6.522, -0.214, 6.633, -0.214, 1, 6.733, -0.214, 6.833, -0.177, 6.933, -0.177, 1, 7.022, -0.177, 7.111, -0.188, 7.2, -0.192]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, -0.014, 1, 0.011, -0.014, 0.022, 0.003, 0.033, 0.003, 1, 0.078, 0.003, 0.122, -1.173, 0.167, -1.173, 1, 0.256, -1.173, 0.344, 3.91, 0.433, 3.91, 1, 0.533, 3.91, 0.633, -4.946, 0.733, -4.946, 1, 0.833, -4.946, 0.933, 4.249, 1.033, 4.249, 1, 1.133, 4.249, 1.233, -3.275, 1.333, -3.275, 1, 1.444, -3.275, 1.556, 2.201, 1.667, 2.201, 1, 1.778, 2.201, 1.889, -1.321, 2, -1.321, 1, 2.122, -1.321, 2.244, 0.783, 2.367, 0.783, 1, 2.478, 0.783, 2.589, -0.439, 2.7, -0.439, 1, 2.767, -0.439, 2.833, 0.422, 2.9, 0.422, 1, 3, 0.422, 3.1, -1.32, 3.2, -1.32, 1, 3.322, -1.32, 3.444, 1.928, 3.567, 1.928, 1, 3.678, 1.928, 3.789, -1.699, 3.9, -1.699, 1, 4, -1.699, 4.1, 1.145, 4.2, 1.145, 1, 4.311, 1.145, 4.422, -0.693, 4.533, -0.693, 1, 4.644, -0.693, 4.756, 0.39, 4.867, 0.39, 1, 4.978, 0.39, 5.089, -0.214, 5.2, -0.214, 1, 5.367, -0.214, 5.533, 0.12, 5.7, 0.12, 1, 5.844, 0.12, 5.989, -0.178, 6.133, -0.178, 1, 6.244, -0.178, 6.356, 0.168, 6.467, 0.168, 1, 6.578, 0.168, 6.689, -0.116, 6.8, -0.116, 1, 6.911, -0.116, 7.022, 0.07, 7.133, 0.07, 1, 7.156, 0.07, 7.178, 0.065, 7.2, 0.057]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0.001, 1, 0.011, 0.001, 0.022, -0.024, 0.033, -0.024, 1, 0.111, -0.024, 0.189, 3.62, 0.267, 3.62, 1, 0.356, 3.62, 0.444, -4.075, 0.533, -4.075, 1, 0.633, -4.075, 0.733, 0.879, 0.833, 0.879, 1, 0.944, 0.879, 1.056, -3.6, 1.167, -3.6, 1, 1.289, -3.6, 1.411, -1.125, 1.533, -1.125, 1, 1.656, -1.125, 1.778, -2.154, 1.9, -2.154, 1, 2, -2.154, 2.1, -1.685, 2.2, -1.685, 1, 2.311, -1.685, 2.422, -1.9, 2.533, -1.9, 1, 2.6, -1.9, 2.667, -1.82, 2.733, -1.82, 1, 2.811, -1.82, 2.889, -2.952, 2.967, -2.952, 1, 3.122, -2.952, 3.278, 1.536, 3.433, 1.536, 1, 3.533, 1.536, 3.633, -0.846, 3.733, -0.846, 1, 3.833, -0.846, 3.933, 0.195, 4.033, 0.195, 1, 4.144, 0.195, 4.256, -0.348, 4.367, -0.348, 1, 4.467, -0.348, 4.567, -0.162, 4.667, -0.162, 1, 4.778, -0.162, 4.889, -0.295, 5, -0.295, 1, 5.2, -0.295, 5.4, -0.094, 5.6, -0.094, 1, 5.733, -0.094, 5.867, -0.433, 6, -0.433, 1, 6.1, -0.433, 6.2, -0.184, 6.3, -0.184, 1, 6.411, -0.184, 6.522, -0.298, 6.633, -0.298, 1, 6.733, -0.298, 6.833, -0.245, 6.933, -0.245, 1, 7.022, -0.245, 7.111, -0.261, 7.2, -0.267]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.009, 0.033, -0.009, 1, 0.122, -0.009, 0.211, 1.804, 0.3, 1.804, 1, 0.411, 1.804, 0.522, -0.888, 0.633, -0.888, 1, 0.711, -0.888, 0.789, -0.36, 0.867, -0.36, 1, 1, -0.36, 1.133, -1.071, 1.267, -1.071, 1, 1.411, -1.071, 1.556, -0.571, 1.7, -0.571, 1, 1.822, -0.571, 1.944, -0.794, 2.067, -0.794, 1, 2.189, -0.794, 2.311, -0.718, 2.433, -0.718, 1, 2.622, -0.718, 2.811, -1.336, 3, -1.336, 1, 3.167, -1.336, 3.333, 0.447, 3.5, 0.447, 1, 3.622, 0.447, 3.744, -0.164, 3.867, -0.164, 1, 3.989, -0.164, 4.111, -0.038, 4.233, -0.038, 1, 4.567, -0.038, 4.9, -0.106, 5.233, -0.106, 1, 5.378, -0.106, 5.522, -0.008, 5.667, -0.008, 1, 5.8, -0.008, 5.933, -0.152, 6.067, -0.152, 1, 6.2, -0.152, 6.333, -0.093, 6.467, -0.093, 1, 6.589, -0.093, 6.711, -0.108, 6.833, -0.108, 1, 6.956, -0.108, 7.078, -0.104, 7.2, -0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation2", "Segments": [0, 0.011, 1, 0.011, 0.016, 0.022, 0.021, 0.033, 0.021, 1, 0.1, 0.021, 0.167, -2.3, 0.233, -2.3, 1, 0.322, -2.3, 0.411, 4.229, 0.5, 4.229, 1, 0.6, 4.229, 0.7, -2.247, 0.8, -2.247, 1, 0.911, -2.247, 1.022, 1.236, 1.133, 1.236, 1, 1.256, 1.236, 1.378, -0.822, 1.5, -0.822, 1, 1.622, -0.822, 1.744, 0.503, 1.867, 0.503, 1, 1.989, 0.503, 2.111, -0.259, 2.233, -0.259, 1, 2.356, -0.259, 2.478, 0.099, 2.6, 0.099, 1, 2.644, 0.099, 2.689, 0.051, 2.733, 0.051, 1, 2.8, 0.051, 2.867, 0.703, 2.933, 0.703, 1, 3.056, 0.703, 3.178, -1.479, 3.3, -1.479, 1, 3.433, -1.479, 3.567, 1.319, 3.7, 1.319, 1, 3.811, 1.319, 3.922, -0.588, 4.033, -0.588, 1, 4.167, -0.588, 4.3, 0.224, 4.433, 0.224, 1, 4.556, 0.224, 4.678, -0.066, 4.8, -0.066, 1, 4.922, -0.066, 5.044, 0.02, 5.167, 0.02, 1, 5.289, 0.02, 5.411, -0.109, 5.533, -0.109, 1, 5.667, -0.109, 5.8, 0.159, 5.933, 0.159, 1, 6.044, 0.159, 6.156, -0.134, 6.267, -0.134, 1, 6.389, -0.134, 6.511, 0.06, 6.633, 0.06, 1, 6.756, 0.06, 6.878, -0.022, 7, -0.022, 1, 7.067, -0.022, 7.133, -0.013, 7.2, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation3", "Segments": [0, -0.001, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, -1.971, 0.333, -1.971, 1, 0.433, -1.971, 0.533, 4.086, 0.633, 4.086, 1, 0.744, 4.086, 0.856, -2.851, 0.967, -2.851, 1, 1.078, -2.851, 1.189, 1.715, 1.3, 1.715, 1, 1.411, 1.715, 1.522, -1.206, 1.633, -1.206, 1, 1.767, -1.206, 1.9, 0.801, 2.033, 0.801, 1, 2.156, 0.801, 2.278, -0.445, 2.4, -0.445, 1, 2.611, -0.445, 2.822, 0.594, 3.033, 0.594, 1, 3.167, 0.594, 3.3, -1.762, 3.433, -1.762, 1, 3.567, -1.762, 3.7, 1.718, 3.833, 1.718, 1, 3.956, 1.718, 4.078, -0.994, 4.2, -0.994, 1, 4.322, -0.994, 4.444, 0.465, 4.567, 0.465, 1, 4.689, 0.465, 4.811, -0.176, 4.933, -0.176, 1, 5.056, -0.176, 5.178, 0.061, 5.3, 0.061, 1, 5.422, 0.061, 5.544, -0.118, 5.667, -0.118, 1, 5.8, -0.118, 5.933, 0.198, 6.067, 0.198, 1, 6.189, 0.198, 6.311, -0.177, 6.433, -0.177, 1, 6.556, -0.177, 6.678, 0.101, 6.8, 0.101, 1, 6.922, 0.101, 7.044, -0.046, 7.167, -0.046, 1, 7.178, -0.046, 7.189, -0.045, 7.2, -0.044]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation4", "Segments": [0, -0.028, 1, 0.144, -0.251, 0.289, -1.816, 0.433, -1.816, 1, 0.544, -1.816, 0.656, 4.313, 0.767, 4.313, 1, 0.889, 4.313, 1.011, -3.61, 1.133, -3.61, 1, 1.244, -3.61, 1.356, 2.396, 1.467, 2.396, 1, 1.578, 2.396, 1.689, -1.732, 1.8, -1.732, 1, 1.922, -1.732, 2.044, 1.217, 2.167, 1.217, 1, 2.289, 1.217, 2.411, -0.737, 2.533, -0.737, 1, 2.722, -0.737, 2.911, 0.603, 3.1, 0.603, 1, 3.244, 0.603, 3.389, -2, 3.533, -2, 1, 3.667, -2, 3.8, 2.207, 3.933, 2.207, 1, 4.067, 2.207, 4.2, -1.524, 4.333, -1.524, 1, 4.456, -1.524, 4.578, 0.829, 4.7, 0.829, 1, 4.833, 0.829, 4.967, -0.375, 5.1, -0.375, 1, 5.222, -0.375, 5.344, 0.148, 5.467, 0.148, 1, 5.578, 0.148, 5.689, -0.149, 5.8, -0.149, 1, 5.922, -0.149, 6.044, 0.24, 6.167, 0.24, 1, 6.3, 0.24, 6.433, -0.234, 6.567, -0.234, 1, 6.689, -0.234, 6.811, 0.157, 6.933, 0.157, 1, 7.022, 0.157, 7.111, 0.03, 7.2, -0.039]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation5", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.023, 0.033, -0.023, 1, 0.122, -0.023, 0.211, 4.509, 0.3, 4.509, 1, 0.411, 4.509, 0.522, -2.219, 0.633, -2.219, 1, 0.711, -2.219, 0.789, -0.9, 0.867, -0.9, 1, 1, -0.9, 1.133, -2.676, 1.267, -2.676, 1, 1.411, -2.676, 1.556, -1.427, 1.7, -1.427, 1, 1.822, -1.427, 1.944, -1.984, 2.067, -1.984, 1, 2.189, -1.984, 2.311, -1.795, 2.433, -1.795, 1, 2.622, -1.795, 2.811, -3.341, 3, -3.341, 1, 3.167, -3.341, 3.333, 1.116, 3.5, 1.116, 1, 3.622, 1.116, 3.744, -0.41, 3.867, -0.41, 1, 3.989, -0.41, 4.111, -0.094, 4.233, -0.094, 1, 4.567, -0.094, 4.9, -0.265, 5.233, -0.265, 1, 5.378, -0.265, 5.522, -0.021, 5.667, -0.021, 1, 5.8, -0.021, 5.933, -0.379, 6.067, -0.379, 1, 6.2, -0.379, 6.333, -0.233, 6.467, -0.233, 1, 6.589, -0.233, 6.711, -0.269, 6.833, -0.269, 1, 6.956, -0.269, 7.078, -0.26, 7.2, -0.26]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation6", "Segments": [0, 0.011, 1, 0.011, 0.016, 0.022, 0.021, 0.033, 0.021, 1, 0.1, 0.021, 0.167, -2.3, 0.233, -2.3, 1, 0.322, -2.3, 0.411, 4.229, 0.5, 4.229, 1, 0.6, 4.229, 0.7, -2.247, 0.8, -2.247, 1, 0.911, -2.247, 1.022, 1.236, 1.133, 1.236, 1, 1.256, 1.236, 1.378, -0.822, 1.5, -0.822, 1, 1.622, -0.822, 1.744, 0.503, 1.867, 0.503, 1, 1.989, 0.503, 2.111, -0.259, 2.233, -0.259, 1, 2.356, -0.259, 2.478, 0.099, 2.6, 0.099, 1, 2.644, 0.099, 2.689, 0.051, 2.733, 0.051, 1, 2.8, 0.051, 2.867, 0.703, 2.933, 0.703, 1, 3.056, 0.703, 3.178, -1.479, 3.3, -1.479, 1, 3.433, -1.479, 3.567, 1.319, 3.7, 1.319, 1, 3.811, 1.319, 3.922, -0.588, 4.033, -0.588, 1, 4.167, -0.588, 4.3, 0.224, 4.433, 0.224, 1, 4.556, 0.224, 4.678, -0.066, 4.8, -0.066, 1, 4.922, -0.066, 5.044, 0.02, 5.167, 0.02, 1, 5.289, 0.02, 5.411, -0.109, 5.533, -0.109, 1, 5.667, -0.109, 5.8, 0.159, 5.933, 0.159, 1, 6.044, 0.159, 6.156, -0.134, 6.267, -0.134, 1, 6.389, -0.134, 6.511, 0.06, 6.633, 0.06, 1, 6.756, 0.06, 6.878, -0.022, 7, -0.022, 1, 7.067, -0.022, 7.133, -0.013, 7.2, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation7", "Segments": [0, -0.001, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, -1.971, 0.333, -1.971, 1, 0.433, -1.971, 0.533, 4.086, 0.633, 4.086, 1, 0.744, 4.086, 0.856, -2.851, 0.967, -2.851, 1, 1.078, -2.851, 1.189, 1.715, 1.3, 1.715, 1, 1.411, 1.715, 1.522, -1.206, 1.633, -1.206, 1, 1.767, -1.206, 1.9, 0.801, 2.033, 0.801, 1, 2.156, 0.801, 2.278, -0.445, 2.4, -0.445, 1, 2.611, -0.445, 2.822, 0.594, 3.033, 0.594, 1, 3.167, 0.594, 3.3, -1.762, 3.433, -1.762, 1, 3.567, -1.762, 3.7, 1.718, 3.833, 1.718, 1, 3.956, 1.718, 4.078, -0.994, 4.2, -0.994, 1, 4.322, -0.994, 4.444, 0.465, 4.567, 0.465, 1, 4.689, 0.465, 4.811, -0.176, 4.933, -0.176, 1, 5.056, -0.176, 5.178, 0.061, 5.3, 0.061, 1, 5.422, 0.061, 5.544, -0.118, 5.667, -0.118, 1, 5.8, -0.118, 5.933, 0.198, 6.067, 0.198, 1, 6.189, 0.198, 6.311, -0.177, 6.433, -0.177, 1, 6.556, -0.177, 6.678, 0.101, 6.8, 0.101, 1, 6.922, 0.101, 7.044, -0.046, 7.167, -0.046, 1, 7.178, -0.046, 7.189, -0.045, 7.2, -0.044]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation8", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.009, 0.033, -0.009, 1, 0.122, -0.009, 0.211, 1.804, 0.3, 1.804, 1, 0.411, 1.804, 0.522, -0.888, 0.633, -0.888, 1, 0.711, -0.888, 0.789, -0.36, 0.867, -0.36, 1, 1, -0.36, 1.133, -1.071, 1.267, -1.071, 1, 1.411, -1.071, 1.556, -0.571, 1.7, -0.571, 1, 1.822, -0.571, 1.944, -0.794, 2.067, -0.794, 1, 2.189, -0.794, 2.311, -0.718, 2.433, -0.718, 1, 2.622, -0.718, 2.811, -1.336, 3, -1.336, 1, 3.167, -1.336, 3.333, 0.447, 3.5, 0.447, 1, 3.622, 0.447, 3.744, -0.164, 3.867, -0.164, 1, 3.989, -0.164, 4.111, -0.038, 4.233, -0.038, 1, 4.567, -0.038, 4.9, -0.106, 5.233, -0.106, 1, 5.378, -0.106, 5.522, -0.008, 5.667, -0.008, 1, 5.8, -0.008, 5.933, -0.152, 6.067, -0.152, 1, 6.2, -0.152, 6.333, -0.093, 6.467, -0.093, 1, 6.589, -0.093, 6.711, -0.108, 6.833, -0.108, 1, 6.956, -0.108, 7.078, -0.104, 7.2, -0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation9", "Segments": [0, 0.011, 1, 0.011, 0.016, 0.022, 0.021, 0.033, 0.021, 1, 0.1, 0.021, 0.167, -2.3, 0.233, -2.3, 1, 0.322, -2.3, 0.411, 4.229, 0.5, 4.229, 1, 0.6, 4.229, 0.7, -2.247, 0.8, -2.247, 1, 0.911, -2.247, 1.022, 1.236, 1.133, 1.236, 1, 1.256, 1.236, 1.378, -0.822, 1.5, -0.822, 1, 1.622, -0.822, 1.744, 0.503, 1.867, 0.503, 1, 1.989, 0.503, 2.111, -0.259, 2.233, -0.259, 1, 2.356, -0.259, 2.478, 0.099, 2.6, 0.099, 1, 2.644, 0.099, 2.689, 0.051, 2.733, 0.051, 1, 2.8, 0.051, 2.867, 0.703, 2.933, 0.703, 1, 3.056, 0.703, 3.178, -1.479, 3.3, -1.479, 1, 3.433, -1.479, 3.567, 1.319, 3.7, 1.319, 1, 3.811, 1.319, 3.922, -0.588, 4.033, -0.588, 1, 4.167, -0.588, 4.3, 0.224, 4.433, 0.224, 1, 4.556, 0.224, 4.678, -0.066, 4.8, -0.066, 1, 4.922, -0.066, 5.044, 0.02, 5.167, 0.02, 1, 5.289, 0.02, 5.411, -0.109, 5.533, -0.109, 1, 5.667, -0.109, 5.8, 0.159, 5.933, 0.159, 1, 6.044, 0.159, 6.156, -0.134, 6.267, -0.134, 1, 6.389, -0.134, 6.511, 0.06, 6.633, 0.06, 1, 6.756, 0.06, 6.878, -0.022, 7, -0.022, 1, 7.067, -0.022, 7.133, -0.013, 7.2, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation10", "Segments": [0, -0.001, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, -1.971, 0.333, -1.971, 1, 0.433, -1.971, 0.533, 4.086, 0.633, 4.086, 1, 0.744, 4.086, 0.856, -2.851, 0.967, -2.851, 1, 1.078, -2.851, 1.189, 1.715, 1.3, 1.715, 1, 1.411, 1.715, 1.522, -1.206, 1.633, -1.206, 1, 1.767, -1.206, 1.9, 0.801, 2.033, 0.801, 1, 2.156, 0.801, 2.278, -0.445, 2.4, -0.445, 1, 2.611, -0.445, 2.822, 0.594, 3.033, 0.594, 1, 3.167, 0.594, 3.3, -1.762, 3.433, -1.762, 1, 3.567, -1.762, 3.7, 1.718, 3.833, 1.718, 1, 3.956, 1.718, 4.078, -0.994, 4.2, -0.994, 1, 4.322, -0.994, 4.444, 0.465, 4.567, 0.465, 1, 4.689, 0.465, 4.811, -0.176, 4.933, -0.176, 1, 5.056, -0.176, 5.178, 0.061, 5.3, 0.061, 1, 5.422, 0.061, 5.544, -0.118, 5.667, -0.118, 1, 5.8, -0.118, 5.933, 0.198, 6.067, 0.198, 1, 6.189, 0.198, 6.311, -0.177, 6.433, -0.177, 1, 6.556, -0.177, 6.678, 0.101, 6.8, 0.101, 1, 6.922, 0.101, 7.044, -0.046, 7.167, -0.046, 1, 7.178, -0.046, 7.189, -0.045, 7.2, -0.044]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation11", "Segments": [0, -0.028, 1, 0.144, -0.251, 0.289, -1.816, 0.433, -1.816, 1, 0.544, -1.816, 0.656, 4.313, 0.767, 4.313, 1, 0.889, 4.313, 1.011, -3.61, 1.133, -3.61, 1, 1.244, -3.61, 1.356, 2.396, 1.467, 2.396, 1, 1.578, 2.396, 1.689, -1.732, 1.8, -1.732, 1, 1.922, -1.732, 2.044, 1.217, 2.167, 1.217, 1, 2.289, 1.217, 2.411, -0.737, 2.533, -0.737, 1, 2.722, -0.737, 2.911, 0.603, 3.1, 0.603, 1, 3.244, 0.603, 3.389, -2, 3.533, -2, 1, 3.667, -2, 3.8, 2.207, 3.933, 2.207, 1, 4.067, 2.207, 4.2, -1.524, 4.333, -1.524, 1, 4.456, -1.524, 4.578, 0.829, 4.7, 0.829, 1, 4.833, 0.829, 4.967, -0.375, 5.1, -0.375, 1, 5.222, -0.375, 5.344, 0.148, 5.467, 0.148, 1, 5.578, 0.148, 5.689, -0.149, 5.8, -0.149, 1, 5.922, -0.149, 6.044, 0.24, 6.167, 0.24, 1, 6.3, 0.24, 6.433, -0.234, 6.567, -0.234, 1, 6.689, -0.234, 6.811, 0.157, 6.933, 0.157, 1, 7.022, 0.157, 7.111, 0.03, 7.2, -0.039]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation12", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.023, 0.033, -0.023, 1, 0.122, -0.023, 0.211, 4.509, 0.3, 4.509, 1, 0.411, 4.509, 0.522, -2.219, 0.633, -2.219, 1, 0.711, -2.219, 0.789, -0.9, 0.867, -0.9, 1, 1, -0.9, 1.133, -2.676, 1.267, -2.676, 1, 1.411, -2.676, 1.556, -1.427, 1.7, -1.427, 1, 1.822, -1.427, 1.944, -1.984, 2.067, -1.984, 1, 2.189, -1.984, 2.311, -1.795, 2.433, -1.795, 1, 2.622, -1.795, 2.811, -3.341, 3, -3.341, 1, 3.167, -3.341, 3.333, 1.116, 3.5, 1.116, 1, 3.622, 1.116, 3.744, -0.41, 3.867, -0.41, 1, 3.989, -0.41, 4.111, -0.094, 4.233, -0.094, 1, 4.567, -0.094, 4.9, -0.265, 5.233, -0.265, 1, 5.378, -0.265, 5.522, -0.021, 5.667, -0.021, 1, 5.8, -0.021, 5.933, -0.379, 6.067, -0.379, 1, 6.2, -0.379, 6.333, -0.233, 6.467, -0.233, 1, 6.589, -0.233, 6.711, -0.269, 6.833, -0.269, 1, 6.956, -0.269, 7.078, -0.26, 7.2, -0.26]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation13", "Segments": [0, 0.011, 1, 0.011, 0.016, 0.022, 0.021, 0.033, 0.021, 1, 0.1, 0.021, 0.167, -2.3, 0.233, -2.3, 1, 0.322, -2.3, 0.411, 4.229, 0.5, 4.229, 1, 0.6, 4.229, 0.7, -2.247, 0.8, -2.247, 1, 0.911, -2.247, 1.022, 1.236, 1.133, 1.236, 1, 1.256, 1.236, 1.378, -0.822, 1.5, -0.822, 1, 1.622, -0.822, 1.744, 0.503, 1.867, 0.503, 1, 1.989, 0.503, 2.111, -0.259, 2.233, -0.259, 1, 2.356, -0.259, 2.478, 0.099, 2.6, 0.099, 1, 2.644, 0.099, 2.689, 0.051, 2.733, 0.051, 1, 2.8, 0.051, 2.867, 0.703, 2.933, 0.703, 1, 3.056, 0.703, 3.178, -1.479, 3.3, -1.479, 1, 3.433, -1.479, 3.567, 1.319, 3.7, 1.319, 1, 3.811, 1.319, 3.922, -0.588, 4.033, -0.588, 1, 4.167, -0.588, 4.3, 0.224, 4.433, 0.224, 1, 4.556, 0.224, 4.678, -0.066, 4.8, -0.066, 1, 4.922, -0.066, 5.044, 0.02, 5.167, 0.02, 1, 5.289, 0.02, 5.411, -0.109, 5.533, -0.109, 1, 5.667, -0.109, 5.8, 0.159, 5.933, 0.159, 1, 6.044, 0.159, 6.156, -0.134, 6.267, -0.134, 1, 6.389, -0.134, 6.511, 0.06, 6.633, 0.06, 1, 6.756, 0.06, 6.878, -0.022, 7, -0.022, 1, 7.067, -0.022, 7.133, -0.013, 7.2, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation14", "Segments": [0, -0.001, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, -1.971, 0.333, -1.971, 1, 0.433, -1.971, 0.533, 4.086, 0.633, 4.086, 1, 0.744, 4.086, 0.856, -2.851, 0.967, -2.851, 1, 1.078, -2.851, 1.189, 1.715, 1.3, 1.715, 1, 1.411, 1.715, 1.522, -1.206, 1.633, -1.206, 1, 1.767, -1.206, 1.9, 0.801, 2.033, 0.801, 1, 2.156, 0.801, 2.278, -0.445, 2.4, -0.445, 1, 2.611, -0.445, 2.822, 0.594, 3.033, 0.594, 1, 3.167, 0.594, 3.3, -1.762, 3.433, -1.762, 1, 3.567, -1.762, 3.7, 1.718, 3.833, 1.718, 1, 3.956, 1.718, 4.078, -0.994, 4.2, -0.994, 1, 4.322, -0.994, 4.444, 0.465, 4.567, 0.465, 1, 4.689, 0.465, 4.811, -0.176, 4.933, -0.176, 1, 5.056, -0.176, 5.178, 0.061, 5.3, 0.061, 1, 5.422, 0.061, 5.544, -0.118, 5.667, -0.118, 1, 5.8, -0.118, 5.933, 0.198, 6.067, 0.198, 1, 6.189, 0.198, 6.311, -0.177, 6.433, -0.177, 1, 6.556, -0.177, 6.678, 0.101, 6.8, 0.101, 1, 6.922, 0.101, 7.044, -0.046, 7.167, -0.046, 1, 7.178, -0.046, 7.189, -0.045, 7.2, -0.044]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation15", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.023, 0.033, -0.023, 1, 0.122, -0.023, 0.211, 4.509, 0.3, 4.509, 1, 0.411, 4.509, 0.522, -2.219, 0.633, -2.219, 1, 0.711, -2.219, 0.789, -0.9, 0.867, -0.9, 1, 1, -0.9, 1.133, -2.676, 1.267, -2.676, 1, 1.411, -2.676, 1.556, -1.427, 1.7, -1.427, 1, 1.822, -1.427, 1.944, -1.984, 2.067, -1.984, 1, 2.189, -1.984, 2.311, -1.795, 2.433, -1.795, 1, 2.622, -1.795, 2.811, -3.341, 3, -3.341, 1, 3.167, -3.341, 3.333, 1.116, 3.5, 1.116, 1, 3.622, 1.116, 3.744, -0.41, 3.867, -0.41, 1, 3.989, -0.41, 4.111, -0.094, 4.233, -0.094, 1, 4.567, -0.094, 4.9, -0.265, 5.233, -0.265, 1, 5.378, -0.265, 5.522, -0.021, 5.667, -0.021, 1, 5.8, -0.021, 5.933, -0.379, 6.067, -0.379, 1, 6.2, -0.379, 6.333, -0.233, 6.467, -0.233, 1, 6.589, -0.233, 6.711, -0.269, 6.833, -0.269, 1, 6.956, -0.269, 7.078, -0.26, 7.2, -0.26]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation16", "Segments": [0, 0.011, 1, 0.011, 0.016, 0.022, 0.021, 0.033, 0.021, 1, 0.1, 0.021, 0.167, -2.3, 0.233, -2.3, 1, 0.322, -2.3, 0.411, 4.229, 0.5, 4.229, 1, 0.6, 4.229, 0.7, -2.247, 0.8, -2.247, 1, 0.911, -2.247, 1.022, 1.236, 1.133, 1.236, 1, 1.256, 1.236, 1.378, -0.822, 1.5, -0.822, 1, 1.622, -0.822, 1.744, 0.503, 1.867, 0.503, 1, 1.989, 0.503, 2.111, -0.259, 2.233, -0.259, 1, 2.356, -0.259, 2.478, 0.099, 2.6, 0.099, 1, 2.644, 0.099, 2.689, 0.051, 2.733, 0.051, 1, 2.8, 0.051, 2.867, 0.703, 2.933, 0.703, 1, 3.056, 0.703, 3.178, -1.479, 3.3, -1.479, 1, 3.433, -1.479, 3.567, 1.319, 3.7, 1.319, 1, 3.811, 1.319, 3.922, -0.588, 4.033, -0.588, 1, 4.167, -0.588, 4.3, 0.224, 4.433, 0.224, 1, 4.556, 0.224, 4.678, -0.066, 4.8, -0.066, 1, 4.922, -0.066, 5.044, 0.02, 5.167, 0.02, 1, 5.289, 0.02, 5.411, -0.109, 5.533, -0.109, 1, 5.667, -0.109, 5.8, 0.159, 5.933, 0.159, 1, 6.044, 0.159, 6.156, -0.134, 6.267, -0.134, 1, 6.389, -0.134, 6.511, 0.06, 6.633, 0.06, 1, 6.756, 0.06, 6.878, -0.022, 7, -0.022, 1, 7.067, -0.022, 7.133, -0.013, 7.2, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation17", "Segments": [0, -0.001, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, -1.971, 0.333, -1.971, 1, 0.433, -1.971, 0.533, 4.086, 0.633, 4.086, 1, 0.744, 4.086, 0.856, -2.851, 0.967, -2.851, 1, 1.078, -2.851, 1.189, 1.715, 1.3, 1.715, 1, 1.411, 1.715, 1.522, -1.206, 1.633, -1.206, 1, 1.767, -1.206, 1.9, 0.801, 2.033, 0.801, 1, 2.156, 0.801, 2.278, -0.445, 2.4, -0.445, 1, 2.611, -0.445, 2.822, 0.594, 3.033, 0.594, 1, 3.167, 0.594, 3.3, -1.762, 3.433, -1.762, 1, 3.567, -1.762, 3.7, 1.718, 3.833, 1.718, 1, 3.956, 1.718, 4.078, -0.994, 4.2, -0.994, 1, 4.322, -0.994, 4.444, 0.465, 4.567, 0.465, 1, 4.689, 0.465, 4.811, -0.176, 4.933, -0.176, 1, 5.056, -0.176, 5.178, 0.061, 5.3, 0.061, 1, 5.422, 0.061, 5.544, -0.118, 5.667, -0.118, 1, 5.8, -0.118, 5.933, 0.198, 6.067, 0.198, 1, 6.189, 0.198, 6.311, -0.177, 6.433, -0.177, 1, 6.556, -0.177, 6.678, 0.101, 6.8, 0.101, 1, 6.922, 0.101, 7.044, -0.046, 7.167, -0.046, 1, 7.178, -0.046, 7.189, -0.045, 7.2, -0.044]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation18", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.023, 0.033, -0.023, 1, 0.122, -0.023, 0.211, 4.509, 0.3, 4.509, 1, 0.411, 4.509, 0.522, -2.219, 0.633, -2.219, 1, 0.711, -2.219, 0.789, -0.9, 0.867, -0.9, 1, 1, -0.9, 1.133, -2.676, 1.267, -2.676, 1, 1.411, -2.676, 1.556, -1.427, 1.7, -1.427, 1, 1.822, -1.427, 1.944, -1.984, 2.067, -1.984, 1, 2.189, -1.984, 2.311, -1.795, 2.433, -1.795, 1, 2.622, -1.795, 2.811, -3.341, 3, -3.341, 1, 3.167, -3.341, 3.333, 1.116, 3.5, 1.116, 1, 3.622, 1.116, 3.744, -0.41, 3.867, -0.41, 1, 3.989, -0.41, 4.111, -0.094, 4.233, -0.094, 1, 4.567, -0.094, 4.9, -0.265, 5.233, -0.265, 1, 5.378, -0.265, 5.522, -0.021, 5.667, -0.021, 1, 5.8, -0.021, 5.933, -0.379, 6.067, -0.379, 1, 6.2, -0.379, 6.333, -0.233, 6.467, -0.233, 1, 6.589, -0.233, 6.711, -0.269, 6.833, -0.269, 1, 6.956, -0.269, 7.078, -0.26, 7.2, -0.26]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation19", "Segments": [0, 0.011, 1, 0.011, 0.016, 0.022, 0.021, 0.033, 0.021, 1, 0.1, 0.021, 0.167, -2.3, 0.233, -2.3, 1, 0.322, -2.3, 0.411, 4.229, 0.5, 4.229, 1, 0.6, 4.229, 0.7, -2.247, 0.8, -2.247, 1, 0.911, -2.247, 1.022, 1.236, 1.133, 1.236, 1, 1.256, 1.236, 1.378, -0.822, 1.5, -0.822, 1, 1.622, -0.822, 1.744, 0.503, 1.867, 0.503, 1, 1.989, 0.503, 2.111, -0.259, 2.233, -0.259, 1, 2.356, -0.259, 2.478, 0.099, 2.6, 0.099, 1, 2.644, 0.099, 2.689, 0.051, 2.733, 0.051, 1, 2.8, 0.051, 2.867, 0.703, 2.933, 0.703, 1, 3.056, 0.703, 3.178, -1.479, 3.3, -1.479, 1, 3.433, -1.479, 3.567, 1.319, 3.7, 1.319, 1, 3.811, 1.319, 3.922, -0.588, 4.033, -0.588, 1, 4.167, -0.588, 4.3, 0.224, 4.433, 0.224, 1, 4.556, 0.224, 4.678, -0.066, 4.8, -0.066, 1, 4.922, -0.066, 5.044, 0.02, 5.167, 0.02, 1, 5.289, 0.02, 5.411, -0.109, 5.533, -0.109, 1, 5.667, -0.109, 5.8, 0.159, 5.933, 0.159, 1, 6.044, 0.159, 6.156, -0.134, 6.267, -0.134, 1, 6.389, -0.134, 6.511, 0.06, 6.633, 0.06, 1, 6.756, 0.06, 6.878, -0.022, 7, -0.022, 1, 7.067, -0.022, 7.133, -0.013, 7.2, -0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation20", "Segments": [0, -0.001, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.133, 0, 0.233, -1.971, 0.333, -1.971, 1, 0.433, -1.971, 0.533, 4.086, 0.633, 4.086, 1, 0.744, 4.086, 0.856, -2.851, 0.967, -2.851, 1, 1.078, -2.851, 1.189, 1.715, 1.3, 1.715, 1, 1.411, 1.715, 1.522, -1.206, 1.633, -1.206, 1, 1.767, -1.206, 1.9, 0.801, 2.033, 0.801, 1, 2.156, 0.801, 2.278, -0.445, 2.4, -0.445, 1, 2.611, -0.445, 2.822, 0.594, 3.033, 0.594, 1, 3.167, 0.594, 3.3, -1.762, 3.433, -1.762, 1, 3.567, -1.762, 3.7, 1.718, 3.833, 1.718, 1, 3.956, 1.718, 4.078, -0.994, 4.2, -0.994, 1, 4.322, -0.994, 4.444, 0.465, 4.567, 0.465, 1, 4.689, 0.465, 4.811, -0.176, 4.933, -0.176, 1, 5.056, -0.176, 5.178, 0.061, 5.3, 0.061, 1, 5.422, 0.061, 5.544, -0.118, 5.667, -0.118, 1, 5.8, -0.118, 5.933, 0.198, 6.067, 0.198, 1, 6.189, 0.198, 6.311, -0.177, 6.433, -0.177, 1, 6.556, -0.177, 6.678, 0.101, 6.8, 0.101, 1, 6.922, 0.101, 7.044, -0.046, 7.167, -0.046, 1, 7.178, -0.046, 7.189, -0.045, 7.2, -0.044]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation21", "Segments": [0, 0, 1, 0.089, 0, 0.178, 1.053, 0.267, 1.053, 1, 0.356, 1.053, 0.444, -0.3, 0.533, -0.3, 1, 0.6, -0.3, 0.667, 0.425, 0.733, 0.425, 1, 0.878, 0.425, 1.022, -4.183, 1.167, -4.183, 1, 1.322, -4.183, 1.478, -2.687, 1.633, -2.687, 1, 1.7, -2.687, 1.767, -2.849, 1.833, -2.849, 1, 1.944, -2.849, 2.056, -2.426, 2.167, -2.426, 1, 2.278, -2.426, 2.389, -3.004, 2.5, -3.004, 1, 2.8, -3.004, 3.1, 0.77, 3.4, 0.77, 1, 3.478, 0.77, 3.556, 0.339, 3.633, 0.339, 1, 3.756, 0.339, 3.878, 1.247, 4, 1.247, 1, 4.167, 1.247, 4.333, -0.039, 4.5, -0.039, 1, 4.667, -0.039, 4.833, 0.858, 5, 0.858, 1, 5.156, 0.858, 5.311, 0.034, 5.467, 0.034, 1, 5.656, 0.034, 5.844, 0.43, 6.033, 0.43, 1, 6.156, 0.43, 6.278, 0.326, 6.4, 0.326, 1, 6.522, 0.326, 6.644, 0.356, 6.767, 0.356, 1, 6.889, 0.356, 7.011, 0.347, 7.133, 0.347, 1, 7.156, 0.347, 7.178, 0.347, 7.2, 0.347]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation22", "Segments": [0, 0.001, 1, 0.011, 0.001, 0.022, -0.419, 0.033, -0.419, 1, 0.044, -0.419, 0.056, -0.357, 0.067, -0.357, 1, 0.111, -0.357, 0.156, -0.477, 0.2, -0.477, 1, 0.289, -0.477, 0.378, 1.105, 0.467, 1.105, 1, 0.544, 1.105, 0.622, -0.881, 0.7, -0.881, 1, 0.8, -0.881, 0.9, 1.961, 1, 1.961, 1, 1.111, 1.961, 1.222, -1.346, 1.333, -1.346, 1, 1.456, -1.346, 1.578, 0.612, 1.7, 0.612, 1, 1.822, 0.612, 1.944, -0.444, 2.067, -0.444, 1, 2.167, -0.444, 2.267, 0.446, 2.367, 0.446, 1, 2.467, 0.446, 2.567, -0.296, 2.667, -0.296, 1, 2.733, -0.296, 2.8, -0.093, 2.867, -0.093, 1, 2.967, -0.093, 3.067, -0.866, 3.167, -0.866, 1, 3.289, -0.866, 3.411, 0.807, 3.533, 0.807, 1, 3.644, 0.807, 3.756, -0.709, 3.867, -0.709, 1, 3.989, -0.709, 4.111, 0.564, 4.233, 0.564, 1, 4.378, 0.564, 4.522, -0.378, 4.667, -0.378, 1, 4.844, -0.378, 5.022, 0.26, 5.2, 0.26, 1, 5.344, 0.26, 5.489, -0.223, 5.633, -0.223, 1, 5.744, -0.223, 5.856, 0.058, 5.967, 0.058, 1, 5.989, 0.058, 6.011, 0.055, 6.033, 0.055, 1, 6.067, 0.055, 6.1, 0.059, 6.133, 0.059, 1, 6.267, 0.059, 6.4, -0.039, 6.533, -0.039, 1, 6.656, -0.039, 6.778, 0.018, 6.9, 0.018, 1, 7, 0.018, 7.1, -0.002, 7.2, -0.006]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation23", "Segments": [0, 0.003, 1, 0.1, 0.003, 0.2, -0.505, 0.3, -0.505, 1, 0.4, -0.505, 0.5, 0.753, 0.6, 0.753, 1, 0.678, 0.753, 0.756, -0.709, 0.833, -0.709, 1, 0.922, -0.709, 1.011, 1.534, 1.1, 1.534, 1, 1.211, 1.534, 1.322, -1.39, 1.433, -1.39, 1, 1.556, -1.39, 1.678, 0.81, 1.8, 0.81, 1, 1.922, 0.81, 2.044, -0.556, 2.167, -0.556, 1, 2.278, -0.556, 2.389, 0.465, 2.5, 0.465, 1, 2.6, 0.465, 2.7, -0.342, 2.8, -0.342, 1, 2.867, -0.342, 2.933, -0.11, 3, -0.11, 1, 3.1, -0.11, 3.2, -0.635, 3.3, -0.635, 1, 3.411, -0.635, 3.522, 0.779, 3.633, 0.779, 1, 3.744, 0.779, 3.856, -0.766, 3.967, -0.766, 1, 4.089, -0.766, 4.211, 0.629, 4.333, 0.629, 1, 4.467, 0.629, 4.6, -0.39, 4.733, -0.39, 1, 4.911, -0.39, 5.089, 0.202, 5.267, 0.202, 1, 5.422, 0.202, 5.578, -0.184, 5.733, -0.184, 1, 5.856, -0.184, 5.978, 0.087, 6.1, 0.087, 1, 6.267, 0.087, 6.433, -0.035, 6.6, -0.035, 1, 6.722, -0.035, 6.844, 0.021, 6.967, 0.021, 1, 7.044, 0.021, 7.122, 0.008, 7.2, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation24", "Segments": [0, 0.005, 1, 0.111, 0.005, 0.222, -0.225, 0.333, -0.225, 1, 0.444, -0.225, 0.556, 0.648, 0.667, 0.648, 1, 0.744, 0.648, 0.822, -0.309, 0.9, -0.309, 1, 0.989, -0.309, 1.078, 1.508, 1.167, 1.508, 1, 1.278, 1.508, 1.389, -1.748, 1.5, -1.748, 1, 1.611, -1.748, 1.722, 1.12, 1.833, 1.12, 1, 1.956, 1.12, 2.078, -0.683, 2.2, -0.683, 1, 2.311, -0.683, 2.422, 0.54, 2.533, 0.54, 1, 2.656, 0.54, 2.778, -0.462, 2.9, -0.462, 1, 2.967, -0.462, 3.033, -0.279, 3.1, -0.279, 1, 3.178, -0.279, 3.256, -0.661, 3.333, -0.661, 1, 3.456, -0.661, 3.578, 0.819, 3.7, 0.819, 1, 3.811, 0.819, 3.922, -0.785, 4.033, -0.785, 1, 4.144, -0.785, 4.256, 0.758, 4.367, 0.758, 1, 4.489, 0.758, 4.611, -0.514, 4.733, -0.514, 1, 4.889, -0.514, 5.044, 0.253, 5.2, 0.253, 1, 5.389, 0.253, 5.578, -0.208, 5.767, -0.208, 1, 5.889, -0.208, 6.011, 0.139, 6.133, 0.139, 1, 6.267, 0.139, 6.4, -0.046, 6.533, -0.046, 1, 6.689, -0.046, 6.844, 0.024, 7, 0.024, 1, 7.067, 0.024, 7.133, 0.013, 7.2, 0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation25", "Segments": [0, 0.011, 1, 0.011, 0.013, 0.022, 0.014, 0.033, 0.014, 1, 0.156, 0.014, 0.278, -0.277, 0.4, -0.277, 1, 0.511, -0.277, 0.622, 0.765, 0.733, 0.765, 1, 0.811, 0.765, 0.889, -0.662, 0.967, -0.662, 1, 1.067, -0.662, 1.167, 1.871, 1.267, 1.871, 1, 1.367, 1.871, 1.467, -2.428, 1.567, -2.428, 1, 1.678, -2.428, 1.789, 1.84, 1.9, 1.84, 1, 2.011, 1.84, 2.122, -1.17, 2.233, -1.17, 1, 2.356, -1.17, 2.478, 0.862, 2.6, 0.862, 1, 2.711, 0.862, 2.822, -0.67, 2.933, -0.67, 1, 3.022, -0.67, 3.111, -0.054, 3.2, -0.054, 1, 3.278, -0.054, 3.356, -0.666, 3.433, -0.666, 1, 3.544, -0.666, 3.656, 1.094, 3.767, 1.094, 1, 3.878, 1.094, 3.989, -1.155, 4.1, -1.155, 1, 4.211, -1.155, 4.322, 1.13, 4.433, 1.13, 1, 4.556, 1.13, 4.678, -0.816, 4.8, -0.816, 1, 4.933, -0.816, 5.067, 0.385, 5.2, 0.385, 1, 5.411, 0.385, 5.622, -0.24, 5.833, -0.24, 1, 5.956, -0.24, 6.078, 0.207, 6.2, 0.207, 1, 6.322, 0.207, 6.444, -0.093, 6.567, -0.093, 1, 6.722, -0.093, 6.878, 0.03, 7.033, 0.03, 1, 7.089, 0.03, 7.144, 0.021, 7.2, 0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation26", "Segments": [0, 0, 1, 0.089, 0, 0.178, 1.053, 0.267, 1.053, 1, 0.356, 1.053, 0.444, -0.3, 0.533, -0.3, 1, 0.6, -0.3, 0.667, 0.425, 0.733, 0.425, 1, 0.878, 0.425, 1.022, -4.183, 1.167, -4.183, 1, 1.322, -4.183, 1.478, -2.687, 1.633, -2.687, 1, 1.7, -2.687, 1.767, -2.849, 1.833, -2.849, 1, 1.944, -2.849, 2.056, -2.426, 2.167, -2.426, 1, 2.278, -2.426, 2.389, -3.004, 2.5, -3.004, 1, 2.8, -3.004, 3.1, 0.77, 3.4, 0.77, 1, 3.478, 0.77, 3.556, 0.339, 3.633, 0.339, 1, 3.756, 0.339, 3.878, 1.247, 4, 1.247, 1, 4.167, 1.247, 4.333, -0.039, 4.5, -0.039, 1, 4.667, -0.039, 4.833, 0.858, 5, 0.858, 1, 5.156, 0.858, 5.311, 0.034, 5.467, 0.034, 1, 5.656, 0.034, 5.844, 0.43, 6.033, 0.43, 1, 6.156, 0.43, 6.278, 0.326, 6.4, 0.326, 1, 6.522, 0.326, 6.644, 0.356, 6.767, 0.356, 1, 6.889, 0.356, 7.011, 0.347, 7.133, 0.347, 1, 7.156, 0.347, 7.178, 0.347, 7.2, 0.347]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation27", "Segments": [0, 0.001, 1, 0.011, 0.001, 0.022, -0.419, 0.033, -0.419, 1, 0.044, -0.419, 0.056, -0.357, 0.067, -0.357, 1, 0.111, -0.357, 0.156, -0.477, 0.2, -0.477, 1, 0.289, -0.477, 0.378, 1.105, 0.467, 1.105, 1, 0.544, 1.105, 0.622, -0.881, 0.7, -0.881, 1, 0.8, -0.881, 0.9, 1.961, 1, 1.961, 1, 1.111, 1.961, 1.222, -1.346, 1.333, -1.346, 1, 1.456, -1.346, 1.578, 0.612, 1.7, 0.612, 1, 1.822, 0.612, 1.944, -0.444, 2.067, -0.444, 1, 2.167, -0.444, 2.267, 0.446, 2.367, 0.446, 1, 2.467, 0.446, 2.567, -0.296, 2.667, -0.296, 1, 2.733, -0.296, 2.8, -0.093, 2.867, -0.093, 1, 2.967, -0.093, 3.067, -0.866, 3.167, -0.866, 1, 3.289, -0.866, 3.411, 0.807, 3.533, 0.807, 1, 3.644, 0.807, 3.756, -0.709, 3.867, -0.709, 1, 3.989, -0.709, 4.111, 0.564, 4.233, 0.564, 1, 4.378, 0.564, 4.522, -0.378, 4.667, -0.378, 1, 4.844, -0.378, 5.022, 0.26, 5.2, 0.26, 1, 5.344, 0.26, 5.489, -0.223, 5.633, -0.223, 1, 5.744, -0.223, 5.856, 0.058, 5.967, 0.058, 1, 5.989, 0.058, 6.011, 0.055, 6.033, 0.055, 1, 6.067, 0.055, 6.1, 0.059, 6.133, 0.059, 1, 6.267, 0.059, 6.4, -0.039, 6.533, -0.039, 1, 6.656, -0.039, 6.778, 0.018, 6.9, 0.018, 1, 7, 0.018, 7.1, -0.002, 7.2, -0.006]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation28", "Segments": [0, 0.003, 1, 0.1, 0.003, 0.2, -0.505, 0.3, -0.505, 1, 0.4, -0.505, 0.5, 0.753, 0.6, 0.753, 1, 0.678, 0.753, 0.756, -0.709, 0.833, -0.709, 1, 0.922, -0.709, 1.011, 1.534, 1.1, 1.534, 1, 1.211, 1.534, 1.322, -1.39, 1.433, -1.39, 1, 1.556, -1.39, 1.678, 0.81, 1.8, 0.81, 1, 1.922, 0.81, 2.044, -0.556, 2.167, -0.556, 1, 2.278, -0.556, 2.389, 0.465, 2.5, 0.465, 1, 2.6, 0.465, 2.7, -0.342, 2.8, -0.342, 1, 2.867, -0.342, 2.933, -0.11, 3, -0.11, 1, 3.1, -0.11, 3.2, -0.635, 3.3, -0.635, 1, 3.411, -0.635, 3.522, 0.779, 3.633, 0.779, 1, 3.744, 0.779, 3.856, -0.766, 3.967, -0.766, 1, 4.089, -0.766, 4.211, 0.629, 4.333, 0.629, 1, 4.467, 0.629, 4.6, -0.39, 4.733, -0.39, 1, 4.911, -0.39, 5.089, 0.202, 5.267, 0.202, 1, 5.422, 0.202, 5.578, -0.184, 5.733, -0.184, 1, 5.856, -0.184, 5.978, 0.087, 6.1, 0.087, 1, 6.267, 0.087, 6.433, -0.035, 6.6, -0.035, 1, 6.722, -0.035, 6.844, 0.021, 6.967, 0.021, 1, 7.044, 0.021, 7.122, 0.008, 7.2, -0.001]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation29", "Segments": [0, 0.005, 1, 0.111, 0.005, 0.222, -0.225, 0.333, -0.225, 1, 0.444, -0.225, 0.556, 0.648, 0.667, 0.648, 1, 0.744, 0.648, 0.822, -0.309, 0.9, -0.309, 1, 0.989, -0.309, 1.078, 1.508, 1.167, 1.508, 1, 1.278, 1.508, 1.389, -1.748, 1.5, -1.748, 1, 1.611, -1.748, 1.722, 1.12, 1.833, 1.12, 1, 1.956, 1.12, 2.078, -0.683, 2.2, -0.683, 1, 2.311, -0.683, 2.422, 0.54, 2.533, 0.54, 1, 2.656, 0.54, 2.778, -0.462, 2.9, -0.462, 1, 2.967, -0.462, 3.033, -0.279, 3.1, -0.279, 1, 3.178, -0.279, 3.256, -0.661, 3.333, -0.661, 1, 3.456, -0.661, 3.578, 0.819, 3.7, 0.819, 1, 3.811, 0.819, 3.922, -0.785, 4.033, -0.785, 1, 4.144, -0.785, 4.256, 0.758, 4.367, 0.758, 1, 4.489, 0.758, 4.611, -0.514, 4.733, -0.514, 1, 4.889, -0.514, 5.044, 0.253, 5.2, 0.253, 1, 5.389, 0.253, 5.578, -0.208, 5.767, -0.208, 1, 5.889, -0.208, 6.011, 0.139, 6.133, 0.139, 1, 6.267, 0.139, 6.4, -0.046, 6.533, -0.046, 1, 6.689, -0.046, 6.844, 0.024, 7, 0.024, 1, 7.067, 0.024, 7.133, 0.013, 7.2, 0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation30", "Segments": [0, 0.011, 1, 0.011, 0.013, 0.022, 0.014, 0.033, 0.014, 1, 0.156, 0.014, 0.278, -0.277, 0.4, -0.277, 1, 0.511, -0.277, 0.622, 0.765, 0.733, 0.765, 1, 0.811, 0.765, 0.889, -0.662, 0.967, -0.662, 1, 1.067, -0.662, 1.167, 1.871, 1.267, 1.871, 1, 1.367, 1.871, 1.467, -2.428, 1.567, -2.428, 1, 1.678, -2.428, 1.789, 1.84, 1.9, 1.84, 1, 2.011, 1.84, 2.122, -1.17, 2.233, -1.17, 1, 2.356, -1.17, 2.478, 0.862, 2.6, 0.862, 1, 2.711, 0.862, 2.822, -0.67, 2.933, -0.67, 1, 3.022, -0.67, 3.111, -0.054, 3.2, -0.054, 1, 3.278, -0.054, 3.356, -0.666, 3.433, -0.666, 1, 3.544, -0.666, 3.656, 1.094, 3.767, 1.094, 1, 3.878, 1.094, 3.989, -1.155, 4.1, -1.155, 1, 4.211, -1.155, 4.322, 1.13, 4.433, 1.13, 1, 4.556, 1.13, 4.678, -0.816, 4.8, -0.816, 1, 4.933, -0.816, 5.067, 0.385, 5.2, 0.385, 1, 5.411, 0.385, 5.622, -0.24, 5.833, -0.24, 1, 5.956, -0.24, 6.078, 0.207, 6.2, 0.207, 1, 6.322, 0.207, 6.444, -0.093, 6.567, -0.093, 1, 6.722, -0.093, 6.878, 0.03, 7.033, 0.03, 1, 7.089, 0.03, 7.144, 0.021, 7.2, 0.01]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation41", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.089, 0, 0.144, -1.815, 0.2, -1.815, 1, 0.3, -1.815, 0.4, 3.195, 0.5, 3.195, 1, 0.589, 3.195, 0.678, -1.54, 0.767, -1.54, 1, 0.867, -1.54, 0.967, 1.49, 1.067, 1.49, 1, 1.167, 1.49, 1.267, -0.923, 1.367, -0.923, 1, 1.5, -0.923, 1.633, 0.312, 1.767, 0.312, 1, 1.889, 0.312, 2.011, -0.091, 2.133, -0.091, 1, 2.256, -0.091, 2.378, 0.024, 2.5, 0.024, 1, 2.578, 0.024, 2.656, -0.001, 2.733, -0.001, 1, 2.8, -0.001, 2.867, 1.351, 2.933, 1.351, 1, 3.056, 1.351, 3.178, -2.333, 3.3, -2.333, 1, 3.422, -2.333, 3.544, 1.824, 3.667, 1.824, 1, 3.789, 1.824, 3.911, -0.669, 4.033, -0.669, 1, 4.144, -0.669, 4.256, 0.225, 4.367, 0.225, 1, 4.489, 0.225, 4.611, -0.045, 4.733, -0.045, 1, 4.844, -0.045, 4.956, 0.016, 5.067, 0.016, 1, 5.167, 0.016, 5.267, -0.007, 5.367, -0.007, 1, 5.489, -0.007, 5.611, 0.002, 5.733, 0.002, 1, 5.844, 0.002, 5.956, -0.001, 6.067, -0.001, 1, 6.189, -0.001, 6.311, 0, 6.433, 0, 1, 6.544, 0, 6.656, 0, 6.767, 0, 1, 6.778, 0, 6.789, 0, 6.8, 0, 1, 6.9, 0, 7, 0, 7.1, 0, 1, 7.133, 0, 7.167, 0, 7.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, -0.043, 1, 0.1, -0.293, 0.2, -1.544, 0.3, -1.544, 1, 0.411, -1.544, 0.522, 2.966, 0.633, 2.966, 1, 0.722, 2.966, 0.811, -1.655, 0.9, -1.655, 1, 1, -1.655, 1.1, 1.546, 1.2, 1.546, 1, 1.311, 1.546, 1.422, -1.179, 1.533, -1.179, 1, 1.656, -1.179, 1.778, 0.539, 1.9, 0.539, 1, 2.022, 0.539, 2.144, -0.195, 2.267, -0.195, 1, 2.389, -0.195, 2.511, 0.062, 2.633, 0.062, 1, 2.678, 0.062, 2.722, 0.038, 2.767, 0.038, 1, 2.856, 0.038, 2.944, 1.177, 3.033, 1.177, 1, 3.156, 1.177, 3.278, -2.751, 3.4, -2.751, 1, 3.533, -2.751, 3.667, 2.259, 3.8, 2.259, 1, 3.922, 2.259, 4.044, -1.076, 4.167, -1.076, 1, 4.278, -1.076, 4.389, 0.429, 4.5, 0.429, 1, 4.622, 0.429, 4.744, -0.126, 4.867, -0.126, 1, 4.989, -0.126, 5.111, 0.039, 5.233, 0.039, 1, 5.344, 0.039, 5.456, -0.014, 5.567, -0.014, 1, 5.678, -0.014, 5.789, 0.005, 5.9, 0.005, 1, 6.011, 0.005, 6.122, -0.002, 6.233, -0.002, 1, 6.356, -0.002, 6.478, 0.001, 6.6, 0.001, 1, 6.711, 0.001, 6.822, 0, 6.933, 0, 1, 7.022, 0, 7.111, 0, 7.2, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, -0.021, 1, 0.133, -0.208, 0.267, -1.421, 0.4, -1.421, 1, 0.511, -1.421, 0.622, 3.041, 0.733, 3.041, 1, 0.833, 3.041, 0.933, -1.911, 1.033, -1.911, 1, 1.133, -1.911, 1.233, 1.639, 1.333, 1.639, 1, 1.444, 1.639, 1.556, -1.432, 1.667, -1.432, 1, 1.789, -1.432, 1.911, 0.809, 2.033, 0.809, 1, 2.156, 0.809, 2.278, -0.349, 2.4, -0.349, 1, 2.522, -0.349, 2.644, 0.13, 2.767, 0.13, 1, 2.778, 0.13, 2.789, 0.127, 2.8, 0.127, 1, 2.911, 0.127, 3.022, 1.077, 3.133, 1.077, 1, 3.267, 1.077, 3.4, -3.087, 3.533, -3.087, 1, 3.656, -3.087, 3.778, 2.779, 3.9, 2.779, 1, 4.033, 2.779, 4.167, -1.57, 4.3, -1.57, 1, 4.411, -1.57, 4.522, 0.727, 4.633, 0.727, 1, 4.756, 0.727, 4.878, -0.264, 5, -0.264, 1, 5.122, -0.264, 5.244, 0.09, 5.367, 0.09, 1, 5.478, 0.09, 5.589, -0.031, 5.7, -0.031, 1, 5.822, -0.031, 5.944, 0.011, 6.067, 0.011, 1, 6.178, 0.011, 6.289, -0.004, 6.4, -0.004, 1, 6.511, -0.004, 6.622, 0.001, 6.733, 0.001, 1, 6.856, 0.001, 6.978, 0, 7.1, 0, 1, 7.133, 0, 7.167, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "neko", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "game", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "gitar", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "things", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "hair", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "eyebrows", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "mouth", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 0, 0, 7.2, 0]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Leye", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "face", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 7.2, 1]}, {"Target": "PartOpacity", "Id": "PartSketch0", "Segments": [0, 1, 0, 7.2, 1]}]}