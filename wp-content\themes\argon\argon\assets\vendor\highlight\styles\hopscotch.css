/*
 * Hopscotch
 * by <PERSON>
 * https://github.com/idleberg/Hopscotch
 *
 * This work is licensed under the Creative Commons CC0 1.0 Universal License
 */

/* Comment */
.hljs-comment,
.hljs-quote {
  color: #989498;
}

/* Red */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-link,
.hljs-deletion {
  color: #dd464c;
}

/* Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
  color: #fd8b19;
}

/* Yellow */
.hljs-class .hljs-title {
  color: #fdcc59;
}

/* Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
  color: #8fc13e;
}

/* Aqua */
.hljs-meta {
  color: #149b93;
}

/* Blue */
.hljs-function,
.hljs-section,
.hljs-title {
  color: #1290bf;
}

/* Purple */
.hljs-keyword,
.hljs-selector-tag {
  color: #c85e7c;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #322931;
  color: #b9b5b8;
  padding: 0.5em;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
