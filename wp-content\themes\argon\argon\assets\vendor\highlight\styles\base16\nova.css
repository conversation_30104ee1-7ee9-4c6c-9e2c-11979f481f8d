/*!
  Theme: Nova
  Author: <PERSON> (https://github.com/gessig), <PERSON> (https://trevordmiller.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/

/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme nova
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/

/*
base00  #3C4C55  Default Background
base01  #556873  Lighter Background (Used for status bars, line number and folding marks)
base02  #6A7D89  Selection Background
base03  #899BA6  Comments, Invisibles, Line Highlighting
base04  #899BA6  Dark Foreground (Used for status bars)
base05  #C5D4DD  Default Foreground, Caret, Delimiters, Operators
base06  #899BA6  Light Foreground (Not often used)
base07  #556873  Light Background (Not often used)
base08  #83AFE5  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #7FC1CA  Integers, <PERSON><PERSON>an, Constants, XML Attributes, Markup Link Url
base0A  #A8CE93  Classes, Markup Bold, Search Text Background
base0B  #7FC1CA  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #F2C38F  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #83AFE5  Functions, Methods, Attribute IDs, Headings
base0E  #9A93E1  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #F2C38F  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #C5D4DD;
  background: #3C4C55;
}

.hljs::selection,
.hljs ::selection {
  background-color: #6A7D89;
  color: #C5D4DD;
}


/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property
{}

/* base03 - #899BA6 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #899BA6;
}

/* base04 - #899BA6 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #899BA6;
}

/* base05 - #C5D4DD -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #C5D4DD;
}

.hljs-operator {
  opacity: 0.7;
}

/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #83AFE5;
}

/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #7FC1CA;
}

/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_
{
  color: #A8CE93;
}

.hljs-strong {
  font-weight:bold;
  color: #A8CE93;
}

/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7FC1CA;
}

/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
.hljs-built_in,
.hljs-doctag, /* guessing */
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #F2C38F;
}

/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #83AFE5;
}

/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
.hljs-type,
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9A93E1;
}
.hljs-emphasis {
  color: #9A93E1;
  font-style: italic;
}

/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
.hljs-meta,
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string
{
  color: #F2C38F;
}

.hljs-meta .hljs-keyword,
/* for v10 compatible themes */
.hljs-meta-keyword {
  font-weight: bold;
}
