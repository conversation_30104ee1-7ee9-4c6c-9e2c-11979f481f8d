/* Base16 Atelier Forest Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/forest) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON>on/base16) */

/* Atelier-Forest Comment */
.hljs-comment,
.hljs-quote {
  color: #9c9491;
}

/* Atelier-Forest Red */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-name,
.hljs-regexp,
.hljs-link,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #f22c40;
}

/* Atelier-Forest Orange */
.hljs-number,
.hljs-meta,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
  color: #df5320;
}

/* Atelier-Forest Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet {
  color: #7b9726;
}

/* Atelier-Forest Blue */
.hljs-title,
.hljs-section {
  color: #407ee7;
}

/* Atelier-Forest Purple */
.hljs-keyword,
.hljs-selector-tag {
  color: #6666ea;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #1b1918;
  color: #a8a19f;
  padding: 0.5em;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
